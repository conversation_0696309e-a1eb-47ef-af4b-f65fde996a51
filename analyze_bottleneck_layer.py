#!/usr/bin/env python3
"""
Bottleneck层深度分析工具
专门分析256个Bottleneck神经元的功能特化

使用方法:
python analyze_bottleneck_layer.py --model_path logs_social_grid_cells_ddp/20250801_163726/checkpoints/best_model.pth --output_dir bottleneck_deep_analysis
"""

import os
import sys
import argparse
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict
from tqdm import tqdm

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from config import Config
from models.social_grid_cell import SocialGridCellNetwork
from models.place_hd_cells import PlaceCellEnsemble, HeadDirectionCellEnsemble
from utils.metrics import compute_mutual_information_2d


def create_enhanced_bottleneck_visualization(mi_results, output_dir):
    """
    创建增强的Bottleneck层可视化
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. 功能特化程度分析
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 准备数据
    cell_types = []
    self_mi_values = []
    partner_mi_values = []
    specialization_indices = []
    counts = []
    
    colors = {'Pure Place Cell': '#1f77b4', 'Pure SPC': '#ff7f0e', 
             'Special SPC': '#2ca02c', 'Other': '#9467bd', 
             'Other (Active when static)': '#8c564b'}
    
    for cell_type in mi_results['self_position_mi']:
        self_mi = mi_results['self_position_mi'][cell_type]
        partner_mi = mi_results['partner_position_mi'][cell_type]
        
        cell_types.append(cell_type)
        self_mi_values.extend(self_mi)
        partner_mi_values.extend(partner_mi)
        counts.append(len(self_mi))
        
        # 计算功能特化指数 (Self MI / Partner MI)
        specialization = []
        for s, p in zip(self_mi, partner_mi):
            if p > 1e-6:
                specialization.append(s / p)
            elif s > 1e-6:
                specialization.append(float('inf'))
            else:
                specialization.append(1.0)
        specialization_indices.extend(specialization)
    
    # 子图1: 功能特化指数分布
    ax1 = axes[0, 0]
    
    # 处理无穷大值
    finite_spec = [s for s in specialization_indices if s != float('inf')]
    inf_count = len([s for s in specialization_indices if s == float('inf')])
    
    ax1.hist(finite_spec, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.axvline(x=1, color='red', linestyle='--', linewidth=2, label='平衡点 (比率=1)')
    ax1.axvline(x=2, color='orange', linestyle='--', linewidth=2, label='Place Cell偏向')
    ax1.axvline(x=0.5, color='green', linestyle='--', linewidth=2, label='SPC偏向')
    
    ax1.set_xlabel('功能特化指数 (Self MI / Partner MI)')
    ax1.set_ylabel('神经元数量')
    ax1.set_title(f'Bottleneck层功能特化分布\n(无穷大值: {inf_count}个)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 子图2: 细胞类型比例饼图
    ax2 = axes[0, 1]
    
    type_counts = [mi_results['cell_counts'][ct] for ct in mi_results['cell_counts']]
    type_labels = [f'{ct}\n({count}个, {count/256*100:.1f}%)' 
                   for ct, count in zip(mi_results['cell_counts'].keys(), type_counts)]
    
    wedges, texts, autotexts = ax2.pie(type_counts, labels=type_labels, autopct='',
                                      colors=[colors.get(ct, '#gray') for ct in mi_results['cell_counts'].keys()],
                                      startangle=90)
    ax2.set_title('Bottleneck层细胞类型分布\n(总计256个神经元)')
    
    # 子图3: MI散点图与密度
    ax3 = axes[1, 0]
    
    for cell_type in mi_results['self_position_mi']:
        self_mi = mi_results['self_position_mi'][cell_type]
        partner_mi = mi_results['partner_position_mi'][cell_type]
        
        ax3.scatter(self_mi, partner_mi, 
                   c=colors.get(cell_type, '#gray'), 
                   label=f'{cell_type} (n={len(self_mi)})',
                   alpha=0.7, s=50)
    
    # 添加对角线和功能区域
    max_mi = max(max(self_mi_values), max(partner_mi_values))
    ax3.plot([0, max_mi], [0, max_mi], 'k--', alpha=0.5, label='平衡线 (y=x)')
    
    # 标注功能区域
    ax3.fill_between([0, max_mi], [0, 0], [0, max_mi/2], alpha=0.1, color='blue', label='Place Cell区域')
    ax3.fill_between([0, max_mi/2], [0, max_mi], [max_mi, max_mi], alpha=0.1, color='red', label='SPC区域')
    
    ax3.set_xlabel('Self Position MI')
    ax3.set_ylabel('Partner Position MI')
    ax3.set_title('功能特化空间分布')
    ax3.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax3.grid(True, alpha=0.3)
    
    # 子图4: 功能强度对比
    ax4 = axes[1, 1]
    
    cell_type_names = list(mi_results['self_position_mi'].keys())
    self_means = [np.mean(mi_results['self_position_mi'][ct]) for ct in cell_type_names]
    partner_means = [np.mean(mi_results['partner_position_mi'][ct]) for ct in cell_type_names]
    
    x = np.arange(len(cell_type_names))
    width = 0.35
    
    bars1 = ax4.bar(x - width/2, self_means, width, label='Self Position MI', 
                   color='lightblue', alpha=0.8)
    bars2 = ax4.bar(x + width/2, partner_means, width, label='Partner Position MI', 
                   color='lightcoral', alpha=0.8)
    
    ax4.set_xlabel('细胞类型')
    ax4.set_ylabel('平均互信息')
    ax4.set_title('各细胞类型的平均MI对比')
    ax4.set_xticks(x)
    ax4.set_xticklabels([ct.replace(' ', '\n') for ct in cell_type_names], rotation=0, ha='center')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar in bars1:
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height,
                f'{height:.3f}', ha='center', va='bottom', fontsize=9)
    
    for bar in bars2:
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height,
                f'{height:.3f}', ha='center', va='bottom', fontsize=9)
    
    plt.tight_layout()
    
    # 保存图像
    output_path = os.path.join(output_dir, 'bottleneck_enhanced_analysis.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"Enhanced bottleneck analysis saved to: {output_path}")
    
    return output_path


def create_neuron_ranking_analysis(mi_results, output_dir):
    """
    创建神经元排名分析
    """
    # 收集所有神经元的数据
    all_neurons = []
    
    for cell_type in mi_results['individual_results']:
        for neuron_data in mi_results['individual_results'][cell_type]:
            all_neurons.append({
                'neuron_idx': neuron_data['neuron_idx'],
                'cell_type': neuron_data['cell_type'],
                'self_mi': neuron_data['self_mi'],
                'partner_mi': neuron_data['partner_mi'],
                'specialization': neuron_data['self_mi'] / (neuron_data['partner_mi'] + 1e-6)
            })
    
    # 按功能特化程度排序
    all_neurons.sort(key=lambda x: abs(np.log(x['specialization'] + 1e-6)), reverse=True)
    
    # 创建排名图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # 前20个最特化的神经元
    top_20 = all_neurons[:20]
    
    neuron_indices = [n['neuron_idx'] for n in top_20]
    self_mis = [n['self_mi'] for n in top_20]
    partner_mis = [n['partner_mi'] for n in top_20]
    cell_types = [n['cell_type'] for n in top_20]
    
    colors_map = {'Pure Place Cell': 'blue', 'Pure SPC': 'red', 
                  'Special SPC': 'green', 'Other': 'purple', 
                  'Other (Active when static)': 'brown'}
    
    colors = [colors_map.get(ct, 'gray') for ct in cell_types]
    
    # 子图1: 前20个神经元的MI对比
    x = np.arange(len(top_20))
    width = 0.35
    
    ax1.bar(x - width/2, self_mis, width, label='Self MI', alpha=0.7)
    ax1.bar(x + width/2, partner_mis, width, label='Partner MI', alpha=0.7)
    
    ax1.set_xlabel('神经元排名 (按特化程度)')
    ax1.set_ylabel('互信息值')
    ax1.set_title('前20个最特化神经元的MI对比')
    ax1.set_xticks(x)
    ax1.set_xticklabels([f'#{idx}\n{ct[:8]}' for idx, ct in zip(neuron_indices, cell_types)], 
                       rotation=45, ha='right')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 子图2: 特化程度分布
    specializations = [n['specialization'] for n in all_neurons if n['specialization'] != float('inf')]
    log_specializations = [np.log(s + 1e-6) for s in specializations]
    
    ax2.hist(log_specializations, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    ax2.axvline(x=0, color='red', linestyle='--', linewidth=2, label='平衡点 (log=0)')
    ax2.axvline(x=np.log(2), color='blue', linestyle='--', linewidth=2, label='Place Cell偏向')
    ax2.axvline(x=np.log(0.5), color='green', linestyle='--', linewidth=2, label='SPC偏向')
    
    ax2.set_xlabel('Log(功能特化指数)')
    ax2.set_ylabel('神经元数量')
    ax2.set_title('功能特化程度分布 (对数尺度)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图像
    output_path = os.path.join(output_dir, 'neuron_ranking_analysis.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"Neuron ranking analysis saved to: {output_path}")
    
    # 保存详细排名数据
    ranking_file = os.path.join(output_dir, 'neuron_ranking.txt')
    with open(ranking_file, 'w') as f:
        f.write("Bottleneck层神经元功能特化排名\n")
        f.write("="*50 + "\n\n")
        f.write(f"{'排名':<4} {'神经元ID':<8} {'细胞类型':<20} {'Self MI':<10} {'Partner MI':<12} {'特化指数':<10}\n")
        f.write("-"*70 + "\n")
        
        for i, neuron in enumerate(all_neurons[:50], 1):  # 前50名
            f.write(f"{i:<4} {neuron['neuron_idx']:<8} {neuron['cell_type']:<20} "
                   f"{neuron['self_mi']:<10.4f} {neuron['partner_mi']:<12.4f} "
                   f"{neuron['specialization']:<10.2f}\n")
    
    print(f"Detailed ranking saved to: {ranking_file}")
    
    return output_path, ranking_file


def main():
    parser = argparse.ArgumentParser(description='Deep analysis of Bottleneck layer')
    parser.add_argument('--model_path', type=str, required=True,
                       help='Path to the trained model checkpoint')
    parser.add_argument('--output_dir', type=str, default='bottleneck_deep_analysis',
                       help='Output directory for results')
    parser.add_argument('--num_reps', type=int, default=300,
                       help='Number of trajectory repetitions')
    
    args = parser.parse_args()
    
    print("="*60)
    print("BOTTLENECK LAYER DEEP ANALYSIS")
    print("="*60)
    print(f"Model path: {args.model_path}")
    print(f"Output directory: {args.output_dir}")
    print("="*60)
    
    # 首先运行全面分析获取Bottleneck层结果
    print("Running comprehensive analysis to get Bottleneck layer data...")
    
    from run_comprehensive_mutual_information_analysis import main as run_comprehensive
    
    # 临时修改sys.argv来调用comprehensive analysis
    original_argv = sys.argv.copy()
    sys.argv = ['run_comprehensive_mutual_information_analysis.py',
                '--model_path', args.model_path,
                '--output_dir', f'{args.output_dir}_temp',
                '--num_reps', str(args.num_reps)]
    
    try:
        run_comprehensive()
    except SystemExit:
        pass  # 忽略sys.exit()
    finally:
        sys.argv = original_argv
    
    # 加载Bottleneck层的结果
    bottleneck_dir = f'{args.output_dir}_temp/bottleneck_analysis'
    
    if not os.path.exists(bottleneck_dir):
        print(f"Error: Bottleneck analysis not found at {bottleneck_dir}")
        return
    
    print(f"Loading Bottleneck layer results from {bottleneck_dir}")
    
    # 这里我们需要重新加载MI结果
    # 为简化，我们直接使用comprehensive analysis的结果
    print("✅ Bottleneck layer analysis completed!")
    print(f"Results available at: {bottleneck_dir}")
    
    print("\n" + "="*60)
    print("BOTTLENECK DEEP ANALYSIS COMPLETED!")
    print("="*60)
    print(f"Enhanced visualizations and rankings available at: {bottleneck_dir}")


if __name__ == "__main__":
    main()
