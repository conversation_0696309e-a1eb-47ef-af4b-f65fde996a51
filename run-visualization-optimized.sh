#!/bin/bash
# 运行Grid Cell可视化脚本的示例

# 设置环境变量
export PYTHONPATH=$PYTHONPATH:$(pwd)

# 创建输出目录
OUTPUT_DIR="grid_viz_results-3"
mkdir -p $OUTPUT_DIR

# 配置参数
MODEL_PATH="/data1/fangzr/Research/Memory_Maze_3D/Multi-com/maze-zero/grid-cell-dual/checkpoints/latest_chunk_4.pth"  # 替换为你的模型路径
BATCH_SIZE=64
NUM_BATCHES=10000  # 增大批次数以获取更多数据点
DEVICE="cuda:3"  # 使用GPU，如果没有则使用CPU

# 运行可视化脚本
python grid_cell_visualization.py \
    --gamma 10.0 \
    --outlier_threshold 2.5 \
    --model_path $MODEL_PATH \
    --save_dir $OUTPUT_DIR \
    --batch_size $BATCH_SIZE \
    --num_batches $NUM_BATCHES \
    --device $DEVICE \
    --prefix "nature_style" \
    --split "val"

echo "Grid cell可视化完成！结果保存在 $OUTPUT_DIR 目录"