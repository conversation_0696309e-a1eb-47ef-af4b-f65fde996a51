# train_social_grid_cells.py
import torch
import torch.optim as optim
import os
import time
from collections import defaultdict
import numpy as np
from tqdm import tqdm
import argparse
import json

# 从本地文件导入
from config import Config
from models.social_grid_cell import SocialGridCellNetwork, SocialNavigationLoss
from datasets.social_navigation_dataset import SocialNavigationDataset
from models.place_hd_cells import PlaceCellEnsemble, HeadDirectionCellEnsemble

def main():
    """
    Main function to train the SocialGridCellNetwork.
    This script is now driven by the parameters in config.py.
    """
    parser = argparse.ArgumentParser(description="Train a Social Grid Cell Network.")
    parser.add_argument('--resume', type=str, default=None, help='Path to checkpoint to resume training from.')
    args = parser.parse_args()

    # --- 1. 配置 ---
    # 使用 config.py 中的默认配置
    config = Config()
    if args.resume:
        config.RESUME = args.resume

    # 创建一个带时间戳的实验目录来保存所有产出物
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    exp_dir = os.path.join("logs_social_grid_cells", timestamp)
    config.CHECKPOINT_DIR = os.path.join(exp_dir, "checkpoints")
    os.makedirs(config.CHECKPOINT_DIR, exist_ok=True)
    
    # 保存本次运行的配置，以便复现
    with open(os.path.join(exp_dir, 'config.json'), 'w') as f:
        # 创建一个可序列化的字典
        config_dict = {k: v for k, v in config.__dict__.items() if not k.startswith('__') and not callable(v)}
        json.dump(config_dict, f, indent=4)
        
    print(f"Experiment started. Checkpoints and logs will be saved in: {exp_dir}")

    # 设置随机种子和设备
    torch.manual_seed(config.SEED)
    np.random.seed(config.SEED)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # --- 2. 准备数据 ---
    print("Initializing dataset...")
    # 如果配置的数据目录不存在，创建虚拟数据用于演示
    if not os.path.exists(config.DATA_ROOT):
        print(f"Data root '{config.DATA_ROOT}' not found. Creating dummy data for demonstration.")
        from datasets.social_navigation_dataset import SingleMazeDataset # 仅用于创建假数据
        dummy_root = config.DATA_ROOT
        os.makedirs(dummy_root, exist_ok=True)
        for i in range(5):
            traj_dir = os.path.join(dummy_root, f'trajectory_{i}')
            os.makedirs(traj_dir, exist_ok=True)
            dummy_data_gen = SingleMazeDataset(traj_dir)
            np.save(os.path.join(traj_dir, 'trajectory.npy'), dummy_data_gen.data)

    dataset = SocialNavigationDataset(root_dir=config.DATA_ROOT, sequence_length=config.SEQUENCE_LENGTH)
    data_loader = torch.utils.data.DataLoader(
        dataset, 
        batch_size=config.BATCH_SIZE, 
        shuffle=True, 
        num_workers=config.NUM_WORKERS, 
        pin_memory=config.PIN_MEMORY
    )
    
    # --- 3. 初始化模型、损失函数和优化器 ---
    print("Initializing model...")
    place_cells = PlaceCellEnsemble(
        n_cells=config.PLACE_CELLS_N, 
        scale=config.PLACE_CELLS_SCALE,
        pos_min=0,
        pos_max=config.ENV_SIZE,
        seed=config.SEED
    )
    hd_cells = HeadDirectionCellEnsemble(
        n_cells=config.HD_CELLS_N, 
        concentration=config.HD_CELLS_CONCENTRATION,
        seed=config.SEED
    )

    # 为模型创建一个专用的配置字典
    model_config = {
        'hidden_size': config.HIDDEN_SIZE,
        'bottleneck_size': config.LATENT_DIM, # 使用 LATENT_DIM 作为 bottleneck 大小
        'dropout_rate': config.DROPOUT_RATE,
    }

    model = SocialGridCellNetwork(place_cells, hd_cells, model_config).to(device)
    criterion = SocialNavigationLoss()
    # 使用 Nature 论文推荐的优化器参数
    optimizer = optim.Adam(model.parameters(), lr=config.NATURE_LEARNING_RATE, weight_decay=config.NATURE_WEIGHT_DECAY)
    
    print(f"Model initialized with {sum(p.numel() for p in model.parameters()):,} parameters.")

    # --- 4. 从检查点恢复 (如果提供) ---
    start_epoch = 0
    metrics_history = defaultdict(list)
    if config.RESUME:
        print(f"Resuming from checkpoint: {config.RESUME}")
        if os.path.exists(config.RESUME):
            checkpoint = torch.load(config.RESUME, map_location=device)
            model.load_state_dict(checkpoint['model_state_dict'])
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            start_epoch = checkpoint.get('epoch', 0)
            metrics_history = checkpoint.get('metrics_history', defaultdict(list))
            print(f"Resumed successfully from epoch {start_epoch}.")
        else:
            print(f"Warning: Checkpoint path '{config.RESUME}' not found. Starting from scratch.")

    # --- 5. 训练循环 ---
    print("Starting training loop...")
    for epoch in range(start_epoch, config.NUM_EPOCHS):
        model.train()
        epoch_losses = defaultdict(float)
        
        progress_bar = tqdm(data_loader, desc=f"Epoch {epoch+1}/{config.NUM_EPOCHS}", leave=True)
        
        for batch in progress_bar:
            # 将数据移动到设备
            self_vel = torch.cat([batch['self_velocities'], batch['self_angular_velocities'].unsqueeze(-1)], dim=-1).to(device)
            peer_vel = torch.cat([batch['peer_velocities'], batch['peer_angular_velocities'].unsqueeze(-1)], dim=-1).to(device)
            
            self_init_pos = batch['self_positions'][:, 0, :].to(device)
            self_init_hd = batch['self_angles'][:, 0].to(device)
            peer_init_pos = batch['peer_positions'][:, 0, :].to(device)
            peer_init_hd = batch['peer_angles'][:, 0].to(device)

            # 计算目标 (ground truth)
            with torch.no_grad():
                self_place_targets = place_cells.compute_activation(batch['self_positions'].to(device))
                self_hd_targets = hd_cells.compute_activation(batch['self_angles'].to(device))
                peer_place_targets = place_cells.compute_activation(batch['peer_positions'].to(device))
                peer_hd_targets = hd_cells.compute_activation(batch['peer_angles'].to(device))
            
            targets = {
                'self_place_targets': self_place_targets, 'self_hd_targets': self_hd_targets,
                'peer_place_targets': peer_place_targets, 'peer_hd_targets': peer_hd_targets,
            }

            # 前向传播、计算损失、反向传播
            outputs = model(self_vel, self_init_pos, self_init_hd, peer_vel, peer_init_pos, peer_init_hd)
            loss_dict = criterion(outputs, targets)
            loss = loss_dict['total']
            
            optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), config.GRAD_CLIP)
            optimizer.step()
            
            # 记录损失
            for key, value in loss_dict.items():
                epoch_losses[key] += value if isinstance(value, float) else value.item()
            
            progress_bar.set_postfix(loss=f"{loss.item():.4f}")

        # 打印周期日志
        num_batches = len(data_loader)
        print(f"\n--- Epoch {epoch+1} Summary ---")
        for key, total_loss in epoch_losses.items():
            avg_loss = total_loss / num_batches
            metrics_history[key].append(avg_loss)
            print(f"  - Average {key} loss: {avg_loss:.4f}")
        print("--------------------------")

        # 根据 SAVE_FREQUENCY 保存检查点
        if (epoch + 1) % config.SAVE_FREQUENCY == 0:
            save_path = os.path.join(config.CHECKPOINT_DIR, f'social_model_epoch_{epoch+1}.pth')
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'metrics_history': metrics_history,
                'config': config_dict, # 保存配置
            }, save_path)
            print(f"Checkpoint saved to {save_path}")
            
    print("\nTraining complete!")
    # 保存最终模型
    final_save_path = os.path.join(config.CHECKPOINT_DIR, 'social_model_final.pth')
    torch.save({
        'epoch': config.NUM_EPOCHS,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'metrics_history': metrics_history,
        'config': config_dict,
    }, final_save_path)
    print(f"Final model saved to {final_save_path}")

if __name__ == '__main__':
    main()
