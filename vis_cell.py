import os
import torch
import numpy as np
import matplotlib.pyplot as plt
import math

from config import Config
from models.toroidal_grid_cell import GridCellNetwork
from models.place_hd_cells import PlaceCellEnsemble, HeadDirectionCellEnsemble
from datasets.navigation_dataset import SingleMazeDataset

def build_and_load_model(checkpoint_path, config):
    """
    构造与训练时一致的模型，并加载checkpoint权重
    """
    place_cells = PlaceCellEnsemble(
        n_cells=config.PLACE_CELLS_N,
        scale=config.PLACE_CELLS_SCALE,
        pos_min=0,
        pos_max=config.ENV_SIZE,
        seed=config.SEED
    )
    hd_cells = HeadDirectionCellEnsemble(
        n_cells=config.HD_CELLS_N,
        concentration=config.HD_CELLS_CONCENTRATION,
        seed=config.SEED
    )
    model = GridCellNetwork(
        place_cells=place_cells,
        hd_cells=hd_cells,
        input_size=3,
        hidden_size=config.HIDDEN_SIZE,
        bottleneck_size=256,
        dropout_rate=config.DROPOUT_RATE
    )
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    return model, place_cells, hd_cells

def load_single_trajectory(trajectory_dir, sequence_length=100, stride=1):
    """
    从指定轨迹文件夹中加载连续序列数据，返回位置、角度、速度、角速度张量。
    """
    dataset = SingleMazeDataset(trajectory_dir, sequence_length=sequence_length, stride=stride)
    if len(dataset) == 0:
        raise ValueError(f"轨迹 {trajectory_dir} 没有足够连续的帧数据！")
    sample = dataset[0]
    positions = sample['positions']            # [S, 2]
    angles = sample['angles']                  # [S]
    velocities = sample['velocities']          # [S, 2]
    angular_vels = sample['angular_velocities']# [S]
    return positions, angles, velocities, angular_vels

def prepare_inference_input(positions, angles, velocities, angular_vels):
    """
    组装模型输入：
      velocity_input: [1, S, 3] 其中 3 表示 [vx, vy, ω]
      init_pos: [1, 2]，取序列第一帧的位置
      init_hd:  [1]，取序列第一帧的角度
    """
    w = angular_vels.unsqueeze(-1)  # [S,1]
    velocity_input = torch.cat([velocities, w], dim=-1).unsqueeze(0)  # [1,S,3]
    init_pos = positions[0].unsqueeze(0)  # [1,2]
    init_hd = angles[0].unsqueeze(0)        # [1]
    return velocity_input, init_pos, init_hd

def decode_predictions(place_logits, hd_logits, place_cells, hd_cells):
    """
    根据预测的 place_logits 和 hd_logits 解码预测位置和朝向。
    对于位置：先计算 softmax 后用预测概率加权 place_cells 的中心；
    对于朝向：将每个 HD cell 的角度用复数表示，再计算加权复数平均，最后取角度。
    输入：
      place_logits: [S, n_place]
      hd_logits: [S, n_hd]
    返回：
      pred_positions: [S, 2]
      pred_angles: [S]
    """
    # 计算概率分布
    place_probs = torch.softmax(place_logits, dim=-1)  # [S, n_place]
    hd_probs = torch.softmax(hd_logits, dim=-1)  # [S, n_hd]

    # 解码位置：利用 place_cells.means (shape: [n_place, 2])
    # 注意：place_cells.means 在 CPU 上可能为 numpy 数组，这里转换为 tensor
    cell_centers = place_cells.means.to(place_probs.device)  # [n_place,2]
    pred_positions = torch.matmul(place_probs, cell_centers)  # [S,2]
    
    # 解码朝向：利用 HD cell 的均值 (角度)
    hd_means = hd_cells.means.to(hd_probs.device)  # [n_hd]
    # 将每个角度转换为复数表示
    hd_complex = torch.exp(1j * hd_means)  # [n_hd]
    # 计算复数加权平均
    pred_hd_complex = torch.sum(hd_probs * hd_complex, dim=-1)  # [S]
    pred_angles = torch.angle(pred_hd_complex)  # [S]
    return pred_positions, pred_angles

def visualize_trajectory(gt_positions, pred_positions, gt_angles, pred_angles,
                         save_path, maze_size=15):
    """
    绘制真实轨迹和预测轨迹的对比图，并显示位置和旋转误差。
    gt_positions, pred_positions: [S, 2] numpy arrays
    gt_angles, pred_angles: [S] numpy arrays, 单位弧度
    maze_size: 迷宫尺寸，用于设定坐标轴范围
    """
    # 计算误差 (排除第一个点的误差，因为第一个点是输入不是预测)
    if len(gt_positions) > 1:
        pos_errors = np.linalg.norm(gt_positions[1:] - pred_positions[1:], axis=1)
        mean_pos_error = np.mean(pos_errors)
        # 计算角度误差（取绝对最小角差）
        angle_errors = np.abs(np.angle(np.exp(1j * (gt_angles[1:] - pred_angles[1:]))))
        mean_angle_error = np.mean(angle_errors)
    else:
        mean_pos_error = 0.0
        mean_angle_error = 0.0

    plt.figure(figsize=(10, 8))

    # -- 绘制 GT 轨迹 (连线 + 整体点)
    plt.plot(gt_positions[:, 0], gt_positions[:, 1], 'o-', color='blue',
             label="GT Trajectory", markersize=3, alpha=0.7)

    # 标记 GT 起点(用一个更大的圆) 与 终点(用三角形)
    plt.plot(gt_positions[0, 0], gt_positions[0, 1], 'bo', markersize=10, label='GT/Pred Start (Given)')
    plt.plot(gt_positions[-1, 0], gt_positions[-1, 1], 'b^', markersize=10, label='GT End')

    # -- 绘制 预测轨迹 (连线 + 整体点)
    plt.plot(pred_positions[:, 0], pred_positions[:, 1], 'x-', color='orange',
             label="Predicted Trajectory", markersize=3)

    # 标记 Pred 终点(用三角形)
    # 注：我们不再标记预测的起点，因为它和GT起点是一样的
    plt.plot(pred_positions[-1, 0], pred_positions[-1, 1], '^', color='orange',
             markersize=10, label='Pred End')

    # 设置坐标轴范围
    plt.xlim(0, maze_size)
    plt.ylim(0, maze_size)

    plt.xlabel("X")
    plt.ylabel("Y")
    plt.title(f"Trajectory Comparison (Error Excludes t=0)\n"
              f"Mean Pos Error: {mean_pos_error:.3f}, Mean Rot Error: {mean_angle_error:.3f}")
    plt.legend()
    plt.grid(True)
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"Trajectory visualization saved to: {save_path}")


def main():
    # 配置参数（根据实际情况修改）
    config = Config()
    # Best model
    checkpoint_path = "/data1/fangzr/Research/Memory_Maze_3D/grid-cell-no-img/log_train_grid_cells/20250314_172658/checkpoints/best_model.pth"
    # 单条轨迹数据文件夹
    trajectory_dir = "/data1/fangzr/Research/Memory_Maze_3D/Dataset/self-navigation-maze-frame-only/D9983_P1/000000"
    save_viz_path = "./trajectory_comparison.png"
    
    # 加载模型
    model, place_cells, hd_cells = build_and_load_model(checkpoint_path, config)
    
    # 加载单条轨迹数据
    positions, angles, velocities, angular_vels = load_single_trajectory(trajectory_dir, sequence_length=100, stride=1)
    print(f"Loaded trajectory with {positions.shape[0]} frames.")
    
    # 准备模型输入
    velocity_input, init_pos, init_hd = prepare_inference_input(positions, angles, velocities, angular_vels)

    # Ground Truth 位置 & 角度
    gt_positions = positions.cpu().numpy()  # [S,2]
    gt_angles = angles.cpu().numpy()  # [S]
    
    # 推理 - 使用完整的模型前向传播
    with torch.no_grad():
        outputs = model(velocity_input, init_pos, init_hd)
        place_logits = outputs['place_logits'].squeeze(0)  # [S, n_place]
        hd_logits = outputs['hd_logits'].squeeze(0)  # [S, n_hd]
        
        # 解码预测的位置和角度
        pred_positions, pred_angles = decode_predictions(place_logits, hd_logits, place_cells, hd_cells)
        pred_positions = pred_positions.cpu().numpy()  # [S,2]
        pred_angles = pred_angles.cpu().numpy()  # [S]
    
    # 修改：确保第一个点完全对齐
    pred_positions[0] = gt_positions[0]  # 使用真实的第一个位置
    pred_angles[0] = gt_angles[0]  # 使用真实的第一个角度
    
    # 进行可视化，强调我们只关注从t=1开始的预测能力
    print(f"GT positions shape: {gt_positions.shape}, Pred positions shape: {pred_positions.shape}")
    visualize_trajectory(gt_positions, pred_positions, gt_angles, pred_angles, save_viz_path)
    
    # 生成额外的指标：计算除第一个点外的所有点的误差
    if len(gt_positions) > 1:
        pos_errors = np.linalg.norm(gt_positions[1:] - pred_positions[1:], axis=1)
        mean_pos_error = np.mean(pos_errors)
        max_pos_error = np.max(pos_errors)
        # 角度误差
        angle_errors = np.abs(np.angle(np.exp(1j * (gt_angles[1:] - pred_angles[1:]))))
        mean_angle_error = np.mean(angle_errors)
        max_angle_error = np.max(angle_errors)
        
        print(f"评估指标 (排除t=0):")
        print(f"  位置误差: 平均 {mean_pos_error:.3f}m, 最大 {max_pos_error:.3f}m")
        print(f"  角度误差: 平均 {mean_angle_error:.3f}rad, 最大 {max_angle_error:.3f}rad")
        print(f"  角度误差: 平均 {np.degrees(mean_angle_error):.1f}°, 最大 {np.degrees(max_angle_error):.1f}°")


if __name__ == "__main__":
    main()