# train_grid_cells.py

import os
import sys
import time
import shutil
import argparse
from collections import defaultdict

import torch
import torch.nn as nn
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP
import numpy as np

from config import Config
# from models.toroidal_grid_cell import GridCellNetwork, GridCellSupervisedLoss
from models.toroidal_grid_cell import GridCellNetwork, InitialStateRegularizedLoss
from models.place_hd_cells import PlaceCellEnsemble, HeadDirectionCellEnsemble
from datasets.navigation_dataset import EnhancedNavigationDataset
from utils.visualization import NavigationVisualizer

import torch.multiprocessing as mp
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from tqdm import tqdm
from contextlib import contextmanager

# Add this near the top of your train_grid_cells.py file, after the imports

# Worker shutdown handling
import signal
import gc


def limit_worker_memory():
    """Limit worker memory usage to prevent OOM errors"""
    try:
        # Set soft limit to 2GB per worker
        soft, hard = resource.getrlimit(resource.RLIMIT_AS)
        # 2GB memory limit per worker
        resource.setrlimit(resource.RLIMIT_AS, (2 * 1024 * 1024 * 1024, hard))  
        
        # Also limit CPU time to prevent infinite loops
        soft, hard = resource.getrlimit(resource.RLIMIT_CPU)
        # 1 hour CPU time limit per worker
        resource.setrlimit(resource.RLIMIT_CPU, (60 * 60, hard))  
        
        # Make sure system calls fail rather than block indefinitely
        # Set timeout for system calls
        soft, hard = resource.getrlimit(resource.RLIMIT_NPROC) 
        # 1024 child processes per worker
        resource.setrlimit(resource.RLIMIT_NPROC, (1024, hard))  
    except Exception as e:
        print(f"Warning: Could not set resource limits: {e}")

# Modify init_worker function to include memory limits
def init_worker():
    """Initialize worker process with resource limits"""
    # Ignore SIGINT in worker processes
    signal.signal(signal.SIGINT, signal.SIG_IGN)
    
    # Set resource limits
    limit_worker_memory()
    
    # Set a more graceful way to handle SIGTERM
    def graceful_exit(signum, frame):
        print(f"Worker process received signal {signum}, exiting gracefully...")
        gc.collect()  # Explicitly run garbage collection
        sys.exit(0)
    
    signal.signal(signal.SIGTERM, graceful_exit)
    signal.signal(signal.SIGABRT, graceful_exit)


def backup_code(source_dir, backup_dir):
    """备份代码"""
    os.makedirs(backup_dir, exist_ok=True)
    dirs_to_backup = ['models', 'utils', 'datasets']
    files_to_backup = ['train_grid_cells.py', 'config.py']
    for dir_name in dirs_to_backup:
        src_dir = os.path.join(source_dir, dir_name)
        dst_dir = os.path.join(backup_dir, dir_name)
        if os.path.exists(src_dir):
            shutil.copytree(src_dir, dst_dir, dirs_exist_ok=True)
    for file_name in files_to_backup:
        src_file = os.path.join(source_dir, file_name)
        dst_file = os.path.join(backup_dir, file_name)
        if os.path.exists(src_file):
            shutil.copy2(src_file, dst_file)


class TeeStream:
    """同时输出到文件和终端的流"""

    def __init__(self, file_stream, terminal_stream):
        self.file_stream = file_stream
        self.terminal_stream = terminal_stream

    def write(self, data):
        self.file_stream.write(data)
        self.terminal_stream.write(data)
        self.terminal_stream.flush()  # 确保终端实时显示

    def flush(self):
        self.file_stream.flush()
        self.terminal_stream.flush()


def setup_logger(log_dir):
    """同时输出到日志文件和终端"""
    log_file = os.path.join(log_dir, "train.log")
    file_stream = open(log_file, "w")

    # 保存原始的stdout和stderr
    original_stdout = sys.stdout
    original_stderr = sys.stderr

    # 创建新的输出流
    sys.stdout = TeeStream(file_stream, original_stdout)
    sys.stderr = TeeStream(file_stream, original_stderr)

    print(f"Logging started. All outputs will be saved to {log_file}")


# 新增：简化的可视化器，只绘制损失曲线和模型输出，不依赖图像输入
class SimpleNavigationVisualizer:
    def __init__(self, save_dir):
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
    
    def plot_metrics(self, metrics_history, epoch):
        """绘制训练/验证损失"""
        plt.figure(figsize=(12, 6))
        
        # 绘制总损失
        plt.subplot(1, 3, 1)
        plt.plot(metrics_history['train_total'], label='Train')
        plt.plot(metrics_history['val_total'], label='Val')
        plt.title('Total Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        
        # 绘制位置损失
        plt.subplot(1, 3, 2)
        plt.plot(metrics_history['train_place'], label='Train')
        plt.plot(metrics_history['val_place'], label='Val')
        plt.title('Place Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        
        # 绘制头部方向损失
        plt.subplot(1, 3, 3)
        plt.plot(metrics_history['train_hd'], label='Train')
        plt.plot(metrics_history['val_hd'], label='Val')
        plt.title('HD Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, f'loss_epoch_{epoch}.png'))
        plt.close()
    
    def visualize_grid_cells(self, model, dataloader, device, epoch):
        """可视化栅格细胞活动模式（不需要图像输入）"""
        model.eval()
        
        # 提取一个批次的数据用于可视化
        batch = next(iter(dataloader))
        
        positions = batch['positions'].to(device)
        angles = batch['angles'].to(device)
        velocities = batch['velocities'].to(device)
        ang_vels = batch['angular_velocities'].to(device)
        
        with torch.no_grad():
            # 准备模型输入
            w = ang_vels.unsqueeze(-1)
            velocity_input = torch.cat([velocities, w], dim=-1)
            init_pos = positions[:,0]
            init_hd = angles[:,0]
            
            # 获取模型输出
            outputs = model(velocity_input, init_pos, init_hd)
            
            # 提取bottleneck特征
            bottleneck = outputs['bottleneck'][0].reshape(-1, model.module.bottleneck_size if isinstance(model, DDP) else model.bottleneck_size)
            
            # 计算相关性矩阵分析空间周期性
            corr_matrix = torch.corrcoef(bottleneck.T)
            
            # 简单可视化bottleneck活动
            plt.figure(figsize=(15, 5))
            
            # 1. 相关性矩阵图
            plt.subplot(1, 3, 1)
            plt.imshow(corr_matrix.cpu().numpy(), cmap='coolwarm', vmin=-1, vmax=1)
            plt.colorbar()
            plt.title('Feature Correlation Matrix')
            
            # 2. 前10个神经元的活动图
            plt.subplot(1, 3, 2)
            for i in range(min(10, bottleneck.shape[1])):
                plt.plot(bottleneck[:, i].cpu().numpy(), label=f'Neuron {i+1}')
            plt.title('Bottleneck Neuron Activities')
            plt.xlabel('Position Index')
            plt.ylabel('Activation')
            
            # 3. Place cell预测可视化
            plt.subplot(1, 3, 3)
            place_logits = outputs['place_logits'][0]  # 取第一个样本
            place_probs = torch.softmax(place_logits, dim=-1)
            plt.imshow(place_probs.cpu().numpy(), aspect='auto')
            plt.title('Place Cell Activations')
            plt.xlabel('Place Cell Index')
            plt.ylabel('Time Step')
            plt.colorbar()
            
            plt.tight_layout()
            plt.savefig(os.path.join(self.save_dir, f'grid_cell_viz_epoch_{epoch}.png'))
            plt.close()
            
            return 0.5  # 简化版没有计算真实的grid score，返回一个占位值

    def visualize_epoch(self, epoch, model, dataloader, device, metrics_history, suffix=""):
        """整合两个可视化函数，支持自定义文件名后缀"""
        # 修改保存文件的路径以包含后缀
        self.plot_metrics(metrics_history, epoch, suffix)
        grid_score = self.visualize_grid_cells(model, dataloader, device, epoch, suffix)
        return grid_score


class GridCellTrainer:
    def __init__(self, config, gpu_id, world_size):
        self.config = config
        self.gpu_id = gpu_id
        self.world_size = world_size
        self.is_main_process = (gpu_id == 0)
        self.device = torch.device(f'cuda:{gpu_id}')

        # 创建实验目录
        self.timestamp = time.strftime("%Y%m%d_%H%M%S")
        self.exp_dir = os.path.join("log_train_grid_cells", self.timestamp)
        if self.is_main_process:
            os.makedirs(self.exp_dir, exist_ok=True)
            os.makedirs(os.path.join(self.exp_dir, "checkpoints"), exist_ok=True)
            os.makedirs(os.path.join(self.exp_dir, "visualizations"), exist_ok=True)
            setup_logger(self.exp_dir)

            backup_dir = os.path.join(self.exp_dir, "code_backup")
            backup_code(os.path.dirname(os.path.abspath(__file__)), backup_dir)
            print(f"Code backup completed at {backup_dir}")
            # 新增：使用简化的可视化器
            # self.visualizer = SimpleNavigationVisualizer(os.path.join(self.exp_dir, "visualizations"))
            self.visualizer = NavigationVisualizer(os.path.join(self.exp_dir, "visualizations")) 

        # 构建 place/hd ensembles
        self.place_cells = PlaceCellEnsemble(
            n_cells=config.PLACE_CELLS_N,
            scale=config.PLACE_CELLS_SCALE,
            pos_min=0,
            pos_max=config.ENV_SIZE,
            seed=config.SEED
        )
        self.hd_cells = HeadDirectionCellEnsemble(
            n_cells=config.HD_CELLS_N,
            concentration=config.HD_CELLS_CONCENTRATION,
            seed=config.SEED
        )

        # 初始化网络（使用改进后的 GridCellNetwork，含稳定的初始状态映射模块）
        self.model = GridCellNetwork(
            place_cells=self.place_cells,
            hd_cells=self.hd_cells,
            input_size=3,
            hidden_size=config.HIDDEN_SIZE,
            bottleneck_size=256,
            dropout_rate=config.DROPOUT_RATE
        ).to(self.device)

        if self.world_size > 1:
            self.model = DDP(self.model, device_ids=[gpu_id], find_unused_parameters=True)

        # 损失函数
        # self.criterion = GridCellSupervisedLoss().to(self.device)
        self.criterion = InitialStateRegularizedLoss(initial_frames_weight=5.0, decay_factor=0.8).to(self.device)

        # 使用 Nature 超参数设置的 RMSprop 优化器（lr=1e-5、momentum=0.9、weight_decay=1e-5）
        self.optimizer = torch.optim.RMSprop(
            self.model.parameters(),
            lr=config.NATURE_LEARNING_RATE,
            momentum=0.9,
            weight_decay=config.NATURE_WEIGHT_DECAY
        )

        # 学习率调度器
        self.scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.5, patience=5
        )

        # 数据加载器
        self.dataloaders = self.get_dataloaders()

        # 记录指标
        self.metrics_history = defaultdict(list)

    # Add these changes to the get_dataloaders method in GridCellTrainer class

    def get_dataloaders(self):
        """
        设置数据加载器，使用 EnhancedNavigationDataset，划分为 train 和 val
        增加分块数据加载和内存管理优化
        """
        trajectory_folders = [
            d for d in os.listdir(self.config.DATA_ROOT)
            if os.path.isdir(os.path.join(self.config.DATA_ROOT, d))
               and d.startswith('D')
        ]
        trajectory_folders.sort()

        n_total = len(trajectory_folders)
        n_train = int(n_total * 0.7)
        n_val = int(n_total * 0.15)

        dataset_dirs = {'train': [], 'val': []}
        for folder in trajectory_folders[:n_train]:
            dataset_dirs['train'].append(os.path.join(self.config.DATA_ROOT, folder, '000000'))
        for folder in trajectory_folders[n_train:n_train + n_val]:
            dataset_dirs['val'].append(os.path.join(self.config.DATA_ROOT, folder, '000000'))

        loaders = {}
        for split in ['train', 'val']:
            maze_dirs = dataset_dirs[split]

            # 使用数据分块和延迟加载策略
            chunk_size = 10000  # 每块包含的迷宫数目，可根据内存调整
            dataset = EnhancedNavigationDataset(
                maze_dirs=maze_dirs,
                sequence_length=self.config.SEQUENCE_LENGTH,
                stride=self.config.SEQUENCE_STRIDE,
                split=split,
                chunk_size=chunk_size,
                current_chunk=0  # 从第一个块开始
            )

            if self.world_size > 1:
                sampler = torch.utils.data.distributed.DistributedSampler(
                    dataset,
                    num_replicas=self.world_size,
                    rank=self.gpu_id,
                    shuffle=(split == 'train')
                )
                shuffle_flag = False
            else:
                sampler = None
                shuffle_flag = (split == 'train')

            bs = self.config.BATCH_SIZE if split == 'train' else (
                self.config.BATCH_SIZE // 2 if self.config.BATCH_SIZE > 1 else 1)

            # 减少worker数量和预取量
            num_workers = min(2, self.config.NUM_WORKERS)

            # 添加更多内存优化选项
            loader = torch.utils.data.DataLoader(
                dataset,
                batch_size=bs,
                shuffle=shuffle_flag,
                sampler=sampler,
                num_workers=num_workers,
                pin_memory=False,  # 设为False以减少GPU内存使用
                drop_last=(split == 'train'),
                timeout=60,
                persistent_workers=(num_workers > 0),
                prefetch_factor=1 if num_workers > 0 else None,  # 减少预取以节省内存
            )
            loaders[split] = loader

        return loaders

    def train_epoch(self, epoch):
        self.model.train()
        if self.world_size > 1:
            self.dataloaders['train'].sampler.set_epoch(epoch)

        epoch_losses = defaultdict(list)

        # 获取总批次数
        total_batches = len(self.dataloaders['train'])

        # 只在主进程显示进度条
        # 使用 tqdm.write 输出到终端
        train_loader = tqdm(enumerate(self.dataloaders['train']),
                            desc=f'Train Epoch {epoch + 1}',
                            total=total_batches,  # 设置总批次数
                            disable=not self.is_main_process,
                            file=sys.stdout,
                            dynamic_ncols=True)

        for batch_idx, batch in enumerate(self.dataloaders['train']):
            positions = batch['positions'].to(self.device)
            angles = batch['angles'].to(self.device)
            velocities = batch['velocities'].to(self.device)
            ang_vels = batch['angular_velocities'].to(self.device)
            w = ang_vels.unsqueeze(-1)
            velocity_input = torch.cat([velocities, w], dim=-1)
            init_pos = positions[:,0]
            init_hd = angles[:,0]

            # 计算所有时间步的激活，包括初始点 (t=0)
            place_targets = self.place_cells.compute_activation(positions)
            hd_targets = self.hd_cells.compute_activation(angles)
            targets = {'place_targets': place_targets, 'hd_targets': hd_targets}

            # 模型运行不变，依然输出完整序列预测
            outputs = self.model(velocity_input, init_pos, init_hd)
            
            # 修改后的损失函数将自动排除第一个时间步
            loss_dict = self.criterion(outputs, targets)
            loss = loss_dict['total']

            # 其余训练流程保持不变
            self.optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=self.config.GRAD_CLIP)
            self.optimizer.step()

            # 更新进度条显示的损失值
            if self.is_main_process:
                train_loader.set_postfix({
                    'loss': f"{loss.item():.4f}",
                    'batch': f"{batch_idx + 1}/{total_batches}"
                })

            for k, v in loss_dict.items():
                if k == 'total':
                    epoch_losses[k].append(v.item())
                else:
                    epoch_losses[k].append(v)

        return {k: np.mean(v) for k, v in epoch_losses.items()}

    def validate_epoch(self, epoch):
        self.model.eval()
        epoch_losses = defaultdict(list)

        # 获取总批次数
        total_batches = len(self.dataloaders['val'])

        val_loader = tqdm(enumerate(self.dataloaders['val']),
                          desc=f'Val Epoch {epoch + 1}',
                          total=total_batches,  # 设置总批次数
                          disable=not self.is_main_process,
                          file=sys.stdout,
                          dynamic_ncols=True)

        with torch.no_grad():
            for batch_idx, batch in enumerate(self.dataloaders['val']):
                positions = batch['positions'].to(self.device)
                angles = batch['angles'].to(self.device)
                velocities = batch['velocities'].to(self.device)
                ang_vels = batch['angular_velocities'].to(self.device)
                w = ang_vels.unsqueeze(-1)
                velocity_input = torch.cat([velocities, w], dim=-1)
                init_pos = positions[:,0]
                init_hd = angles[:,0]

                place_targets = self.place_cells.compute_activation(positions)
                hd_targets = self.hd_cells.compute_activation(angles)
                targets = {'place_targets': place_targets, 'hd_targets': hd_targets}

                outputs = self.model(velocity_input, init_pos, init_hd)
                loss_dict = self.criterion(outputs, targets)

                # 更新进度条显示的损失值
                if self.is_main_process:
                    val_loader.set_postfix({
                        'loss': f"{loss_dict['total'].item():.4f}",
                        'batch': f"{batch_idx + 1}/{total_batches}"
                    })

                for k, v in loss_dict.items():
                    if k == 'total':
                        epoch_losses[k].append(v.item())
                    else:
                        epoch_losses[k].append(v)
        return {k: np.mean(v) for k, v in epoch_losses.items()}

    def train(self):
        """训练主循环，添加数据分块处理机制"""
        if self.is_main_process:
            print("\n" + "=" * 50)
            print("开始分块训练流程")
            print("=" * 50 + "\n")

        best_val_loss = float('inf')
        start_epoch = 0
        # 用于跟踪已处理的数据块
        processed_chunks = set()

        # 恢复训练检查点（如果有）
        if self.config.RESUME:
            if self.is_main_process:
                print(f"正在恢复训练，检查点: {self.config.RESUME}")
            try:
                checkpoint = torch.load(self.config.RESUME, map_location=self.device)
                if self.world_size > 1:
                    self.model.module.load_state_dict(checkpoint['model_state_dict'])
                else:
                    self.model.load_state_dict(checkpoint['model_state_dict'])
                self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
                self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
                start_epoch = checkpoint['epoch']

                # 恢复已处理的数据块记录
                if 'processed_chunks' in checkpoint and self.config.RESUME_CHUNK_TRAINING:
                    processed_chunks = checkpoint['processed_chunks']
                    if self.is_main_process:
                        print(f"已恢复处理过的数据块记录，共 {len(processed_chunks)} 个块")

                if 'metrics_history' in checkpoint and self.is_main_process:
                    self.metrics_history = checkpoint['metrics_history']
                if self.is_main_process:
                    print(f"成功恢复训练，从第 {start_epoch} 轮继续")
            except Exception as e:
                if self.is_main_process:
                    print(f"恢复训练失败: {e}")
                    print("将从头开始训练")

        # 获取数据集的总块数
        train_chunks = self.dataloaders['train'].dataset.total_chunks
        val_chunks = self.dataloaders['val'].dataset.total_chunks

        if self.is_main_process:
            print(f"训练数据分为 {train_chunks} 个块")
            print(f"验证数据分为 {val_chunks} 个块")

            # 如果启用内存监控，记录初始内存
            if self.config.MEMORY_PROFILING_ENABLED:
                initial_memory = self.monitor_memory()
                print(f"初始内存使用: {initial_memory:.2f} MB")

        try:
            for epoch in range(start_epoch, self.config.NUM_EPOCHS):
                if self.is_main_process:
                    print(f"\n开始第 {epoch + 1}/{self.config.NUM_EPOCHS} 轮训练")

                # 确定此轮要处理的数据块
                if self.config.ROTATE_CHUNKS_WITHIN_EPOCH:
                    # 在一个epoch内处理所有块
                    chunks_to_process = list(range(train_chunks))
                    if self.config.SHUFFLE_CHUNKS:
                        import random
                        random.shuffle(chunks_to_process)
                else:
                    # 只处理尚未处理过的块，直到所有块都被处理
                    remaining_chunks = [i for i in range(train_chunks) if i not in processed_chunks]
                    if not remaining_chunks:  # 如果所有块都已处理，重置记录
                        processed_chunks = set()
                        remaining_chunks = list(range(train_chunks))

                    # 确定本轮要处理的块数（至少处理一个）
                    chunks_per_epoch = max(1, len(remaining_chunks) // (self.config.NUM_EPOCHS - epoch))
                    chunks_to_process = remaining_chunks[:chunks_per_epoch]

                    if self.is_main_process:
                        print(f"本轮将处理 {chunks_per_epoch} 个数据块（共 {len(remaining_chunks)} 个未处理块）")

                # 针对每个选定的数据块进行训练
                train_metrics_list = []
                for chunk_idx in chunks_to_process:
                    chunk_start_time = time.time()
                    if self.is_main_process:
                        print(f"训练数据块 {chunk_idx + 1}/{train_chunks}")

                    # 切换到当前数据块
                    self.dataloaders['train'].dataset.switch_chunk(chunk_idx)

                    # 确保分布式采样器使用正确的数据大小
                    if self.world_size > 1:
                        self.dataloaders['train'].sampler.set_epoch(epoch * train_chunks + chunk_idx)

                    # 训练当前块
                    try:
                        train_metrics = self.train_epoch(epoch)
                        train_metrics_list.append(train_metrics)

                        # 标记当前块为已处理
                        processed_chunks.add(chunk_idx)
                        
                        # ====== 3) 保存latest模型 ======
                         # 每个块训练完就保存 "latest_chunk_{chunk_idx+1}.pth"
                        if self.is_main_process:
                            try:
                                latest_chunk_path = os.path.join(
                                    self.exp_dir, "checkpoints", f"latest_chunk_{chunk_idx + 1}.pth"
                                )
                                torch.save({
                                    'epoch': epoch + 1,
                                    'chunk_idx': chunk_idx + 1,
                                    'model_state_dict':
                                        self.model.module.state_dict() if self.world_size > 1 else self.model.state_dict(),
                                    'optimizer_state_dict': self.optimizer.state_dict(),
                                    'scheduler_state_dict': self.scheduler.state_dict(),
                                    'metrics_history': self.metrics_history,
                                    'processed_chunks': processed_chunks if self.config.CHECKPOINT_CHUNK_STATE else set()
                                }, latest_chunk_path)
                                print(f"[Chunk {chunk_idx + 1}] latest model saved to {latest_chunk_path}")
                            except Exception as e:
                                print(f"保存 latest chunk 模型失败: {e}")
                                import traceback
                                traceback.print_exc()

                        # 新增：每个块训练完成后进行可视化
                        if self.is_main_process:
                            try:
                                print(f"\n执行数据块 {chunk_idx + 1} 训练后的可视化...")
                                # 使用验证集的第一个块进行可视化
                                if val_chunks > 0:
                                    self.dataloaders['val'].dataset.switch_chunk(0)
                                grid_score = self.visualizer.visualize_epoch(
                                    epoch + 1,
                                    self.model,
                                    self.dataloaders['val'],
                                    self.device,
                                    self.metrics_history,
                                    suffix=f"_chunk{chunk_idx + 1}"  # 在文件名中添加块标识
                                )
                                print(f"数据块 {chunk_idx + 1} Grid Score: {grid_score:.3f}")
                            except Exception as e:
                                print(f"数据块可视化失败: {e}")
                                import traceback
                                traceback.print_exc()

                        # 清理内存
                        if self.config.FORCE_GC_BETWEEN_CHUNKS:
                            torch.cuda.empty_cache()
                            import gc
                            gc.collect()

                        if self.is_main_process and self.config.MEMORY_PROFILING_ENABLED:
                            current_memory = self.monitor_memory()
                            chunk_time = time.time() - chunk_start_time
                            print(
                                f"数据块 {chunk_idx + 1} 训练完成，用时 {chunk_time:.2f} 秒，当前内存使用: {current_memory:.2f} MB")

                    except Exception as e:
                        if self.is_main_process:
                            print(f"训练数据块 {chunk_idx + 1} 出错: {e}")
                            import traceback
                            traceback.print_exc()
                            print("跳过出错的数据块，继续处理下一个")

                # 计算所有块的平均指标
                if train_metrics_list:
                    train_metrics = {k: np.mean([metrics[k] for metrics in train_metrics_list if k in metrics])
                                     for k in train_metrics_list[0].keys()}
                else:
                    train_metrics = {'total': float('inf'), 'place': float('inf'), 'hd': float('inf')}

                # 在验证块上进行验证
                val_metrics_list = []

                # 如果有多个验证块，选择其中部分进行验证
                val_chunks_to_evaluate = list(range(val_chunks))
                if val_chunks > 5:  # 如果验证块太多，可以随机选择一部分
                    import random
                    val_chunks_to_evaluate = random.sample(val_chunks_to_evaluate, min(5, val_chunks))

                for chunk_idx in val_chunks_to_evaluate:
                    if self.is_main_process:
                        print(f"验证数据块 {chunk_idx + 1}/{val_chunks}")

                    # 切换到当前验证块
                    self.dataloaders['val'].dataset.switch_chunk(chunk_idx)

                    # 验证当前块
                    try:
                        val_chunk_metrics = self.validate_epoch(epoch)
                        val_metrics_list.append(val_chunk_metrics)

                        # 清理内存
                        if self.config.FORCE_GC_BETWEEN_CHUNKS:
                            torch.cuda.empty_cache()
                            import gc
                            gc.collect()

                    except Exception as e:
                        if self.is_main_process:
                            print(f"验证数据块 {chunk_idx + 1} 出错: {e}")
                            import traceback
                            traceback.print_exc()

                # 计算所有验证块的平均指标
                if val_metrics_list:
                    val_metrics = {k: np.mean([metrics[k] for metrics in val_metrics_list if k in metrics])
                                   for k in val_metrics_list[0].keys()}
                else:
                    val_metrics = {'total': float('inf'), 'place': float('inf'), 'hd': float('inf')}

                # 更新学习率和记录指标
                if self.is_main_process:
                    try:
                        self.scheduler.step(val_metrics['total'])
                        current_lr = self.optimizer.param_groups[0]['lr']
                        print(f"更新学习率: {current_lr:.2e}")
                    except Exception as e:
                        print(f"更新学习率失败: {e}")

                    # 记录指标
                    try:
                        for k, v in train_metrics.items():
                            self.metrics_history[f"train_{k}"].append(v)
                        for k, v in val_metrics.items():
                            self.metrics_history[f"val_{k}"].append(v)

                        print(f"\n第 {epoch + 1} 轮总结:")
                        print("训练:", {k: f"{v:.4f}" for k, v in train_metrics.items()})
                        print("验证:", {k: f"{v:.4f}" for k, v in val_metrics.items()})
                        print(f"已处理数据块: {len(processed_chunks)}/{train_chunks}")
                    except Exception as e:
                        print(f"记录指标失败: {e}")

                    # 可视化
                    try:
                        if (epoch + 1) % self.config.VIZ_INTERVAL == 0:
                            print("\n执行可视化...")
                            grid_score = self.visualizer.visualize_epoch(epoch + 1, self.model, self.dataloaders['val'],
                                                                         self.device, self.metrics_history)
                            print(f"第 {epoch + 1} 轮 Grid Score: {grid_score:.3f}")
                    except Exception as e:
                        print(f"可视化失败: {e}")
                        import traceback
                        traceback.print_exc()

                    # 保存模型
                    try:
                        # 保存最佳模型
                        if val_metrics['total'] < best_val_loss:
                            best_val_loss = val_metrics['total']
                            save_path = os.path.join(self.exp_dir, "checkpoints", "best_model.pth")
                            print(f"发现更好的模型，保存到 {save_path}")
                            torch.save({
                                'epoch': epoch + 1,
                                'model_state_dict': self.model.module.state_dict() if self.world_size > 1 else self.model.state_dict(),
                                'optimizer_state_dict': self.optimizer.state_dict(),
                                'scheduler_state_dict': self.scheduler.state_dict(),
                                'best_val_loss': best_val_loss,
                                'metrics_history': self.metrics_history,
                                'processed_chunks': processed_chunks if self.config.CHECKPOINT_CHUNK_STATE else set()
                            }, save_path)

                        # 定期保存检查点
                        if (epoch + 1) % self.config.SAVE_FREQUENCY == 0:
                            save_path = os.path.join(self.exp_dir, "checkpoints", f"epoch_{epoch + 1}.pth")
                            print(f"保存周期性检查点到 {save_path}")
                            torch.save({
                                'epoch': epoch + 1,
                                'model_state_dict': self.model.module.state_dict() if self.world_size > 1 else self.model.state_dict(),
                                'optimizer_state_dict': self.optimizer.state_dict(),
                                'scheduler_state_dict': self.scheduler.state_dict(),
                                'metrics_history': self.metrics_history,
                                'processed_chunks': processed_chunks if self.config.CHECKPOINT_CHUNK_STATE else set()
                            }, save_path)
                    except Exception as e:
                        print(f"保存模型失败: {e}")
                        import traceback
                        traceback.print_exc()

                    # 记录内存使用
                    if self.config.MEMORY_PROFILING_ENABLED:
                        current_memory = self.monitor_memory()
                        print(f"第 {epoch + 1} 轮结束，当前内存使用: {current_memory:.2f} MB")

                # 分布式训练同步
                if self.world_size > 1:
                    try:
                        dist.barrier()
                    except Exception as e:
                        if self.is_main_process:
                            print(f"分布式同步失败: {e}")

        except KeyboardInterrupt:
            if self.is_main_process:
                print("\n接收到键盘中断，正在保存检查点并结束训练")
                try:
                    save_path = os.path.join(self.exp_dir, "checkpoints", "interrupted.pth")
                    torch.save({
                        'epoch': epoch + 1,
                        'model_state_dict': self.model.module.state_dict() if self.world_size > 1 else self.model.state_dict(),
                        'optimizer_state_dict': self.optimizer.state_dict(),
                        'scheduler_state_dict': self.scheduler.state_dict(),
                        'metrics_history': self.metrics_history,
                        'processed_chunks': processed_chunks if self.config.CHECKPOINT_CHUNK_STATE else set()
                    }, save_path)
                    print(f"中断检查点已保存到 {save_path}")
                except Exception as e:
                    print(f"保存中断检查点失败: {e}")

        except Exception as e:
            if self.is_main_process:
                print(f"训练循环中发生未处理的异常: {e}")
                import traceback
                traceback.print_exc()
                try:
                    save_path = os.path.join(self.exp_dir, "checkpoints", "error_recovery.pth")
                    torch.save({
                        'epoch': epoch + 1 if 'epoch' in locals() else start_epoch,
                        'model_state_dict': self.model.module.state_dict() if self.world_size > 1 else self.model.state_dict(),
                        'optimizer_state_dict': self.optimizer.state_dict(),
                        'scheduler_state_dict': self.scheduler.state_dict(),
                        'metrics_history': self.metrics_history,
                        'processed_chunks': processed_chunks if self.config.CHECKPOINT_CHUNK_STATE else set()
                    }, save_path)
                    print(f"错误恢复检查点已保存到 {save_path}")
                except Exception as e2:
                    print(f"保存错误恢复检查点失败: {e2}")

        finally:
            if self.is_main_process:
                print("\n" + "=" * 50)
                print("训练结束")
                print("=" * 50)

                # 最终内存使用报告
                if self.config.MEMORY_PROFILING_ENABLED:
                    final_memory = self.monitor_memory()
                    print(f"最终内存使用: {final_memory:.2f} MB")


    def manage_memory(threshold_mb=10000):
        """当内存使用超过阈值时执行内存优化"""
        memory_mb = self.monitor_memory()
        if memory_mb > threshold_mb:
            print(f"内存使用达到 {memory_mb:.2f}MB，执行内存优化...")
            torch.cuda.empty_cache()
            import gc
            gc.collect()

    # 5. 添加内存监控和自动管理功能
    def monitor_memory(self):
        """监控当前进程的内存使用情况"""
        try:
            import psutil
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / (1024 * 1024)  # MB
            return memory_mb
        except ImportError:
            return -1  # 无法获取内存信息

def train_worker(gpu_id, world_size, args, config):
    if world_size > 1:
        os.environ['MASTER_ADDR'] = 'localhost'
        os.environ['MASTER_PORT'] = '12355'
        dist.init_process_group(
            backend='nccl',
            init_method='env://',
            world_size=world_size,
            rank=gpu_id
        )
        torch.cuda.set_device(gpu_id)
    trainer = GridCellTrainer(config, gpu_id, world_size)
    try:
        trainer.train()
    except Exception as e:
        print(f"[rank={gpu_id}] Error occurred: {str(e)}")
        raise e
    finally:
        if world_size > 1:
            dist.destroy_process_group()



def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--gpus', type=str, default='0', help='GPU IDs (comma-separated)')
    parser.add_argument('--resume', type=str, default='', help='checkpoint path')
    args = parser.parse_args()

    os.environ['CUDA_VISIBLE_DEVICES'] = args.gpus
    gpu_ids = [i for i in range(len(args.gpus.split(',')))]
    world_size = len(gpu_ids)

    config = Config()  # 读取配置，确保其中包含 NATURE_LEARNING_RATE、VIZ_INTERVAL 等新属性
    if args.resume:
        config.RESUME = args.resume

    if world_size > 1:
        mp.spawn(
            train_worker,
            args=(world_size, args, config),
            nprocs=world_size,
            join=True
        )
    else:
        train_worker(0, world_size, args, config)

if __name__ == "__main__":
    main()