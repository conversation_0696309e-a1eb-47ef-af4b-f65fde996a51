# datasets/social_navigation_dataset.py
import torch
from torch.utils.data import Dataset
import os
import numpy as np
import random
import json
from typing import List, Dict

class SingleMazeDataset(Dataset):
    """
    处理单个迷宫轨迹数据的数据集。
    这个修正版本会从 frame_info.json 文件加载轨迹数据。
    """
    def __init__(self, maze_dir: str, sequence_length: int = 100, stride: int = 1):
        self.maze_dir = maze_dir
        self.sequence_length = sequence_length
        self.stride = stride
        
        # 修正路径以指向 frame_info.json
        json_path = os.path.join(maze_dir, '000000', 'frame_info.json')
        
        if not os.path.exists(json_path):
            raise FileNotFoundError(f"轨迹文件未找到: {json_path}")

        # 加载并解析JSON文件
        self.data = self._load_from_json(json_path)

        self.num_frames = len(self.data['positions'])
        self.indices = self._create_indices()

    def _load_from_json(self, json_path: str) -> Dict:
        """从 frame_info.json 加载并格式化数据"""
        with open(json_path, 'r') as f:
            frame_data = json.load(f)

        # 按照 frame_id 进行排序，确保时间顺序正确
        sorted_frames = sorted(frame_data.items(), key=lambda item: int(item[1]['frame_id']))

        positions = []
        angles = []
        velocities = []
        angular_velocities = []

        for frame_key, frame_info in sorted_frames:
            agent_state = frame_info.get('agent_state', {})
            
            # 提取数据，并提供默认值以防数据缺失
            pos = agent_state.get('position', [0.0, 0.0])
            # 确保位置是2D的
            if len(pos) == 3:
                pos = pos[:2]
            
            positions.append(pos)
            angles.append(agent_state.get('rotation', 0.0))
            
            vel = agent_state.get('velocity', [0.0, 0.0])
            if len(vel) == 3:
                vel = vel[:2]
            velocities.append(vel)
            
            angular_velocities.append(agent_state.get('angular_velocity', 0.0))

        return {
            'positions': np.array(positions, dtype=np.float32),
            'angles': np.array(angles, dtype=np.float32),
            'velocities': np.array(velocities, dtype=np.float32),
            'angular_velocities': np.array(angular_velocities, dtype=np.float32),
        }

    def _create_indices(self) -> List[int]:
        """创建有效的起始索引"""
        if self.num_frames < self.sequence_length:
            return []
        return [i for i in range(0, self.num_frames - self.sequence_length + 1, self.stride)]

    def __len__(self) -> int:
        return len(self.indices)

    def __getitem__(self, idx: int) -> Dict:
        start_idx = self.indices[idx]
        end_idx = start_idx + self.sequence_length
        
        return {
            'positions': torch.from_numpy(self.data['positions'][start_idx:end_idx]),
            'angles': torch.from_numpy(self.data['angles'][start_idx:end_idx]),
            'velocities': torch.from_numpy(self.data['velocities'][start_idx:end_idx]),
            'angular_velocities': torch.from_numpy(self.data['angular_velocities'][start_idx:end_idx]),
        }


class SocialNavigationDataset(Dataset):
    """
    为双流模型创建配对的agent轨迹数据。
    它会随机从数据集中抽取两个不同的轨迹作为 'self' 和 'peer'。
    """
    def __init__(self, root_dir: str, sequence_length: int = 100, stride: int = 1):
        self.root_dir = root_dir
        self.sequence_length = sequence_length
        self.stride = stride
        
        self.trajectory_dirs = [os.path.join(root_dir, d) for d in os.listdir(root_dir) 
                                if os.path.isdir(os.path.join(root_dir, d))]
        
        if len(self.trajectory_dirs) < 2:
            raise ValueError(f"需要至少两个轨迹文件夹在目录下 '{root_dir}'，但只找到了 {len(self.trajectory_dirs)} 个。")

        print("Loading individual trajectory datasets...")
        self.datasets = []
        for d in self.trajectory_dirs:
            try:
                dataset = SingleMazeDataset(d, sequence_length, stride)
                if len(dataset) > 0:
                    self.datasets.append(dataset)
                else:
                    print(f"Warning: No valid sequences in {d}, skipping.")
            except FileNotFoundError as e:
                print(f"Warning: {e}, skipping directory {d}.")
            except Exception as e:
                print(f"An unexpected error occurred while loading {d}: {e}, skipping.")

        if len(self.datasets) < 2:
             raise ValueError("有效的数据集少于2个，无法进行配对训练。请检查数据目录和格式。")

        self.min_len = min(len(d) for d in self.datasets)
        self.num_datasets = len(self.datasets)
        print(f"Successfully loaded {self.num_datasets} valid trajectory datasets.")

    def __len__(self) -> int:
        return self.min_len * self.num_datasets * 5

    def __getitem__(self, idx: int) -> Dict:
        self_ds_idx, peer_ds_idx = random.sample(range(self.num_datasets), 2)
        
        self_dataset = self.datasets[self_ds_idx]
        peer_dataset = self.datasets[peer_ds_idx]
        
        self_sample_idx = random.randint(0, len(self_dataset) - 1)
        peer_sample_idx = random.randint(0, len(peer_dataset) - 1)
        
        self_data = self_dataset[self_sample_idx]
        peer_data = peer_dataset[peer_sample_idx]
        
        return {
            'self_positions': self_data['positions'],
            'self_angles': self_data['angles'],
            'self_velocities': self.add_noise(self_data['velocities']),
            'self_angular_velocities': self.add_noise(self_data['angular_velocities']),
            'peer_positions': peer_data['positions'],
            'peer_angles': peer_data['angles'],
            'peer_velocities': self.add_noise(peer_data['velocities']),
            'peer_angular_velocities': self.add_noise(peer_data['angular_velocities']),
        }

    def add_noise(self, tensor: torch.Tensor, noise_level: float = 0.01) -> torch.Tensor:
        noise = torch.randn_like(tensor) * noise_level
        return tensor + noise
