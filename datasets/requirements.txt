absl-py==2.1.0
accelerate==1.0.1
aiofiles==23.2.1
aiohappyeyeballs==2.4.4
aiohttp==3.10.11
aiosignal==1.3.1
annotated-types==0.7.0
anyio==4.5.2
async-timeout==5.0.1
attrs==24.3.0
beautifulsoup4==4.13.3
bitsandbytes==0.37.2
B<PERSON><PERSON> @ file:///croot/brotli-split_1714483155106/work
cachetools==5.5.1
certifi @ file:///croot/certifi_1725551672989/work/certifi
charset-normalizer @ file:///croot/charset-normalizer_1721748349566/work
click==8.1.8
clip @ git+https://github.com/openai/CLIP.git@dcba3cb2e2827b402d2701e7e1c7d9fed8a20ef1
cloudpickle==3.1.1
contourpy==1.1.1
cycler==0.12.1
datasets==3.1.0
decorator==4.4.2
dill==0.3.8
distro==1.9.0
dm-env==1.6
dm-tree==0.1.8
dm_control==1.0.23
etils==1.3.0
exceptiongroup==1.2.2
faiss-gpu==1.7.2
fastapi==0.116.1
ffmpy==0.5.0
filelock @ file:///croot/filelock_1700591183607/work
fonttools==4.55.3
frozenlist==1.5.0
fsspec==2024.9.0
ftfy==6.2.3
gitdb==4.0.12
GitPython==3.1.44
glfw==2.8.0
gmpy2 @ file:///tmp/build/80754af9/gmpy2_1645455532332/work
google-auth==2.37.0
google-auth-oauthlib==1.0.0
gradio==4.44.1
gradio_client==1.3.0
grpcio==1.69.0
gym==0.26.2
gym-notices==0.0.8
h11==0.14.0
hf-xet==1.1.5
httpcore==1.0.7
httpx==0.28.1
huggingface-hub==0.33.4
idna @ file:///croot/idna_1714398848350/work
imageio==2.35.1
imageio-ffmpeg==0.5.1
importlib_metadata==8.5.0
importlib_resources==6.4.5
inquirerpy==0.3.4
Jinja2 @ file:///croot/jinja2_1716993405101/work
jiter==0.8.2
joblib==1.4.2
kiwisolver==1.4.7
labmaze==1.0.6
lazy_loader==0.4
lxml==5.3.0
Markdown==3.7
markdown-it-py==3.0.0
MarkupSafe @ file:///croot/markupsafe_1704205993651/work
matplotlib==3.7.5
mdurl==0.1.2
# Editable install with no version control (memory-maze==0.1)
-e /data1/fangzr/Research/Memory_Maze_3D/only-data-for-place-cell
mkl-fft @ file:///croot/mkl_fft_1695058164594/work
mkl-random @ file:///croot/mkl_random_1695059800811/work
mkl-service==2.4.0
moviepy==1.0.3
mpmath @ file:///croot/mpmath_1690848262763/work
mujoco==3.2.3
multidict==6.1.0
multiprocess==0.70.16
networkx @ file:///croot/networkx_1690561992265/work
ninja==********
numpy @ file:///work/mkl/numpy_and_numpy_base_1682953417311/work
oauthlib==3.2.2
openai==1.58.1
opencv-python==*********
orjson==3.10.15
packaging==24.2
pandas==2.0.3
peft==0.13.2
pfzy==0.3.4
pillow @ file:///croot/pillow_1721059439630/work
proglog==0.1.10
prompt_toolkit==3.0.51
propcache==0.2.0
protobuf==5.29.3
psutil==6.1.1
py-cpuinfo==9.0.0
pyarrow==17.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.1
pydantic==2.10.4
pydantic_core==2.27.2
pydub==0.25.1
pygame==2.6.1
Pygments==2.19.2
pynvml==11.5.3
PyOpenGL==3.1.9
pyparsing==3.1.4
PySocks @ file:///tmp/build/80754af9/pysocks_1605305779399/work
python-dateutil==2.9.0.post0
python-multipart==0.0.20
pytz==2024.2
PyWavelets==1.4.1
PyYAML @ file:///croot/pyyaml_1728657952215/work
regex==2024.11.6
requests @ file:///croot/requests_1721410876868/work
requests-oauthlib==2.0.0
rich==14.0.0
rsa==4.9
ruff==0.12.4
rwkv==0.8.29
safetensors==0.4.5
scikit-image==0.21.0
scikit-learn==1.3.2
scipy==1.10.1
seaborn==0.13.2
semantic-version==2.10.0
shellingham==1.5.4
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
soupsieve==2.6
starlette==0.44.0
sympy @ file:///croot/sympy_1734622612703/work
tensorboard==2.14.0
tensorboard-data-server==0.7.2
threadpoolctl==3.5.0
tifffile==2023.7.10
timm==1.0.14
tokenizers==0.20.3
tomlkit==0.12.0
torch==2.4.1
torchaudio==2.4.1
torchvision==0.20.0
tqdm==4.67.1
transformers==4.46.3
triton==3.0.0
typer==0.16.0
typing_extensions==4.12.2
tzdata==2024.2
ultralytics==8.3.119
ultralytics-thop==2.0.14
urllib3 @ file:///croot/urllib3_1727769808118/work
uvicorn==0.33.0
wcwidth==0.2.13
websockets==12.0
Werkzeug==3.0.6
xxhash==3.5.0
yarl==1.15.2
zipp==3.20.2
