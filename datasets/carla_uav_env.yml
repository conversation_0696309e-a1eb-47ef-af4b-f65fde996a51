name: CARLA-UAV
channels:
  - pytorch
  - nvidia
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - blas=1.0=mkl
  - brotli-python=1.0.9=py38h6a678d5_8
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2024.11.26=h06a4308_0
  - certifi=2024.8.30=py38h06a4308_0
  - charset-normalizer=3.3.2=pyhd3eb1b0_0
  - cuda-cudart=11.8.89=0
  - cuda-cupti=11.8.87=0
  - cuda-libraries=11.8.0=0
  - cuda-nvrtc=11.8.89=0
  - cuda-nvtx=11.8.86=0
  - cuda-runtime=11.8.0=0
  - cuda-version=12.6=3
  - ffmpeg=4.3=hf484d3e_0
  - filelock=3.13.1=py38h06a4308_0
  - freetype=2.12.1=h4a9f257_0
  - giflib=5.2.2=h5eee18b_0
  - gmp=6.2.1=h295c915_3
  - gmpy2=2.1.2=py38heeb90bb_0
  - gnutls=3.6.15=he1e5248_0
  - idna=3.7=py38h06a4308_0
  - intel-openmp=2023.1.0=hdb19cb5_46306
  - jinja2=3.1.4=py38h06a4308_0
  - jpeg=9e=h5eee18b_3
  - lame=3.100=h7b6447c_0
  - lcms2=2.16=hb9589c4_0
  - ld_impl_linux-64=2.40=h12ee557_0
  - lerc=4.0.0=h6a678d5_0
  - libcublas=*********=0
  - libcufft=*********=0
  - libcufile=********=0
  - libcurand=*********=0
  - libcusolver=*********=0
  - libcusparse=*********=0
  - libdeflate=1.22=h5eee18b_0
  - libffi=3.4.4=h6a678d5_1
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libiconv=1.16=h5eee18b_3
  - libidn2=2.3.4=h5eee18b_0
  - libjpeg-turbo=2.0.0=h9bf148f_0
  - libnpp=11.8.0.86=0
  - libnvjpeg=11.9.0.86=0
  - libpng=1.6.39=h5eee18b_0
  - libstdcxx-ng=11.2.0=h1234567_1
  - libtasn1=4.19.0=h5eee18b_0
  - libtiff=4.5.1=hffd6297_1
  - libunistring=0.9.10=h27cfd23_0
  - libwebp=1.3.2=h11a3e52_0
  - libwebp-base=1.3.2=h5eee18b_1
  - llvm-openmp=14.0.6=h9e868ea_0
  - lz4-c=1.9.4=h6a678d5_1
  - markupsafe=2.1.3=py38h5eee18b_0
  - mkl=2023.1.0=h213fc3f_46344
  - mkl-service=2.4.0=py38h5eee18b_1
  - mkl_fft=1.3.8=py38h5eee18b_0
  - mkl_random=1.2.4=py38hdb19cb5_0
  - mpc=1.1.0=h10f8cd9_1
  - mpfr=4.0.2=hb69a4c5_1
  - mpmath=1.3.0=py38h06a4308_0
  - ncurses=6.4=h6a678d5_0
  - nettle=3.7.3=hbbd107a_1
  - networkx=3.1=py38h06a4308_0
  - numpy=1.24.3=py38hf6e8229_1
  - numpy-base=1.24.3=py38h060ed82_1
  - openh264=2.1.1=h4ff587b_0
  - openjpeg=2.5.2=he7f1fd0_0
  - openssl=3.0.15=h5eee18b_0
  - pillow=10.4.0=py38h5eee18b_0
  - pip=24.2=py38h06a4308_0
  - pysocks=1.7.1=py38h06a4308_0
  - python=3.8.20=he870216_0
  - pytorch=2.4.1=py3.8_cuda11.8_cudnn9.1.0_0
  - pytorch-cuda=11.8=h7e8668a_6
  - pytorch-mutex=1.0=cuda
  - pyyaml=6.0.2=py38h5eee18b_0
  - readline=8.2=h5eee18b_0
  - requests=2.32.3=py38h06a4308_0
  - setuptools=75.1.0=py38h06a4308_0
  - sqlite=3.45.3=h5eee18b_0
  - sympy=1.13.3=py38h06a4308_0
  - tbb=2021.8.0=hdb19cb5_0
  - tk=8.6.14=h39e8969_0
  - torchaudio=2.4.1=py38_cu118
  - torchtriton=3.0.0=py38
  - torchvision=0.20.0=py38_cu118
  - urllib3=2.2.3=py38h06a4308_0
  - wheel=0.44.0=py38h06a4308_0
  - xz=5.4.6=h5eee18b_1
  - yaml=0.2.5=h7b6447c_0
  - zlib=1.2.13=h5eee18b_1
  - zstd=1.5.6=hc292b87_0
  - pip:
      - absl-py==2.1.0
      - accelerate==1.0.1
      - aiofiles==23.2.1
      - aiohappyeyeballs==2.4.4
      - aiohttp==3.10.11
      - aiosignal==1.3.1
      - annotated-types==0.7.0
      - anyio==4.5.2
      - async-timeout==5.0.1
      - attrs==24.3.0
      - beautifulsoup4==4.13.3
      - bitsandbytes==0.37.2
      - cachetools==5.5.1
      - click==8.1.8
      - clip==1.0
      - cloudpickle==3.1.1
      - contourpy==1.1.1
      - cycler==0.12.1
      - datasets==3.1.0
      - decorator==4.4.2
      - dill==0.3.8
      - distro==1.9.0
      - dm-control==1.0.23
      - dm-env==1.6
      - dm-tree==0.1.8
      - etils==1.3.0
      - exceptiongroup==1.2.2
      - faiss-gpu==1.7.2
      - fastapi==0.116.1
      - ffmpy==0.5.0
      - fonttools==4.55.3
      - frozenlist==1.5.0
      - fsspec==2024.9.0
      - ftfy==6.2.3
      - gitdb==4.0.12
      - gitpython==3.1.44
      - glfw==2.8.0
      - google-auth==2.37.0
      - google-auth-oauthlib==1.0.0
      - gradio==4.44.1
      - gradio-client==1.3.0
      - grpcio==1.69.0
      - gym==0.26.2
      - gym-notices==0.0.8
      - h11==0.14.0
      - hf-xet==1.1.5
      - httpcore==1.0.7
      - httpx==0.28.1
      - huggingface-hub==0.33.4
      - imageio==2.35.1
      - imageio-ffmpeg==0.5.1
      - importlib-metadata==8.5.0
      - importlib-resources==6.4.5
      - inquirerpy==0.3.4
      - jiter==0.8.2
      - joblib==1.4.2
      - kiwisolver==1.4.7
      - labmaze==1.0.6
      - lazy-loader==0.4
      - lxml==5.3.0
      - markdown==3.7
      - markdown-it-py==3.0.0
      - matplotlib==3.7.5
      - mdurl==0.1.2
      - moviepy==1.0.3
      - mujoco==3.2.3
      - multidict==6.1.0
      - multiprocess==0.70.16
      - ninja==********
      - oauthlib==3.2.2
      - openai==1.58.1
      - opencv-python==*********
      - orjson==3.10.15
      - packaging==24.2
      - pandas==2.0.3
      - peft==0.13.2
      - pfzy==0.3.4
      - proglog==0.1.10
      - prompt-toolkit==3.0.51
      - propcache==0.2.0
      - protobuf==5.29.3
      - psutil==6.1.1
      - py-cpuinfo==9.0.0
      - pyarrow==17.0.0
      - pyasn1==0.6.1
      - pyasn1-modules==0.4.1
      - pydantic==2.10.4
      - pydantic-core==2.27.2
      - pydub==0.25.1
      - pygame==2.6.1
      - pygments==2.19.2
      - pynvml==11.5.3
      - pyopengl==3.1.9
      - pyparsing==3.1.4
      - python-dateutil==2.9.0.post0
      - python-multipart==0.0.20
      - pytz==2024.2
      - pywavelets==1.4.1
      - regex==2024.11.6
      - requests-oauthlib==2.0.0
      - rich==14.0.0
      - rsa==4.9
      - ruff==0.12.4
      - rwkv==0.8.29
      - safetensors==0.4.5
      - scikit-image==0.21.0
      - scikit-learn==1.3.2
      - scipy==1.10.1
      - seaborn==0.13.2
      - semantic-version==2.10.0
      - shellingham==1.5.4
      - six==1.17.0
      - smmap==5.0.2
      - sniffio==1.3.1
      - soupsieve==2.6
      - starlette==0.44.0
      - tensorboard==2.14.0
      - tensorboard-data-server==0.7.2
      - threadpoolctl==3.5.0
      - tifffile==2023.7.10
      - timm==1.0.14
      - tokenizers==0.20.3
      - tomlkit==0.12.0
      - tqdm==4.67.1
      - transformers==4.46.3
      - typer==0.16.0
      - typing-extensions==4.12.2
      - tzdata==2024.2
      - ultralytics==8.3.119
      - ultralytics-thop==2.0.14
      - uvicorn==0.33.0
      - wcwidth==0.2.13
      - websockets==12.0
      - werkzeug==3.0.6
      - xxhash==3.5.0
      - yarl==1.15.2
      - zipp==3.20.2
prefix: /home/<USER>/miniconda3/envs/CARLA-UAV
