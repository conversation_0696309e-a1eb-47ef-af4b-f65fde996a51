# datasets/navigation_dataset.py
import torch
from torch.utils.data import Dataset, ConcatDataset
import os
import json
import numpy as np


# 2. 修改 SingleMazeDataset 以支持延迟加载
class SingleMazeDataset(Dataset):
    """处理单个迷宫数据的数据集 - 添加延迟加载和内存管理功能"""

    def __init__(self, maze_dir, sequence_length=4, stride=1, lazy_loading=True):
        super().__init__()
        self.maze_dir = maze_dir
        self.sequence_length = sequence_length
        self.stride = stride
        self.lazy_loading = lazy_loading

        # 读取frame_info.json
        with open(os.path.join(maze_dir, 'frame_info.json'), 'r') as f:
            # 使用惰性加载时，只读取必要的元数据
            if lazy_loading:
                # 只加载frame_id和时间戳等最小的必要信息
                frame_info_temp = json.load(f)
                self.frame_info = {}
                for k, v in frame_info_temp.items():
                    self.frame_info[k] = {
                        'frame_id': v.get('frame_id'),
                        'timestamp': v.get('timestamp')
                    }
            else:
                self.frame_info = json.load(f)

        # 根据frame ID排序并找到连续序列
        self.sequences = self._create_sequences()

        # 为了节省内存，可以在此处释放不需要的大对象
        if lazy_loading and hasattr(self, 'frame_info_full'):
            del self.frame_info_full

    # 修复 SingleMazeDataset 类的 __len__ 方法
    def __len__(self):
        """返回数据集中序列的数量"""
        if not hasattr(self, 'sequences') or self.sequences is None:
            return 0
        return len(self.sequences)

    def _split_sequence(self, sequence):
        """将长序列分割成固定长度的子序列"""
        splits = []
        for i in range(0, len(sequence) - self.sequence_length + 1, self.stride):
            splits.append(sequence[i:i + self.sequence_length])
        return splits

    def _create_sequences(self):
        """创建连续帧序列"""
        sequences = []

        # 将frame_info按frame_id排序
        sorted_frames = sorted(self.frame_info.items(),
                               key=lambda x: int(x[1]['frame_id']))

        # 查找连续序列
        current_sequence = []
        prev_frame_num = None

        for frame_id, info in sorted_frames:
            frame_num = int(info['frame_id'])

            if prev_frame_num is None or frame_num == prev_frame_num + 1:
                current_sequence.append((frame_id, info))
            else:
                if len(current_sequence) >= self.sequence_length:
                    sequences.extend(self._split_sequence(current_sequence))
                current_sequence = [(frame_id, info)]

            prev_frame_num = frame_num

        # 处理最后一个序列
        if len(current_sequence) >= self.sequence_length:
            sequences.extend(self._split_sequence(current_sequence))

        return sequences

    def _load_full_frame_info(self, frame_id):
        """按需加载完整的帧信息"""
        if not self.lazy_loading or 'agent_state' in self.frame_info[frame_id]:
            return self.frame_info[frame_id]

        # 如果是惰性加载，并且完整信息尚未加载，则现在加载它
        frame_path = os.path.join(self.maze_dir, f'frame_info_{frame_id}.json')
        if os.path.exists(frame_path):
            try:
                with open(frame_path, 'r') as f:
                    return json.load(f)
            except Exception:
                pass

        # 如果无法加载单独的文件，则重新加载完整的frame_info.json
        if not hasattr(self, 'frame_info_full'):
            with open(os.path.join(self.maze_dir, 'frame_info.json'), 'r') as f:
                self.frame_info_full = json.load(f)

        return self.frame_info_full.get(frame_id, {})

    def __getitem__(self, idx):
        sequence = self.sequences[idx]
        batch_data = {
            'angles': [],
            'positions': [],
            'velocities': [],
            'angular_velocities': [],
            'timestamps': [],
            'frame_ids': []
        }

        # 获取目录信息 - 从".../D1_P1/000000"提取"D1_P1"
        dataset_dir = os.path.basename(os.path.dirname(self.maze_dir))

        for frame_id, info in sequence:
            # 如果使用惰性加载，则在这里获取完整的帧信息
            if self.lazy_loading:
                info = self._load_full_frame_info(frame_id)

            # 基础信息
            agent_state = info.get('agent_state', {})
            position = agent_state.get('position', [0, 0, 0])
            rotation = agent_state.get('rotation', 0)
            velocity = agent_state.get('velocity', [0, 0, 0])
            angular_velocity = agent_state.get('angular_velocity', 0)

            batch_data['positions'].append(np.array(position, dtype=np.float32))
            batch_data['angles'].append(np.float32(rotation))
            batch_data['velocities'].append(np.array(velocity, dtype=np.float32))
            batch_data['angular_velocities'].append(np.float32(angular_velocity))
            batch_data['timestamps'].append(np.float32(info.get('timestamp', 0)))
            # 存储原始frame_id（不含'frame_'前缀）
            batch_data['frame_ids'].append(frame_id.replace('frame_', ''))

        # 转换为张量
        tensor_keys = ['positions', 'angles', 'velocities',
                       'angular_velocities', 'timestamps']
        for key in tensor_keys:
            if key in batch_data and batch_data[key]:
                batch_data[key] = torch.stack([
                    x if isinstance(x, torch.Tensor) else torch.tensor(x, dtype=torch.float32)
                    for x in batch_data[key]
                ])

        # 添加 sequence_id（整个序列共用同一个id）
        batch_data['sequence_id'] = dataset_dir  # 单个字符串，而不是列表

        return batch_data


# 1. Add data chunking mechanism to EnhancedNavigationDataset class
class EnhancedNavigationDataset(Dataset):
    """组合多个迷宫数据的数据集，添加数据分块机制"""

    def __init__(self, maze_dirs, sequence_length=4, stride=1, split='train', chunk_size=1000, current_chunk=0):
        super().__init__()
        self.maze_dirs = maze_dirs
        self.sequence_length = sequence_length
        self.stride = stride
        self.split = split

        # 数据分块参数
        self.chunk_size = chunk_size
        self.current_chunk = current_chunk
        self.total_chunks = None  # 会在加载时计算

        # 按块加载数据集
        self._load_chunk()

    # 在 EnhancedNavigationDataset 中修改 _load_chunk 方法

    def _load_chunk(self):
        """只加载当前数据块"""
        self.datasets = []

        # 计算总块数
        if self.total_chunks is None:
            # 这里改为直接使用迷宫目录数量来计算块数
            # 而不是尝试创建和计算所有数据集的序列数
            total_dirs = len(self.maze_dirs)
            self.total_chunks = max(1, (total_dirs + self.chunk_size - 1) // self.chunk_size)
            print(f"数据集分为 {self.total_chunks} 个块，每块包含约 {self.chunk_size} 个目录")

        # 确保当前块索引在有效范围内
        self.current_chunk = min(max(0, self.current_chunk), self.total_chunks - 1)

        # 计算当前块的目录范围
        start_idx = self.current_chunk * self.chunk_size
        end_idx = min((self.current_chunk + 1) * self.chunk_size, len(self.maze_dirs))

        chunk_maze_dirs = self.maze_dirs[start_idx:end_idx]
        print(f"正在加载数据块 {self.current_chunk + 1}/{self.total_chunks}，包含 {end_idx - start_idx} 个目录")

        for maze_dir in chunk_maze_dirs:
            try:
                dataset = SingleMazeDataset(maze_dir, self.sequence_length, self.stride)
                if len(dataset) > 0:  # 确保数据集非空
                    self.datasets.append(dataset)
            except Exception as e:
                print(f"Warning: Failed to load dataset from {maze_dir}: {e}")

        if not self.datasets:
            print(f"Warning: No valid datasets found in chunk {self.current_chunk}!")
            # 创建一个空的数据集，避免程序崩溃
            try:
                # 尝试使用第一个目录创建一个最小的数据集
                self.datasets = [SingleMazeDataset(self.maze_dirs[0], self.sequence_length, self.stride)]
            except Exception as e:
                # 如果仍然失败，创建一个虚拟数据集
                print(f"无法创建备用数据集: {e}")
                from torch.utils.data import TensorDataset
                dummy_data = torch.zeros((1, self.sequence_length, 3))
                self.datasets = [TensorDataset(dummy_data)]

        # 计算累积长度
        self._calculate_cumulative_lengths()

    def switch_chunk(self, new_chunk_index):
        """切换到新的数据块"""
        if new_chunk_index < 0 or (self.total_chunks is not None and new_chunk_index >= self.total_chunks):
            print(f"Warning: Chunk index {new_chunk_index} out of valid range [0, {self.total_chunks-1 if self.total_chunks else '?'}]")
            return False
        
        if new_chunk_index == self.current_chunk:
            return True  # 已经在正确的数据块上
        
        try:
            old_chunk = self.current_chunk
            self.current_chunk = new_chunk_index
            print(f"切换到数据块 {self.current_chunk + 1}/{self.total_chunks}")
            self._load_chunk()
            return True
        except Exception as e:
            print(f"切换数据块失败: {e}")
            self.current_chunk = old_chunk  # 恢复到原来的块
            return False

    def _calculate_cumulative_lengths(self):
        """计算累积长度用于索引"""
        self.cumulative_lengths = []
        current_length = 0
        for dataset in self.datasets:
            current_length += len(dataset)
            self.cumulative_lengths.append(current_length)

    def __len__(self):
        if not self.cumulative_lengths:
            return 0
        return self.cumulative_lengths[-1]

    def __getitem__(self, idx):
        # 边界检查
        if idx < 0 or not self.cumulative_lengths or idx >= self.cumulative_lengths[-1]:
            raise IndexError(f"Index {idx} out of bounds for dataset of length {len(self)}")
        
        # 找到对应的数据集和局部索引
        dataset_idx = 0
        while dataset_idx < len(self.cumulative_lengths) and idx >= self.cumulative_lengths[dataset_idx]:
            dataset_idx += 1
        
        # 确保 dataset_idx 在有效范围内
        if dataset_idx >= len(self.datasets):
            raise IndexError(f"Dataset index {dataset_idx} out of range (max: {len(self.datasets)-1})")
        
        # 计算本地索引
        if dataset_idx == 0:
            local_idx = idx
        else:
            local_idx = idx - self.cumulative_lengths[dataset_idx - 1]
        
        # 确保 local_idx 在有效范围内
        if local_idx < 0 or local_idx >= len(self.datasets[dataset_idx]):
            raise IndexError(f"Local index {local_idx} out of range for dataset {dataset_idx}")
        
        try:
            return self.datasets[dataset_idx][local_idx]
        except Exception as e:
            print(f"Error accessing dataset {dataset_idx}, local_idx {local_idx}")
            print(f"Datasets length: {len(self.datasets)}, Cumulative lengths: {self.cumulative_lengths}")
            raise