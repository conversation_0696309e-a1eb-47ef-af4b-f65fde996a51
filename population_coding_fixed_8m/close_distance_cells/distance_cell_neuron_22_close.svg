<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="828.560413pt" height="352.267438pt" viewBox="0 0 828.560413 352.267438" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:56:27.407765</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 352.267438 
L 828.560413 352.267438 
L 828.560413 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
L 311.889906 44.84925 
L 42.113906 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g clip-path="url(#p00c32cfd7e)">
    <image xlink:href="data:image/png;base64,
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" id="image1517840f8e" transform="scale(1 -1) translate(0 -270.24)" x="42.113906" y="-44.38525" width="270.24" height="270.24"/>
   </g>
   <g id="patch_3">
    <path d="M 177.001906 230.32025 
C 190.416675 230.32025 203.283815 224.990506 212.769489 215.504832 
C 222.255162 206.019159 227.584906 193.152018 227.584906 179.73725 
C 227.584906 166.322482 222.255162 153.455341 212.769489 143.969668 
C 203.283815 134.483994 190.416675 129.15425 177.001906 129.15425 
C 163.587138 129.15425 150.719998 134.483994 141.234324 143.969668 
C 131.74865 153.455341 126.418906 166.322482 126.418906 179.73725 
C 126.418906 193.152018 131.74865 206.019159 141.234324 215.504832 
C 150.719998 224.990506 163.587138 230.32025 177.001906 230.32025 
L 177.001906 230.32025 
z
" clip-path="url(#p00c32cfd7e)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 177.001906 264.04225 
C 199.359854 264.04225 220.805087 255.159343 236.614543 239.349887 
C 252.424 223.540431 261.306906 202.095197 261.306906 179.73725 
C 261.306906 157.379303 252.424 135.934069 236.614543 120.124613 
C 220.805087 104.315157 199.359854 95.43225 177.001906 95.43225 
C 154.643959 95.43225 133.198725 104.315157 117.389269 120.124613 
C 101.579813 135.934069 92.696906 157.379303 92.696906 179.73725 
C 92.696906 202.095197 101.579813 223.540431 117.389269 239.349887 
C 133.198725 255.159343 154.643959 264.04225 177.001906 264.04225 
L 177.001906 264.04225 
z
" clip-path="url(#p00c32cfd7e)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 177.001906 314.62525 
C 212.774622 314.62525 247.086996 300.412599 272.382126 275.11747 
C 297.677256 249.82234 311.889906 215.509966 311.889906 179.73725 
C 311.889906 143.964534 297.677256 109.65216 272.382126 84.35703 
C 247.086996 59.061901 212.774622 44.84925 177.001906 44.84925 
C 141.22919 44.84925 106.916817 59.061901 81.621687 84.35703 
C 56.326557 109.65216 42.113906 143.964534 42.113906 179.73725 
C 42.113906 215.509966 56.326557 249.82234 81.621687 275.11747 
C 106.916817 300.412599 141.22919 314.62525 177.001906 314.62525 
L 177.001906 314.62525 
z
" clip-path="url(#p00c32cfd7e)" style="fill: none; opacity: 0.7; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" clip-path="url(#p00c32cfd7e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="m78de2d4a73" d="M 0 0 
L 0 3.5 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m78de2d4a73" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −8 -->
      <g transform="translate(35.479922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 75.835906 314.62525 
L 75.835906 44.84925 
" clip-path="url(#p00c32cfd7e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#m78de2d4a73" x="75.835906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −6 -->
      <g transform="translate(69.201922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 109.557906 314.62525 
L 109.557906 44.84925 
" clip-path="url(#p00c32cfd7e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#m78de2d4a73" x="109.557906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_3">
      <!-- −4 -->
      <g transform="translate(102.923922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 143.279906 314.62525 
L 143.279906 44.84925 
" clip-path="url(#p00c32cfd7e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#m78de2d4a73" x="143.279906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_4">
      <!-- −2 -->
      <g transform="translate(136.645922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 177.001906 314.62525 
L 177.001906 44.84925 
" clip-path="url(#p00c32cfd7e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#m78de2d4a73" x="177.001906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(174.138781 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 210.723906 314.62525 
L 210.723906 44.84925 
" clip-path="url(#p00c32cfd7e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#m78de2d4a73" x="210.723906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 2 -->
      <g transform="translate(207.860781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 244.445906 314.62525 
L 244.445906 44.84925 
" clip-path="url(#p00c32cfd7e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#m78de2d4a73" x="244.445906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 4 -->
      <g transform="translate(241.582781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <path d="M 278.167906 314.62525 
L 278.167906 44.84925 
" clip-path="url(#p00c32cfd7e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#m78de2d4a73" x="278.167906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 6 -->
      <g transform="translate(275.304781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_17">
      <path d="M 311.889906 314.62525 
L 311.889906 44.84925 
" clip-path="url(#p00c32cfd7e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#m78de2d4a73" x="311.889906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 8 -->
      <g transform="translate(309.026781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- Relative X Position (m) -->
     <g transform="translate(105.68925 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-52" d="M 2297 2597 
Q 2675 2597 2839 2737 
Q 3003 2878 3003 3200 
Q 3003 3519 2839 3656 
Q 2675 3794 2297 3794 
L 1791 3794 
L 1791 2597 
L 2297 2597 
z
M 1791 1766 
L 1791 0 
L 588 0 
L 588 4666 
L 2425 4666 
Q 3347 4666 3776 4356 
Q 4206 4047 4206 3378 
Q 4206 2916 3982 2619 
Q 3759 2322 3309 2181 
Q 3556 2125 3751 1926 
Q 3947 1728 4147 1325 
L 4800 0 
L 3519 0 
L 2950 1159 
Q 2778 1509 2601 1637 
Q 2425 1766 2131 1766 
L 1791 1766 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6c" d="M 538 4863 
L 1656 4863 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-76" d="M 97 3500 
L 1216 3500 
L 2088 1081 
L 2956 3500 
L 4078 3500 
L 2700 0 
L 1472 0 
L 97 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-58" d="M 3188 2381 
L 4806 0 
L 3553 0 
L 2463 1594 
L 1381 0 
L 122 0 
L 1741 2381 
L 184 4666 
L 1441 4666 
L 2463 3163 
L 3481 4666 
L 4744 4666 
L 3188 2381 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-50" d="M 588 4666 
L 2584 4666 
Q 3475 4666 3951 4270 
Q 4428 3875 4428 3144 
Q 4428 2409 3951 2014 
Q 3475 1619 2584 1619 
L 1791 1619 
L 1791 0 
L 588 0 
L 588 4666 
z
M 1791 3794 
L 1791 2491 
L 2456 2491 
Q 2806 2491 2997 2661 
Q 3188 2831 3188 3144 
Q 3188 3456 2997 3625 
Q 2806 3794 2456 3794 
L 1791 3794 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6f" d="M 2203 2784 
Q 1831 2784 1636 2517 
Q 1441 2250 1441 1747 
Q 1441 1244 1636 976 
Q 1831 709 2203 709 
Q 2569 709 2762 976 
Q 2956 1244 2956 1747 
Q 2956 2250 2762 2517 
Q 2569 2784 2203 2784 
z
M 2203 3584 
Q 3106 3584 3614 3096 
Q 4122 2609 4122 1747 
Q 4122 884 3614 396 
Q 3106 -91 2203 -91 
Q 1297 -91 786 396 
Q 275 884 275 1747 
Q 275 2609 786 3096 
Q 1297 3584 2203 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-28" d="M 2413 -844 
L 1484 -844 
Q 1006 -72 778 623 
Q 550 1319 550 2003 
Q 550 2688 779 3389 
Q 1009 4091 1484 4856 
L 2413 4856 
Q 2013 4116 1813 3408 
Q 1613 2700 1613 2009 
Q 1613 1319 1811 609 
Q 2009 -100 2413 -844 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6d" d="M 3781 2919 
Q 3994 3244 4286 3414 
Q 4578 3584 4928 3584 
Q 5531 3584 5847 3212 
Q 6163 2841 6163 2131 
L 6163 0 
L 5038 0 
L 5038 1825 
Q 5041 1866 5042 1909 
Q 5044 1953 5044 2034 
Q 5044 2406 4934 2573 
Q 4825 2741 4581 2741 
Q 4263 2741 4089 2478 
Q 3916 2216 3909 1719 
L 3909 0 
L 2784 0 
L 2784 1825 
Q 2784 2406 2684 2573 
Q 2584 2741 2328 2741 
Q 2006 2741 1831 2477 
Q 1656 2213 1656 1722 
L 1656 0 
L 531 0 
L 531 3500 
L 1656 3500 
L 1656 2988 
Q 1863 3284 2130 3434 
Q 2397 3584 2719 3584 
Q 3081 3584 3359 3409 
Q 3638 3234 3781 2919 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-29" d="M 513 -844 
Q 913 -100 1113 609 
Q 1313 1319 1313 2009 
Q 1313 2700 1113 3408 
Q 913 4116 513 4856 
L 1441 4856 
Q 1916 4091 2145 3389 
Q 2375 2688 2375 2003 
Q 2375 1319 2147 623 
Q 1919 -72 1441 -844 
L 513 -844 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-58" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="573.583984"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="608.398438"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="681.689453"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="750.390625"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="809.912109"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="844.189453"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="891.992188"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="926.269531"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="994.970703"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1066.162109"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1100.976562"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1146.679688"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1250.878906"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_19">
      <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" clip-path="url(#p00c32cfd7e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <defs>
       <path id="m79157ec694" d="M 0 0 
L -3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m79157ec694" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_11">
      <!-- −8 -->
      <g transform="translate(21.845937 318.044547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_21">
      <path d="M 42.113906 280.90325 
L 311.889906 280.90325 
" clip-path="url(#p00c32cfd7e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#m79157ec694" x="42.113906" y="280.90325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_12">
      <!-- −6 -->
      <g transform="translate(21.845937 284.322547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_23">
      <path d="M 42.113906 247.18125 
L 311.889906 247.18125 
" clip-path="url(#p00c32cfd7e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#m79157ec694" x="42.113906" y="247.18125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_13">
      <!-- −4 -->
      <g transform="translate(21.845937 250.600547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_25">
      <path d="M 42.113906 213.45925 
L 311.889906 213.45925 
" clip-path="url(#p00c32cfd7e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#m79157ec694" x="42.113906" y="213.45925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_14">
      <!-- −2 -->
      <g transform="translate(21.845937 216.878547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_27">
      <path d="M 42.113906 179.73725 
L 311.889906 179.73725 
" clip-path="url(#p00c32cfd7e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#m79157ec694" x="42.113906" y="179.73725" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 0 -->
      <g transform="translate(29.387656 183.156547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_29">
      <path d="M 42.113906 146.01525 
L 311.889906 146.01525 
" clip-path="url(#p00c32cfd7e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#m79157ec694" x="42.113906" y="146.01525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 2 -->
      <g transform="translate(29.387656 149.434547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_31">
      <path d="M 42.113906 112.29325 
L 311.889906 112.29325 
" clip-path="url(#p00c32cfd7e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#m79157ec694" x="42.113906" y="112.29325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 4 -->
      <g transform="translate(29.387656 115.712547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_33">
      <path d="M 42.113906 78.57125 
L 311.889906 78.57125 
" clip-path="url(#p00c32cfd7e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#m79157ec694" x="42.113906" y="78.57125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 6 -->
      <g transform="translate(29.387656 81.990547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_35">
      <path d="M 42.113906 44.84925 
L 311.889906 44.84925 
" clip-path="url(#p00c32cfd7e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#m79157ec694" x="42.113906" y="44.84925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 8 -->
      <g transform="translate(29.387656 48.268547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_20">
     <!-- Relative Y Position (m) -->
     <g transform="translate(15.558281 250.792094) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-59" d="M -63 4666 
L 1253 4666 
L 2316 3003 
L 3378 4666 
L 4697 4666 
L 2919 1966 
L 2919 0 
L 1716 0 
L 1716 1966 
L -63 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-59" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="568.896484"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="603.710938"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="677.001953"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="745.703125"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="805.224609"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="839.501953"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="887.304688"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="921.582031"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="990.283203"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1061.474609"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1096.289062"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1141.992188"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1246.191406"/>
     </g>
    </g>
   </g>
   <g id="patch_6">
    <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_21">
    <g id="patch_8">
     <path d="M 200.10902 150.124824 
L 225.429957 150.124824 
Q 227.229957 150.124824 227.229957 148.324824 
L 227.229957 139.614511 
Q 227.229957 137.814511 225.429957 137.814511 
L 200.10902 137.814511 
Q 198.30902 137.814511 198.30902 139.614511 
L 198.30902 148.324824 
Q 198.30902 150.124824 200.10902 150.124824 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 3.0m -->
    <g style="fill: #ffffff" transform="translate(200.10902 146.453105) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-33" d="M 2981 2516 
Q 3453 2394 3698 2092 
Q 3944 1791 3944 1325 
Q 3944 631 3412 270 
Q 2881 -91 1863 -91 
Q 1503 -91 1142 -33 
Q 781 25 428 141 
L 428 1069 
Q 766 900 1098 814 
Q 1431 728 1753 728 
Q 2231 728 2486 893 
Q 2741 1059 2741 1369 
Q 2741 1688 2480 1852 
Q 2219 2016 1709 2016 
L 1228 2016 
L 1228 2791 
L 1734 2791 
Q 2188 2791 2409 2933 
Q 2631 3075 2631 3366 
Q 2631 3634 2415 3781 
Q 2200 3928 1806 3928 
Q 1516 3928 1219 3862 
Q 922 3797 628 3669 
L 628 4550 
Q 984 4650 1334 4700 
Q 1684 4750 2022 4750 
Q 2931 4750 3382 4451 
Q 3834 4153 3834 3553 
Q 3834 3144 3618 2883 
Q 3403 2622 2981 2516 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2e" d="M 653 1209 
L 1778 1209 
L 1778 0 
L 653 0 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-30" d="M 2944 2338 
Q 2944 3213 2780 3570 
Q 2616 3928 2228 3928 
Q 1841 3928 1675 3570 
Q 1509 3213 1509 2338 
Q 1509 1453 1675 1090 
Q 1841 728 2228 728 
Q 2613 728 2778 1090 
Q 2944 1453 2944 2338 
z
M 4147 2328 
Q 4147 1169 3647 539 
Q 3147 -91 2228 -91 
Q 1306 -91 806 539 
Q 306 1169 306 2328 
Q 306 3491 806 4120 
Q 1306 4750 2228 4750 
Q 3147 4750 3647 4120 
Q 4147 3491 4147 2328 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-33"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_22">
    <g id="patch_9">
     <path d="M 223.954075 126.279769 
L 249.275012 126.279769 
Q 251.075012 126.279769 251.075012 124.479769 
L 251.075012 115.769457 
Q 251.075012 113.969457 249.275012 113.969457 
L 223.954075 113.969457 
Q 222.154075 113.969457 222.154075 115.769457 
L 222.154075 124.479769 
Q 222.154075 126.279769 223.954075 126.279769 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 5.0m -->
    <g style="fill: #ffffff" transform="translate(223.954075 122.60805) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-35" d="M 678 4666 
L 3669 4666 
L 3669 3781 
L 1638 3781 
L 1638 3059 
Q 1775 3097 1914 3117 
Q 2053 3138 2203 3138 
Q 3056 3138 3531 2711 
Q 4006 2284 4006 1522 
Q 4006 766 3489 337 
Q 2972 -91 2053 -91 
Q 1656 -91 1267 -14 
Q 878 63 494 219 
L 494 1166 
Q 875 947 1217 837 
Q 1559 728 1863 728 
Q 2300 728 2551 942 
Q 2803 1156 2803 1522 
Q 2803 1891 2551 2103 
Q 2300 2316 1863 2316 
Q 1603 2316 1309 2248 
Q 1016 2181 678 2041 
L 678 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-35"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_23">
    <g id="patch_10">
     <path d="M 259.721657 90.512187 
L 285.042595 90.512187 
Q 286.842595 90.512187 286.842595 88.712187 
L 286.842595 80.001874 
Q 286.842595 78.201874 285.042595 78.201874 
L 259.721657 78.201874 
Q 257.921657 78.201874 257.921657 80.001874 
L 257.921657 88.712187 
Q 257.921657 90.512187 259.721657 90.512187 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 8.0m -->
    <g style="fill: #ffffff" transform="translate(259.721657 86.840468) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-38" d="M 2228 2088 
Q 1891 2088 1709 1903 
Q 1528 1719 1528 1375 
Q 1528 1031 1709 848 
Q 1891 666 2228 666 
Q 2563 666 2741 848 
Q 2919 1031 2919 1375 
Q 2919 1722 2741 1905 
Q 2563 2088 2228 2088 
z
M 1350 2484 
Q 925 2613 709 2878 
Q 494 3144 494 3541 
Q 494 4131 934 4440 
Q 1375 4750 2228 4750 
Q 3075 4750 3515 4442 
Q 3956 4134 3956 3541 
Q 3956 3144 3739 2878 
Q 3522 2613 3097 2484 
Q 3572 2353 3814 2058 
Q 4056 1763 4056 1313 
Q 4056 619 3595 264 
Q 3134 -91 2228 -91 
Q 1319 -91 855 264 
Q 391 619 391 1313 
Q 391 1763 633 2058 
Q 875 2353 1350 2484 
z
M 1631 3419 
Q 1631 3141 1786 2991 
Q 1941 2841 2228 2841 
Q 2509 2841 2662 2991 
Q 2816 3141 2816 3419 
Q 2816 3697 2662 3845 
Q 2509 3994 2228 3994 
Q 1941 3994 1786 3844 
Q 1631 3694 1631 3419 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-38"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_24">
    <!-- Close-Distance Cell (Neuron 22) -->
    <g transform="translate(69.464094 16.411875) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2d" d="M 347 2297 
L 2309 2297 
L 2309 1388 
L 347 1388 
L 347 2297 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-44" d="M 1791 3756 
L 1791 909 
L 2222 909 
Q 2959 909 3348 1275 
Q 3738 1641 3738 2338 
Q 3738 3031 3350 3393 
Q 2963 3756 2222 3756 
L 1791 3756 
z
M 588 4666 
L 1856 4666 
Q 2919 4666 3439 4514 
Q 3959 4363 4331 4000 
Q 4659 3684 4818 3271 
Q 4978 2859 4978 2338 
Q 4978 1809 4818 1395 
Q 4659 981 4331 666 
Q 3956 303 3431 151 
Q 2906 0 1856 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4e" d="M 588 4666 
L 1931 4666 
L 3628 1466 
L 3628 4666 
L 4769 4666 
L 4769 0 
L 3425 0 
L 1728 3200 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-75" d="M 500 1363 
L 500 3500 
L 1625 3500 
L 1625 3150 
Q 1625 2866 1622 2436 
Q 1619 2006 1619 1863 
Q 1619 1441 1641 1255 
Q 1663 1069 1716 984 
Q 1784 875 1895 815 
Q 2006 756 2150 756 
Q 2500 756 2700 1025 
Q 2900 1294 2900 1772 
L 2900 3500 
L 4019 3500 
L 4019 0 
L 2900 0 
L 2900 506 
Q 2647 200 2364 54 
Q 2081 -91 1741 -91 
Q 1134 -91 817 281 
Q 500 653 500 1363 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-32" d="M 1844 884 
L 3897 884 
L 3897 0 
L 506 0 
L 506 884 
L 2209 2388 
Q 2438 2594 2547 2791 
Q 2656 2988 2656 3200 
Q 2656 3528 2436 3728 
Q 2216 3928 1850 3928 
Q 1569 3928 1234 3808 
Q 900 3688 519 3450 
L 519 4475 
Q 925 4609 1322 4679 
Q 1719 4750 2100 4750 
Q 2938 4750 3402 4381 
Q 3866 4013 3866 3353 
Q 3866 2972 3669 2642 
Q 3472 2313 2841 1759 
L 1844 884 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-43"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="73.388672"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="107.666016"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="176.367188"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="235.888672"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="303.710938"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="345.214844"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="428.222656"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="462.5"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="522.021484"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="569.824219"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="637.304688"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="708.496094"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="767.773438"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="835.595703"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="870.410156"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="943.798828"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="1011.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="1045.898438"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1080.175781"/>
     <use xlink:href="#DejaVuSans-Bold-28" x="1114.990234"/>
     <use xlink:href="#DejaVuSans-Bold-4e" x="1160.693359"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1244.384766"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1312.207031"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1383.398438"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="1432.714844"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1501.416016"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1572.607422"/>
     <use xlink:href="#DejaVuSans-Bold-32" x="1607.421875"/>
     <use xlink:href="#DejaVuSans-Bold-32" x="1677.001953"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="1746.582031"/>
    </g>
    <!-- 2D Activation Map -->
    <g transform="translate(114.950656 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-41" d="M 3419 850 
L 1538 850 
L 1241 0 
L 31 0 
L 1759 4666 
L 3194 4666 
L 4922 0 
L 3713 0 
L 3419 850 
z
M 1838 1716 
L 3116 1716 
L 2478 3572 
L 1838 1716 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4d" d="M 588 4666 
L 2119 4666 
L 3181 2169 
L 4250 4666 
L 5778 4666 
L 5778 0 
L 4641 0 
L 4641 3413 
L 3566 897 
L 2803 897 
L 1728 3413 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-32"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="152.587891"/>
     <use xlink:href="#DejaVuSans-Bold-41" x="187.402344"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="264.794922"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="324.072266"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="371.875"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="406.152344"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.337891"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="538.818359"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="586.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="620.898438"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="689.599609"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="760.791016"/>
     <use xlink:href="#DejaVuSans-Bold-4d" x="795.605469"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="895.117188"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="962.597656"/>
    </g>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="ma7eb9c98e7" d="M 0 -5.477226 
L -1.229714 -1.692556 
L -5.209151 -1.692556 
L -1.989719 0.646499 
L -3.219432 4.431169 
L -0 2.092114 
L 3.219432 4.431169 
L 1.989719 0.646499 
L 5.209151 -1.692556 
L 1.229714 -1.692556 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#p00c32cfd7e)">
     <use xlink:href="#ma7eb9c98e7" x="177.001906" y="179.73725" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_11">
     <path d="M 242.388656 62.99175 
L 307.889906 62.99175 
L 307.889906 48.84925 
L 242.388656 48.84925 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="PathCollection_2">
     <g>
      <use xlink:href="#ma7eb9c98e7" x="253.588656" y="56.028" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
     </g>
    </g>
    <g id="text_25">
     <!-- Observer -->
     <g transform="translate(267.988656 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4f"/>
      <use xlink:href="#DejaVuSans-62" x="78.710938"/>
      <use xlink:href="#DejaVuSans-73" x="142.1875"/>
      <use xlink:href="#DejaVuSans-65" x="194.287109"/>
      <use xlink:href="#DejaVuSans-72" x="255.810547"/>
      <use xlink:href="#DejaVuSans-76" x="296.923828"/>
      <use xlink:href="#DejaVuSans-65" x="356.103516"/>
      <use xlink:href="#DejaVuSans-72" x="417.626953"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_12">
    <path d="M 446.519015 314.62525 
L 818.497288 314.62525 
L 818.497288 44.84925 
L 446.519015 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="mda9c207912" d="M 451.16468 -37.642188 
L 451.16468 -37.642188 
L 460.456009 -180.479782 
L 469.747338 -160.894468 
L 479.038668 -175.684468 
L 488.329997 -181.853401 
L 497.621326 -153.763618 
L 506.912655 -171.993742 
L 516.203985 -103.711569 
L 525.495314 -198.663331 
L 534.786643 -294.571711 
L 544.077972 -203.784167 
L 553.369302 -249.367444 
L 562.660631 -185.604578 
L 571.95196 -138.725951 
L 581.243289 -176.225189 
L 590.534618 -100.823954 
L 599.825948 -197.399648 
L 609.117277 -223.857929 
L 618.408606 -173.204746 
L 627.699935 -127.783326 
L 636.991265 -147.875697 
L 646.282594 -88.739677 
L 655.573923 -104.295432 
L 664.865252 -101.362786 
L 674.156582 -103.916473 
L 683.447911 -145.876383 
L 692.73924 -96.363468 
L 702.030569 -78.005012 
L 711.321899 -70.186643 
L 720.613228 -57.419705 
L 720.613228 -37.642188 
L 720.613228 -37.642188 
L 711.321899 -37.642188 
L 702.030569 -37.642188 
L 692.73924 -37.642188 
L 683.447911 -37.642188 
L 674.156582 -37.642188 
L 664.865252 -37.642188 
L 655.573923 -37.642188 
L 646.282594 -37.642188 
L 636.991265 -37.642188 
L 627.699935 -37.642188 
L 618.408606 -37.642188 
L 609.117277 -37.642188 
L 599.825948 -37.642188 
L 590.534618 -37.642188 
L 581.243289 -37.642188 
L 571.95196 -37.642188 
L 562.660631 -37.642188 
L 553.369302 -37.642188 
L 544.077972 -37.642188 
L 534.786643 -37.642188 
L 525.495314 -37.642188 
L 516.203985 -37.642188 
L 506.912655 -37.642188 
L 497.621326 -37.642188 
L 488.329997 -37.642188 
L 479.038668 -37.642188 
L 469.747338 -37.642188 
L 460.456009 -37.642188 
L 451.16468 -37.642188 
z
" style="stroke: #e31a1c; stroke-opacity: 0.3"/>
    </defs>
    <g clip-path="url(#pb93d0f7124)">
     <use xlink:href="#mda9c207912" x="0" y="352.267438" style="fill: #e31a1c; fill-opacity: 0.3; stroke: #e31a1c; stroke-opacity: 0.3"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10">
     <g id="line2d_37">
      <path d="M 446.519015 314.62525 
L 446.519015 44.84925 
" clip-path="url(#pb93d0f7124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#m78de2d4a73" x="446.519015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 0 -->
      <g transform="translate(443.65589 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_39">
      <path d="M 493.016299 314.62525 
L 493.016299 44.84925 
" clip-path="url(#pb93d0f7124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#m78de2d4a73" x="493.016299" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 1 -->
      <g transform="translate(490.153174 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_41">
      <path d="M 539.513584 314.62525 
L 539.513584 44.84925 
" clip-path="url(#pb93d0f7124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#m78de2d4a73" x="539.513584" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 2 -->
      <g transform="translate(536.650459 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_43">
      <path d="M 586.010868 314.62525 
L 586.010868 44.84925 
" clip-path="url(#pb93d0f7124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#m78de2d4a73" x="586.010868" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 3 -->
      <g transform="translate(583.147743 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_45">
      <path d="M 632.508152 314.62525 
L 632.508152 44.84925 
" clip-path="url(#pb93d0f7124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#m78de2d4a73" x="632.508152" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 4 -->
      <g transform="translate(629.645027 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_47">
      <path d="M 679.005436 314.62525 
L 679.005436 44.84925 
" clip-path="url(#pb93d0f7124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#m78de2d4a73" x="679.005436" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 5 -->
      <g transform="translate(676.142311 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_49">
      <path d="M 725.50272 314.62525 
L 725.50272 44.84925 
" clip-path="url(#pb93d0f7124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#m78de2d4a73" x="725.50272" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 6 -->
      <g transform="translate(722.639595 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_51">
      <path d="M 772.000004 314.62525 
L 772.000004 44.84925 
" clip-path="url(#pb93d0f7124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#m78de2d4a73" x="772.000004" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 7 -->
      <g transform="translate(769.136879 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-37"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_53">
      <path d="M 818.497288 314.62525 
L 818.497288 44.84925 
" clip-path="url(#pb93d0f7124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#m78de2d4a73" x="818.497288" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 8 -->
      <g transform="translate(815.634163 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_35">
     <!-- Inter-Agent Distance (m) -->
     <g transform="translate(555.165261 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-49" d="M 588 4666 
L 1791 4666 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-49"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="37.207031"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="108.398438"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="156.201172"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="224.023438"/>
      <use xlink:href="#DejaVuSans-Bold-2d" x="273.339844"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="314.84375"/>
      <use xlink:href="#DejaVuSans-Bold-67" x="392.236328"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="463.818359"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="531.640625"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="602.832031"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="650.634766"/>
      <use xlink:href="#DejaVuSans-Bold-44" x="685.449219"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="768.457031"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="802.734375"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="862.255859"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="910.058594"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="977.539062"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="1048.730469"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="1108.007812"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1175.830078"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1210.644531"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1256.347656"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1360.546875"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_55">
      <path d="M 446.519015 314.62525 
L 818.497288 314.62525 
" clip-path="url(#pb93d0f7124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#m79157ec694" x="446.519015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 0.000 -->
      <g transform="translate(413.753703 318.044547) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
       <use xlink:href="#DejaVuSans-30" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_57">
      <path d="M 446.519015 277.655868 
L 818.497288 277.655868 
" clip-path="url(#pb93d0f7124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#m79157ec694" x="446.519015" y="277.655868" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 0.002 -->
      <g transform="translate(413.753703 281.075164) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
       <use xlink:href="#DejaVuSans-32" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_59">
      <path d="M 446.519015 240.686485 
L 818.497288 240.686485 
" clip-path="url(#pb93d0f7124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#m79157ec694" x="446.519015" y="240.686485" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 0.004 -->
      <g transform="translate(413.753703 244.105782) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
       <use xlink:href="#DejaVuSans-34" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_61">
      <path d="M 446.519015 203.717103 
L 818.497288 203.717103 
" clip-path="url(#pb93d0f7124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#m79157ec694" x="446.519015" y="203.717103" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 0.006 -->
      <g transform="translate(413.753703 207.1364) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
       <use xlink:href="#DejaVuSans-36" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_63">
      <path d="M 446.519015 166.74772 
L 818.497288 166.74772 
" clip-path="url(#pb93d0f7124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#m79157ec694" x="446.519015" y="166.74772" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 0.008 -->
      <g transform="translate(413.753703 170.167017) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
       <use xlink:href="#DejaVuSans-38" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_65">
      <path d="M 446.519015 129.778338 
L 818.497288 129.778338 
" clip-path="url(#pb93d0f7124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#m79157ec694" x="446.519015" y="129.778338" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 0.010 -->
      <g transform="translate(413.753703 133.197635) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-31" x="159.033203"/>
       <use xlink:href="#DejaVuSans-30" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_67">
      <path d="M 446.519015 92.808955 
L 818.497288 92.808955 
" clip-path="url(#pb93d0f7124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_68">
      <g>
       <use xlink:href="#m79157ec694" x="446.519015" y="92.808955" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_42">
      <!-- 0.012 -->
      <g transform="translate(413.753703 96.228252) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-31" x="159.033203"/>
       <use xlink:href="#DejaVuSans-32" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_69">
      <path d="M 446.519015 55.839573 
L 818.497288 55.839573 
" clip-path="url(#pb93d0f7124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_70">
      <g>
       <use xlink:href="#m79157ec694" x="446.519015" y="55.839573" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_43">
      <!-- 0.014 -->
      <g transform="translate(413.753703 59.25887) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-31" x="159.033203"/>
       <use xlink:href="#DejaVuSans-34" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="text_44">
     <!-- Neural Activation -->
     <g transform="translate(407.466047 233.746391) rotate(-90) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="line2d_71">
    <path d="M 451.16468 314.62525 
L 460.456009 171.787655 
L 469.747338 191.37297 
L 479.038668 176.582969 
L 488.329997 170.414037 
L 497.621326 198.50382 
L 506.912655 180.273695 
L 516.203985 248.555868 
L 525.495314 153.604107 
L 534.786643 57.695726 
L 544.077972 148.48327 
L 553.369302 102.899993 
L 562.660631 166.662859 
L 571.95196 213.541487 
L 581.243289 176.042248 
L 590.534618 251.443483 
L 599.825948 154.867789 
L 609.117277 128.409509 
L 618.408606 179.062691 
L 627.699935 224.484111 
L 636.991265 204.39174 
L 646.282594 263.52776 
L 655.573923 247.972006 
L 664.865252 250.904651 
L 674.156582 248.350964 
L 683.447911 206.391054 
L 692.73924 255.90397 
L 702.030569 274.262425 
L 711.321899 282.080795 
L 720.613228 294.847732 
" clip-path="url(#pb93d0f7124)" style="fill: none; stroke: #e31a1c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_72">
    <path d="M 534.786643 314.62525 
L 534.786643 44.84925 
" clip-path="url(#pb93d0f7124)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #e31a1c; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_73">
    <path d="M 586.010868 314.62525 
L 586.010868 44.84925 
" clip-path="url(#pb93d0f7124)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_74">
    <path d="M 679.005436 314.62525 
L 679.005436 44.84925 
" clip-path="url(#pb93d0f7124)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="patch_13">
    <path d="M 446.519015 314.62525 
L 446.519015 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_14">
    <path d="M 446.519015 314.62525 
L 818.497288 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_45">
    <g id="patch_15">
     <path d="M 705.457124 114.0718 
L 799.898374 114.0718 
Q 803.098374 114.0718 803.098374 110.8718 
L 803.098374 58.33805 
Q 803.098374 55.13805 799.898374 55.13805 
L 705.457124 55.13805 
Q 702.257124 55.13805 702.257124 58.33805 
L 702.257124 110.8718 
Q 702.257124 114.0718 705.457124 114.0718 
z
" style="fill: #ffffff; opacity: 0.9; stroke: #808080; stroke-linejoin: miter"/>
    </g>
    <!-- Peak Distance: 1.9m -->
    <g transform="translate(717.632124 64.4168) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-3a" d="M 750 794 
L 1409 794 
L 1409 0 
L 750 0 
L 750 794 
z
M 750 3309 
L 1409 3309 
L 1409 2516 
L 750 2516 
L 750 3309 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-44" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="346.179688"/>
     <use xlink:href="#DejaVuSans-73" x="373.962891"/>
     <use xlink:href="#DejaVuSans-74" x="426.0625"/>
     <use xlink:href="#DejaVuSans-61" x="465.271484"/>
     <use xlink:href="#DejaVuSans-6e" x="526.550781"/>
     <use xlink:href="#DejaVuSans-63" x="589.929688"/>
     <use xlink:href="#DejaVuSans-65" x="644.910156"/>
     <use xlink:href="#DejaVuSans-3a" x="706.433594"/>
     <use xlink:href="#DejaVuSans-20" x="740.125"/>
     <use xlink:href="#DejaVuSans-31" x="771.912109"/>
     <use xlink:href="#DejaVuSans-2e" x="835.535156"/>
     <use xlink:href="#DejaVuSans-39" x="867.322266"/>
     <use xlink:href="#DejaVuSans-6d" x="930.945312"/>
    </g>
    <!-- Max Activation: 0.014 -->
    <g transform="translate(712.624624 73.37505) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-4d"/>
     <use xlink:href="#DejaVuSans-61" x="86.279297"/>
     <use xlink:href="#DejaVuSans-78" x="147.558594"/>
     <use xlink:href="#DejaVuSans-20" x="206.738281"/>
     <use xlink:href="#DejaVuSans-41" x="238.525391"/>
     <use xlink:href="#DejaVuSans-63" x="305.183594"/>
     <use xlink:href="#DejaVuSans-74" x="360.164062"/>
     <use xlink:href="#DejaVuSans-69" x="399.373047"/>
     <use xlink:href="#DejaVuSans-76" x="427.15625"/>
     <use xlink:href="#DejaVuSans-61" x="486.335938"/>
     <use xlink:href="#DejaVuSans-74" x="547.615234"/>
     <use xlink:href="#DejaVuSans-69" x="586.824219"/>
     <use xlink:href="#DejaVuSans-6f" x="614.607422"/>
     <use xlink:href="#DejaVuSans-6e" x="675.789062"/>
     <use xlink:href="#DejaVuSans-3a" x="739.167969"/>
     <use xlink:href="#DejaVuSans-20" x="772.859375"/>
     <use xlink:href="#DejaVuSans-30" x="804.646484"/>
     <use xlink:href="#DejaVuSans-2e" x="868.269531"/>
     <use xlink:href="#DejaVuSans-30" x="900.056641"/>
     <use xlink:href="#DejaVuSans-31" x="963.679688"/>
     <use xlink:href="#DejaVuSans-34" x="1027.302734"/>
    </g>
    <!-- Sparsity: 0.224 -->
    <g transform="translate(739.728374 82.3333) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-70" x="63.476562"/>
     <use xlink:href="#DejaVuSans-61" x="126.953125"/>
     <use xlink:href="#DejaVuSans-72" x="188.232422"/>
     <use xlink:href="#DejaVuSans-73" x="229.345703"/>
     <use xlink:href="#DejaVuSans-69" x="281.445312"/>
     <use xlink:href="#DejaVuSans-74" x="309.228516"/>
     <use xlink:href="#DejaVuSans-79" x="348.4375"/>
     <use xlink:href="#DejaVuSans-3a" x="400.367188"/>
     <use xlink:href="#DejaVuSans-20" x="434.058594"/>
     <use xlink:href="#DejaVuSans-30" x="465.845703"/>
     <use xlink:href="#DejaVuSans-2e" x="529.46875"/>
     <use xlink:href="#DejaVuSans-32" x="561.255859"/>
     <use xlink:href="#DejaVuSans-32" x="624.878906"/>
     <use xlink:href="#DejaVuSans-34" x="688.501953"/>
    </g>
    <!-- Peak Significance: 3.33 -->
    <g transform="translate(706.910874 91.29155) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-53" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="332.654297"/>
     <use xlink:href="#DejaVuSans-67" x="360.4375"/>
     <use xlink:href="#DejaVuSans-6e" x="423.914062"/>
     <use xlink:href="#DejaVuSans-69" x="487.292969"/>
     <use xlink:href="#DejaVuSans-66" x="515.076172"/>
     <use xlink:href="#DejaVuSans-69" x="550.28125"/>
     <use xlink:href="#DejaVuSans-63" x="578.064453"/>
     <use xlink:href="#DejaVuSans-61" x="633.044922"/>
     <use xlink:href="#DejaVuSans-6e" x="694.324219"/>
     <use xlink:href="#DejaVuSans-63" x="757.703125"/>
     <use xlink:href="#DejaVuSans-65" x="812.683594"/>
     <use xlink:href="#DejaVuSans-3a" x="874.207031"/>
     <use xlink:href="#DejaVuSans-20" x="907.898438"/>
     <use xlink:href="#DejaVuSans-33" x="939.685547"/>
     <use xlink:href="#DejaVuSans-2e" x="1003.308594"/>
     <use xlink:href="#DejaVuSans-33" x="1035.095703"/>
     <use xlink:href="#DejaVuSans-33" x="1098.71875"/>
    </g>
    <!-- Selectivity Index: 0.394 -->
    <g transform="translate(705.457124 100.2498) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-65" x="63.476562"/>
     <use xlink:href="#DejaVuSans-6c" x="125"/>
     <use xlink:href="#DejaVuSans-65" x="152.783203"/>
     <use xlink:href="#DejaVuSans-63" x="214.306641"/>
     <use xlink:href="#DejaVuSans-74" x="269.287109"/>
     <use xlink:href="#DejaVuSans-69" x="308.496094"/>
     <use xlink:href="#DejaVuSans-76" x="336.279297"/>
     <use xlink:href="#DejaVuSans-69" x="395.458984"/>
     <use xlink:href="#DejaVuSans-74" x="423.242188"/>
     <use xlink:href="#DejaVuSans-79" x="462.451172"/>
     <use xlink:href="#DejaVuSans-20" x="521.630859"/>
     <use xlink:href="#DejaVuSans-49" x="553.417969"/>
     <use xlink:href="#DejaVuSans-6e" x="582.910156"/>
     <use xlink:href="#DejaVuSans-64" x="646.289062"/>
     <use xlink:href="#DejaVuSans-65" x="709.765625"/>
     <use xlink:href="#DejaVuSans-78" x="769.539062"/>
     <use xlink:href="#DejaVuSans-3a" x="828.71875"/>
     <use xlink:href="#DejaVuSans-20" x="862.410156"/>
     <use xlink:href="#DejaVuSans-30" x="894.197266"/>
     <use xlink:href="#DejaVuSans-2e" x="957.820312"/>
     <use xlink:href="#DejaVuSans-33" x="989.607422"/>
     <use xlink:href="#DejaVuSans-39" x="1053.230469"/>
     <use xlink:href="#DejaVuSans-34" x="1116.853516"/>
    </g>
    <!-- Peak Width: 3.4m -->
    <g transform="translate(729.374624 109.20805) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-57" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="365.804688"/>
     <use xlink:href="#DejaVuSans-64" x="393.587891"/>
     <use xlink:href="#DejaVuSans-74" x="457.064453"/>
     <use xlink:href="#DejaVuSans-68" x="496.273438"/>
     <use xlink:href="#DejaVuSans-3a" x="559.652344"/>
     <use xlink:href="#DejaVuSans-20" x="593.34375"/>
     <use xlink:href="#DejaVuSans-33" x="625.130859"/>
     <use xlink:href="#DejaVuSans-2e" x="688.753906"/>
     <use xlink:href="#DejaVuSans-34" x="720.541016"/>
     <use xlink:href="#DejaVuSans-6d" x="784.164062"/>
    </g>
   </g>
   <g id="text_46">
    <!-- Distance Tuning Curve -->
    <g transform="translate(556.694402 16.318125) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-54" d="M 31 4666 
L 4331 4666 
L 4331 3756 
L 2784 3756 
L 2784 0 
L 1581 0 
L 1581 3756 
L 31 3756 
L 31 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-44"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
     <use xlink:href="#DejaVuSans-Bold-54" x="525.195312"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="582.408203"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="653.599609"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="724.791016"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="759.068359"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="830.259766"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="901.841797"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="936.65625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1010.044922"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1081.236328"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="1130.552734"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1195.738281"/>
    </g>
    <!-- Peak: 1.9m, Sparsity: 0.224 -->
    <g transform="translate(538.769402 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-6b" d="M 538 4863 
L 1656 4863 
L 1656 2216 
L 2944 3500 
L 4244 3500 
L 2534 1894 
L 4378 0 
L 3022 0 
L 1656 1459 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-3a" d="M 716 3500 
L 1844 3500 
L 1844 2291 
L 716 2291 
L 716 3500 
z
M 716 1209 
L 1844 1209 
L 1844 0 
L 716 0 
L 716 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-31" d="M 750 831 
L 1813 831 
L 1813 3847 
L 722 3622 
L 722 4441 
L 1806 4666 
L 2950 4666 
L 2950 831 
L 4013 831 
L 4013 0 
L 750 0 
L 750 831 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-39" d="M 641 103 
L 641 966 
Q 928 831 1190 764 
Q 1453 697 1709 697 
Q 2247 697 2547 995 
Q 2847 1294 2900 1881 
Q 2688 1725 2447 1647 
Q 2206 1569 1925 1569 
Q 1209 1569 770 1986 
Q 331 2403 331 3084 
Q 331 3838 820 4291 
Q 1309 4744 2131 4744 
Q 3044 4744 3544 4128 
Q 4044 3513 4044 2388 
Q 4044 1231 3459 570 
Q 2875 -91 1856 -91 
Q 1528 -91 1228 -42 
Q 928 6 641 103 
z
M 2125 2350 
Q 2441 2350 2600 2554 
Q 2759 2759 2759 3169 
Q 2759 3575 2600 3781 
Q 2441 3988 2125 3988 
Q 1809 3988 1650 3781 
Q 1491 3575 1491 3169 
Q 1491 2759 1650 2554 
Q 1809 2350 2125 2350 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2c" d="M 653 1209 
L 1778 1209 
L 1778 256 
L 1006 -909 
L 341 -909 
L 653 256 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-53" d="M 3834 4519 
L 3834 3531 
Q 3450 3703 3084 3790 
Q 2719 3878 2394 3878 
Q 1963 3878 1756 3759 
Q 1550 3641 1550 3391 
Q 1550 3203 1689 3098 
Q 1828 2994 2194 2919 
L 2706 2816 
Q 3484 2659 3812 2340 
Q 4141 2022 4141 1434 
Q 4141 663 3683 286 
Q 3225 -91 2284 -91 
Q 1841 -91 1394 -6 
Q 947 78 500 244 
L 500 1259 
Q 947 1022 1364 901 
Q 1781 781 2169 781 
Q 2563 781 2772 912 
Q 2981 1044 2981 1288 
Q 2981 1506 2839 1625 
Q 2697 1744 2272 1838 
L 1806 1941 
Q 1106 2091 782 2419 
Q 459 2747 459 3303 
Q 459 4000 909 4375 
Q 1359 4750 2203 4750 
Q 2588 4750 2994 4692 
Q 3400 4634 3834 4519 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-79" d="M 78 3500 
L 1197 3500 
L 2138 1125 
L 2938 3500 
L 4056 3500 
L 2584 -331 
Q 2363 -916 2067 -1148 
Q 1772 -1381 1288 -1381 
L 641 -1381 
L 641 -647 
L 991 -647 
Q 1275 -647 1404 -556 
Q 1534 -466 1606 -231 
L 1638 -134 
L 78 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-34" d="M 2356 3675 
L 1038 1722 
L 2356 1722 
L 2356 3675 
z
M 2156 4666 
L 3494 4666 
L 3494 1722 
L 4159 1722 
L 4159 850 
L 3494 850 
L 3494 0 
L 2356 0 
L 2356 850 
L 288 850 
L 288 1881 
L 2156 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-50"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="73.291016"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="141.113281"/>
     <use xlink:href="#DejaVuSans-Bold-6b" x="208.59375"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="275.097656"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="315.087891"/>
     <use xlink:href="#DejaVuSans-Bold-31" x="349.902344"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="419.482422"/>
     <use xlink:href="#DejaVuSans-Bold-39" x="457.470703"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="527.050781"/>
     <use xlink:href="#DejaVuSans-Bold-2c" x="631.25"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="669.238281"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="704.052734"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="776.074219"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="847.65625"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="915.136719"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="964.453125"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1023.974609"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="1058.251953"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="1106.054688"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="1171.240234"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1211.230469"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1246.044922"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="1315.625"/>
     <use xlink:href="#DejaVuSans-Bold-32" x="1353.613281"/>
     <use xlink:href="#DejaVuSans-Bold-32" x="1423.193359"/>
     <use xlink:href="#DejaVuSans-Bold-34" x="1492.773438"/>
    </g>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="mbb3fb01e82" d="M 0 5 
C 1.326016 5 2.597899 4.473168 3.535534 3.535534 
C 4.473168 2.597899 5 1.326016 5 0 
C 5 -1.326016 4.473168 -2.597899 3.535534 -3.535534 
C 2.597899 -4.473168 1.326016 -5 0 -5 
C -1.326016 -5 -2.597899 -4.473168 -3.535534 -3.535534 
C -4.473168 -2.597899 -5 -1.326016 -5 0 
C -5 1.326016 -4.473168 2.597899 -3.535534 3.535534 
C -2.597899 4.473168 -1.326016 5 0 5 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#pb93d0f7124)">
     <use xlink:href="#mbb3fb01e82" x="534.786643" y="57.695726" style="fill: #e31a1c; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="patch_16">
     <path d="M 695.989788 86.47675 
L 812.897288 86.47675 
Q 814.497288 86.47675 814.497288 84.87675 
L 814.497288 50.44925 
Q 814.497288 48.84925 812.897288 48.84925 
L 695.989788 48.84925 
Q 694.389788 48.84925 694.389788 50.44925 
L 694.389788 84.87675 
Q 694.389788 86.47675 695.989788 86.47675 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_75">
     <path d="M 697.589788 55.328 
L 705.589788 55.328 
L 713.589788 55.328 
" style="fill: none; stroke: #e31a1c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
    </g>
    <g id="text_47">
     <!-- Tuning Curve -->
     <g transform="translate(719.989788 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-54"/>
      <use xlink:href="#DejaVuSans-75" x="45.958984"/>
      <use xlink:href="#DejaVuSans-6e" x="109.337891"/>
      <use xlink:href="#DejaVuSans-69" x="172.716797"/>
      <use xlink:href="#DejaVuSans-6e" x="200.5"/>
      <use xlink:href="#DejaVuSans-67" x="263.878906"/>
      <use xlink:href="#DejaVuSans-20" x="327.355469"/>
      <use xlink:href="#DejaVuSans-43" x="359.142578"/>
      <use xlink:href="#DejaVuSans-75" x="428.966797"/>
      <use xlink:href="#DejaVuSans-72" x="492.345703"/>
      <use xlink:href="#DejaVuSans-76" x="533.458984"/>
      <use xlink:href="#DejaVuSans-65" x="592.638672"/>
     </g>
    </g>
    <g id="line2d_76">
     <path d="M 697.589788 67.0705 
L 705.589788 67.0705 
L 713.589788 67.0705 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_48">
     <!-- Close threshold (3.0m) -->
     <g transform="translate(719.989788 69.8705) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6c" x="69.824219"/>
      <use xlink:href="#DejaVuSans-6f" x="97.607422"/>
      <use xlink:href="#DejaVuSans-73" x="158.789062"/>
      <use xlink:href="#DejaVuSans-65" x="210.888672"/>
      <use xlink:href="#DejaVuSans-20" x="272.412109"/>
      <use xlink:href="#DejaVuSans-74" x="304.199219"/>
      <use xlink:href="#DejaVuSans-68" x="343.408203"/>
      <use xlink:href="#DejaVuSans-72" x="406.787109"/>
      <use xlink:href="#DejaVuSans-65" x="445.650391"/>
      <use xlink:href="#DejaVuSans-73" x="507.173828"/>
      <use xlink:href="#DejaVuSans-68" x="559.273438"/>
      <use xlink:href="#DejaVuSans-6f" x="622.652344"/>
      <use xlink:href="#DejaVuSans-6c" x="683.833984"/>
      <use xlink:href="#DejaVuSans-64" x="711.617188"/>
      <use xlink:href="#DejaVuSans-20" x="775.09375"/>
      <use xlink:href="#DejaVuSans-28" x="806.880859"/>
      <use xlink:href="#DejaVuSans-33" x="845.894531"/>
      <use xlink:href="#DejaVuSans-2e" x="909.517578"/>
      <use xlink:href="#DejaVuSans-30" x="941.304688"/>
      <use xlink:href="#DejaVuSans-6d" x="1004.927734"/>
      <use xlink:href="#DejaVuSans-29" x="1102.339844"/>
     </g>
    </g>
    <g id="line2d_77">
     <path d="M 697.589788 78.813 
L 705.589788 78.813 
L 713.589788 78.813 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_49">
     <!-- Far threshold (5.0m) -->
     <g transform="translate(719.989788 81.613) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" x="48.394531"/>
      <use xlink:href="#DejaVuSans-72" x="109.673828"/>
      <use xlink:href="#DejaVuSans-20" x="150.787109"/>
      <use xlink:href="#DejaVuSans-74" x="182.574219"/>
      <use xlink:href="#DejaVuSans-68" x="221.783203"/>
      <use xlink:href="#DejaVuSans-72" x="285.162109"/>
      <use xlink:href="#DejaVuSans-65" x="324.025391"/>
      <use xlink:href="#DejaVuSans-73" x="385.548828"/>
      <use xlink:href="#DejaVuSans-68" x="437.648438"/>
      <use xlink:href="#DejaVuSans-6f" x="501.027344"/>
      <use xlink:href="#DejaVuSans-6c" x="562.208984"/>
      <use xlink:href="#DejaVuSans-64" x="589.992188"/>
      <use xlink:href="#DejaVuSans-20" x="653.46875"/>
      <use xlink:href="#DejaVuSans-28" x="685.255859"/>
      <use xlink:href="#DejaVuSans-35" x="724.269531"/>
      <use xlink:href="#DejaVuSans-2e" x="787.892578"/>
      <use xlink:href="#DejaVuSans-30" x="819.679688"/>
      <use xlink:href="#DejaVuSans-6d" x="883.302734"/>
      <use xlink:href="#DejaVuSans-29" x="980.714844"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_17">
    <path d="M 330.48882 287.64765 
L 341.27986 287.64765 
L 341.27986 71.82685 
L 330.48882 71.82685 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAABYAAAHBCAYAAABt+EsYAAACDUlEQVR4nO2bS25CQQwEDUzunXsHQhYz9MsFelFS1QGskrv9+AhuM9/vKbBmVmPu3CtTZ2bNfFUGN43dcQa7io11C9YtIOvGM+60wlUE6xasW7BuwR0HX/4Dsm48Y5/HG96B3GbelS9CasYOBg9ec+sMLho/WoM7D7fmKjoPN40vrFsghscztm4HYng8Y+t2IIbHM7ZuB2J4PGPrdiCGxzO2bgdieDzjUt2KxqUeE8PT+MA7EJ/HQeNQM3YVQePg0y0Qw9O4PZi3Co2DT7dADE/ja3Dl1zzVVbwqgzUO6/54dgavL9oqHrjwiMatuvFWcccZj8abpnHrQICr0Hhj3YLhBY3/D+70uLcKD+QDz9i6BV54PGPrFnjh8YyRq/BANtYtWLdg3fqDeavwQELtQJDh8Yyt2xlMDE/jjQcSPJCANLZuZzAxPJ6xdTuDieFpvOEdiF8rBI2DdQvI8DTOYN9XbHjhFY1frbo9cXV7/VYGF8PrrBhovG6dGjf/XYELT+NrsHU7aBxqrSCuQuMDr268HRte4IXHMza8oHGwbsHwAu8VhLgKnrEn3R7MW4V1C9YtGF7gGRNXYd0OPGPDCxoHPyoEjQOvbrwdG17ghcczNrygcbBuYf3gwiv9jq6549a7TeAqcMYl4eaOaYMNL7jja7Cr+GDdgnUL1i3wjA0veCDBugXrFng75q3iD9EGue+xdPB4AAAAAElFTkSuQmCC" id="imageff6e129e34" transform="scale(1 -1) translate(0 -215.52)" x="330.72" y="-71.52" width="10.56" height="215.52"/>
   <g id="matplotlib.axis_5"/>
   <g id="matplotlib.axis_6">
    <g id="ytick_18">
     <g id="line2d_78">
      <defs>
       <path id="m52006a6518" d="M 0 0 
L 3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m52006a6518" x="341.27986" y="287.64765" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_50">
      <!-- 0.0 -->
      <g transform="translate(348.27986 291.066947) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_79">
      <g>
       <use xlink:href="#m52006a6518" x="341.27986" y="244.48349" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_51">
      <!-- 0.2 -->
      <g transform="translate(348.27986 247.902787) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_80">
      <g>
       <use xlink:href="#m52006a6518" x="341.27986" y="201.31933" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_52">
      <!-- 0.4 -->
      <g transform="translate(348.27986 204.738627) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_81">
      <g>
       <use xlink:href="#m52006a6518" x="341.27986" y="158.15517" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_53">
      <!-- 0.6 -->
      <g transform="translate(348.27986 161.574467) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_82">
      <g>
       <use xlink:href="#m52006a6518" x="341.27986" y="114.99101" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_54">
      <!-- 0.8 -->
      <g transform="translate(348.27986 118.410307) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-38" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_23">
     <g id="line2d_83">
      <g>
       <use xlink:href="#m52006a6518" x="341.27986" y="71.82685" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_55">
      <!-- 1.0 -->
      <g transform="translate(348.27986 75.246147) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_56">
     <!-- Neural Activation -->
     <g transform="translate(369.234391 125.728109) rotate(-270) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_18">
    <path d="M 330.48882 287.64765 
L 335.88434 287.64765 
L 341.27986 287.64765 
L 341.27986 71.82685 
L 335.88434 71.82685 
L 330.48882 71.82685 
L 330.48882 287.64765 
z
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p00c32cfd7e">
   <rect x="42.113906" y="44.84925" width="269.776" height="269.776"/>
  </clipPath>
  <clipPath id="pb93d0f7124">
   <rect x="446.519015" y="44.84925" width="371.978273" height="269.776"/>
  </clipPath>
 </defs>
</svg>
