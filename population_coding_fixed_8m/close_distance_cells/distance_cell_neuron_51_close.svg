<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="824.000413pt" height="352.267438pt" viewBox="0 0 824.000413 352.267438" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:56:25.053230</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 352.267438 
L 824.000413 352.267438 
L 824.000413 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
L 311.889906 44.84925 
L 42.113906 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g clip-path="url(#pc50dd6261f)">
    <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAjMAAAIzCAYAAAD1UpjCAAB410lEQVR4nO29bXOjTJKGe7vBQra7ZyZ2f/D5vedE7MTOttuWZHCfD5CQJFkvIMlW2fc1oQHxLqSnuZyZVXUH/D9/QQghhBBSKD8++wIIIYQQQs6BMkMIIYSQoqHMEEIIIaRoKDOEEEIIKRrKDCGEEEKKhjJDCCGEkKKhzBBCCCGkaCgzhBBCCCkaygwhhBBCioYyQwghhJCiocwQQgghpGgoM4QQQggpGsoMIYQQQoqGMkMIIYSQoqHMEEIIIaRoKDOEEEIIKRrKDCGEEEKKhjJDCCGEkKKhzBBCCCGkaCgzhBBCCCkaygwhhBBCioYyQwghhJCiocwQQgghpGgoM4QQQggpGsoMIYQQQoqGMkMIIYSQoqHMEEIIIaRoKDOEEEIIKRrKDCGEEEKKhjJDCCGEkKKhzBBCCCGkaCgzhBBCCCkaygwhhBBCioYyQwghhJCiocwQQgghpGgoM4QQQggpGsoMIYQQQoqGMkMIIYSQoqHMEEIIIaRoKDOEEEIIKRrKDCGEEEKKhjJDCCGEkKKhzBBCCCGkaCgzhBBCCCkaygwhhBBCioYyQwghhJCiocwQQgghpGgoM4QQQggpGsoMIYQQQoqGMkMIIYSQoqHMEEIIIaRoKDOEEEIIKRrKDCGEEEKKhjJDCCGEkKKhzBBCCCGkaCgzhBBCCCkaygwhhBBCioYyQwghhJCiocwQQgghpGgoM4QQQggpGsoMIYQQQoqGMkMIIYSQoqHMEEIIIaRoKDOEEEIIKRrKDCGEEEKKhjJDCCGEkKKhzBBCCCGkaCgzhBBCCCkaygwhhBBCioYyQwghhJCiocwQQgghpGgoM4QQQggpGsoMIYQQQoqGMkMIIYSQoqHMEEIIIaRoKDOEEEIIKRrKDCGEEEKKhjJDCCGEkKKhzBBCCCGkaCgzhBBCCCkaygwhhBBCioYyQwghhJCiocwQQgghpGgoM4QQQggpGsoMIYQQQoqGMkMIIYSQoqHMEEIIIaRoKDOEEEIIKRrKDCGEEEKKhjJDCCGEkKKhzBBCCCGkaCgzhBBCCCkaygwhhBBCioYyQwghhJCiocwQQgghpGgoM4QQQggpGsoMIYQQQoqGMkMIIYSQoqHMEEIIIaRoKDOEEEIIKRrKDCGEEEKKhjJDCCGEkKKhzBBCCCGkaCgzhBBCCCkaygwhhBBCioYyQwghhJCiocwQQgghpGgoM4QQQggpGsoMIYQQQoqGMkMIIYSQoqHMEEIIIaRoKDOEEEIIKRrKDCGEEEKKhjJDCCGEkKKhzBBCCCGkaCgzhBBCCCkaygwhhBBCioYyQwghhJCiocwQQgghpGgoM4QQQggpGsoMIYQQQoqGMkMIIYSQoqHMEEIIIaRoKDOEEEIIKRrKDCGEEEKKhjJDCCGEkKKhzBBCCCGkaCgzhBBCCCkaygwhhBBCioYyQwghhJCiocwQQgghpGgoM4QQQggpmvqzL4CQ2+d+5fZvV7kKQgghPozMEBJlrcjIPlv2I4QQsgVGZghxuYSMyDEYqSGEkGvCyAwhCy4dVWGkhhBCrgkjM4TMuKZ06GMzWkMIIZeCMkPIyEdGT+y5KDeEELIVygwhAD4/DcSoDSGEbIUyQ8ini4zFux4KDiGEhKDMkG/OJUTG/mfUXuCYlo8ULooTIaQsKDPkG7NFEHL+k9HbXENsrg1TXoSQsqDMkG/KWpHZ+p+K7Fei1ADsK4cQUgKUGUKSXOI/k68SraHUEEJuD3aaR74ha6Iy1/D9Wr1K49aKpQkhpMx/TUlx3MpAjR+VWrrEOW45esMoDSHktqDMkCuy9a/4a3Qod4sic0vn3yJP96DQEEJugc/+F5t8Sa4xtpGw9uF5rRZLa45fwgN/a00PhYYQ8vlQZsgF+Yh6imufI/c/iTXXUZrwrBUbpp0IIZ8LZYZcgK9SFJrzn8O1PuuW436EPJTetJwQ8h2gzJAz+CoSA3yuyGzlIwurc6SGKSdCyOdAmSEbubUH+7X5Cp839RlyRKQGhYYQcmtQZsgGvsKDXZP6zyDn817zP6WPSvHk1r5QaAghtwVlhqyEIrP+GOeSe/xLSU+O1KSEhhBCPg7KDFnBVxGZS7VYurX/fC4tPSmpiQkNWzgRQj6OW/vXmNws54rMJX5qayMB55wz9nm3HPejRDC37sUjJSbe8VOFwUw5EUKuD2WGZHDOg/iSP7GP+LmeE425hchV6BrWSs5aOUlFaSg0hJDrQZkhCbY+oEv8aX2kyFzr/uREV4RUTUzoeKEUEoWGEPI5lPjEIR8GRWYi9JkuXSB8bud53rlyBScUcQkdxxMUCg0h5OMp8alDPoTvIDK5n3GLyFxjWITcY4TqWoStRbueqFBoCCGfT0lPHvJhXHtwxnP5qNqUtWmlS/QivPU+anlIDcyZqo2JSc0lhIYQQi4LZYYYbkVkPruY9pIic+mWUanjXEps1tTGrBEaRmcIIZeFMkPO5BI/oc8WF82WTvTWpqHOqb+JIYJwrtjkSI23LSM0hJDPgTJDFB/VIueW5EVzKZFZIzFb01UakQVPVkL1MrE6m5QM2W23CA071SOEXA7KDBm4pshcWl7O+dl6UYIttS7niknqeGvu2bkRkTZwvjfnulLb2GNZqaLUEEIuD2WG4PZE5po/y7XH3hqN2bKNXX+u0FwDLS+euFhpsbISq6PR2xFCSD6UmW/PtURmzXFv8WeYmyrKkZ2YxKQE5rPuTU5URqPFJiQ1qSiN3Y4QQvK4xacI+RCuWbdyzZGmz+mu/9xzrJGU0HE8kbkPrE9dj7ddrJ7FY420hKIyqeWe0MSuj0JDCFkHZeZbcs0O8S7Vj8raa0xtn/NwXNOEeq3IhKIx94n13rH1Ntd46HuCc4682G1TraLgbEcIIWEoM9+OzxKZLS2FLkmqWfLa+hRNTGRC67xtPLFJne8zmz6HRCUkL7WzDGYdIYSshzLzrbhFkTm3ZmfLA9CKzaUiMlujMaH3uddgtz1HCryWTal75EVydJQmV2gsjM4QQvKgzHwbrikyW/a/VF1NbJuch/o50aRrisyatFNMYC4tNyI2uQITk5ecGhoKDSEkDWXmW/CRKRzNpXrG3Uqs1UzOfpZQ+igkG57EeOtrxKMz5/RBEyPVR8yaY8QEJvQeoNAQQi4BZeZL8xHRmHM6iltzrnNaMW2Vmi2s/TwxkYmJTah/F2D5OUPrHjC/fzkdCrZquRaUnEiMhy0g1sfWsB8aQkgYysyX45y/2i/RGV5u0+JzmiCH9rnmg+6c1lWhKIs3f4+01MT6pvHqW3JFJ3e9ja6ExAbwpSZWLAwsj6+h1BBCllBmvgwfVRNzTrFs6HxbioC3POhy6kfW1umk+ojx9rOiY0UmJTF6GyE2FlNM+LbIjBYQLR6pFFKInCbdFqaeCCETlJkvwbktgi5xnrX9sORukzrf2gfdRzcBjkWwPKnxhCUUrbGthELEUksPWN4Pu96momozL8LkzYdI1dJoYvJKoSGEUGYK56MkZu251jYtPue6QlLzmQ+6NbVBIiU25XQPX2zs9ql75xX1ro3GePU5VlpshGYNOa2fPlpCCSElQZkpljVycU2JWSMq5/Sf4nHrrWC8z2MjMKF1nrzoZXdnXIP3e2gj6+06ua9aPlKyEaud0awVms/+jgkhtwBlpkhyH/rXjsTk1ojY412qqfHWh10snbH2/Dn7erUysvzemYbqaNZITAzvGLHPYmVG5MXe23ss5cZGa3RhsBzbKxim0BBC8qHMFMe1ROYSkZ7QAzFneey4HrpYFIg36z2XNUW+57Qm844VEZlz/uuN3iIrO/bzW1nRkZoUVmJC81aSKDSEkDCUmS9H7ld66dZP54jMlp+hfaDZYtjQuo8i1CIpFI2xUZmIyNSY37Itt8+TmTaxHhiuIxSB0VGVV2df22zcSgww/+5iLaMoNISQCcpMMaytXdlyjK3Hv5TI5Fxf7IG2RmhutaA0U2Ss0Oj1uawRmtYuC0lN6gSe5Gh0k+wtTb0pNIR8RygzN8+5EnPNQuHcYt9LiYzdLtac95LoiIJumuwt153G3TvLY0MGeMud+hZPZmLzubcnR2T0a7w+2xRbROwFfdNuexFbhEYu4N4sZ8d6hBDKzI2zVWRi+537ledIxzkiY2UldZ7YX++3kG7y8AZpzGh1ZKMytdnMExrtATnkCs0BAamRk8oK22eN8Iq4adlC4VjHeqFj3Mr3TQi5NpSZm2WLyIT22fo1b4nqXEpkUucPtX7Ry4D4w85GE2Jsic6cg7O/JzMhofEEJ0ZKYmReXjXmQjNuJ1GaV8zlRZbpC1pjWDF5iRkbhYaQ7wBl5ia5lMhsidysIfbzOVdkUqSaXIeE5hoPt5C8hFJNFnsf5X2kVsaTGU9k1v4XnpIaLTCe0LRy3bpnYSsbqTSTJmcUbuFW66AIIdeGMlMcG9ITwe3OOa+Hd441IrM2jKBrU67ZNFsIRWfstZ3zn1XgXlhBiQnNGpnxAiUxoZHbe4AvNDKPu2Gljs5oYkLjpQbPERpGZwj56lBmbo419S45/bSsidicQ6qp9aVERraNCQ3MutA+a89rCaWb9Ps1KafAtYQEZu+s97Zfcap4Cyb4EiPb6PkDsBSanLbftp4oR2AYoSHku0OZKYa1InNticmpz0lFajyRyS0AjgnNR0Vn1qyzEZsVrZpCqaSYwMRkxstq5dbMeBLjLQMCQgP4BdCy3LsnoZqZWPrQfihGZwj5ylBmboqtBbyXiticS6pOZo3IeO81KWnx/oq/huR4aaVYqin0ILc4vf1ufelj2GNaciIztZl6ErNX86PQyEu+V5tq0idaO+wBkBYWCg0hXxXKzM2TEpGYDOREay5FSmRS17G1ANhKSyrdFFuWi9eT7ZpIA9T292bq1MqsicKsEZpUqimnmXZoe5Gd0TWkKNiihcb7DbWJ+dzUoreeEPJVoMzcDFsKeXMEIidKcyli54p9vpT8eMTSNOeQ07eNJRTyCEWnrLgkhMYeeovY2MvwltnsjV5uM2TAFIGxU8usKFhad8WM6ZxhD4A8oQEoNYR8HSgzN8GlRGaL3FyDWERoTYFzzjm2So3tWVZYc6ycz3mvtrPz+hieACEsJd7pve1TMhOSmtC8lZZYikn2nfmELPCiNN73AbXM1lPVzvyaJviM0hDyVaDMfDq5D89UDUoqyrGl1VCKUOdlmtw0i0fq3oS6utfnOKdGJlaPEyIkjrHvSn83NcZ6mZDAxCIzse1SU69bGDsfisbYOpkayyjNWD/zgHkHeiIU3gXYCI1eb+tn7AdBZJlAoSHkK0CZuUlif/HniMyaqM0WUs2aU610QiGF1P7X+Ll6EmQ/X6rvmFjhsjWMWIop8LlzIzMh0UHGMj2NSY1eH0o3WRYRGj30wSuAR/TjOIXyXaHvJ1Q/U0eWeVBoCCkdysynkiMVqXqSkMjEJOYcmfGKWnP6Uskt+s1tiZUagHAr9lixa4hhozIhwZR1sj7Q629oWc57O/XmNTGpCUVjrNhogdGCM9bPeGkh74R2G01Oce+1W7MRQm4BysynkRN9yI1w2IejPdaKAtPVxEQmtxYolHKKRaguJS+amCB512fxQiUhqdGRGOd7SUmMPd1awYkJjXUKWaZdIJVmshEaNzpTI96hnr0wb/gKu48WXU9cQssZnSGkZCgzxZATgcltJZMbmbH/8Id6Z91CbspJb2vXXbJFk5ayVC2Ohycpel0s9ST7mb5lZHVIbHLm7Su2j3d51iti0RgrMV50ZiZFtv8ZYLr3OdG+VK+/XroptC0hpGQoM5/C2qhM6GtaKzJrIjNri29ziJ13bYssLyJzCbnxpCan0NliBcYus/P1ctOYxNjLyBUab7neP/ZRc9NMVmBkvV72LCt0c22o+dzflJaVkLx4eN8rozOElApl5ubxHu6pJ9q9s20oFZU6p8eWGhWv+XOO3NjtQnU/OdcRe/B5KY3QupxrFuR+6/vupZjMLimJCclLaH2O5AD+R/W6c8npY8ZGZuz7gz6pTjfp6EwK73vcKjiEkFKhzHw4a4t+7TItJ6H50F//W9JNXpQiFblIHcs7vyddqf1s6sDDKyp9UPt670PXmIOVF2/6iOkBbtJS++FVm6ldFnvBeQ9neWgb7yMJutm1FhnvvFZgvNeYbtIny4nObEkb5aSbvN8WIeTWoczcNKGvJ6eotHbWefOxYwspkbk323qkIkyxSJJ+7xUBx/CaWW95n0NKZGQ+IDExcfEEx4pDjthskRn7EUMCY88VWhc8cG40BvB/c6nojN5Gn5cFwYSUDmXmZoh9FaH0kn2wp0TGLss5r1cbEwvf50jA2ijM1gJmYIq6PGB+/aHozKVl5tFZHpAYKzO50uItg7MsV2hyvrqUsORIjesREpUJ1UTFLiw3nUShIeSrQZn5UNYU/uZGTqzU2AentywkBbH0lq0hCdWb2D5BvH4/9LV6y2wUxkpZ7Hr1cUJtjEOCUzvbrC1uvscUedH3+wGLPmRsFCYnlZSTZvIiIGu2D/30dAFwbeZzxCa0ftaRXo5JAfkFvl50BsgXFQoNISVAmbl5cqIysl2uyJjowOJnECpklXW6xY+NYKT+6o2JmycxsZofwWnSvDjeX3OdVnBEOCQq4F27h70WHYl5GOYdidkqM4isyxWVHPkJfdSQvOQIjF3m3lrZSIuNJxP2N5pT7Bv7PnO+a0LIrUKZ+XTO+Qq0mHiCExOZ0PaCrUnQDwotMFpucruOD50rFInRy0PikkL2CwmOjsTIfG7KQtD3ORCJWSszUPNrhGGLDOUg29qoTM6xQ9uMP5UaaXFZMySBJzf6hIzOEPJVoMx8GDl1HjlFsJ6EaEIC44mMPY8+F9A/jP9i+VBIvQfCDxDvnLF0kswnJMZGHzS2V/wRKzj2Om1LJ0tIHu/ni0LFuymZkf09GdDrYpKDjG298ZRChERmi1S53KMfr0l+P6Fef1MRli2ikpJwCg0htwpl5lNZc/tjD86UwOhtH9R7ew2eMIQe+Kn6Ge+BEhMYu0wLTaBn3NjU4gnNYpkeANETME0oFWdGvM5tah2TGZh5u8yTidi6HLnwltmamZDM5E71a9ZEW6eYQrVXnrFuic5YYusIIbcKZeZmCdWH5O6bEpmMaIeLPPBjEZvQNWm8CE1GNGbtwzNHYlpvOys2msR9y4m8pMTGi6x470OSEFu2Olqi9g2JTM7xY69ZqskjJ9WkjxGS6Zx0E4WGkNKgzHwaoVsf+0r0EyMWlUFgu0yRyRECN2KjN7Lv7Vg7+jrtMRISk/sAt9ecIzRBsclAy4mVlTUik0r7rBEbvY33svU5KXKExDv/KvRv1ZOKGnkRGLtu7TV4Heox1UTILUKZuRlidTBeeiaGPAi895GIh0xTMpMlNzLVRba1s32E1F/+9rq9qb3OUKTGkxq7LiQJen4feIXEJiQ1sedvqHFYaD639+AUuT4Qi3jF5HGxIiYOIdERzo3OEEJKgjLzIeQIiMfar8dKjC5GlXV3y11CIhOLbqSiHCNWciKXbq/JE5gcibG3bW10xptPnVOmIZlZIzI6MhO73x5e1CYUjbFyY7HnSUmPG9Vyll0sgyMRmpw0Z0hccvchhNwylJlP4dK33UqMPoessykohP869x5wuUKQWuY9bL1L9q5jS1Qmds32WlMRqNC5YzLz01nmCUWoXiZ0XTllI3o+9vLSWjaQps+fe39z03pn4UVUUq3uUvutPR8h5LOhzNwEVkRCLWW8Zd62oX5bVFTGa/4bSz3Yh05KDkLzoY9h53MiRpeWmdg0dB32fUhg1gqNnDtUy7NGBLwozOz119nJRPCs3IT+5UhFu/TU7gPvOi6NF22JSQ6jM4SUAGXm6mxNMW05rtd3jBOVSf2lHko9eP+mb5GcWHRGX583H1pm13ucG13KiWRZkQmJjRUMLzLjyUxIbmKf1UZhZtO/QN0CdQe0ldlJjnU3PdNToqjf56b0Znh9y+Swpg8kuw8hpHQoMzeF93XEIi+pffUT9265eIvQAOE/VHNEJnSpenmOvOROY9exRm70dcReWl5CYuNJRQ1g/wbUHe73J3Rthfe26gWjrYHD3VxiDonrr8188NWLzI+6wzsc2lpFblQfOnoaugaZxiIzwaDHNSXj3OgMJYiQW4Myc1VypOMSkZtQh3rOOWMPtlAqIodcaXAuyV1+qWmuwKTWyTFj90nLjCc1C5npoyI/9idUdYeqbtEM8wBwOuzQthVOhwbv+wo4NH2URPp6CYmCffYGBervKDJyTmEhNm093YMYOYIYTJPl/HA8RC4uGZ1JCQuFhpBbgjJzs4SiNHZ5bea91kyJqIx9OKdauaTI+svbuXy7LHcaW5d6PuamzlIiI8utuCymb6NA7PZH1INI7JoTKrSo0aFCLxbHpx061DjtTzgeduj2J7wddkDdzCM1wFIWasyve/H6C+yP43UAQFW36NoaXVv1kZq2wph+quVgJjrj3bNQCiznXi8WrBm5PIdYyydvGSGkBCgzV+NatTKhc9h556tNiUxo3jtOiC1/XG8Rmtx1udcUu055jsekT8uMKzJ/8ePnC3b74xh9qaoODY6oBoGp0KLBCRU6dKiwww4nNGiaI3ZNg9Nxh2Pd4lR3eK93fbSkNeknG7EBnO99iAgNIlWbqIzlHZjqaWKCa88bSjW5kRld/LtGKKyA5LRksvvmRGcoOoTcMpSZL00itFKbzUIvva3dNwf7YE2RkpPQfM7xbT1Jzn6ewISKdxMi8/jzFVXVzaIwOxzHeZGa/rTd8L8Km5HPqCM17R2AGu9tLzHtICrdkEoa63WAqWYHmFJc9mUFxVumryVJjfk4X3ZnLe52u3PTTWukhakmQm4Fysx3IiYpKbHR28Tmt15Xatk5MuWhpSaHnObUbo3MXGR21VJcvPcAUKFChXaa1hXqukNXd3ivu0ky7OeKRZ5mclPhHZkSo0XFykpIakIvfR3ZeGIjyzxRyRWaVLqJwkJICVBmisL+FXomKZGxfdHEpvaYqXOuWbf1I4dqX7wi2ZRQaWEJzS+EZikykkYKSYzUywBAh26MztTocFr72b100ygTd+MHfDvsphoZkZj2bnkcT2AOzrJUdCYqMhKVsaOwi1DEojJygq1CkxIXT3woO4TcApSZq3CJeplQ6ED+0QUuIjepFFMoiuPNw2yXWhZbnrPN2tqXNeex5/T6iPGWSVQmIDKPeAEAN6VUqdcJu1lUBtj16alEfUuURT3LJDRTRKZe1uEA+RITi8R4chlFi4L85lNRmRhrBGZNuokQ8tlQZr4MmWLjSYq3PCY0IZnJTUNdIgLjpStsxMV7HnnRGe9a7TQViZlFZd7wY39yRWY3xFfm6aQpIiOCU6EaozJ6ObBDVbd4wy59f0JRkNl9mYRmlBi7f6gexpOanKhMMjpjB5G0XyqwjNqkoi1ahrwUU0xuGH0h5NahzHwXvAiKXh4SljWCg8jUzseWhQj9ZW8flqk/qj2RyZl6IvPTLutF5vHnC3b700JkpPUSAGc6RWk6k34apWaIzPyoO7zXLdA60YhUQGGxPpFSku21rHgSExKZpLx4xNJN3rz+cLHoi7eM0RlCSocy86Hc0O32ojN2uTy8P0NqUikk76/92pkPERKa0HxSYgD8POB+f0KzP44i84hXNDiOIrMbamaASV76U8zFpsFxbMUky6Rupq4j9TP2Hq15FsdkxovGpGpkUnKUxNa+6GU5aSZvXSgiA7PtmkgMIzeEfDY39HT9Kly6fxkdIt+CGTAwR2BSr5jg2HN4goPAe0usxiL0kNRCY/f1hMmLRtn3VmasyPzsO6ATkXl4WgpMgyMeBrERdLFv6P0UpRlSTJWpman/YizWjd2rHELpJG95bnopJDCrhQbwU0lAOM3krQP8iEyukDBaQ8gtQpn5TpwjMakO47zj2WWh6/CW2RSSXbblr369rlbLUtcdSjEpkdn/fEGzP2HXWIF5QYPTTGw0VeSC+9qZKSqjZafyxlIKRbPqxDayPCcqY6exZfY7WR2VkZ28qIqVFpjtQ0Jjjw2zzjv+lmgNIeQjocwUg34qbYzWeH8dr/0jMxWByUlFwZvXPcACs6bB9vpjD9rQOkvsc68SvBb3+9M4LIEISIUWO5xQq9oX3Xophe0oT/Ye14+tj8x9ismdJzb6u8mJruREYYDluZOoQuTxAJ7QrCFUFK8jPfo9IaRUKDPF4/2j/wbgYVj2F7NUUyjkn3oA2qlmTYQHmMSlHk6kmhuP/Z2M51dNhsdld8uHa43lA1YkZm/e289mI0cxrJTVU2FuVXWjvNRYNqGWNTl0qHBCoxpr9/t1XYV2HFG7nj5HaKrnvWiUXb5GaDyB0VNBn6c2y2ZooQnVyWyNkujIi8BWS4R8FSgzX5Z2+dZKSkhg9HpPZDyh0bgRjL8LefmhRaBePNnGnmn7+Ulwxs7dDvdzidHzh+G8WmC8qERr1qdETB9nkLKqbmdRGY8OtSs4/brlcAWtieeM82013RfbH0wsUuJ9bitmuVGu0HY5eL+f2b536KXCROpccuTDRnXs9t6FMLVESGlQZi7KRwwuKeQ0V5WnjLqu2MMn9AALCUwoSiPT8TWIzDBaNDAJTD17r3q/bStgqC/RIqPHEXobR3S+nx6sUFMrNDYysSoNAldofjgDNcakRrACs3w/xXjaYV2HWqWXZIppGkv5aDw5tceIFQDndJJnsWUp3rXMkChNjlicIx050ZmcHwvFh5DPhDJzU6RCHql9EzIVir7kvGoz77FIwQDYDyM0y0jRQwRmrDEZ0jPA1JFciwr62d41KkLTicxU/QjSh6YvhK1r4ODU2WihsSmONUJjJQ0AhogMgFmKSfDqY2IS0+qamFlUZjiy+ux9T73wxSUkFzbwoD9XaN9UpGZtVCaGKzR2xy0ppy2S4f04KCyE3CqUmZtECnzliavfA/MQgycwLeZ1M2bV2geSJzEhqVmkZ/qIjI7EaImxYxQBmPVt2z/spxZAXdU/0I9Vg2qIihzrDm+HHYAGwN0kMIKIjK3d0PNZtT72c6qoEuYtj1J4EqPraWYSo1JM7WwwSEzTmNRYPKnxjhOLzITOZ+eFkEB5ZEnNtcgxsy1hPULINaHMFE+sR1R5X2PR34xHLCpjt/HSS4uH/V+g7nC/P6GqWzQSnXEkxh9o0a8lAfo+WI7VbiYt/d/MSmhCYnLOc8h8zkpHZ1RrpViaST6XlZhufG/qZERspF6mrbAYCFLPr5FU+z5HZkLRGu+YNrWnt4n96+Net/yGc2tqYsQiLFv6nyGEfCaUmYvxkfUyMULRGuQ9rFKv1C/GRGV+DBEZEZlddVxIjB1Jur/UpcgA04N/rCipOlRPfaTnWLdDecwgNN7n3yo2C1n7O6uXketeppqm+X7EpfnnC4mMlph2SDG1baVqZrCUiHPSQLnHWSMyHrn/4ui0psuaQmGNHND+N8JICyElQ5kpEissEYH5DFRURlJBWmT6Lv3bwHzf3603jKJOOS2KbJthm7aeR2hCqaTotUe21+mzgT7StOxDJtQ8e5qfREbLTDs0y7ZRGQBTk+xYSilXZmyKKbRvrtTE8CI0iCzT+0VlSaRGbygRydB4S1Dbect16MibJ4TcGpSZYrEd53npJqmzUbKz9mFVZ2ynMb+osdnyUCDriYxEZnY4JiMzUZqp5dObtHKSPmZC9S9rJWf22eYd5dmI07idSaNNYy61o9Do5XpgSdlOKol+SM+/NrLkRZpCz9+QxMg09xVji7DYaYig1OgvKdaxpI3OMI1ESOlQZi5CTlTkGrfa1sjoJ5ter+pmtKiksP20aLTk6KbPowD1aZ6+TxRVwIvKLZbVMtChcqMamh1Oaus+gVOhHQuN3+oOqIfP7AmM/gzeZ4u99n/xQwaUbE5oZoNITtKyfN/L2qyptYrIABi1SIY/eMEjdtihaU44Nju87h9x3B/7VlyHXd/Xjm4ubV+2TiU2tUGIc16pc4TWWaGJSY4rNbH/FnXPvymBYa0MISVBmSmG0F+aVmBkmYrG2PfS2sdGYXIRabHLFg811ZS4WR5GF8vaSEYKPTyARDJqTGmtH3WH97rtozOx1FEqBWWXDUMY2KhMhQ4NjuO8iMty/rQQGFsQDGDUox1OeMEDTjjNpOb0s8HL80Oe1FiJyInG6M+/VWRypcaKil1m1wdTT3ZIBK8PmTYwT3EhpGQoM18G/a+67otDhEZFZ2RzLTGhLv8tVmRshGb28JtqPbquQltVC1GxxbI5ItOfdhKDHU5jbKaqpqbf73U3lxlgKTH2r/tkrQyAIfpT1Z2KyhzHmh+dOhPB0VEazbxvmemEIi/99IjTMFylRGpO1Qm7fx5x+tngdNjh5flxKTXP8CUhJB656aSU4ADLY8eWxSRGny/EQmgEr+ffnKgMIaQ0KDNnc+3CW/nXfQ26UzE9L8e6m+TDSkwqtWRFRqeY7EOovcP70DeKV9Ar2B5VUtRDcsmLzvSppgpV3aKrK7zXw9hUntDozxGVF/2aWjHtqqOKylh5OS5qgURoLN54TUMcZiY1x5neDHJTPeL4tMNuf5pHap7NUA+XfoWEBs68t8wTqZDEeJEiy2yZF525txuBYkPI14EyUxzph/30L75EaHR0BpgVA3sSEyvU1PN6+5y/6CPYptneevteD+o4k5qql41Op5rkOr30kv2MUanpIzK7fT8ito7KNGoq0RhPbnLQcRktNR1e8YIHPOAVr3iY0lBDpOZ1/4jjYYdD/QgcGuDZjN90Sbmx3zsS86H1qWMCvthYxnWSbvI6m7TLBG8ZIaQUKDNfEv2PNzD99an/sY4ITSg6o0XAK/qtsazFGTp669r5EAX9rn7LH4u3rMFxSDEdhyiN6S93Vgh8v5QY+7lCEmPXS4ppbI4tUZl2JjTVKDrLZR72Mx6xwwNehyjMXJUaHHEcpi94nBUK102Hqn5EXXd4ee7wXg+pJ08W7KCROSyib4H3iCyLyYwXlcmV48U2uhdtIN2ztv2QhJBSoMycRW6K6bNus47OeNfhCE0sFeNtY+tkbHFxgFikJbWtLFt2+K+kRo3M/V7/Beo7X1Ziy5z1P+qu7wAQ7aAZx1FoRDemJNCUXtLLpsOFo1CPqMY9bLqpm63rIzN99GfXf/aqw/Fph6ru8PL8OA3IaUcZB/zvbE1ExqvJWTsfk5o1zBxED4HAVBIhXx3KzKeytt4m1KIp9o917Cs2BcHeoIz2MFpgbL2M+0BURcCooZtpA5PEyLwvLvMn27xmphplokM9LRtSTScA8Fo1ebdCz7tRmb9jcXGN6bVTqSYtLf30tIjQeJ9Li83UceC8DPgB1VgILMsk2tPghBc8Krl7BJq+L5zTYYfjoVmOMq6/w60pJiAuKqn1575sDZc+3yL6khOV8WBrJ0JuHcrMZq5d+Bvr9EuQf4hj28X+sX4d9jURGhET70HhTbUE2ajM2N+M3zwbgIqsTKNna+yDXqYLgYEtBB5aNc0P5r+QMV+3Y4ppN4jKbtALkZqQyDziZVxuP8f02drx87ZKZh7wMh6pG1NPy8iMHQihQotXM9xD19ZD6q2ZRhkH8mRB7oUnKXZ6TlRmbTcB3rWNWIHxIjWM3hBSOpSZq7P1Fm+RpdzYvHdsJTTAPDpjozHeNJIqGEd5xrJvFbusGnqM0UIjzZclIqP3m49pVC/mF4RSGPahKFP16jvKOw2RkONY/PuIF/zEbzzidZCWfpsHvKAZRWaaamkB5hEpLTe6XkanlnqpqfGCh+GMj+P8b/zCC17xgBc84hEvQ8HwqWn6/mm6ZojUnPqRxg+D1IQKg71oGxLTLSKjz29/T/batOzYwvXsVFMoKsMoDCElQpkpFm84g9wIzUtgWzPkgW62nSMuwdcwSGLXFwFLuqmdycg8BaVlRB7+rREZryfd2TFlYMbW+ZnblIkQEBns+xTTrukjLg+jPryO01/4PRMXUQwRm0e8zjrQ08XPdlmHehaBkfGabCLrFQ+DwPRnfMDLTGrkWkSKXqpHnJ52OD41OB37/mlGqWnvlt+dN/7SuUITkiZPWuT70aN12+4EvO9s/LlrkaGoEPJVocx8Gp5MbP06tMiEojO549CoOhqvxZJuuaTlJlZECowtmtqq73PGDqwoy3TkRaIUNsrSP+yX5b/eKNPvoVGmQx9dpotXi2Z/VFGZXhJEYH7h9zgv4hKaD43lVHcdqrZD1b6jq3/g2OyU1MyrbzpUoy79xC88D0LziBf8xi88DFNp+dT3ItzgES94wSOO2OG1ecSuOeH1zwOOhxM6uV8igIe7bZEZONuG3ociMvr35h3bpjb173KRbtJjlOn38mXrAvnYD4QQcqtQZq7KR9xeHVrQUnPOX6BGaELykiMyLWaRkfl4RFM0RkvMtO1ymaSh3EjMIioznFciDkJKaJzXj/0Ju72OyryMKSZ5TTLzOhObXyoF9av7PQpL1QJ1B9wdAHTqfnUAqnc81QegAf7ugWMDnPb3OFbNTG5+49dw7D6dJNfSi8xpLAwWidF1NrKuemqx25/GSFbbVn3ne/tBbHTTbu8+poQmFpnRERn72/Km+vghkZkJje4RGGB/MoR8TSgzm7h28W8KmxsBltGZRdOOjGM+qPdKaDx58eaDD6+7KeXTeNGYKe1UBQRGsBKju86bpZ3GiEy1fMiuuS0qxdT3LdOOkvA41Kb8VFGZaf51jNaI0Pz6zwH3fwAcMUnLwcyL1AB9wfQeuGuA/R7YN29A8zaTm9+VnOXXKDA78xKR0a2jdjjiFY9jVOhUndBVFdqmwqlr0Axyczw06PanPmJzGCq4W1M47N3bnMiMlRhvagUGmCRG5kNF3Yvv2NbOsPCXkK8CZeZTuJQMeXUytsnp2r9E9ZPkAcDdepFx/oJ/V2M0Sd2MF1HxCK3zCoC7IcUEYF4v4z10vY+eTDGdRhl4HFNIc6npkz5KMY6/8fSfd+A/6F9aXOSBfjRToH9gNxilxpObh//6Nx7+OUWK5Lp2qki5r6V5nUmNHs1btpEi42YQm2PTYLc/4XTY9TVPg+AI71oYhbaeZCcVmdFRFS0vNp2ppUaISUzwi6W4EPJVocx8CeRJICJzrtAIr1gITa7IOFIjYzRJVAZmvpeRvO7+x9oYVS8jx5NUyaxeJjYFlv8lqMJfDE28p35kjqPATCmmXmD+hf9VIvOMf/3nGff/h0lk/geTxGiBse9ruCJjl93/Af7r/w54/K8DHp9e8b94Vc3CdYRGWl+dho7+2nG9NO2Wvm16qekjOF1V4fg01O4c+29KC02rW6oN4tjN7n01CY4XmamxTBXZqEwK2T4rOqO/XMoNIV8FyszV+OhbmxIay5vaR09tl+8A8Lh8AGXXy8hrPqyBjapoqZk+UTxaMzXFnsRmXNbWw4NURQn0NIQTlfkxyIwIwAOmPmPGFJKJyPwL/9unlf6NXmL+jV5k/oO5vHhCI/c4ITLYA/gD4L+B/QFo/vsZzT+P2FVTz8RTtOYRvwd5kRiWjBUlUZpWycwOU/GxjNrdNMf+O2nkO1C1UDoaNkxb/Z0D8+Li9m4pL7Y2KxhpMfvsMf+5LvazI2l7EsOWToSUDGXmZtj6VcRSTWt6OQ2hBMdGY3RKQF5BmZn+au+aqUbGFgHHBEaj2wHNUk1dNdXnyOXn1szUzlSiMtVxFvGYIjNThMYVmf/BJDIyFYk5BKYy4kED4AlhkXma73t3BP5xeEP13/+DupGkWy9gvzGN4N0vO876thGZsaNzT+NA9cv7+6wkRr6vQVBRDdGxZvi+1fchcgOgbw6+H4qLvZRRLCojKSqZD6WaZpEZ26JJw6JgQkqHMvPh5NTL6G3WfEW5hcFr/gKVp8N9RFQQrotQdKpupqts53eT1OTiDn1QDQ9o6fnXPtzsfBtYvgdQ/x3GYpIxlqZUjaSaHhdC8xwXmX9jEpA/mAuMlhpgEpZQhOanf1+e8A789/+ibXo5DI0BpdNzQN9Jn9TRyHARJzSo0OE4DJ3Qb79sgTbvA2hqdI4KaKtebkRsAOBYt32LKQDj7zNW/6J/XzoSo+eTtTOEkK8K/5NfTY6MrL2tuduvKRy2zbS9lk4e9i/U++Xq0B+4geLf/tWH+qVuZi32gayrZWTk7N0Q7dntT2PB6ntbA/Vd+AG4x1xgZu+P45AIugal7wTvdTZUwdhp3vGlb7H0B4DUyshUR2ZCEiPLKkwtm57UvK3z+QM3UrM7vOOxecUrHtGhHmtl+vRRH4GR/9nm7QDGaAyAPsWE03BZ7bBdPZPJaiYzHXTLMunXua2m4u+q7sfOOtadP7yCoIuFvWksKpP8zyoWrdE3mVEbQm4dysyHktNRXigqs3Z5DC00CXkZt/mLRZ8dkSiMt1wPawDMWzR5eAIjaIHRItPghK6qh6bFNd73RwBD77ae0ABzgRml5m2Myuya5WCRU6++U18yD3jB45/3SV6eMRX+6ldEYv4egNcjcF8D90f0gtJiKTMyfVLH+DO9vz8CTdfXz/TyIqXAJ5zQjGIjDbVlAMwjmtm8FZr+e5tHe9ohiqO/12qUmW5MJ05N8Qex2Y+7DLHCBsHfmJ4PiQywlBjXRXIkhhBSEpSZb4OtrZF/yEPyovd7mK9aE5nBfJntCTiEN7ikntd9/85EBjUaHNHVFZp9X3zy1lbA/n5Za6Fb0dioTN1htz9itz+N55Cp7dl3nP454E4kRkdm9Ov/EJWY1wPw2vXfyq8n4KEF7p7UNc9v0kxgrNDsDm94fHpVkZkpOtPXw8yjM/J9xITGfh8ynpZ+L/U3/fspMtN/hGqSnQppodG/KV1o7kVkrNDM/nXTC1noS8hXgzLzqZwblVlLaPymWHTmDVPLp0jKKZheUsvbvOJejR2AUU8lOtOhm4sMKuyqI7DHPN20v1tGZgJRmfv9CfXQUV4ziIweNFL64X1QQxXsbXrJppikabaIR7eUmFeo8u0/wD9a4BeAu67ffry3wtNwTp1mGoSmOQK7p15emlk3eqeh0HeK0PTNsREUml4uj+NppQjY1i1JpM1GZmSfaqi50YXDUaGxvyPbgikUoRGYJSLkW0CZuTihW3qNXoNTxwy1dJJ9Q9EZ20uqIhaZia1f8UCpHXGZ5vtmxBIBkKhMhWoSmeGhu0g3tftligJwozJV3XfxLyNjzwcRkObOr9P8n0MvEX8wTy/9UfMiNEfgbRCYt3aSGBGZVwyRGQzbtsA/Oufb1pEZE5XBnz6i0xxPODWnoW3SPDrTqAiNyElIaPp7P9XWeEXFOt2kRzDXYiPbaRmqqy4sNLGC81B05mJFwLHm2rQkQm4Jysyn8dFRGUso7WTPI08Os33oAQNn1zEyU4/NdLtGtXhxiImMjspIxEQKUkeRMemmrq3wvn8D2vt5VEY3C1ZRmWZ/GoYv6EZpalTNjIiMpJv2Wlr+qJdJM738G2jbPhrzMlzGK3yZkXjYrw7AM/AP842N8qKFxqSvdod37JpwdGY3RGj6FFqPFRrpJVjmPZbppklc5PvQUZtF54ihCE175zfdvqrEEEJKg//ZF0NMfrz1Qii1JHjRGW0levlQBBz7gzSWdgruEhYaT2S0YExpJpGY+fu2qtDVFXb7Iw5tBezreTGwTTMNUZmq7saoi66XkZqZKcX0isfuZS4wtlZmeP/3P8C//8yl5RWT0OhlepSs4YMthUZHZgJic3/Mi84s00wYo10Axj5nQt9ZZb7gKfKyjMr0x2sW37dbFKyjafIKyY1msfwO88Lf18X5CSHlQpn5FLZEZULb52BD5V6TbcAXG10zYzZNRWYW293hfeg8bQ1WZKoxsTQV/+r306mHouCmjwjd70/zYmCbZlJRmV11HKVpGlxS5GaeYnp4fltGYxyh+f/+Dfy/WMqLJzYP6s6PWKGpEBUZ3Uw7FZ0RydBCA0grpklopu8kPuyEF5np95vSVO4xbIRGf18iMnsspQZYGZ1hGIeQrwT/S/4Qzq2XSQnO2q9R180A8eiMTTndb4vMGGTAyRBaXKzI9DUz3ThtsOxsb9G6aV/5xcCzv/KnqIx0kqeLfadU02mWYrrXIqPrZlSa6eU/vd/8hi8ynuCgv9v4rT7XfQfcH4C66gecnPVNo19KaOqhdua1kRTdSd3Pbqw1EqaozNSKSaehUkjKSaeePLmR4RPsdyZC07ZV3wfNPtBLcKoFU/Z/Ft6GrIkhpCQoMxdly+1cE5XJFZm18lQ7+2R+ljXddajtxp56VfSjgh4vqBu73LfzIhbSP8ppiDm8qhRUhQ6n4VgveOjrX/7Z4bXu8ALgvd71n7lG35PuzwP2P1/GqIytkdHXWM3O085bGXnNpwOLdNRFq+P9MP2FPkLzgD4a8wvAQ9U32b57wjTUgfQSLC95qFfTybpa6lV0LVI7Sw9ZSZxqXGSZ/5vw6p5sJ3w2QtOntqa7qt+fuganw24aX0uGydDDZXjLkpHCN8wjjamQIiGkFCgzq7hEi6ScW55T9HsJiUkJzArBSbVuclg2t25n8iK1KiI4O8iwAics005TnzNacmT5qxjHUz/UwcvzYy8T9T3w8zCmlx6al1lKSR9XmmjPhKbr4p9/uG339TJw8Ij+sVpjSi1JPGAhMQD+UQH/+AncicB4wx0YiUmh76OWF0nlxSQGSIuMVwCsvzHRxul9M47j9HbY9WM3hUQm9wWgr/daC/ujIaQUKDNXJyYYa1oqpbbdKlopgVlx3FT6Sc5YzzvBk7/864DI6J53J7lpZ1EZrTDHhcx0s4f2S9OX174AeKs73O9PePz5MvT0O08pTRIj13aaRYmqtsurHVKEvmmRGOmm8B9q+g8AD3vgXg8+qUVmZcseSddN38FUhxRuYdZmi80kLPX4XkdpZETuMRozfMNdV+F42OF0aPqoTExkcqVmvLu6ZR5FhZCvBGXmppGvZ63IXDpCE+CM6LykOCTN4UVZrMjMp8exlkPv1yipecFj+AKGxjTHuu1TS83USkmKe3V6ayYwKkVTte/9Z7a1rDL8gOIeU5NrW9qhezSp0UdtHqBSTQ3wKKmlBsuIjE0zKR+J1Vtr2eucqRYd+X9PaHJERkdpTtjNozEiOEN66b1VI2qLuOSKzPjBkfg96rQTIaRkKDMXI+dWhmpivOWhokRNbl1NzvXYupnQfAYryw3mxagn1UndJBh6+QNecBp6pe2lZh6VmdfeTGmhBYPQSGpJji/StHOFph0f/uMx5dCOwAhDycriG5S00j3mUZlZZKbp62RGiQkJja2ViURl9H2f5ntdySWUYgqJjE01daj7aMyw/nTcqfRSExaXVREZISUuTos9ILCMEHJrUGauyiVqbHKbZ289V2y/M34eWekWXQI6ScfUSd3U+64MI6CHE5A4jSc10pLG9n+yoIEZpmASmUYJjUxr+2j2HqD2lMMz32qqFhmYdRKV+UfVp5cWBb9eqmmPeb2MidDYez9dXgvdIFunnKa6mf7zetEZW+yrl1uR0cts0W/XViq9dOdHYzyx8ZYv6mW8kI3322CUhpASoczcFLlikRKZtV9rTnvWy/1UpOnzdOS51MxTSnORecDL0OS6UuJzXEiNRGj0OSzy8LVpLBsJ0gXHtvi4tqNZx7tfSX5zD2r6iKFORsRFF/7+VPM6xaTrZxLYpu82xbQGWw+TFpll0e/x0CzTSymJya6XkamVlZxoDAWHkFuHMvMppGpgvFoZb1/v/ZqvNCetdOZPJPJH8LwGZV4EPBUCH2ciIyNUy7hBohw6UqPrZuaXsmwuLFEX3WLKiowWLNtU/M7+wR/gbriNobvpSc2vp6FOJpRe8gSmGpZVGKM0XY2+d12H2nwHOkqj62b09qEem3NFplP6pIt+u7aain63SIy3fOTNzHsN4wkhpUKZuRprJSMnKhMTma1fZSgqs+J4GSkly7JZdjf2S6slRrcuEpF5GCI10lppatV0HFNM0u+MPZ+mG1IocoxGpZS0yOjWTCI244PfSzEFamfqql+Xqpa6R1/w++BJjJ0P9TETiM7YTgj1cqmX8epn5L2XSgoREhkp/pU001j0K02xQ+mlTU2ygUlcbF2MFRxCSKlQZrK5RP1LDrlfSc71pLbxJMmbl+3u5m/1YVb+kmIPQWH+d/xUtDo1Ke7HDpIozTQO0HQxfS+zfZFp//B8RYcaj3iZ1dWIsExCY+XmOPZzE0VHR/Zq+gT84wi8/kfdVamlkahNPb2/t8JiX3qkbzvq9/ThgRaoWqDuunHgTB1dsq2MpNdf6XlGtomlnjzB0VEwnVrS80c0OB13OB6aqU8ZkZZnM8152cgNgKleRqeYtO3ECoIJIaVAmflUrnX714iXFhZv3k4VH/zrsdEV6ejN9mIL9IkS6R6/j9bUmAam7F8PeBm2DXfOp5fbBMoMnebZox8TQITkCKDtB338r+FS7+tJZiQFNd5POUZIZKReRqIxklKydP2r7oDd4Q3t03GIWk3DF0jdSr+5bl49tUaSkchtesk20Z63VqoXInOUSIzEvo67vvPCww543s8Fxs7HXp7UjD8JGRxCojI6xWSFxkZuYjlEpqYIuSUoMxfhs29j7vlDTbtDIhOp3dnyka/w778nOEIvMs348NYP10f1YJ7ExR9CQTfT1tGZsfdfLSMiMnr+afjsR+CxxbyVUR14ryI6s9TST8TrZQSV9rprgarqr3dXSeRlkhUPKzsyBtZRrQ91sNeaey0RmYuJjJUXbxmA+bAFdhgDKzSUE0JK5rOfwuTirB2IMtTXjMw7/d6c+avp2mrs48V2tha+ynj9i7DDNLpzN8iHPEitxEikYdkp3rxOpnFaNS2afNv+XUQ2RGSklgZmO29fqH33Zl5HZLTYeHUy7XDcY5/C2h3egD3QVctoyyQ3k+TowTqlsbZO8ck9Dn2HU1l3NS+rPu5wPOzCIhMSmkNku4XI/IU/FpOWGB2R0TDFREhpUGY+nI+qvRG8r9iLvMSkJiIyub+gD/jDV6eYbOdsus3UFDl4XZSo2mJfHbUJdsCnRULqZbTIyDQkM5XZX6I6Ovqip1pyvKgMMBUh1/307tBHZ6q2w646qs38gl7d60+jhLAf5XoHOxClTVHpuyXFvlpkDs+Pfcd4MUGJLQvVzYy06FNM8rKpJph5eW+h2BBSApSZq/DRwhIilFayy0JSo7e/Wx56i8jE+tZHXmHwdAnxYlxJC3ULFZm30vGaJy8l5zguk2Obk81TS3uMNSvjS28HjNGphQzpNJPt4dcOYVBjLjUaCUYMx7w/AsA7gBPQzOthgHmxrkRkZJlEqI5o1FbhVJNuei19yojInA5NLzLPd2Fh8aIy3ja2VgZALyCvmKeX9HtPYmyUhmknQkqCMvMhfNRtjjXV9tbZlkt6+b0zHzh07ONFngm5KSaNlokpMrA8iYhO/8A+zQpd7fGswAAYH/K66fasSbZ/cVNkRjcRflpc3Ly3Xptm0oJiJUaKg+0I2aEIjbnU/puchEZHmuZRqykioyVQ9wp8wpL5SFlTVEz38Ns3wb7zIyspufFkZxaREXGRaIz+It6c6ZrIC6M0hNwqlJlvQUxyaiylxut4P7B7jEgDkFDHax6hGpnY8g7V7GEskQZg2QrHRmFsPyzzhEm/fowK1c5LojL286uO7BYRFW+5iIue11MdjbEiI+fu1PohSlPXQNe+96OXDymneWxqHpGpzZoOk1B6UjNP8Zkefg874Pk+r3WSF4XxlgGYmmBrkdHppZDIeAXAtpUTozSE3DqUmSxuJW3kkfoKYxGZUJ2Mfp84vuM7a8mPykx9zdh6DZtyqtDOWjKFzuNFZbxlUgQ8265V59Qi0qqpxkqPja7YQl5PXFKRGI2cv8KUbkKfMOwzXG+o6m5MOc0jM1VwXsudHtPJ1s2M+ww9/C76kolFZHLFBkAvMpJGsiIjy21qSRMqBCaElAJlpijesBQrqfJMEdrG61U4sK0XhdCXYS9Lz49/ANdo2/mDUz8op/fzwQ1s1/iALyciNdJfrRTw9tvPP5ct6PWlRtJOJ3OVnT7QXDL2gXt2wDLqkhOpibV+Ct3vEMNl33XAvgXemndU7WGZCoMnONO89NkTvCf2WG2N2VAFXs++oebWXn3MLCITEhnbLBtYRmVyxmoihNw6lJmbQ8uJyIu3TIuNrPdkxztGDrpm5m46TazreG/5Af1De3xY3eF0aHDan3BqGrzgETucxmnsgagJp5j8h5EUsNply5TSXGz0vrvhtUDLyHTwZYSlQV8cbNNHIZGBM63Me/1xO7OsVdsO/dyMEaNh2f2x7224ag+of3aoqmW/Pfae1gFx8dJUJzSoqg7N/thHZup7P5rnyVjs99bCbKTTSTDLWmeZZm0rJgoPIbcGZeZT8STFLo9tGxOaNfJygZ9B6gGk/qJ+P/StWo7NDjvs8IIH7HDECx5cqdDva7O8NtsLej89L0JToRtH1rbiYs8v0Z0pzTT1APy3HlRPR06Gvl2CqadQ9EULjiX2FWmJ0d+D1O3IeUUqRWpENA/A/R4A3oCfL0A1l7npEnpFeRnH9g5dzlQrs8Oxv891hx91h/f6LxYt4zS5ogxg3pdMrB4m1YIJCMsLxYWQEqDMfDhe9CR3+61CszU6k7Ft7C9q9bAc0ywtgMM9TocGr/tHNE0/3MArHqELa0PyIkgM4AQshGT5KZb9z0gaajqeL0e6dVMwnaKlRQuKCMQBc5kI1b7YeS/qInhpPXl1znytpnpexGYYALPu3lA9/Z5HmSJ4NTXSeqwZ2oDV6FDV/etd3y/LmujMYqU3JIEdngCIR2XawDahZYSQW4EyczY5tzAmETnRmXOExmOtUCUuPZYK0GkmFaGR6MxL8+jWXdiIjMZvedSXAktPvhYtOg94ndXe2PPYa5giM9NgkzO5kpoZ/TmliXaHeTFwh6W4AEuxkW3lPtuIS22WyeuotjmY84m4iFhKfc9h2u8OwCPeAfxG1czvib2vHepxfCt5ryVGN2k/Vg2qusVb3WL22/MCkDGRmfFmXlpsvKhMqLM8RmUIKR3KzKeQKxpWXIC8GhrN2mhMJqHDhoRGHqDj636snXlpHiDNnkNpJmAuJN5gkP3pl4KiH8K6QFgTGi5Bt2bSRcDjx62GUa9FCnS9SyjS4ImLF5WRqEosUqOF5+hM5d7rdJc+j9TwqFKgu1aE5iUaoZkKsaehIvo6mdMkMdiN321dD6Ne1vfTZw4RSzUBWI6GrQXFCg3MOkTmGYEhpEQoMzeBNYNY/UxOUbAXnUlFYzZGamJRGU9qdO3M8yNehvTDqZJ0U6CHXbNsns7oRUYGloSKznhiAugGxbJu/le4lZs+yjD1AjwOMjltNG/F1CKeqjGFvn+Hxj6d+qqbYy8Wo5xo/9Kn1+JjReaIedoplNYyxcN3AJ7ad+CfL2PnetOp7ZAHU+FvXyS9G6Mzr3gY04dVbZqya2LppGCKKVQvg8gyIC8qEyIUraEEEfKZUGZuhpTQAEt5yREae+xYpCZzkMo1URkdmZHohTS3re/wdtjh5fkB1T87Ix5+REaQFjPS7FqYOsebL7c1Mf2y+OCV+hp0M28rPmMRMDDdl2a+XiMjOnQ10NU/hvkKbTVFOiq06OoTqvYdTd2PrTSsHA6CZWBBC80BwB9Mhcix8aAkQuPwhEloBB2RWUZldjgN/6vQLaJZfRGwf675TQq8AEyFv7lCY9NOwPLm6XV2PUWFkFuHMvNp5PQZY7fx5CWVclpbH1Mjq7M8fTmhv6J1iuOAebpJps97nOoOr3WH6mkuD978dOplmkj6pwUwVtH0+/ud4nnpJ4u9jgot6q4bO8yTSEo9/J8WlX46yQqAmbDIdepIh772rnlFUx8BvKGBEib7HNYvKzJ/sIgCuXLzpI5nzqOFRu6cvnaJyjzgBS2qITazG4qmH8fprAg4RigiM4vKaHmxIqOFRhOL0HgXQAgpBcrMh+GFM64pNDnHSpGxrQ36yNSTGjt97qfvz484Dg86iQCEojSyznaYJ49UPQZTH7mZUkO2+z2J6gTHWlLotNKs519ocZHpjzHSYjv6m1/fJDR6qj9/V1XA/gXAG/YSfbHRFButOaqXRHS0uHi1OjbSY5gLTT37LPp1Gpqwv6JDMxRNv2KoiZI+bOq/QB1onh1LMY3Ywl9PZLyoDbAUmZTYMCpDSAlQZq7CGmnIFRohJC92mWyr33uVqTmo62vN1LZm0sKiT6lTGs/2+Hd4wy/8bit0Pyt0T8uohUiB4MtMO4qONL3uUEM377bpIh1lCVG1yVhCUmTswIuhzyFRD0/Yhg38V4d5bYxeHvxgmL5HOZUXCRnmq/Z9qJ9Z/n6WWjPvRXnsn0daNEnnefr3IVO7XL8H0P8eX80UuIx4MCpDSIlQZs4mVoOSu62VD9kWzvZaXmS9t0wf10rM2mJf1dGZTi3pFjZWaOS0sxGNI7R3eG+f8NxWaNsK+GdYZkRJLLOHplIJ3aeM1ovm2NekVIHnV+2IQOv4hS7a9UTmiN0iQiOfY3GsMVpjOgG00YmYvNh1sw9lltnBKb1ehLvpGvzBJrxeg32hqeWm1itfs2uXVKiITOh3femoSkh0GL0h5LOhzFyNLU2lQ1EaIZZKsk23Zd5GdYB1//g6nyEVnbFSYy/fO9742uMwGEP3s+rTLJg/+L2oTD9689S3yRSdqWALeXc4ojmesDu8o+6GFkOhz2iu/76dCnpFbKp2Xswr12hFRubPIjci46Znps/hSs508dPUNgEf8CIxU/P6qZm9FZpKN89eIzGL26aFJoT9/RNCviqUmU8hFHUB/CiNt58nL16UxraI0tvlfP2R67FRmdwojD3GT3Oc9h4H/OoHJ/wXgGqSmaXITA/N3diWZocGR9hWTWN0putQtYPISMdy3ueyDB3Q3Q3+WGMZqdFRGR0fEpE5mUbhXnFzfwmB4bBD4hKL0niE5KV15odp7yEd6sX4TUM/Mph6cK5ncqMSbTKsgVxDKNUEZ90oYbrexpqOlRf938JaKEKElAJl5qqkamdypAbOMbyaGb3ctmaC897781xjr8mkmvRmTn3FQmxaZ15ERvbRPejiHm9tjd/oIzRSGKwjGzrd0eA4dtW2wxG6OFW2lWnVdqjaQWSkXxaNJzc6tTZcwh18obHd+2uROaKZRYpaVLAtqcbiX/sd2IhJToop5znufYcw820vcVXboaqWtTFy3ZURGt1bshQBV9I826uLyYnUAAi3uDtHXrbsR+Eh5BagzFyEtakjb38kjiF4fc94URqo9foYtn4mlvux5zSrbc2MTPWYTLJsr+at+PxU28zWT4XB+BfQNtVCZiQiI6Nu73AahitYfq4xWtC+4163+PEe3va9m+qYL+vqeYHvVFUyiUyfCpsLlvQrI9foFf6O9Tu2liUmNrkyA0z9yNjvxs4P6HSSjsjI59LyOB9zS4qAhxZNa2tmZg5+j/lvXv/G9X8z9v0WtgoSIeQjoMx8CLmtm2KFDPpYwDIa462zEhSTIjjr7DlrzEL8ngTYwSVlN+/Bqh++ezgyAwB9YfAYoRn6Q6nQDv2XtIMmTJGZE3azKg5Nn2Iaji1Nl/V12Z51veWCpJy8ouB5cmUUmaMMvDhIi5dmkj5z5DiL6/HSTPYl1xzzbE1IXsx3XLXvszGb5s3e4y+Rmn5YgzZcNwNnmVszY7HpVSvka5pfM+JCSElQZrLIja7EbmesFiZ2TIsnLjbKkmrFlJKmWIorcHl63vb4ax9GoQfwAcEH6Tv6lk4AgCfMIgKPeB10psEDXmaPUGBK10w1M1iOY+R9jpDYCOpzdfWPoV5mXisjw1PaR7pcvxYaaUoOLIdbSN67UJQm3up8/lnk+CtaNAnLImCn+FdeoSJgfU/178VKTatXiKSEojLXhMJDyK1AmflwvALFNYTExSsEtucICVUspWSv14nOhMpvtMjo6IyWl715hVIjLQDsxy5q6qe+U7YHvOIFD9jhiEe8DPLQqEfq/CdetV0fTZHz2wLgQERiNu6S3IYW7vhLXt8y8zRTp2Iz8w8rQmObcbvkpJhS2ZHQeE+JFk02ItMfqp2nk9Au7gSAZRGwTFN1MrP3ttM9LyrzkWJDCPlMKDMXJTemrwn9Y7s2EpTq+RdYCpB3rsyojL0U79JiDyYtCFoufppLXbDHS91htz9hV53wiBc0OOIRr3jBIx7wMqSaGnR4XURpFnhRCPuZdLrGCox5P+8Qb54q0mkvL71ki5WzkGEJdM/AMjq2yGFt1tVqvz2mQTKf0N//J2f+Cfj7BLz+vMcJDV7wOL6m9F4vkr28NcN3UI3T8T60Fd69Dnss0TQTMBUBx+TFppdC5r0WShIhtwRl5uJsERqPHMmJCY0+RijdZLfL7XQsUjsjq0PPC9szsH7o7tW65/D0vX7Ey/6Ix3++DALTR2d0ZEbXzxyxw+PQqmjX7PBYH3AnD0gZMVqu2V675xUZz0LdwV8u81RNQGismHhRIxGVP2peJGbvLGswycw/MRMYeX94An4//cQLHvCMX6PI/MZPvOIRv4dlv/FrmH8YtnnAEbtxesQOx8MOaCu/zifHM9zfl4hMblRGSw77oyGkdCgz2awZouBSQpNzHSGhiYlOKPpi/4JdiX7A2AeOfuiGioQFLTBwtqnvcNg/4uXnKx6qF7wOInNEM0rNcYgeSOumqZ6mRlsB9/aYWmjs9bdwi3zXsEZqBC00i2vWQrPccb5eR19kakWmwUJg8AS8PQG//7nH6xiJecBv/Brf9/MPSm5+4Td+jstf8YhXPI6RmlPX9P0HtfWU5stNiS0iNDoq44mMvNdTSgshXxHKzNX4aKGBOp8nLjoU4qWgYJbHnizO54rVzsi6WGGwrqmBWe7Jzc8GL88PePzn45hiksetSI1t4SSpp9P+B+7/vC8lKZXd0SmlQL3MJQiN4D1Kip7unfUNppZaT5gLjBUaKzM/Afyjl5jXn/d4qZYC8zJIynJ+itToZS94HKMyp8MOb4cdcLjzC5nDNyVBSGTkfchUL5V2IoR8JpSZVayJzgDLJ/O1r8WKin5vU0xWcvQ2qeuV4975i1PPBq8wWCRH5r00E9T88x1OPxu8/HzEQzVJzBG7WaRGlj2MTaR3eKwroH6fn18T+wwRickZqkAPfGmRFFM/P11AW1UYvy8rMhLlqtELjFy7CIqe9wRGT4e6mJenH/jd/FqIio7A6PkXPAx1NA+zlNO0btcLzvERx0MDHJqwxORGaFr9RgtLKtXE6AwhXxHKzIcQ+tf53NsfExobnckZQdvrkG8lMQmoEy958MbSTLLsGXh/fsTp5wtOT81Yl/GIx6Gp9jwiI7U0D3hBW1X4W7/hroKfPvI+w4qaXLePmASpot+uHtJM+l50mARQJEe3aLIyo2tjjMiIxLw0y8iLrpE5YWcEZhKb4yAtOv0k2xzR4ChRmfbOTzFZqRHqwPxMpkVSPJGRddZSY8XBlB5CSoIys5q10ZkYW8Lb9ivzindtRCY0b/fzOuRLXYttImt2DWXbQnKjIzNQ84HozMvzIx6eXh2JmaIyXj8vYw2KSEEIeche8b8Ub9TpKDaipOe1FEgrrAZ9+qgx80/AWwOc9r7EhOZFUERs7NRGbo7Y4fXPA04SlfEkJqcA2P0ORF50bYy3/lqtmgghtwBlZhOXFJq1hJ6s3jV50ZlQuikmNWdixcaKi442SGRG3tvojMz/BHAYojP/+o1j06c5fi1aM/VNhceaDREbiXQA6b5W9OfYUCejm2anCKWguvoH/tbvUyssO/yARGFkuT6MlpghKnN4Ak77viZGR1RSQiP3VuTliAaveFgs12kmSS+9S61MKCqTIzRA4F8tHYlJpZvOjbowYkPIrUGZ2cxnCw0Qj9J4ERkv3ZQjNTEi0RkPG+mwMqM/WkhqntXr5xCdafpHpxQCS5Ptvp+TSWraoW6mq38AlSoClod/hfTDdEXKyaNVvf7Oe9GdTqyXz0b/ltSY3CvtRyHRktTSEInRhb1TnzFpiRFpkf10R4B6uIZXHZE5PuLl+XEo+r1fikws1QQsi7QXvxVdM6N7Aw5FaeAsl3WUFEJKhTJzFp8pNEBelMZrqq3nQx3qXflzeWkoHbXRRcLPZh6YCc3bYYdT1+BY7cYHq0RhpCO94yxSU+HY7PBUH5ZZN13EXJl1OpJjHq79IJPzPm8BXT8z/55kGAMtNro4uEWFHUR8KnR1hWPzjr3UwGjsT6Ay65pl66SZcGRIjK6ZOY5pvN0oNa0aumHc788DjocGb88Pvcg8Y5KXVM2MkJ0JCo3LZEXHgyknQkqHMkMGpK5A+CCpkVPrtJMs0y9pteOlJg4NTocdXp8eccTvsfXMy/AYfsUjGpzwglfscMLj8P7PP094at/7VkAN5kWzMpV1uvmyavlzbICXp/3YmserJ7HRDKneaVGhRocTGuxwHOZ32OE01vc02OGEE47VDs3TCbunIx7/HNAch0EuAfwd7pl0qtuNgvUDABb1MLqI15OXaX63qJU5aiE0UjMOqHnsO8Y7PD8ONTJ3k8jIS2T0kHjlRnBmosIICyHfDcrM2Xx2dGYtseuVFh92WYqVqSYPLTShl47WzB56UyGwlhhp3SQVNA/ohz74jV99nKY5Yff0jHo/iIGIyxHzZs8ylVZAT8Df/dT6JyQuVgDkvcRuJDpTocMROzQ4jfMiNTJtsMMrOuxwxOvTCbun05iWkrGc+nkdFZrGhYo1r9bSorf1CnwlnSSRGBGYrqtwOuz6SMxhN0lMSFKezTQkMaG6mtkPRxZ6v1VGXAj5DlBmLsJnCc05X1+o7ia0PMVfXERoZJrzUg+/98Oujwg0zRiZkc70JPmkxUaWVf9s0RxPqNp3VA1wr4VGT1VkRupOjtVSXGwqRr+XyEeFFg1O0KNnV4sITT8vkRzRiAqPox71QjQN+agH1tTLli2R/P5jrHRFRaZr0LUVurbC8dDPvx926Hv2dSTGfF/ZQpObjlpUD1uxYbSGkK8MZeZilBahEWzrJrtcyPmpXEBo7CUkJGZ63eN42OHUSJ3MNJq2NNKWNNMwQhAa/OrFoWlQNR2a7ojTvsPu8N5Ha3Rqq06nlWzLHu99LyfVQmREPSZ5OQ1ycRrWPYyRGxGeadztaiY1dpnXyZ2XcorLWF8D07YVuraeBos8NEB7N/9urITEpMYTmVhqaSYzfzEV/AIUFkK+L5SZi/KBdSZZeK2YQqSuXctN7GdzptB4zXRDaSbzYDwdph6BX4fhDJqxUmVKM4lENKMo9IJwqnbYVSccmxZ112HXvKFq+2iN7o/Fpl9sRCP0XqaiG1Y/9PsjdqjRDamlPgqjxwBvcBxuz5RK0gKj34f6ibEpp2XLJFXEK6mjNjAMgf2eQtNQlGZNqmnxYwHmIhNLOxFCviKUmavwEVGaLV9djtzkDDapnyjecS6UclqRZhr7nPn5gtenPp30MHSi16eajtgNkZhqEJnd0NxZ0jkSDanQoalOOD5N0ZpjMwnJ60xg/FSMF9mQ5XOJmYsMgCzZmVpAzcXFq2eJF/zOm1KfuiZe++KJRUpqYmKzRWjcH4sQE5jc+q9UnQ2bcRNya1BmrsZnpp2u0UNxSn4u+FPKKQJ2H353OB4aHJ+mMZokOiMtmhY1M6r49ohmiNyompUhWmPrXkLRGRvZ8PpkiUvMtNwu6+/yXGbkuNK6SIRGz8ciMlOndjucxo7t7n2p0HKBxHxIRKzgpOTFLhuxKSaYDULzhJCvCGXmqtxyHU1OBMbbPvfznBGdiaWYEg/Ft+cHvO6PaJ7m/QBLHcokMe0oCA1ObsHtfL5xoi1zubHppFC/LKlojLz3p5PUAFicW0dj9HkXfcl0j3h5fpiaTz/f5dW42O8nNM2NrMXO533Xix8JsJSaS0VNGIEhpBQoM1fnGnU057ZiCrVgCuH1P+Md4wrRGT2vozLBVMX9GJ2Z0ku9vEgxsK6Z8VsOnRZNpaX25BSJuugamQ41XvEA3ZmcbAtgFl1JiYxQOd+TPXasR94XPOJ03PU98j4/AM/3y/5eQrUtMq+/n9j82lSUFwFKRmW8vJN9Txkh5DtAmSmO1FemxcPb9hJfuSdm+rgXbtFk34fSFM8A9sDbvo/OVE8tmqHlT3+Fy3EItLRIYa1O7ci6mCh4y7XU2GU2hdTPL1NLFm9ZqEdee02zSMzzfrpfWmZiUiHzoe/EzodERr+PFQoHozJ/1YI39ZL3UPOepOvtYh8AznaEkFuFMvMhnBuVyfmaYufw1t0Px9XTS0SPLigyQk66YiY193iuf6GqO9SNP5CSFMz+wm+32FYLjvTnEmrx4wmF7SFXS40vM/O6GFlmscu8a5inmpp5NOZgojGezIRExgZCrhGd8ZaNiMi8Yi4wdiroZTGBgbM+BiWHkFuDMnPTnCMxtbPeW+btF4vu2H3v1TYXEpnUx/ZEZojKTOM47fG7/gX8C4sBGKcG2+GCXPs+Fv2QVFK4h9y5ZNhiXmBeEzMffDIsNHKV9pzSKqnv0G63rIuxndWlZEZPgXyh8UTG+/5i8yOeyLwgLDQ5URlKCSFfBcrM1dka7biEyKw93jnbZx7Se8bknl4edKHWTHpAyhp4rx/xUg8y0PTRGC0ZIhrpZtF9mmkR8QiklMYu/hctjPpu/6vKTy3p889vhS80nfo843mOu7FX3rfDDmirvjZG3yM9DclMSGj0dxGbj0mMzNuIT7RG5hVzUUmJjI3KrAkpARQdQsqCMnOTrKmLie0bSi9disw6mTowr2kz1sfGbnKl5g5v+IXfbYXuZ4X2SfpimUTjEY9us2cbKYlFX0LNo8e+X5RgdG2FahCsqm5Rj/PTVGRH4xX/AnCPPxtWoIU/yKP3PiYx56SZQvMhkRmRaIwWk1SKKZRyArZHZXINnBDyWVBmbo6tIhNLB22N1NhzpUQpcpo1EmP30SJjhUYeyDUmiZnte4f39gnPbYW2rXD62eBU7UaRecULgHQxrpWXRURERV4WYxaZbv/fh2t7q/8O1zrchLrDDyM3mqpePlRPh6Y/flv19TCekIReVmpi6Z6taSaYaSzVNCNUH5MrMoJXHGwvmFEYQkqHMnNV1kZBLiUyoXWxepdzCERltFicO56lFhl5f8A0qrWc41lNZbsWQLvHoa1wOjQ4/dzh+NRAhhwI9+0yRWxCkRfpMbcdjj2KRVuP8rKIbIz3Zbhv9f04FdF5d+7NGwCIAAmHO18MvIhV7BWSmLWRmZyp/Q5neNGYLSITSy+l5IVyQ0hpUGZuhkuKTCq9lCrqTZGRN7IiozdrEZab2GG9v/y1yGihkW1+6m3v8f7zHs9Dd/2v+wfs9qdZWse2KOqXdVMdTFct5UWiLvahH3rZ6FFqOuNueS9D5w21TForM2sjM2uEZuQciYEzH1uHyHtCSIlQZm6CLSKTIzFr0kspodlQa+OJjL0EL0ITux32gahlxtu2xTTytURx9vd4e77H28+3WWpHsCmeqm7RtfVUUKvrUUItcC4hM6F7kSszntRYeclJMdkX1DQ0H5ouyJUYRLaDmffkxc6HuGQTbkLIR0GZuXlyJSKnviVV87K2jiZwbTZSEjq0jdB4ERu9Th/HPqy983gSsVev53tgf9+ndBTv5hrevPPF3nvn1ss8eUlFZULvcyJBXp8xMYnJjczkSEyQlMQgsB6RZbGoS2xUbUZlCCkdysynE/sKthT72vUxkamx7CxPd6Kn3yOwzNTLhCIxqWiMfcivic5oobHNh2WdCIx9HzqPlwYLSUJIWrxr1ceXV+vM22vxZC6Fvp/eOey8/qz2PPoaLKtE5q/aYEs0JrQsN33kRWwQWEbJIaQUKDNX41oDTF5SZOx+tiDYm9diE/n52OiMfjimxMY7vd5eHqyeXNht9fYiMXI9MZmx15ETAcmNWHj3xkpFTGpS1+yJSCgKZL9Wfb9g5j1CsrbAkxg9H6qNgVlm9wHSIhN6r5cRQkqGMnOzbJGhVBrJioxEZTxBquFHafT+gaiMJzHeJa4RG8saiZGIjRaY0PWlzmfPHXqYe89IKxdrrkN/lpDQ5IqQJ0/2uJ7caLGxnzXoBH/VRqnIil0ekhdvPyBPWkIikxOVYb0MIbcKZeZTWXv7Y1GZVL7EExl7rNpZDrM8ENmJPZS95Z7IxAJB3r5WJg5mH/vQ1lKzRmTk+J64hCIyMUIy48mNFyXJFR+7TygiE4oKpT5P8vOGUkpQ82vTSlD7wSzz3qeW2eV2HSGkBCgzN0lOVCZnYMmUyIQEJhSVCYmQs3ss8pASGX0cBLazAmGLgK3E2OvS73PSKN45Q1GZ2H9VoXuzT+yTilrFIjeh+xKLzngyFIo2ufdPF/gCYSFJyYsnKV6ExpOTkLDkfuGEkFKgzBRD6gkprBEZe4xQHUyi8DcnwhC63Fyxseu1xGgRsE21c6extJB9HxMcTyLsvE116Y7/QlKTimzZbb30UG50xs6vxmupBDUfa5kUkx8E9oXZBpnLU+tC2xFCbg3KTPF4IpPCtmayPwNbM2PPY7bPSXt4D2O7b04KxW4n6aXYgzp0nfbc3vza9zny1GISGisyVs5ChCQxlJIKiQsC823gvWWxPNTkGvCjMXCWhWpj7LI16aOYxKSgyBBy61BmPo3Qrc/p/yUlLamoTOiYMbmBWu8cJhWdSUVjvEtakw0IPbhzCclM9kNcnTMmCaGokRaZkNDkfB4teXY+llbyBMZKjH2fLTJri3xTtTGhOhmY5e5FwpcTppcIKRnKTNHkRGWsyMT6lEFknRaazFZM3sM7lVbyohwhcQlFSEK/6lQqKZZGyiXnPuzVtvJeBMa7BnsfvAiTvU4vIpUTnQlFYWLRmRlb+o7xhOac5tZ6OQLrQ9tZGJUhpAQoM18OLwoDsyyUo/CiM4nC39xfkH2QIvIeSD9jbOQhtl3u+9R86Lbp+ZyXTjHtnfd6Gztv38v1hYRQX2MoYqTFJvSy9yAklLgfFt57K7EUGMFbdimRCUGRIeSrQJm5Gm+Ip4NCBSKp/bbgpZdiaSW7X6Tw9xKkpCQUFViTAlojLanjyzXJNqH7kBIaT2TWyowndaH3VlxSKaZQZMmmrRb3KRSCs+T0wnsJkdmSVqLIEFISlJkvjf56U2MqeR3lxXoETi9OXpYXQWid+VwukUZKRXk8ibCE0jdeBMaKzBaZ0demP4Os10XGoeiMPZ79LPZzuyJj0o9ZhORjSw0LRYaQ7wplpghiT04gr88ZmGNYcYkR2cZ7IOYSenjGhGZL2igmMefWfdrP692PnOiMJzRrZUamKSnR71MpJpsCTApNjfUyEJKXc4YlYI0MId8JysxV2Zpq2oocK1du7L5eVCZQ+BsTlzVS40VpcqMyOZISE5otePUzXjEtMJcCL52UIzPe8At7cw5PLkIiEovGpOTLi0y5QhOql0mhC3+FS4lMzvVQZAgpFcrMTXLpuplUD7/2KextG+FaUhMjR1qyBOZvbKVCiVyowNYus+LgRVjOlRm5npBc1FgO4RCKxqRSTDGhcW/AW+R9TBxskXBqWw1FhpDvCGXmy2K/2pSU6GJfL/3k1EN4D+zYpeREXJJ/9StC4pKUGDtmkCXU109GTUgsAmIFJiY2MZmR5UKLpdCEriklNCG5yRYafY9iLZpkukYiQlEavSy0TwyKDCGlQ5m5Oh+datKkBo30tvcKgc315YhLCLvee9aEHsih56IVGff5ZUdv9mTmXm3jXbQSGptuikVnQjKypkXTYv/hutt6uDd3888u9ysmLTnSY7cNCc3iQGslxQ5zIKTqZvQye8wUlBhCvgqUmS9Pbo3MGT8FLwiU2ia2PFYAHCIoMp7EhGQmJDGy/QOSEZpcgfGEJlYEPL7+AnWLH/sTAOC97YC26ld6UpO6ppiwpCI6nuBchNAgk6FtLSzyJeS7QZn5EC4RnTkngrN1P5GcRHrFXpr3PkZo29h86DUjNnKzV2yq0SmSxP2z0ZmQyKx5HxEZ1B0AoBqm73Id7YX+c459X1ZaghIj929tlCZGjtysFR9CyFfgx2dfAAlx6X941xYUb3wwetKRs0+s7sWb9/afkSsyeuecAtVAi65URGatuGTQtRXe26qPzLT1PCqz9SXkfnefypqefykyhHxlGJm5Ga5ZOyOkjp/b74yDDWK0Zn4LoYdrNBoDLAc8BMIiI1EzHUXI+PxbRCaaPkq9MltdnSsyntTE7oG7fkvneSHW5BkJId8VRmY+jFv5y3BtUXAGKemw2+QcL/ZwTaaX1oiMV5ux4rvaJCYrXrNzBW6ijsrIx9gajUnNh3A9+ZJyfs5/P7fy3x4h5FowMnPT2CjByuhBEJGXM6Mw+nCyPDa/9o/n3BTTDFvwq+c9kdEH0REaYH6fjV3kiEhOfzGxaM14nkhUpq3m92Kt0IS21bflbO5xOaHI+hEoKDKEfAcYmflQUv+wFhwq9/6Sz5GR0LE2pZh0eskTmVa9t9sBmx58qfqYUKoplm6aHXcQGYnKDEW/C2L3PFdk9HHWkB2VuWRHkJQUQsgEIzPksugojI7GbKmfiUmNeywv4mL7L4k1+7UXGRmPKrdmJiU6IZEBohGZ91lExvR9c4mITEpuvEibG33bOrRB7OS521B4CPkuUGY+nEsOVaBTIp+ElhVPZGLppdCl50iMG5Wx/cjkiow9WOb3ExOanI7v1kRkQkhz7HNFBpgfx86H2JI+zOLMiBkh5FvBNNPNc8P/kIdSS7Gp3T/0sEXi/ex4dngCKzJ2HmZ5LqZZtkzXRGWyWjRJfzLmpl0yxRSLxsDM5+CK6X1gPkXo5Gsu6ob/uyGEXBxGZj6FWHTmBqItOejLjEVkciI0MMeSqfdg1tvMLsbWxIR6+/XmYxeUsSr0PjSV+dRxLDq1NC5z+pY5mPlDZL333hPITX6xNrqiZTP3uFvWE0K+GgU8NUmac1JXOf/wy8/E9B+SW9KghSa2nZ1PRQ8ALNNLa0VGPr+9f/ZE7XCuO/+a1r4Ow/57NT++vwPqjO/TE5ZD4H1sPiY1a6I67u8hJjS5vTETQkgcykxxxKwgtG5jBGK2X43xYZ5zaO9S7OFip816UP4F8Iq5uHhTffLQuEypoQ3kGPfpB/th+Hyx6V5ta4XGi9pY5HxbJCa07NzX4gJj9zzHhPX2Ht4xKEWEfEcoM5/GmmjKlsiL/oc+tW/oAeC1RKnh9vCqH+aexMTERh86S2KAfJEJPURjaSZ7v94wGYZatzUio+Vl7yzzZCZ0qVskJjTd+pkW1yfRMrvCk8hccuWHEPIdoczcJLHoixATnJT82IdJHVkXEhog2G19TGxiWJFxkebKnsi8IiwyayID3sNW7ufGVJNEZUJRHGASGiszoUuNRVpC6aSQzKREJbVucWHAdB9tvz+yrFXrQjJqiYkQozKEfFcoM5/KJZpp5xqDfuLk5H+8B0NtliekRh9aprnpkwW2Z187DT0U9UFD73ORJ3ck1RSTGJtmkvc2KiMpKH2p9jKENVGZkMQcMD/fGqFZyIz+noDzUkyEEJIHZebLIGLkyY0nTfpBfh9YJu+txFgypEZY/RyLDU+gRSaUcgLCEhMSNg8n1ZQrNFZudGoJWMqNxt6v1synamE8aQm9j8lL7PMuLjDUFB5YL5D2Q4dgVIaQ7wxl5tPJjc6cUzej99OC4i0LCU0O+pjnjpwckxiZf1HLYmkK/TCMiYzdVqPrZVQh9JqIjCc0NcLpJXs5NlrjRVtiaSRPXuwyZMwHRcaLyuip/TDntGJidIcQMkGZ+bJ48mOjNp7EpKIwa865Rm5s1/0xiUnVWdjIDOBLzJoHon2ir0g1WaEBwiJjWzZZgbHLclsprWmxBGcekW0BzMfF8i481nLM+77sh/X2Sy0nhHwXKDM3QY54eOtsaim3KNhGZ1pn3srNlpGPt9YExSRG1ueITOgBGnpA5rT6OiPVpFswCZ7IeMW/oWmqxdIlJcZ+Xvf+2O8JZuOUQMZamRFCiA9lpihy5cDKCdR+3jqPrRGaSxU1xyRGz6dEJkdiYssELTFyfBOdOYT3niFfgR3qQC+3lxyTmZDIbI3IILFs8bPxImU6BRhquZRqtRT6vig7hJA5lJmb4RISYI/jRWN0U2sb1Vl7fTpio9dpYVpTE6QJ/YUfKgL2Hoo5IpP7YJSnuI7ODH3upCTGSoMVGbvMG9rBXqqVKDu/pSO80Dm98wOYp5ZCItOaecCvlQlJTQqmmAghlJkbR0tBCJtq8va1wuL1HbOGmNh4771rSx1fT63EyDpPZPRf/fZcuRLjPSC1adyjbz0FAA/IEprQoTyRScmMFynJFRq7X47MuIjI2L59Ut+LNbLQMg2FhRAShzJTLDHR8aIzIaEJ1d7knMeey27rRYZysH/FI/A+JjK5EuNdl/48ej95eAPAo1o2bB8TmlBkJiQydt/Q1JOZVGTG7pstMEA4GhMSGW9dqqYpdhHnSDgh5KtCmSmOWFQkFJ2Jycoa1kSK1uwj29nj6OWxVJOez31AbqmZAfpIjBc9CAiNyMLeLI+JTKxmxi6LyUusMzy7LEqqw0IEltvIiycydt7CqAwhJA1l5uaJyYC3LiYw3nu9fOt1pQQmJGCxY+ttYu/tg1LWhx6QMYmJ9bPTqm30Z6vN9lgOGinYyIw+XK7MeMGiLREZe6wZof5i7H3PTSvZSJn9cN657Lx3wRQdQkgPZebLkBOxuVSExiMlMGtkKUdi5L2NDHjb62Xe9cT62RGh0YUsrwijioJ1REbmtcx4QgPkyUwo4hJ72Y89YuUFSN/zHHmJ7ROTl5T4EELIHMrMTeEJSWq7WHRGb+sJDTB/eOf0JZNbIxO6thixh6m3zEZjQtuEzmGvMyQ0wPS5bHWuff+AUWh0pGSvprJbSma8wMUWmXHJib54762IeHLiLYuJjBdJi9U5EULIHMpMEeRKQUg0QhGZ3PRSTLK2pJj0uhA5UZZYNCb1MLTRl5TQyLyOynjWIcvvseiHRkdmZDMrMGuaZst0lcBskRd9Ui8Sg8C8l/oLbR8i9GGYYiKETFBmvgSeuISWhyIyCKyT9dd4eMREKiUxet05IiPvY0JjP79nFRpbU6OiNCmR0dNYZEbms1JIsfuUm+4JRVFispIjMpqYwGooMoSQOZSZYlgb8YgJjbdcsNGalNikts/F22dtXUVo/9R5YvunPn/oPyEZxduknWx2KjS1l6GnoWUjoUE6EXmfI4Q5QiPbeevtcb3zEkLIeigzN0du3YwlJjsxoRGs2IS2WRux2SI3XkTAzttlOSmLnOvQAuM1d8+pwXlD3w+NoNJOdjfvK4hFZqLYKIwViVyBCUVkQu9TdTWy/ZrzEUJIPpSZokhFZ9YIDZx9hVABbM42sajNGkISE3vQIrBd7vlircFChP4TesFchmxIBliMJB6Ntnh4RbwxiUhFW1LbpM6VIzq5IsMUEyEkH8pM8awVGiCcXoLZLratfdhco84mFgWJRWlyjqWPmdMaLIR9cD9EtoU5172z3AjOKCz2fHJOew3yfmv6yB47tt3aGqZzIzIUGUKID2XmJok9QL2Hb2obT3jgbKPJSUXpbVPhhNzUWW4aJ7Y+F7tvTk2Rh72/EomRqY3wyHFf1X4536k9p113ifRRars159kiTnZdaBtCCJmgzHwJPPkJ1XrEOrJLyY33kMlJSW0h9vD2tokJTmpfu+2aATJD4qllRksNkP8dxM7pza+Rl9Qy73i5x8lNVdnldl1oG0IImUOZKRIvOhMSGiAsKaHtNbFojLfPJeUmlhoKbZcjMt7xUqk5qGWpdJY+npUae8xXLL/LWIou97NvEZVY9GXr8XO29faJbUcIIXMoMzdLTq1GjtDItghsb8kVnNC+OXKzhUsWhIYKhlNN39ekm7TUWMHR6SWNXp9zzfaa1ohFbPnWyEpuxIUiQwi5LJSZL4eXyhC8+pfQ/pbUOEvnRGRinauEyHkgrn0YxlJzWkz05/I+45vZJzSiuUdOmsmeK7b9Vnk5d3nqWkLbxLYlhBAfysxNsyU6s2ZfRPb3jifkRn9S+9h9c1nzULwEVk5i6+S9lZ6155L5FOfUFl0z2nKOyBBCyDooM8VzjtDI/pbUzyKn7mZLSivnfB6XeiiuEbvUaOSppumx72bN59kSobpmxCU3XRT7jIzKEELWQZn58sTSTiFSLWy849tznJPSuja6E7utxIRGc61xrYTcNE1uVOWc4upLiAwhhKyHMnPz5EZXcqMpW4dK0OTISU6Nzbl8xl/33r3WQgNcZjTyXEKfc026ya6z67ekjHJbY1kYlSGErIcy82XIERogv44ldS6Nd97cGpvUcXKv4TOwTbi94SJ02s17UJ8rNGtTcB9R90KRIYR8LJSZIsh94OUKjT5uitzzCimxiR3zIwXFpppyIlu54iLHy304r/3evP1jrJWULekkb7ucfXL2JYSQOJSZL8faVkop1hburo3apI63hY94MMaacct6IUdszomYXaLV07WaUeem2wghZDuUmWJYm464tNTYa/HISSflyE3seCnWPBhtz7w5x94yxtXaiFNuHzSp7S1bCnavJTA5xyGEkDwoM0Wxpb5iTcukc8mJuGxtKRU7ZmjbHNbeD+87iH2mSz6sU9//lghNaL+1HRBeoq8gQgjZBmXm27H2oXPOTyQndbKmbsUe87PI7ZDQ49z/5LZ+/nOaTVNkCCG3DWWmOC7RnHcNl2pWndM0/JqpsVvh3CLnnHuztdiWIkMIKZOv/NT4wny00FjOEZw1UrPmuN+FS3T2l3tMigwhpAz4pCAXYutYT7nDLdzaT/WaHd5ZLiGua/t++aghJAgh5Hxu7QlBsvns6EyIc2pgSktBxb6DSxf+XpqtvSdvlRhGZQgh1+OWngxkNbcqNMJWsSkpBaWvuYQH9jlDQFBkCCG3yS08DchZ3LrQCGt72wXWjfj92T/ltQ/sjygEzj3P1ibdOVBkCCHX57OfAOQilCI0Qq6ErBkc8xbTUJpL15hc4nhrhlu45vEJIeQ8bvVffrKa0oRGyBGbtVJzSz/rS/WGe80i4BCUGEJIGdzSv/rkbC4pNFsfSOecPxVdudaAm9fikoMrfqQgnBP1ocgQQj6eW/gXn1yUrQMWXuohtCaKsvX4JUSgOEo0IYR8FJSZL00qbXHNB+o5UnMrkZVrccsiw/5jCCHl8eOzL4B8Fh/1QH3beK5zmhCn9r825177Z3GN3oUJIeT6UGbIB7FVamLHK41bveYWjMgQQkqGMkM+mDUP9HMfsKU+oFt8jGBQYgghX4OvXJhAbpY1Rbyx+pmc43x0/c3WsY5C++fIxiU70NvKrUadCCHfAcoM+SQu1SqplNZNOWyVDUZXCCHfG6aZyCdy7R5oL7X/udx6wfK5MCpDCPlcKDPkk/lKD0JGVggh5DOgzJAb4NzIxS1EPr6ryHwlGSWElAplhnwjriEOqRZBfNgTQsi1ocyQG+GjHvqXbI5cei3PuVDUCCG3AWWG3BDnPBw/akRo2Tdn/6/8sP/Kn40QUhpsmk0K4tJ9xmghyTnuJSMpJUdlKDKEkNuCMkO+EOf0OXNpueADnxBCPgqmmciN8RUkIPUZGJUhhJBLwsgMKYyPHp6A9FBiCCG3CyMz5Ivx2Q/drxaVufRo54QQcnn4Jy75gnzWeE1f5aH/VT4HIeS7QJkhN0hKRm4t1fRRY0xdEwoMIaRcbumJQMgKUkKjH87XiNKU9PAv6VoJIWQ9lBnyDfAe5lsE5xwpuHZUhsJCCPm+UGbIjZJT93JOuqn0h3/p108IIZeDMkMK59bqZzwuFZWhwBBCiAebZpMb5isU1l7i2tg8mhBCYlBmyBfhFoXmUiJDCCEkBmWG3DhrHua3IjS5o2rHYDSGEEJyocyQAlgrNJ8pNYzGEELIR3PrlZOEbESk4to/cY62TQghnw1lhhTC1iEKbiX1lANFhhBCtsA0EymIr/yw/8qfjRBCrgtlhhTGV3zof8XPRAghHwdlhhTIV3r4f6XPQgghnwNrZkihiARcYxDJj4ASQwghl4KRGVI4JfbHUtr1EkLIbcPIDPkilBCpocQQQsg1oMyQL4YVhs+WGwoMIYRcG8oM+eKkZOIaskOBIYSQj4QyQ745FA9CCCkdFgATQgghpGgoM4QQQggpGsoMIYQQQoqGMkMIIYSQoqHMEEIIIaRoKDOEEEIIKRrKDCGEEEKKhjJDCCGEkKKhzBBCCCGkaCgzhBBCCCkaygwhhBBCioYyQwghhJCiocwQQgghpGgoM4QQQggpGsoMIYQQQoqGMkMIIYSQoqHMEEIIIaRoKDOEEEIIKRrKDCGEEEKKhjJDCCGEkKKhzBBCCCGkaCgzhBBCCCkaygwhhBBCioYyQwghhJCiocwQQgghpGgoM4QQQggpGsoMIYQQQoqGMkMIIYSQoqHMEEIIIaRoKDOEEEIIKRrKDCGEEEKKhjJDCCGEkKKhzBBCCCGkaCgzhBBCCCkaygwhhBBCioYyQwghhJCiocwQQgghpGgoM4QQQggpGsoMIYQQQoqGMkMIIYSQoqHMEEIIIaRoKDOEEEIIKRrKDCGEEEKKhjJDCCGEkKKhzBBCCCGkaCgzhBBCCCkaygwhhBBCioYyQwghhJCiocwQQgghpGgoM4QQQggpGsoMIYQQQoqGMkMIIYSQoqHMEEIIIaRoKDOEEEIIKRrKDCGEEEKKhjJDCCGEkKKhzBBCCCGkaCgzhBBCCCkaygwhhBBCioYyQwghhJCiocwQQgghpGgoM4QQQggpGsoMIYQQQoqGMkMIIYSQoqHMEEIIIaRoKDOEEEIIKRrKDCGEEEKKhjJDCCGEkKKhzBBCCCGkaCgzhBBCCCkaygwhhBBCioYyQwghhJCiocwQQgghpGgoM4QQQggpGsoMIYQQQoqGMkMIIYSQoqHMEEIIIaRoKDOEEEIIKRrKDCGEEEKKhjJDCCGEkKKhzBBCCCGkaCgzhBBCCCkaygwhhBBCioYyQwghhJCiocwQQgghpGgoM4QQQggpGsoMIYQQQoqGMkMIIYSQoqHMEEIIIaRoKDOEEEIIKRrKDCGEEEKKhjJDCCGEkKKhzBBCCCGkaCgzhBBCCCkaygwhhBBCioYyQwghhJCiocwQQgghpGgoM4QQQggpGsoMIYQQQoqGMkMIIYSQoqHMEEIIIaRoKDOEEEIIKRrKDCGEEEKKhjJDCCGEkKKhzBBCCCGkaCgzhBBCCCkaygwhhBBCioYyQwghhJCiocwQQgghpGgoM4QQQggpGsoMIYQQQoqGMkMIIYSQoqHMEEIIIaRoKDOEEEIIKRrKDCGEEEKK5v8HJ/h9jgfvNCcAAAAASUVORK5CYII=" id="imagede9ed3f837" transform="scale(1 -1) translate(0 -270.24)" x="42.113906" y="-44.38525" width="270.24" height="270.24"/>
   </g>
   <g id="patch_3">
    <path d="M 177.001906 230.32025 
C 190.416675 230.32025 203.283815 224.990506 212.769489 215.504832 
C 222.255162 206.019159 227.584906 193.152018 227.584906 179.73725 
C 227.584906 166.322482 222.255162 153.455341 212.769489 143.969668 
C 203.283815 134.483994 190.416675 129.15425 177.001906 129.15425 
C 163.587138 129.15425 150.719998 134.483994 141.234324 143.969668 
C 131.74865 153.455341 126.418906 166.322482 126.418906 179.73725 
C 126.418906 193.152018 131.74865 206.019159 141.234324 215.504832 
C 150.719998 224.990506 163.587138 230.32025 177.001906 230.32025 
L 177.001906 230.32025 
z
" clip-path="url(#pc50dd6261f)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 177.001906 264.04225 
C 199.359854 264.04225 220.805087 255.159343 236.614543 239.349887 
C 252.424 223.540431 261.306906 202.095197 261.306906 179.73725 
C 261.306906 157.379303 252.424 135.934069 236.614543 120.124613 
C 220.805087 104.315157 199.359854 95.43225 177.001906 95.43225 
C 154.643959 95.43225 133.198725 104.315157 117.389269 120.124613 
C 101.579813 135.934069 92.696906 157.379303 92.696906 179.73725 
C 92.696906 202.095197 101.579813 223.540431 117.389269 239.349887 
C 133.198725 255.159343 154.643959 264.04225 177.001906 264.04225 
L 177.001906 264.04225 
z
" clip-path="url(#pc50dd6261f)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 177.001906 314.62525 
C 212.774622 314.62525 247.086996 300.412599 272.382126 275.11747 
C 297.677256 249.82234 311.889906 215.509966 311.889906 179.73725 
C 311.889906 143.964534 297.677256 109.65216 272.382126 84.35703 
C 247.086996 59.061901 212.774622 44.84925 177.001906 44.84925 
C 141.22919 44.84925 106.916817 59.061901 81.621687 84.35703 
C 56.326557 109.65216 42.113906 143.964534 42.113906 179.73725 
C 42.113906 215.509966 56.326557 249.82234 81.621687 275.11747 
C 106.916817 300.412599 141.22919 314.62525 177.001906 314.62525 
L 177.001906 314.62525 
z
" clip-path="url(#pc50dd6261f)" style="fill: none; opacity: 0.7; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" clip-path="url(#pc50dd6261f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="meba4412ae3" d="M 0 0 
L 0 3.5 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#meba4412ae3" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −8 -->
      <g transform="translate(35.479922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 75.835906 314.62525 
L 75.835906 44.84925 
" clip-path="url(#pc50dd6261f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#meba4412ae3" x="75.835906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −6 -->
      <g transform="translate(69.201922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 109.557906 314.62525 
L 109.557906 44.84925 
" clip-path="url(#pc50dd6261f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#meba4412ae3" x="109.557906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_3">
      <!-- −4 -->
      <g transform="translate(102.923922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 143.279906 314.62525 
L 143.279906 44.84925 
" clip-path="url(#pc50dd6261f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#meba4412ae3" x="143.279906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_4">
      <!-- −2 -->
      <g transform="translate(136.645922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 177.001906 314.62525 
L 177.001906 44.84925 
" clip-path="url(#pc50dd6261f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#meba4412ae3" x="177.001906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(174.138781 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 210.723906 314.62525 
L 210.723906 44.84925 
" clip-path="url(#pc50dd6261f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#meba4412ae3" x="210.723906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 2 -->
      <g transform="translate(207.860781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 244.445906 314.62525 
L 244.445906 44.84925 
" clip-path="url(#pc50dd6261f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#meba4412ae3" x="244.445906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 4 -->
      <g transform="translate(241.582781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <path d="M 278.167906 314.62525 
L 278.167906 44.84925 
" clip-path="url(#pc50dd6261f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#meba4412ae3" x="278.167906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 6 -->
      <g transform="translate(275.304781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_17">
      <path d="M 311.889906 314.62525 
L 311.889906 44.84925 
" clip-path="url(#pc50dd6261f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#meba4412ae3" x="311.889906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 8 -->
      <g transform="translate(309.026781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- Relative X Position (m) -->
     <g transform="translate(105.68925 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-52" d="M 2297 2597 
Q 2675 2597 2839 2737 
Q 3003 2878 3003 3200 
Q 3003 3519 2839 3656 
Q 2675 3794 2297 3794 
L 1791 3794 
L 1791 2597 
L 2297 2597 
z
M 1791 1766 
L 1791 0 
L 588 0 
L 588 4666 
L 2425 4666 
Q 3347 4666 3776 4356 
Q 4206 4047 4206 3378 
Q 4206 2916 3982 2619 
Q 3759 2322 3309 2181 
Q 3556 2125 3751 1926 
Q 3947 1728 4147 1325 
L 4800 0 
L 3519 0 
L 2950 1159 
Q 2778 1509 2601 1637 
Q 2425 1766 2131 1766 
L 1791 1766 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6c" d="M 538 4863 
L 1656 4863 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-76" d="M 97 3500 
L 1216 3500 
L 2088 1081 
L 2956 3500 
L 4078 3500 
L 2700 0 
L 1472 0 
L 97 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-58" d="M 3188 2381 
L 4806 0 
L 3553 0 
L 2463 1594 
L 1381 0 
L 122 0 
L 1741 2381 
L 184 4666 
L 1441 4666 
L 2463 3163 
L 3481 4666 
L 4744 4666 
L 3188 2381 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-50" d="M 588 4666 
L 2584 4666 
Q 3475 4666 3951 4270 
Q 4428 3875 4428 3144 
Q 4428 2409 3951 2014 
Q 3475 1619 2584 1619 
L 1791 1619 
L 1791 0 
L 588 0 
L 588 4666 
z
M 1791 3794 
L 1791 2491 
L 2456 2491 
Q 2806 2491 2997 2661 
Q 3188 2831 3188 3144 
Q 3188 3456 2997 3625 
Q 2806 3794 2456 3794 
L 1791 3794 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6f" d="M 2203 2784 
Q 1831 2784 1636 2517 
Q 1441 2250 1441 1747 
Q 1441 1244 1636 976 
Q 1831 709 2203 709 
Q 2569 709 2762 976 
Q 2956 1244 2956 1747 
Q 2956 2250 2762 2517 
Q 2569 2784 2203 2784 
z
M 2203 3584 
Q 3106 3584 3614 3096 
Q 4122 2609 4122 1747 
Q 4122 884 3614 396 
Q 3106 -91 2203 -91 
Q 1297 -91 786 396 
Q 275 884 275 1747 
Q 275 2609 786 3096 
Q 1297 3584 2203 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-28" d="M 2413 -844 
L 1484 -844 
Q 1006 -72 778 623 
Q 550 1319 550 2003 
Q 550 2688 779 3389 
Q 1009 4091 1484 4856 
L 2413 4856 
Q 2013 4116 1813 3408 
Q 1613 2700 1613 2009 
Q 1613 1319 1811 609 
Q 2009 -100 2413 -844 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6d" d="M 3781 2919 
Q 3994 3244 4286 3414 
Q 4578 3584 4928 3584 
Q 5531 3584 5847 3212 
Q 6163 2841 6163 2131 
L 6163 0 
L 5038 0 
L 5038 1825 
Q 5041 1866 5042 1909 
Q 5044 1953 5044 2034 
Q 5044 2406 4934 2573 
Q 4825 2741 4581 2741 
Q 4263 2741 4089 2478 
Q 3916 2216 3909 1719 
L 3909 0 
L 2784 0 
L 2784 1825 
Q 2784 2406 2684 2573 
Q 2584 2741 2328 2741 
Q 2006 2741 1831 2477 
Q 1656 2213 1656 1722 
L 1656 0 
L 531 0 
L 531 3500 
L 1656 3500 
L 1656 2988 
Q 1863 3284 2130 3434 
Q 2397 3584 2719 3584 
Q 3081 3584 3359 3409 
Q 3638 3234 3781 2919 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-29" d="M 513 -844 
Q 913 -100 1113 609 
Q 1313 1319 1313 2009 
Q 1313 2700 1113 3408 
Q 913 4116 513 4856 
L 1441 4856 
Q 1916 4091 2145 3389 
Q 2375 2688 2375 2003 
Q 2375 1319 2147 623 
Q 1919 -72 1441 -844 
L 513 -844 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-58" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="573.583984"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="608.398438"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="681.689453"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="750.390625"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="809.912109"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="844.189453"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="891.992188"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="926.269531"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="994.970703"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1066.162109"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1100.976562"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1146.679688"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1250.878906"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_19">
      <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" clip-path="url(#pc50dd6261f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <defs>
       <path id="mf3d7efa1c7" d="M 0 0 
L -3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#mf3d7efa1c7" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_11">
      <!-- −8 -->
      <g transform="translate(21.845937 318.044547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_21">
      <path d="M 42.113906 280.90325 
L 311.889906 280.90325 
" clip-path="url(#pc50dd6261f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#mf3d7efa1c7" x="42.113906" y="280.90325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_12">
      <!-- −6 -->
      <g transform="translate(21.845937 284.322547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_23">
      <path d="M 42.113906 247.18125 
L 311.889906 247.18125 
" clip-path="url(#pc50dd6261f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#mf3d7efa1c7" x="42.113906" y="247.18125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_13">
      <!-- −4 -->
      <g transform="translate(21.845937 250.600547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_25">
      <path d="M 42.113906 213.45925 
L 311.889906 213.45925 
" clip-path="url(#pc50dd6261f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#mf3d7efa1c7" x="42.113906" y="213.45925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_14">
      <!-- −2 -->
      <g transform="translate(21.845937 216.878547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_27">
      <path d="M 42.113906 179.73725 
L 311.889906 179.73725 
" clip-path="url(#pc50dd6261f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#mf3d7efa1c7" x="42.113906" y="179.73725" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 0 -->
      <g transform="translate(29.387656 183.156547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_29">
      <path d="M 42.113906 146.01525 
L 311.889906 146.01525 
" clip-path="url(#pc50dd6261f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#mf3d7efa1c7" x="42.113906" y="146.01525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 2 -->
      <g transform="translate(29.387656 149.434547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_31">
      <path d="M 42.113906 112.29325 
L 311.889906 112.29325 
" clip-path="url(#pc50dd6261f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#mf3d7efa1c7" x="42.113906" y="112.29325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 4 -->
      <g transform="translate(29.387656 115.712547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_33">
      <path d="M 42.113906 78.57125 
L 311.889906 78.57125 
" clip-path="url(#pc50dd6261f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#mf3d7efa1c7" x="42.113906" y="78.57125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 6 -->
      <g transform="translate(29.387656 81.990547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_35">
      <path d="M 42.113906 44.84925 
L 311.889906 44.84925 
" clip-path="url(#pc50dd6261f)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#mf3d7efa1c7" x="42.113906" y="44.84925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 8 -->
      <g transform="translate(29.387656 48.268547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_20">
     <!-- Relative Y Position (m) -->
     <g transform="translate(15.558281 250.792094) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-59" d="M -63 4666 
L 1253 4666 
L 2316 3003 
L 3378 4666 
L 4697 4666 
L 2919 1966 
L 2919 0 
L 1716 0 
L 1716 1966 
L -63 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-59" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="568.896484"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="603.710938"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="677.001953"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="745.703125"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="805.224609"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="839.501953"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="887.304688"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="921.582031"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="990.283203"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1061.474609"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1096.289062"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1141.992188"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1246.191406"/>
     </g>
    </g>
   </g>
   <g id="patch_6">
    <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_21">
    <g id="patch_8">
     <path d="M 200.10902 150.124824 
L 225.429957 150.124824 
Q 227.229957 150.124824 227.229957 148.324824 
L 227.229957 139.614511 
Q 227.229957 137.814511 225.429957 137.814511 
L 200.10902 137.814511 
Q 198.30902 137.814511 198.30902 139.614511 
L 198.30902 148.324824 
Q 198.30902 150.124824 200.10902 150.124824 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 3.0m -->
    <g style="fill: #ffffff" transform="translate(200.10902 146.453105) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-33" d="M 2981 2516 
Q 3453 2394 3698 2092 
Q 3944 1791 3944 1325 
Q 3944 631 3412 270 
Q 2881 -91 1863 -91 
Q 1503 -91 1142 -33 
Q 781 25 428 141 
L 428 1069 
Q 766 900 1098 814 
Q 1431 728 1753 728 
Q 2231 728 2486 893 
Q 2741 1059 2741 1369 
Q 2741 1688 2480 1852 
Q 2219 2016 1709 2016 
L 1228 2016 
L 1228 2791 
L 1734 2791 
Q 2188 2791 2409 2933 
Q 2631 3075 2631 3366 
Q 2631 3634 2415 3781 
Q 2200 3928 1806 3928 
Q 1516 3928 1219 3862 
Q 922 3797 628 3669 
L 628 4550 
Q 984 4650 1334 4700 
Q 1684 4750 2022 4750 
Q 2931 4750 3382 4451 
Q 3834 4153 3834 3553 
Q 3834 3144 3618 2883 
Q 3403 2622 2981 2516 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2e" d="M 653 1209 
L 1778 1209 
L 1778 0 
L 653 0 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-30" d="M 2944 2338 
Q 2944 3213 2780 3570 
Q 2616 3928 2228 3928 
Q 1841 3928 1675 3570 
Q 1509 3213 1509 2338 
Q 1509 1453 1675 1090 
Q 1841 728 2228 728 
Q 2613 728 2778 1090 
Q 2944 1453 2944 2338 
z
M 4147 2328 
Q 4147 1169 3647 539 
Q 3147 -91 2228 -91 
Q 1306 -91 806 539 
Q 306 1169 306 2328 
Q 306 3491 806 4120 
Q 1306 4750 2228 4750 
Q 3147 4750 3647 4120 
Q 4147 3491 4147 2328 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-33"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_22">
    <g id="patch_9">
     <path d="M 223.954075 126.279769 
L 249.275012 126.279769 
Q 251.075012 126.279769 251.075012 124.479769 
L 251.075012 115.769457 
Q 251.075012 113.969457 249.275012 113.969457 
L 223.954075 113.969457 
Q 222.154075 113.969457 222.154075 115.769457 
L 222.154075 124.479769 
Q 222.154075 126.279769 223.954075 126.279769 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 5.0m -->
    <g style="fill: #ffffff" transform="translate(223.954075 122.60805) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-35" d="M 678 4666 
L 3669 4666 
L 3669 3781 
L 1638 3781 
L 1638 3059 
Q 1775 3097 1914 3117 
Q 2053 3138 2203 3138 
Q 3056 3138 3531 2711 
Q 4006 2284 4006 1522 
Q 4006 766 3489 337 
Q 2972 -91 2053 -91 
Q 1656 -91 1267 -14 
Q 878 63 494 219 
L 494 1166 
Q 875 947 1217 837 
Q 1559 728 1863 728 
Q 2300 728 2551 942 
Q 2803 1156 2803 1522 
Q 2803 1891 2551 2103 
Q 2300 2316 1863 2316 
Q 1603 2316 1309 2248 
Q 1016 2181 678 2041 
L 678 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-35"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_23">
    <g id="patch_10">
     <path d="M 259.721657 90.512187 
L 285.042595 90.512187 
Q 286.842595 90.512187 286.842595 88.712187 
L 286.842595 80.001874 
Q 286.842595 78.201874 285.042595 78.201874 
L 259.721657 78.201874 
Q 257.921657 78.201874 257.921657 80.001874 
L 257.921657 88.712187 
Q 257.921657 90.512187 259.721657 90.512187 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 8.0m -->
    <g style="fill: #ffffff" transform="translate(259.721657 86.840468) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-38" d="M 2228 2088 
Q 1891 2088 1709 1903 
Q 1528 1719 1528 1375 
Q 1528 1031 1709 848 
Q 1891 666 2228 666 
Q 2563 666 2741 848 
Q 2919 1031 2919 1375 
Q 2919 1722 2741 1905 
Q 2563 2088 2228 2088 
z
M 1350 2484 
Q 925 2613 709 2878 
Q 494 3144 494 3541 
Q 494 4131 934 4440 
Q 1375 4750 2228 4750 
Q 3075 4750 3515 4442 
Q 3956 4134 3956 3541 
Q 3956 3144 3739 2878 
Q 3522 2613 3097 2484 
Q 3572 2353 3814 2058 
Q 4056 1763 4056 1313 
Q 4056 619 3595 264 
Q 3134 -91 2228 -91 
Q 1319 -91 855 264 
Q 391 619 391 1313 
Q 391 1763 633 2058 
Q 875 2353 1350 2484 
z
M 1631 3419 
Q 1631 3141 1786 2991 
Q 1941 2841 2228 2841 
Q 2509 2841 2662 2991 
Q 2816 3141 2816 3419 
Q 2816 3697 2662 3845 
Q 2509 3994 2228 3994 
Q 1941 3994 1786 3844 
Q 1631 3694 1631 3419 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-38"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_24">
    <!-- Close-Distance Cell (Neuron 51) -->
    <g transform="translate(69.464094 16.411875) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2d" d="M 347 2297 
L 2309 2297 
L 2309 1388 
L 347 1388 
L 347 2297 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-44" d="M 1791 3756 
L 1791 909 
L 2222 909 
Q 2959 909 3348 1275 
Q 3738 1641 3738 2338 
Q 3738 3031 3350 3393 
Q 2963 3756 2222 3756 
L 1791 3756 
z
M 588 4666 
L 1856 4666 
Q 2919 4666 3439 4514 
Q 3959 4363 4331 4000 
Q 4659 3684 4818 3271 
Q 4978 2859 4978 2338 
Q 4978 1809 4818 1395 
Q 4659 981 4331 666 
Q 3956 303 3431 151 
Q 2906 0 1856 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4e" d="M 588 4666 
L 1931 4666 
L 3628 1466 
L 3628 4666 
L 4769 4666 
L 4769 0 
L 3425 0 
L 1728 3200 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-75" d="M 500 1363 
L 500 3500 
L 1625 3500 
L 1625 3150 
Q 1625 2866 1622 2436 
Q 1619 2006 1619 1863 
Q 1619 1441 1641 1255 
Q 1663 1069 1716 984 
Q 1784 875 1895 815 
Q 2006 756 2150 756 
Q 2500 756 2700 1025 
Q 2900 1294 2900 1772 
L 2900 3500 
L 4019 3500 
L 4019 0 
L 2900 0 
L 2900 506 
Q 2647 200 2364 54 
Q 2081 -91 1741 -91 
Q 1134 -91 817 281 
Q 500 653 500 1363 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-31" d="M 750 831 
L 1813 831 
L 1813 3847 
L 722 3622 
L 722 4441 
L 1806 4666 
L 2950 4666 
L 2950 831 
L 4013 831 
L 4013 0 
L 750 0 
L 750 831 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-43"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="73.388672"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="107.666016"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="176.367188"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="235.888672"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="303.710938"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="345.214844"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="428.222656"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="462.5"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="522.021484"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="569.824219"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="637.304688"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="708.496094"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="767.773438"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="835.595703"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="870.410156"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="943.798828"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="1011.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="1045.898438"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1080.175781"/>
     <use xlink:href="#DejaVuSans-Bold-28" x="1114.990234"/>
     <use xlink:href="#DejaVuSans-Bold-4e" x="1160.693359"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1244.384766"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1312.207031"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1383.398438"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="1432.714844"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1501.416016"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1572.607422"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="1607.421875"/>
     <use xlink:href="#DejaVuSans-Bold-31" x="1677.001953"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="1746.582031"/>
    </g>
    <!-- 2D Activation Map -->
    <g transform="translate(114.950656 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-32" d="M 1844 884 
L 3897 884 
L 3897 0 
L 506 0 
L 506 884 
L 2209 2388 
Q 2438 2594 2547 2791 
Q 2656 2988 2656 3200 
Q 2656 3528 2436 3728 
Q 2216 3928 1850 3928 
Q 1569 3928 1234 3808 
Q 900 3688 519 3450 
L 519 4475 
Q 925 4609 1322 4679 
Q 1719 4750 2100 4750 
Q 2938 4750 3402 4381 
Q 3866 4013 3866 3353 
Q 3866 2972 3669 2642 
Q 3472 2313 2841 1759 
L 1844 884 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-41" d="M 3419 850 
L 1538 850 
L 1241 0 
L 31 0 
L 1759 4666 
L 3194 4666 
L 4922 0 
L 3713 0 
L 3419 850 
z
M 1838 1716 
L 3116 1716 
L 2478 3572 
L 1838 1716 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4d" d="M 588 4666 
L 2119 4666 
L 3181 2169 
L 4250 4666 
L 5778 4666 
L 5778 0 
L 4641 0 
L 4641 3413 
L 3566 897 
L 2803 897 
L 1728 3413 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-32"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="152.587891"/>
     <use xlink:href="#DejaVuSans-Bold-41" x="187.402344"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="264.794922"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="324.072266"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="371.875"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="406.152344"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.337891"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="538.818359"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="586.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="620.898438"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="689.599609"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="760.791016"/>
     <use xlink:href="#DejaVuSans-Bold-4d" x="795.605469"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="895.117188"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="962.597656"/>
    </g>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="m828082a7c1" d="M 0 -5.477226 
L -1.229714 -1.692556 
L -5.209151 -1.692556 
L -1.989719 0.646499 
L -3.219432 4.431169 
L -0 2.092114 
L 3.219432 4.431169 
L 1.989719 0.646499 
L 5.209151 -1.692556 
L 1.229714 -1.692556 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#pc50dd6261f)">
     <use xlink:href="#m828082a7c1" x="177.001906" y="179.73725" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_11">
     <path d="M 242.388656 62.99175 
L 307.889906 62.99175 
L 307.889906 48.84925 
L 242.388656 48.84925 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="PathCollection_2">
     <g>
      <use xlink:href="#m828082a7c1" x="253.588656" y="56.028" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
     </g>
    </g>
    <g id="text_25">
     <!-- Observer -->
     <g transform="translate(267.988656 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4f"/>
      <use xlink:href="#DejaVuSans-62" x="78.710938"/>
      <use xlink:href="#DejaVuSans-73" x="142.1875"/>
      <use xlink:href="#DejaVuSans-65" x="194.287109"/>
      <use xlink:href="#DejaVuSans-72" x="255.810547"/>
      <use xlink:href="#DejaVuSans-76" x="296.923828"/>
      <use xlink:href="#DejaVuSans-65" x="356.103516"/>
      <use xlink:href="#DejaVuSans-72" x="417.626953"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_12">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
L 813.937288 44.84925 
L 436.259015 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="m9b18756dce" d="M 440.975868 -37.642188 
L 440.975868 -37.642188 
L 450.409572 -252.743566 
L 459.843277 -255.332454 
L 469.276982 -257.925442 
L 478.710686 -294.571711 
L 488.144391 -169.098984 
L 497.578096 -274.135829 
L 507.011801 -165.719881 
L 516.445505 -244.306655 
L 525.87921 -176.805718 
L 535.312915 -195.760232 
L 544.746619 -127.872921 
L 554.180324 -123.892606 
L 563.614029 -132.294757 
L 573.047733 -139.878347 
L 582.481438 -130.489015 
L 591.915143 -92.220301 
L 601.348847 -66.779161 
L 610.782552 -86.519897 
L 620.216257 -59.845906 
L 629.649962 -65.715444 
L 639.083666 -53.032012 
L 648.517371 -51.9928 
L 657.951076 -48.744902 
L 667.38478 -45.096941 
L 676.818485 -41.09161 
L 686.25219 -39.791055 
L 695.685894 -38.720874 
L 705.119599 -37.884626 
L 714.553304 -37.910676 
L 714.553304 -37.642188 
L 714.553304 -37.642188 
L 705.119599 -37.642188 
L 695.685894 -37.642188 
L 686.25219 -37.642188 
L 676.818485 -37.642188 
L 667.38478 -37.642188 
L 657.951076 -37.642188 
L 648.517371 -37.642188 
L 639.083666 -37.642188 
L 629.649962 -37.642188 
L 620.216257 -37.642188 
L 610.782552 -37.642188 
L 601.348847 -37.642188 
L 591.915143 -37.642188 
L 582.481438 -37.642188 
L 573.047733 -37.642188 
L 563.614029 -37.642188 
L 554.180324 -37.642188 
L 544.746619 -37.642188 
L 535.312915 -37.642188 
L 525.87921 -37.642188 
L 516.445505 -37.642188 
L 507.011801 -37.642188 
L 497.578096 -37.642188 
L 488.144391 -37.642188 
L 478.710686 -37.642188 
L 469.276982 -37.642188 
L 459.843277 -37.642188 
L 450.409572 -37.642188 
L 440.975868 -37.642188 
z
" style="stroke: #e31a1c; stroke-opacity: 0.3"/>
    </defs>
    <g clip-path="url(#p6bb8fc07e1)">
     <use xlink:href="#m9b18756dce" x="0" y="352.267438" style="fill: #e31a1c; fill-opacity: 0.3; stroke: #e31a1c; stroke-opacity: 0.3"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10">
     <g id="line2d_37">
      <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" clip-path="url(#p6bb8fc07e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#meba4412ae3" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 0 -->
      <g transform="translate(433.39589 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_39">
      <path d="M 483.468799 314.62525 
L 483.468799 44.84925 
" clip-path="url(#p6bb8fc07e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#meba4412ae3" x="483.468799" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 1 -->
      <g transform="translate(480.605674 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_41">
      <path d="M 530.678584 314.62525 
L 530.678584 44.84925 
" clip-path="url(#p6bb8fc07e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#meba4412ae3" x="530.678584" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 2 -->
      <g transform="translate(527.815459 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_43">
      <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#p6bb8fc07e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#meba4412ae3" x="577.888368" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 3 -->
      <g transform="translate(575.025243 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_45">
      <path d="M 625.098152 314.62525 
L 625.098152 44.84925 
" clip-path="url(#p6bb8fc07e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#meba4412ae3" x="625.098152" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 4 -->
      <g transform="translate(622.235027 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_47">
      <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#p6bb8fc07e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#meba4412ae3" x="672.307936" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 5 -->
      <g transform="translate(669.444811 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_49">
      <path d="M 719.51772 314.62525 
L 719.51772 44.84925 
" clip-path="url(#p6bb8fc07e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#meba4412ae3" x="719.51772" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 6 -->
      <g transform="translate(716.654595 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_51">
      <path d="M 766.727504 314.62525 
L 766.727504 44.84925 
" clip-path="url(#p6bb8fc07e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#meba4412ae3" x="766.727504" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 7 -->
      <g transform="translate(763.864379 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-37"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_53">
      <path d="M 813.937288 314.62525 
L 813.937288 44.84925 
" clip-path="url(#p6bb8fc07e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#meba4412ae3" x="813.937288" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 8 -->
      <g transform="translate(811.074163 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_35">
     <!-- Inter-Agent Distance (m) -->
     <g transform="translate(547.755261 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-49" d="M 588 4666 
L 1791 4666 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-49"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="37.207031"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="108.398438"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="156.201172"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="224.023438"/>
      <use xlink:href="#DejaVuSans-Bold-2d" x="273.339844"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="314.84375"/>
      <use xlink:href="#DejaVuSans-Bold-67" x="392.236328"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="463.818359"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="531.640625"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="602.832031"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="650.634766"/>
      <use xlink:href="#DejaVuSans-Bold-44" x="685.449219"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="768.457031"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="802.734375"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="862.255859"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="910.058594"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="977.539062"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="1048.730469"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="1108.007812"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1175.830078"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1210.644531"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1256.347656"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1360.546875"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_55">
      <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" clip-path="url(#p6bb8fc07e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#mf3d7efa1c7" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 0.0 -->
      <g transform="translate(414.946203 318.044547) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_57">
      <path d="M 436.259015 269.365125 
L 813.937288 269.365125 
" clip-path="url(#p6bb8fc07e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#mf3d7efa1c7" x="436.259015" y="269.365125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 0.1 -->
      <g transform="translate(414.946203 272.784422) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_59">
      <path d="M 436.259015 224.104999 
L 813.937288 224.104999 
" clip-path="url(#p6bb8fc07e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#mf3d7efa1c7" x="436.259015" y="224.104999" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 0.2 -->
      <g transform="translate(414.946203 227.524296) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_61">
      <path d="M 436.259015 178.844874 
L 813.937288 178.844874 
" clip-path="url(#p6bb8fc07e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#mf3d7efa1c7" x="436.259015" y="178.844874" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 0.3 -->
      <g transform="translate(414.946203 182.264171) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-33" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_63">
      <path d="M 436.259015 133.584749 
L 813.937288 133.584749 
" clip-path="url(#p6bb8fc07e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#mf3d7efa1c7" x="436.259015" y="133.584749" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 0.4 -->
      <g transform="translate(414.946203 137.004045) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_65">
      <path d="M 436.259015 88.324623 
L 813.937288 88.324623 
" clip-path="url(#p6bb8fc07e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#mf3d7efa1c7" x="436.259015" y="88.324623" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 0.5 -->
      <g transform="translate(414.946203 91.74392) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_42">
     <!-- Neural Activation -->
     <g transform="translate(408.658547 233.746391) rotate(-90) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="line2d_67">
    <path d="M 440.975868 314.62525 
L 450.409572 99.523871 
L 459.843277 96.934984 
L 469.276982 94.341995 
L 478.710686 57.695726 
L 488.144391 183.168454 
L 497.578096 78.131609 
L 507.011801 186.547556 
L 516.445505 107.960782 
L 525.87921 175.46172 
L 535.312915 156.507206 
L 544.746619 224.394517 
L 554.180324 228.374831 
L 563.614029 219.972681 
L 573.047733 212.38909 
L 582.481438 221.778422 
L 591.915143 260.047137 
L 601.348847 285.488276 
L 610.782552 265.74754 
L 620.216257 292.421532 
L 629.649962 286.551993 
L 639.083666 299.235426 
L 648.517371 300.274637 
L 657.951076 303.522535 
L 667.38478 307.170497 
L 676.818485 311.175827 
L 686.25219 312.476383 
L 695.685894 313.546563 
L 705.119599 314.382812 
L 714.553304 314.356762 
" clip-path="url(#p6bb8fc07e1)" style="fill: none; stroke: #e31a1c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_68">
    <path d="M 478.710686 314.62525 
L 478.710686 44.84925 
" clip-path="url(#p6bb8fc07e1)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #e31a1c; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_69">
    <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#p6bb8fc07e1)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_70">
    <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#p6bb8fc07e1)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="patch_13">
    <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_14">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_43">
    <g id="patch_15">
     <path d="M 700.612124 114.0718 
L 795.053374 114.0718 
Q 798.253374 114.0718 798.253374 110.8718 
L 798.253374 58.33805 
Q 798.253374 55.13805 795.053374 55.13805 
L 700.612124 55.13805 
Q 697.412124 55.13805 697.412124 58.33805 
L 697.412124 110.8718 
Q 697.412124 114.0718 700.612124 114.0718 
z
" style="fill: #ffffff; opacity: 0.9; stroke: #808080; stroke-linejoin: miter"/>
    </g>
    <!-- Peak Distance: 0.9m -->
    <g transform="translate(712.787124 64.4168) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-3a" d="M 750 794 
L 1409 794 
L 1409 0 
L 750 0 
L 750 794 
z
M 750 3309 
L 1409 3309 
L 1409 2516 
L 750 2516 
L 750 3309 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-44" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="346.179688"/>
     <use xlink:href="#DejaVuSans-73" x="373.962891"/>
     <use xlink:href="#DejaVuSans-74" x="426.0625"/>
     <use xlink:href="#DejaVuSans-61" x="465.271484"/>
     <use xlink:href="#DejaVuSans-6e" x="526.550781"/>
     <use xlink:href="#DejaVuSans-63" x="589.929688"/>
     <use xlink:href="#DejaVuSans-65" x="644.910156"/>
     <use xlink:href="#DejaVuSans-3a" x="706.433594"/>
     <use xlink:href="#DejaVuSans-20" x="740.125"/>
     <use xlink:href="#DejaVuSans-30" x="771.912109"/>
     <use xlink:href="#DejaVuSans-2e" x="835.535156"/>
     <use xlink:href="#DejaVuSans-39" x="867.322266"/>
     <use xlink:href="#DejaVuSans-6d" x="930.945312"/>
    </g>
    <!-- Max Activation: 0.568 -->
    <g transform="translate(707.779624 73.37505) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-4d"/>
     <use xlink:href="#DejaVuSans-61" x="86.279297"/>
     <use xlink:href="#DejaVuSans-78" x="147.558594"/>
     <use xlink:href="#DejaVuSans-20" x="206.738281"/>
     <use xlink:href="#DejaVuSans-41" x="238.525391"/>
     <use xlink:href="#DejaVuSans-63" x="305.183594"/>
     <use xlink:href="#DejaVuSans-74" x="360.164062"/>
     <use xlink:href="#DejaVuSans-69" x="399.373047"/>
     <use xlink:href="#DejaVuSans-76" x="427.15625"/>
     <use xlink:href="#DejaVuSans-61" x="486.335938"/>
     <use xlink:href="#DejaVuSans-74" x="547.615234"/>
     <use xlink:href="#DejaVuSans-69" x="586.824219"/>
     <use xlink:href="#DejaVuSans-6f" x="614.607422"/>
     <use xlink:href="#DejaVuSans-6e" x="675.789062"/>
     <use xlink:href="#DejaVuSans-3a" x="739.167969"/>
     <use xlink:href="#DejaVuSans-20" x="772.859375"/>
     <use xlink:href="#DejaVuSans-30" x="804.646484"/>
     <use xlink:href="#DejaVuSans-2e" x="868.269531"/>
     <use xlink:href="#DejaVuSans-35" x="900.056641"/>
     <use xlink:href="#DejaVuSans-36" x="963.679688"/>
     <use xlink:href="#DejaVuSans-38" x="1027.302734"/>
    </g>
    <!-- Sparsity: 0.495 -->
    <g transform="translate(734.883374 82.3333) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-70" x="63.476562"/>
     <use xlink:href="#DejaVuSans-61" x="126.953125"/>
     <use xlink:href="#DejaVuSans-72" x="188.232422"/>
     <use xlink:href="#DejaVuSans-73" x="229.345703"/>
     <use xlink:href="#DejaVuSans-69" x="281.445312"/>
     <use xlink:href="#DejaVuSans-74" x="309.228516"/>
     <use xlink:href="#DejaVuSans-79" x="348.4375"/>
     <use xlink:href="#DejaVuSans-3a" x="400.367188"/>
     <use xlink:href="#DejaVuSans-20" x="434.058594"/>
     <use xlink:href="#DejaVuSans-30" x="465.845703"/>
     <use xlink:href="#DejaVuSans-2e" x="529.46875"/>
     <use xlink:href="#DejaVuSans-34" x="561.255859"/>
     <use xlink:href="#DejaVuSans-39" x="624.878906"/>
     <use xlink:href="#DejaVuSans-35" x="688.501953"/>
    </g>
    <!-- Peak Significance: 2.94 -->
    <g transform="translate(702.065874 91.29155) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-53" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="332.654297"/>
     <use xlink:href="#DejaVuSans-67" x="360.4375"/>
     <use xlink:href="#DejaVuSans-6e" x="423.914062"/>
     <use xlink:href="#DejaVuSans-69" x="487.292969"/>
     <use xlink:href="#DejaVuSans-66" x="515.076172"/>
     <use xlink:href="#DejaVuSans-69" x="550.28125"/>
     <use xlink:href="#DejaVuSans-63" x="578.064453"/>
     <use xlink:href="#DejaVuSans-61" x="633.044922"/>
     <use xlink:href="#DejaVuSans-6e" x="694.324219"/>
     <use xlink:href="#DejaVuSans-63" x="757.703125"/>
     <use xlink:href="#DejaVuSans-65" x="812.683594"/>
     <use xlink:href="#DejaVuSans-3a" x="874.207031"/>
     <use xlink:href="#DejaVuSans-20" x="907.898438"/>
     <use xlink:href="#DejaVuSans-32" x="939.685547"/>
     <use xlink:href="#DejaVuSans-2e" x="1003.308594"/>
     <use xlink:href="#DejaVuSans-39" x="1035.095703"/>
     <use xlink:href="#DejaVuSans-34" x="1098.71875"/>
    </g>
    <!-- Selectivity Index: 1.070 -->
    <g transform="translate(700.612124 100.2498) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-65" x="63.476562"/>
     <use xlink:href="#DejaVuSans-6c" x="125"/>
     <use xlink:href="#DejaVuSans-65" x="152.783203"/>
     <use xlink:href="#DejaVuSans-63" x="214.306641"/>
     <use xlink:href="#DejaVuSans-74" x="269.287109"/>
     <use xlink:href="#DejaVuSans-69" x="308.496094"/>
     <use xlink:href="#DejaVuSans-76" x="336.279297"/>
     <use xlink:href="#DejaVuSans-69" x="395.458984"/>
     <use xlink:href="#DejaVuSans-74" x="423.242188"/>
     <use xlink:href="#DejaVuSans-79" x="462.451172"/>
     <use xlink:href="#DejaVuSans-20" x="521.630859"/>
     <use xlink:href="#DejaVuSans-49" x="553.417969"/>
     <use xlink:href="#DejaVuSans-6e" x="582.910156"/>
     <use xlink:href="#DejaVuSans-64" x="646.289062"/>
     <use xlink:href="#DejaVuSans-65" x="709.765625"/>
     <use xlink:href="#DejaVuSans-78" x="769.539062"/>
     <use xlink:href="#DejaVuSans-3a" x="828.71875"/>
     <use xlink:href="#DejaVuSans-20" x="862.410156"/>
     <use xlink:href="#DejaVuSans-31" x="894.197266"/>
     <use xlink:href="#DejaVuSans-2e" x="957.820312"/>
     <use xlink:href="#DejaVuSans-30" x="989.607422"/>
     <use xlink:href="#DejaVuSans-37" x="1053.230469"/>
     <use xlink:href="#DejaVuSans-30" x="1116.853516"/>
    </g>
    <!-- Peak Width: 1.8m -->
    <g transform="translate(724.529624 109.20805) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-57" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="365.804688"/>
     <use xlink:href="#DejaVuSans-64" x="393.587891"/>
     <use xlink:href="#DejaVuSans-74" x="457.064453"/>
     <use xlink:href="#DejaVuSans-68" x="496.273438"/>
     <use xlink:href="#DejaVuSans-3a" x="559.652344"/>
     <use xlink:href="#DejaVuSans-20" x="593.34375"/>
     <use xlink:href="#DejaVuSans-31" x="625.130859"/>
     <use xlink:href="#DejaVuSans-2e" x="688.753906"/>
     <use xlink:href="#DejaVuSans-38" x="720.541016"/>
     <use xlink:href="#DejaVuSans-6d" x="784.164062"/>
    </g>
   </g>
   <g id="text_44">
    <!-- Distance Tuning Curve -->
    <g transform="translate(549.284402 16.318125) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-54" d="M 31 4666 
L 4331 4666 
L 4331 3756 
L 2784 3756 
L 2784 0 
L 1581 0 
L 1581 3756 
L 31 3756 
L 31 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-44"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
     <use xlink:href="#DejaVuSans-Bold-54" x="525.195312"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="582.408203"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="653.599609"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="724.791016"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="759.068359"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="830.259766"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="901.841797"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="936.65625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1010.044922"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1081.236328"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="1130.552734"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1195.738281"/>
    </g>
    <!-- Peak: 0.9m, Sparsity: 0.495 -->
    <g transform="translate(531.359402 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-6b" d="M 538 4863 
L 1656 4863 
L 1656 2216 
L 2944 3500 
L 4244 3500 
L 2534 1894 
L 4378 0 
L 3022 0 
L 1656 1459 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-3a" d="M 716 3500 
L 1844 3500 
L 1844 2291 
L 716 2291 
L 716 3500 
z
M 716 1209 
L 1844 1209 
L 1844 0 
L 716 0 
L 716 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-39" d="M 641 103 
L 641 966 
Q 928 831 1190 764 
Q 1453 697 1709 697 
Q 2247 697 2547 995 
Q 2847 1294 2900 1881 
Q 2688 1725 2447 1647 
Q 2206 1569 1925 1569 
Q 1209 1569 770 1986 
Q 331 2403 331 3084 
Q 331 3838 820 4291 
Q 1309 4744 2131 4744 
Q 3044 4744 3544 4128 
Q 4044 3513 4044 2388 
Q 4044 1231 3459 570 
Q 2875 -91 1856 -91 
Q 1528 -91 1228 -42 
Q 928 6 641 103 
z
M 2125 2350 
Q 2441 2350 2600 2554 
Q 2759 2759 2759 3169 
Q 2759 3575 2600 3781 
Q 2441 3988 2125 3988 
Q 1809 3988 1650 3781 
Q 1491 3575 1491 3169 
Q 1491 2759 1650 2554 
Q 1809 2350 2125 2350 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2c" d="M 653 1209 
L 1778 1209 
L 1778 256 
L 1006 -909 
L 341 -909 
L 653 256 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-53" d="M 3834 4519 
L 3834 3531 
Q 3450 3703 3084 3790 
Q 2719 3878 2394 3878 
Q 1963 3878 1756 3759 
Q 1550 3641 1550 3391 
Q 1550 3203 1689 3098 
Q 1828 2994 2194 2919 
L 2706 2816 
Q 3484 2659 3812 2340 
Q 4141 2022 4141 1434 
Q 4141 663 3683 286 
Q 3225 -91 2284 -91 
Q 1841 -91 1394 -6 
Q 947 78 500 244 
L 500 1259 
Q 947 1022 1364 901 
Q 1781 781 2169 781 
Q 2563 781 2772 912 
Q 2981 1044 2981 1288 
Q 2981 1506 2839 1625 
Q 2697 1744 2272 1838 
L 1806 1941 
Q 1106 2091 782 2419 
Q 459 2747 459 3303 
Q 459 4000 909 4375 
Q 1359 4750 2203 4750 
Q 2588 4750 2994 4692 
Q 3400 4634 3834 4519 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-79" d="M 78 3500 
L 1197 3500 
L 2138 1125 
L 2938 3500 
L 4056 3500 
L 2584 -331 
Q 2363 -916 2067 -1148 
Q 1772 -1381 1288 -1381 
L 641 -1381 
L 641 -647 
L 991 -647 
Q 1275 -647 1404 -556 
Q 1534 -466 1606 -231 
L 1638 -134 
L 78 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-34" d="M 2356 3675 
L 1038 1722 
L 2356 1722 
L 2356 3675 
z
M 2156 4666 
L 3494 4666 
L 3494 1722 
L 4159 1722 
L 4159 850 
L 3494 850 
L 3494 0 
L 2356 0 
L 2356 850 
L 288 850 
L 288 1881 
L 2156 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-50"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="73.291016"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="141.113281"/>
     <use xlink:href="#DejaVuSans-Bold-6b" x="208.59375"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="275.097656"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="315.087891"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="349.902344"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="419.482422"/>
     <use xlink:href="#DejaVuSans-Bold-39" x="457.470703"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="527.050781"/>
     <use xlink:href="#DejaVuSans-Bold-2c" x="631.25"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="669.238281"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="704.052734"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="776.074219"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="847.65625"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="915.136719"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="964.453125"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1023.974609"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="1058.251953"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="1106.054688"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="1171.240234"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1211.230469"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1246.044922"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="1315.625"/>
     <use xlink:href="#DejaVuSans-Bold-34" x="1353.613281"/>
     <use xlink:href="#DejaVuSans-Bold-39" x="1423.193359"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="1492.773438"/>
    </g>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="mf6021c03a1" d="M 0 5 
C 1.326016 5 2.597899 4.473168 3.535534 3.535534 
C 4.473168 2.597899 5 1.326016 5 0 
C 5 -1.326016 4.473168 -2.597899 3.535534 -3.535534 
C 2.597899 -4.473168 1.326016 -5 0 -5 
C -1.326016 -5 -2.597899 -4.473168 -3.535534 -3.535534 
C -4.473168 -2.597899 -5 -1.326016 -5 0 
C -5 1.326016 -4.473168 2.597899 -3.535534 3.535534 
C -2.597899 4.473168 -1.326016 5 0 5 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#p6bb8fc07e1)">
     <use xlink:href="#mf6021c03a1" x="478.710686" y="57.695726" style="fill: #e31a1c; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="patch_16">
     <path d="M 691.429788 86.47675 
L 808.337288 86.47675 
Q 809.937288 86.47675 809.937288 84.87675 
L 809.937288 50.44925 
Q 809.937288 48.84925 808.337288 48.84925 
L 691.429788 48.84925 
Q 689.829788 48.84925 689.829788 50.44925 
L 689.829788 84.87675 
Q 689.829788 86.47675 691.429788 86.47675 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_71">
     <path d="M 693.029788 55.328 
L 701.029788 55.328 
L 709.029788 55.328 
" style="fill: none; stroke: #e31a1c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
    </g>
    <g id="text_45">
     <!-- Tuning Curve -->
     <g transform="translate(715.429788 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-54"/>
      <use xlink:href="#DejaVuSans-75" x="45.958984"/>
      <use xlink:href="#DejaVuSans-6e" x="109.337891"/>
      <use xlink:href="#DejaVuSans-69" x="172.716797"/>
      <use xlink:href="#DejaVuSans-6e" x="200.5"/>
      <use xlink:href="#DejaVuSans-67" x="263.878906"/>
      <use xlink:href="#DejaVuSans-20" x="327.355469"/>
      <use xlink:href="#DejaVuSans-43" x="359.142578"/>
      <use xlink:href="#DejaVuSans-75" x="428.966797"/>
      <use xlink:href="#DejaVuSans-72" x="492.345703"/>
      <use xlink:href="#DejaVuSans-76" x="533.458984"/>
      <use xlink:href="#DejaVuSans-65" x="592.638672"/>
     </g>
    </g>
    <g id="line2d_72">
     <path d="M 693.029788 67.0705 
L 701.029788 67.0705 
L 709.029788 67.0705 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_46">
     <!-- Close threshold (3.0m) -->
     <g transform="translate(715.429788 69.8705) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6c" x="69.824219"/>
      <use xlink:href="#DejaVuSans-6f" x="97.607422"/>
      <use xlink:href="#DejaVuSans-73" x="158.789062"/>
      <use xlink:href="#DejaVuSans-65" x="210.888672"/>
      <use xlink:href="#DejaVuSans-20" x="272.412109"/>
      <use xlink:href="#DejaVuSans-74" x="304.199219"/>
      <use xlink:href="#DejaVuSans-68" x="343.408203"/>
      <use xlink:href="#DejaVuSans-72" x="406.787109"/>
      <use xlink:href="#DejaVuSans-65" x="445.650391"/>
      <use xlink:href="#DejaVuSans-73" x="507.173828"/>
      <use xlink:href="#DejaVuSans-68" x="559.273438"/>
      <use xlink:href="#DejaVuSans-6f" x="622.652344"/>
      <use xlink:href="#DejaVuSans-6c" x="683.833984"/>
      <use xlink:href="#DejaVuSans-64" x="711.617188"/>
      <use xlink:href="#DejaVuSans-20" x="775.09375"/>
      <use xlink:href="#DejaVuSans-28" x="806.880859"/>
      <use xlink:href="#DejaVuSans-33" x="845.894531"/>
      <use xlink:href="#DejaVuSans-2e" x="909.517578"/>
      <use xlink:href="#DejaVuSans-30" x="941.304688"/>
      <use xlink:href="#DejaVuSans-6d" x="1004.927734"/>
      <use xlink:href="#DejaVuSans-29" x="1102.339844"/>
     </g>
    </g>
    <g id="line2d_73">
     <path d="M 693.029788 78.813 
L 701.029788 78.813 
L 709.029788 78.813 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_47">
     <!-- Far threshold (5.0m) -->
     <g transform="translate(715.429788 81.613) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" x="48.394531"/>
      <use xlink:href="#DejaVuSans-72" x="109.673828"/>
      <use xlink:href="#DejaVuSans-20" x="150.787109"/>
      <use xlink:href="#DejaVuSans-74" x="182.574219"/>
      <use xlink:href="#DejaVuSans-68" x="221.783203"/>
      <use xlink:href="#DejaVuSans-72" x="285.162109"/>
      <use xlink:href="#DejaVuSans-65" x="324.025391"/>
      <use xlink:href="#DejaVuSans-73" x="385.548828"/>
      <use xlink:href="#DejaVuSans-68" x="437.648438"/>
      <use xlink:href="#DejaVuSans-6f" x="501.027344"/>
      <use xlink:href="#DejaVuSans-6c" x="562.208984"/>
      <use xlink:href="#DejaVuSans-64" x="589.992188"/>
      <use xlink:href="#DejaVuSans-20" x="653.46875"/>
      <use xlink:href="#DejaVuSans-28" x="685.255859"/>
      <use xlink:href="#DejaVuSans-35" x="724.269531"/>
      <use xlink:href="#DejaVuSans-2e" x="787.892578"/>
      <use xlink:href="#DejaVuSans-30" x="819.679688"/>
      <use xlink:href="#DejaVuSans-6d" x="883.302734"/>
      <use xlink:href="#DejaVuSans-29" x="980.714844"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_17">
    <path d="M 330.77382 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 330.77382 71.82685 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAABcAAAHBCAYAAACCOiAmAAACGUlEQVR4nO2b0W0bURDEnqRz3+k7VpwCjvkcAQzIAhbEzuzpLMiPc379nBHXOddq9nnOJp9zrnO+ZsPX5u0ch7eWO1URqYqIuope811bWgtSFZGqiFRFpJ0jvVog6ip6zXue3/Ee0eOcn9mXOVPzhv9nw6/z2A0fm7+Ww3cPxfVadg/FzJmqiJgD9ZpXRcAcqNe8KgLmQL3mVREwB+o1r4qAOVCveVUEzIF6zYdVHJsPe24ONHPAe0Q9z5HMkal5a0EyR3oqIuZAM//0cO9aMkd6KiLmQDPn4bNfWs3X8p4Nzxy5nq/v3fDry7qWlzZQs/myit61PLXmJ/M7a/PlEYnXkvmdqogUKJL5v4bver5dS0dEeM2rIuIN1GteFRFvoF5z9Vo6ojtVEamKSFX8/HDvWjoiZHpE6kC95lURhpsDzfxOR4R0RIjavCrCcHOgXvOqCMPNgWZ+x3tEfSWCZI5URUQdaOY4vPeWO95Ax+bvZRW/tVV8/5kNHwe6W7nY/Hrsar7+rxxtoJnz8KoIZI5M22JeS+aAt4renRco4g3Ua16gSOZIVUQKFPF+EpnX4jXv/D893LuWqohURaRAEa+5eS1VEfCaFyiSOdKfLUjmiLeK3p0XKOIN1GteoEjmSFVErt/aQIe/hVzvfPmWK16L1nwovt65dXiBIu2ch7cWoioiVRGpiojXvECRjgipikhVRLw7967lLyPgue9DbEF4AAAAAElFTkSuQmCC" id="image2e6a67517d" transform="scale(1 -1) translate(0 -215.52)" x="330.72" y="-71.52" width="11.04" height="215.52"/>
   <g id="matplotlib.axis_5"/>
   <g id="matplotlib.axis_6">
    <g id="ytick_16">
     <g id="line2d_74">
      <defs>
       <path id="m90d081ef54" d="M 0 0 
L 3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m90d081ef54" x="341.56486" y="287.64765" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_48">
      <!-- 0.0 -->
      <g transform="translate(348.56486 291.066947) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_75">
      <g>
       <use xlink:href="#m90d081ef54" x="341.56486" y="244.48349" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_49">
      <!-- 0.2 -->
      <g transform="translate(348.56486 247.902787) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_76">
      <g>
       <use xlink:href="#m90d081ef54" x="341.56486" y="201.31933" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_50">
      <!-- 0.4 -->
      <g transform="translate(348.56486 204.738627) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_77">
      <g>
       <use xlink:href="#m90d081ef54" x="341.56486" y="158.15517" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_51">
      <!-- 0.6 -->
      <g transform="translate(348.56486 161.574467) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_78">
      <g>
       <use xlink:href="#m90d081ef54" x="341.56486" y="114.99101" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_52">
      <!-- 0.8 -->
      <g transform="translate(348.56486 118.410307) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-38" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_79">
      <g>
       <use xlink:href="#m90d081ef54" x="341.56486" y="71.82685" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_53">
      <!-- 1.0 -->
      <g transform="translate(348.56486 75.246147) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_54">
     <!-- Neural Activation -->
     <g transform="translate(369.519391 125.728109) rotate(-270) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_18">
    <path d="M 330.77382 287.64765 
L 336.16934 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 336.16934 71.82685 
L 330.77382 71.82685 
L 330.77382 287.64765 
z
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="pc50dd6261f">
   <rect x="42.113906" y="44.84925" width="269.776" height="269.776"/>
  </clipPath>
  <clipPath id="p6bb8fc07e1">
   <rect x="436.259015" y="44.84925" width="377.678273" height="269.776"/>
  </clipPath>
 </defs>
</svg>
