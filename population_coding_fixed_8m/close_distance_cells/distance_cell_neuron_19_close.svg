<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="824.000413pt" height="352.267438pt" viewBox="0 0 824.000413 352.267438" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:56:26.264960</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 352.267438 
L 824.000413 352.267438 
L 824.000413 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
L 311.889906 44.84925 
L 42.113906 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g clip-path="url(#p9d28241054)">
    <image xlink:href="data:image/png;base64,
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" id="imagefa567c8aed" transform="scale(1 -1) translate(0 -270.24)" x="42.113906" y="-44.38525" width="270.24" height="270.24"/>
   </g>
   <g id="patch_3">
    <path d="M 177.001906 230.32025 
C 190.416675 230.32025 203.283815 224.990506 212.769489 215.504832 
C 222.255162 206.019159 227.584906 193.152018 227.584906 179.73725 
C 227.584906 166.322482 222.255162 153.455341 212.769489 143.969668 
C 203.283815 134.483994 190.416675 129.15425 177.001906 129.15425 
C 163.587138 129.15425 150.719998 134.483994 141.234324 143.969668 
C 131.74865 153.455341 126.418906 166.322482 126.418906 179.73725 
C 126.418906 193.152018 131.74865 206.019159 141.234324 215.504832 
C 150.719998 224.990506 163.587138 230.32025 177.001906 230.32025 
L 177.001906 230.32025 
z
" clip-path="url(#p9d28241054)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 177.001906 264.04225 
C 199.359854 264.04225 220.805087 255.159343 236.614543 239.349887 
C 252.424 223.540431 261.306906 202.095197 261.306906 179.73725 
C 261.306906 157.379303 252.424 135.934069 236.614543 120.124613 
C 220.805087 104.315157 199.359854 95.43225 177.001906 95.43225 
C 154.643959 95.43225 133.198725 104.315157 117.389269 120.124613 
C 101.579813 135.934069 92.696906 157.379303 92.696906 179.73725 
C 92.696906 202.095197 101.579813 223.540431 117.389269 239.349887 
C 133.198725 255.159343 154.643959 264.04225 177.001906 264.04225 
L 177.001906 264.04225 
z
" clip-path="url(#p9d28241054)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 177.001906 314.62525 
C 212.774622 314.62525 247.086996 300.412599 272.382126 275.11747 
C 297.677256 249.82234 311.889906 215.509966 311.889906 179.73725 
C 311.889906 143.964534 297.677256 109.65216 272.382126 84.35703 
C 247.086996 59.061901 212.774622 44.84925 177.001906 44.84925 
C 141.22919 44.84925 106.916817 59.061901 81.621687 84.35703 
C 56.326557 109.65216 42.113906 143.964534 42.113906 179.73725 
C 42.113906 215.509966 56.326557 249.82234 81.621687 275.11747 
C 106.916817 300.412599 141.22919 314.62525 177.001906 314.62525 
L 177.001906 314.62525 
z
" clip-path="url(#p9d28241054)" style="fill: none; opacity: 0.7; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" clip-path="url(#p9d28241054)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="m7d22cb5dac" d="M 0 0 
L 0 3.5 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m7d22cb5dac" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −8 -->
      <g transform="translate(35.479922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 75.835906 314.62525 
L 75.835906 44.84925 
" clip-path="url(#p9d28241054)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#m7d22cb5dac" x="75.835906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −6 -->
      <g transform="translate(69.201922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 109.557906 314.62525 
L 109.557906 44.84925 
" clip-path="url(#p9d28241054)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#m7d22cb5dac" x="109.557906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_3">
      <!-- −4 -->
      <g transform="translate(102.923922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 143.279906 314.62525 
L 143.279906 44.84925 
" clip-path="url(#p9d28241054)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#m7d22cb5dac" x="143.279906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_4">
      <!-- −2 -->
      <g transform="translate(136.645922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 177.001906 314.62525 
L 177.001906 44.84925 
" clip-path="url(#p9d28241054)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#m7d22cb5dac" x="177.001906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(174.138781 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 210.723906 314.62525 
L 210.723906 44.84925 
" clip-path="url(#p9d28241054)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#m7d22cb5dac" x="210.723906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 2 -->
      <g transform="translate(207.860781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 244.445906 314.62525 
L 244.445906 44.84925 
" clip-path="url(#p9d28241054)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#m7d22cb5dac" x="244.445906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 4 -->
      <g transform="translate(241.582781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <path d="M 278.167906 314.62525 
L 278.167906 44.84925 
" clip-path="url(#p9d28241054)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#m7d22cb5dac" x="278.167906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 6 -->
      <g transform="translate(275.304781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_17">
      <path d="M 311.889906 314.62525 
L 311.889906 44.84925 
" clip-path="url(#p9d28241054)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#m7d22cb5dac" x="311.889906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 8 -->
      <g transform="translate(309.026781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- Relative X Position (m) -->
     <g transform="translate(105.68925 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-52" d="M 2297 2597 
Q 2675 2597 2839 2737 
Q 3003 2878 3003 3200 
Q 3003 3519 2839 3656 
Q 2675 3794 2297 3794 
L 1791 3794 
L 1791 2597 
L 2297 2597 
z
M 1791 1766 
L 1791 0 
L 588 0 
L 588 4666 
L 2425 4666 
Q 3347 4666 3776 4356 
Q 4206 4047 4206 3378 
Q 4206 2916 3982 2619 
Q 3759 2322 3309 2181 
Q 3556 2125 3751 1926 
Q 3947 1728 4147 1325 
L 4800 0 
L 3519 0 
L 2950 1159 
Q 2778 1509 2601 1637 
Q 2425 1766 2131 1766 
L 1791 1766 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6c" d="M 538 4863 
L 1656 4863 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-76" d="M 97 3500 
L 1216 3500 
L 2088 1081 
L 2956 3500 
L 4078 3500 
L 2700 0 
L 1472 0 
L 97 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-58" d="M 3188 2381 
L 4806 0 
L 3553 0 
L 2463 1594 
L 1381 0 
L 122 0 
L 1741 2381 
L 184 4666 
L 1441 4666 
L 2463 3163 
L 3481 4666 
L 4744 4666 
L 3188 2381 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-50" d="M 588 4666 
L 2584 4666 
Q 3475 4666 3951 4270 
Q 4428 3875 4428 3144 
Q 4428 2409 3951 2014 
Q 3475 1619 2584 1619 
L 1791 1619 
L 1791 0 
L 588 0 
L 588 4666 
z
M 1791 3794 
L 1791 2491 
L 2456 2491 
Q 2806 2491 2997 2661 
Q 3188 2831 3188 3144 
Q 3188 3456 2997 3625 
Q 2806 3794 2456 3794 
L 1791 3794 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6f" d="M 2203 2784 
Q 1831 2784 1636 2517 
Q 1441 2250 1441 1747 
Q 1441 1244 1636 976 
Q 1831 709 2203 709 
Q 2569 709 2762 976 
Q 2956 1244 2956 1747 
Q 2956 2250 2762 2517 
Q 2569 2784 2203 2784 
z
M 2203 3584 
Q 3106 3584 3614 3096 
Q 4122 2609 4122 1747 
Q 4122 884 3614 396 
Q 3106 -91 2203 -91 
Q 1297 -91 786 396 
Q 275 884 275 1747 
Q 275 2609 786 3096 
Q 1297 3584 2203 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-28" d="M 2413 -844 
L 1484 -844 
Q 1006 -72 778 623 
Q 550 1319 550 2003 
Q 550 2688 779 3389 
Q 1009 4091 1484 4856 
L 2413 4856 
Q 2013 4116 1813 3408 
Q 1613 2700 1613 2009 
Q 1613 1319 1811 609 
Q 2009 -100 2413 -844 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6d" d="M 3781 2919 
Q 3994 3244 4286 3414 
Q 4578 3584 4928 3584 
Q 5531 3584 5847 3212 
Q 6163 2841 6163 2131 
L 6163 0 
L 5038 0 
L 5038 1825 
Q 5041 1866 5042 1909 
Q 5044 1953 5044 2034 
Q 5044 2406 4934 2573 
Q 4825 2741 4581 2741 
Q 4263 2741 4089 2478 
Q 3916 2216 3909 1719 
L 3909 0 
L 2784 0 
L 2784 1825 
Q 2784 2406 2684 2573 
Q 2584 2741 2328 2741 
Q 2006 2741 1831 2477 
Q 1656 2213 1656 1722 
L 1656 0 
L 531 0 
L 531 3500 
L 1656 3500 
L 1656 2988 
Q 1863 3284 2130 3434 
Q 2397 3584 2719 3584 
Q 3081 3584 3359 3409 
Q 3638 3234 3781 2919 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-29" d="M 513 -844 
Q 913 -100 1113 609 
Q 1313 1319 1313 2009 
Q 1313 2700 1113 3408 
Q 913 4116 513 4856 
L 1441 4856 
Q 1916 4091 2145 3389 
Q 2375 2688 2375 2003 
Q 2375 1319 2147 623 
Q 1919 -72 1441 -844 
L 513 -844 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-58" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="573.583984"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="608.398438"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="681.689453"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="750.390625"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="809.912109"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="844.189453"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="891.992188"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="926.269531"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="994.970703"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1066.162109"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1100.976562"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1146.679688"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1250.878906"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_19">
      <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" clip-path="url(#p9d28241054)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <defs>
       <path id="m200f749b03" d="M 0 0 
L -3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m200f749b03" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_11">
      <!-- −8 -->
      <g transform="translate(21.845937 318.044547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_21">
      <path d="M 42.113906 280.90325 
L 311.889906 280.90325 
" clip-path="url(#p9d28241054)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#m200f749b03" x="42.113906" y="280.90325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_12">
      <!-- −6 -->
      <g transform="translate(21.845937 284.322547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_23">
      <path d="M 42.113906 247.18125 
L 311.889906 247.18125 
" clip-path="url(#p9d28241054)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#m200f749b03" x="42.113906" y="247.18125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_13">
      <!-- −4 -->
      <g transform="translate(21.845937 250.600547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_25">
      <path d="M 42.113906 213.45925 
L 311.889906 213.45925 
" clip-path="url(#p9d28241054)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#m200f749b03" x="42.113906" y="213.45925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_14">
      <!-- −2 -->
      <g transform="translate(21.845937 216.878547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_27">
      <path d="M 42.113906 179.73725 
L 311.889906 179.73725 
" clip-path="url(#p9d28241054)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#m200f749b03" x="42.113906" y="179.73725" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 0 -->
      <g transform="translate(29.387656 183.156547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_29">
      <path d="M 42.113906 146.01525 
L 311.889906 146.01525 
" clip-path="url(#p9d28241054)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#m200f749b03" x="42.113906" y="146.01525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 2 -->
      <g transform="translate(29.387656 149.434547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_31">
      <path d="M 42.113906 112.29325 
L 311.889906 112.29325 
" clip-path="url(#p9d28241054)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#m200f749b03" x="42.113906" y="112.29325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 4 -->
      <g transform="translate(29.387656 115.712547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_33">
      <path d="M 42.113906 78.57125 
L 311.889906 78.57125 
" clip-path="url(#p9d28241054)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#m200f749b03" x="42.113906" y="78.57125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 6 -->
      <g transform="translate(29.387656 81.990547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_35">
      <path d="M 42.113906 44.84925 
L 311.889906 44.84925 
" clip-path="url(#p9d28241054)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#m200f749b03" x="42.113906" y="44.84925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 8 -->
      <g transform="translate(29.387656 48.268547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_20">
     <!-- Relative Y Position (m) -->
     <g transform="translate(15.558281 250.792094) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-59" d="M -63 4666 
L 1253 4666 
L 2316 3003 
L 3378 4666 
L 4697 4666 
L 2919 1966 
L 2919 0 
L 1716 0 
L 1716 1966 
L -63 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-59" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="568.896484"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="603.710938"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="677.001953"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="745.703125"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="805.224609"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="839.501953"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="887.304688"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="921.582031"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="990.283203"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1061.474609"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1096.289062"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1141.992188"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1246.191406"/>
     </g>
    </g>
   </g>
   <g id="patch_6">
    <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_21">
    <g id="patch_8">
     <path d="M 200.10902 150.124824 
L 225.429957 150.124824 
Q 227.229957 150.124824 227.229957 148.324824 
L 227.229957 139.614511 
Q 227.229957 137.814511 225.429957 137.814511 
L 200.10902 137.814511 
Q 198.30902 137.814511 198.30902 139.614511 
L 198.30902 148.324824 
Q 198.30902 150.124824 200.10902 150.124824 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 3.0m -->
    <g style="fill: #ffffff" transform="translate(200.10902 146.453105) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-33" d="M 2981 2516 
Q 3453 2394 3698 2092 
Q 3944 1791 3944 1325 
Q 3944 631 3412 270 
Q 2881 -91 1863 -91 
Q 1503 -91 1142 -33 
Q 781 25 428 141 
L 428 1069 
Q 766 900 1098 814 
Q 1431 728 1753 728 
Q 2231 728 2486 893 
Q 2741 1059 2741 1369 
Q 2741 1688 2480 1852 
Q 2219 2016 1709 2016 
L 1228 2016 
L 1228 2791 
L 1734 2791 
Q 2188 2791 2409 2933 
Q 2631 3075 2631 3366 
Q 2631 3634 2415 3781 
Q 2200 3928 1806 3928 
Q 1516 3928 1219 3862 
Q 922 3797 628 3669 
L 628 4550 
Q 984 4650 1334 4700 
Q 1684 4750 2022 4750 
Q 2931 4750 3382 4451 
Q 3834 4153 3834 3553 
Q 3834 3144 3618 2883 
Q 3403 2622 2981 2516 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2e" d="M 653 1209 
L 1778 1209 
L 1778 0 
L 653 0 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-30" d="M 2944 2338 
Q 2944 3213 2780 3570 
Q 2616 3928 2228 3928 
Q 1841 3928 1675 3570 
Q 1509 3213 1509 2338 
Q 1509 1453 1675 1090 
Q 1841 728 2228 728 
Q 2613 728 2778 1090 
Q 2944 1453 2944 2338 
z
M 4147 2328 
Q 4147 1169 3647 539 
Q 3147 -91 2228 -91 
Q 1306 -91 806 539 
Q 306 1169 306 2328 
Q 306 3491 806 4120 
Q 1306 4750 2228 4750 
Q 3147 4750 3647 4120 
Q 4147 3491 4147 2328 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-33"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_22">
    <g id="patch_9">
     <path d="M 223.954075 126.279769 
L 249.275012 126.279769 
Q 251.075012 126.279769 251.075012 124.479769 
L 251.075012 115.769457 
Q 251.075012 113.969457 249.275012 113.969457 
L 223.954075 113.969457 
Q 222.154075 113.969457 222.154075 115.769457 
L 222.154075 124.479769 
Q 222.154075 126.279769 223.954075 126.279769 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 5.0m -->
    <g style="fill: #ffffff" transform="translate(223.954075 122.60805) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-35" d="M 678 4666 
L 3669 4666 
L 3669 3781 
L 1638 3781 
L 1638 3059 
Q 1775 3097 1914 3117 
Q 2053 3138 2203 3138 
Q 3056 3138 3531 2711 
Q 4006 2284 4006 1522 
Q 4006 766 3489 337 
Q 2972 -91 2053 -91 
Q 1656 -91 1267 -14 
Q 878 63 494 219 
L 494 1166 
Q 875 947 1217 837 
Q 1559 728 1863 728 
Q 2300 728 2551 942 
Q 2803 1156 2803 1522 
Q 2803 1891 2551 2103 
Q 2300 2316 1863 2316 
Q 1603 2316 1309 2248 
Q 1016 2181 678 2041 
L 678 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-35"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_23">
    <g id="patch_10">
     <path d="M 259.721657 90.512187 
L 285.042595 90.512187 
Q 286.842595 90.512187 286.842595 88.712187 
L 286.842595 80.001874 
Q 286.842595 78.201874 285.042595 78.201874 
L 259.721657 78.201874 
Q 257.921657 78.201874 257.921657 80.001874 
L 257.921657 88.712187 
Q 257.921657 90.512187 259.721657 90.512187 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 8.0m -->
    <g style="fill: #ffffff" transform="translate(259.721657 86.840468) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-38" d="M 2228 2088 
Q 1891 2088 1709 1903 
Q 1528 1719 1528 1375 
Q 1528 1031 1709 848 
Q 1891 666 2228 666 
Q 2563 666 2741 848 
Q 2919 1031 2919 1375 
Q 2919 1722 2741 1905 
Q 2563 2088 2228 2088 
z
M 1350 2484 
Q 925 2613 709 2878 
Q 494 3144 494 3541 
Q 494 4131 934 4440 
Q 1375 4750 2228 4750 
Q 3075 4750 3515 4442 
Q 3956 4134 3956 3541 
Q 3956 3144 3739 2878 
Q 3522 2613 3097 2484 
Q 3572 2353 3814 2058 
Q 4056 1763 4056 1313 
Q 4056 619 3595 264 
Q 3134 -91 2228 -91 
Q 1319 -91 855 264 
Q 391 619 391 1313 
Q 391 1763 633 2058 
Q 875 2353 1350 2484 
z
M 1631 3419 
Q 1631 3141 1786 2991 
Q 1941 2841 2228 2841 
Q 2509 2841 2662 2991 
Q 2816 3141 2816 3419 
Q 2816 3697 2662 3845 
Q 2509 3994 2228 3994 
Q 1941 3994 1786 3844 
Q 1631 3694 1631 3419 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-38"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_24">
    <!-- Close-Distance Cell (Neuron 19) -->
    <g transform="translate(69.464094 16.411875) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2d" d="M 347 2297 
L 2309 2297 
L 2309 1388 
L 347 1388 
L 347 2297 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-44" d="M 1791 3756 
L 1791 909 
L 2222 909 
Q 2959 909 3348 1275 
Q 3738 1641 3738 2338 
Q 3738 3031 3350 3393 
Q 2963 3756 2222 3756 
L 1791 3756 
z
M 588 4666 
L 1856 4666 
Q 2919 4666 3439 4514 
Q 3959 4363 4331 4000 
Q 4659 3684 4818 3271 
Q 4978 2859 4978 2338 
Q 4978 1809 4818 1395 
Q 4659 981 4331 666 
Q 3956 303 3431 151 
Q 2906 0 1856 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4e" d="M 588 4666 
L 1931 4666 
L 3628 1466 
L 3628 4666 
L 4769 4666 
L 4769 0 
L 3425 0 
L 1728 3200 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-75" d="M 500 1363 
L 500 3500 
L 1625 3500 
L 1625 3150 
Q 1625 2866 1622 2436 
Q 1619 2006 1619 1863 
Q 1619 1441 1641 1255 
Q 1663 1069 1716 984 
Q 1784 875 1895 815 
Q 2006 756 2150 756 
Q 2500 756 2700 1025 
Q 2900 1294 2900 1772 
L 2900 3500 
L 4019 3500 
L 4019 0 
L 2900 0 
L 2900 506 
Q 2647 200 2364 54 
Q 2081 -91 1741 -91 
Q 1134 -91 817 281 
Q 500 653 500 1363 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-31" d="M 750 831 
L 1813 831 
L 1813 3847 
L 722 3622 
L 722 4441 
L 1806 4666 
L 2950 4666 
L 2950 831 
L 4013 831 
L 4013 0 
L 750 0 
L 750 831 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-39" d="M 641 103 
L 641 966 
Q 928 831 1190 764 
Q 1453 697 1709 697 
Q 2247 697 2547 995 
Q 2847 1294 2900 1881 
Q 2688 1725 2447 1647 
Q 2206 1569 1925 1569 
Q 1209 1569 770 1986 
Q 331 2403 331 3084 
Q 331 3838 820 4291 
Q 1309 4744 2131 4744 
Q 3044 4744 3544 4128 
Q 4044 3513 4044 2388 
Q 4044 1231 3459 570 
Q 2875 -91 1856 -91 
Q 1528 -91 1228 -42 
Q 928 6 641 103 
z
M 2125 2350 
Q 2441 2350 2600 2554 
Q 2759 2759 2759 3169 
Q 2759 3575 2600 3781 
Q 2441 3988 2125 3988 
Q 1809 3988 1650 3781 
Q 1491 3575 1491 3169 
Q 1491 2759 1650 2554 
Q 1809 2350 2125 2350 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-43"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="73.388672"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="107.666016"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="176.367188"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="235.888672"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="303.710938"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="345.214844"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="428.222656"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="462.5"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="522.021484"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="569.824219"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="637.304688"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="708.496094"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="767.773438"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="835.595703"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="870.410156"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="943.798828"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="1011.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="1045.898438"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1080.175781"/>
     <use xlink:href="#DejaVuSans-Bold-28" x="1114.990234"/>
     <use xlink:href="#DejaVuSans-Bold-4e" x="1160.693359"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1244.384766"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1312.207031"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1383.398438"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="1432.714844"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1501.416016"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1572.607422"/>
     <use xlink:href="#DejaVuSans-Bold-31" x="1607.421875"/>
     <use xlink:href="#DejaVuSans-Bold-39" x="1677.001953"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="1746.582031"/>
    </g>
    <!-- 2D Activation Map -->
    <g transform="translate(114.950656 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-32" d="M 1844 884 
L 3897 884 
L 3897 0 
L 506 0 
L 506 884 
L 2209 2388 
Q 2438 2594 2547 2791 
Q 2656 2988 2656 3200 
Q 2656 3528 2436 3728 
Q 2216 3928 1850 3928 
Q 1569 3928 1234 3808 
Q 900 3688 519 3450 
L 519 4475 
Q 925 4609 1322 4679 
Q 1719 4750 2100 4750 
Q 2938 4750 3402 4381 
Q 3866 4013 3866 3353 
Q 3866 2972 3669 2642 
Q 3472 2313 2841 1759 
L 1844 884 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-41" d="M 3419 850 
L 1538 850 
L 1241 0 
L 31 0 
L 1759 4666 
L 3194 4666 
L 4922 0 
L 3713 0 
L 3419 850 
z
M 1838 1716 
L 3116 1716 
L 2478 3572 
L 1838 1716 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4d" d="M 588 4666 
L 2119 4666 
L 3181 2169 
L 4250 4666 
L 5778 4666 
L 5778 0 
L 4641 0 
L 4641 3413 
L 3566 897 
L 2803 897 
L 1728 3413 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-32"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="152.587891"/>
     <use xlink:href="#DejaVuSans-Bold-41" x="187.402344"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="264.794922"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="324.072266"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="371.875"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="406.152344"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.337891"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="538.818359"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="586.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="620.898438"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="689.599609"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="760.791016"/>
     <use xlink:href="#DejaVuSans-Bold-4d" x="795.605469"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="895.117188"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="962.597656"/>
    </g>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="me1e13d70a0" d="M 0 -5.477226 
L -1.229714 -1.692556 
L -5.209151 -1.692556 
L -1.989719 0.646499 
L -3.219432 4.431169 
L -0 2.092114 
L 3.219432 4.431169 
L 1.989719 0.646499 
L 5.209151 -1.692556 
L 1.229714 -1.692556 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#p9d28241054)">
     <use xlink:href="#me1e13d70a0" x="177.001906" y="179.73725" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_11">
     <path d="M 242.388656 62.99175 
L 307.889906 62.99175 
L 307.889906 48.84925 
L 242.388656 48.84925 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="PathCollection_2">
     <g>
      <use xlink:href="#me1e13d70a0" x="253.588656" y="56.028" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
     </g>
    </g>
    <g id="text_25">
     <!-- Observer -->
     <g transform="translate(267.988656 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4f"/>
      <use xlink:href="#DejaVuSans-62" x="78.710938"/>
      <use xlink:href="#DejaVuSans-73" x="142.1875"/>
      <use xlink:href="#DejaVuSans-65" x="194.287109"/>
      <use xlink:href="#DejaVuSans-72" x="255.810547"/>
      <use xlink:href="#DejaVuSans-76" x="296.923828"/>
      <use xlink:href="#DejaVuSans-65" x="356.103516"/>
      <use xlink:href="#DejaVuSans-72" x="417.626953"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_12">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
L 813.937288 44.84925 
L 436.259015 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="m75191cf348" d="M 440.975868 -37.642188 
L 440.975868 -37.642188 
L 450.409572 -294.571711 
L 459.843277 -280.897185 
L 469.276982 -271.541888 
L 478.710686 -260.058299 
L 488.144391 -270.773221 
L 497.578096 -197.583584 
L 507.011801 -246.855847 
L 516.445505 -150.521398 
L 525.87921 -105.326897 
L 535.312915 -117.059007 
L 544.746619 -104.219039 
L 554.180324 -137.950197 
L 563.614029 -88.790575 
L 573.047733 -94.990315 
L 582.481438 -126.875616 
L 591.915143 -82.456247 
L 601.348847 -78.92763 
L 610.782552 -106.672431 
L 620.216257 -94.617631 
L 629.649962 -88.476493 
L 639.083666 -64.97963 
L 648.517371 -59.552488 
L 657.951076 -53.111 
L 667.38478 -58.311896 
L 676.818485 -59.026452 
L 686.25219 -74.986613 
L 695.685894 -49.259737 
L 705.119599 -66.036688 
L 714.553304 -66.660562 
L 714.553304 -37.642188 
L 714.553304 -37.642188 
L 705.119599 -37.642188 
L 695.685894 -37.642188 
L 686.25219 -37.642188 
L 676.818485 -37.642188 
L 667.38478 -37.642188 
L 657.951076 -37.642188 
L 648.517371 -37.642188 
L 639.083666 -37.642188 
L 629.649962 -37.642188 
L 620.216257 -37.642188 
L 610.782552 -37.642188 
L 601.348847 -37.642188 
L 591.915143 -37.642188 
L 582.481438 -37.642188 
L 573.047733 -37.642188 
L 563.614029 -37.642188 
L 554.180324 -37.642188 
L 544.746619 -37.642188 
L 535.312915 -37.642188 
L 525.87921 -37.642188 
L 516.445505 -37.642188 
L 507.011801 -37.642188 
L 497.578096 -37.642188 
L 488.144391 -37.642188 
L 478.710686 -37.642188 
L 469.276982 -37.642188 
L 459.843277 -37.642188 
L 450.409572 -37.642188 
L 440.975868 -37.642188 
z
" style="stroke: #e31a1c; stroke-opacity: 0.3"/>
    </defs>
    <g clip-path="url(#pd4631c28db)">
     <use xlink:href="#m75191cf348" x="0" y="352.267438" style="fill: #e31a1c; fill-opacity: 0.3; stroke: #e31a1c; stroke-opacity: 0.3"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10">
     <g id="line2d_37">
      <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" clip-path="url(#pd4631c28db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#m7d22cb5dac" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 0 -->
      <g transform="translate(433.39589 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_39">
      <path d="M 483.468799 314.62525 
L 483.468799 44.84925 
" clip-path="url(#pd4631c28db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#m7d22cb5dac" x="483.468799" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 1 -->
      <g transform="translate(480.605674 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_41">
      <path d="M 530.678584 314.62525 
L 530.678584 44.84925 
" clip-path="url(#pd4631c28db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#m7d22cb5dac" x="530.678584" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 2 -->
      <g transform="translate(527.815459 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_43">
      <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#pd4631c28db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#m7d22cb5dac" x="577.888368" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 3 -->
      <g transform="translate(575.025243 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_45">
      <path d="M 625.098152 314.62525 
L 625.098152 44.84925 
" clip-path="url(#pd4631c28db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#m7d22cb5dac" x="625.098152" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 4 -->
      <g transform="translate(622.235027 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_47">
      <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#pd4631c28db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#m7d22cb5dac" x="672.307936" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 5 -->
      <g transform="translate(669.444811 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_49">
      <path d="M 719.51772 314.62525 
L 719.51772 44.84925 
" clip-path="url(#pd4631c28db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#m7d22cb5dac" x="719.51772" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 6 -->
      <g transform="translate(716.654595 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_51">
      <path d="M 766.727504 314.62525 
L 766.727504 44.84925 
" clip-path="url(#pd4631c28db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#m7d22cb5dac" x="766.727504" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 7 -->
      <g transform="translate(763.864379 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-37"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_53">
      <path d="M 813.937288 314.62525 
L 813.937288 44.84925 
" clip-path="url(#pd4631c28db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#m7d22cb5dac" x="813.937288" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 8 -->
      <g transform="translate(811.074163 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_35">
     <!-- Inter-Agent Distance (m) -->
     <g transform="translate(547.755261 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-49" d="M 588 4666 
L 1791 4666 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-49"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="37.207031"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="108.398438"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="156.201172"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="224.023438"/>
      <use xlink:href="#DejaVuSans-Bold-2d" x="273.339844"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="314.84375"/>
      <use xlink:href="#DejaVuSans-Bold-67" x="392.236328"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="463.818359"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="531.640625"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="602.832031"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="650.634766"/>
      <use xlink:href="#DejaVuSans-Bold-44" x="685.449219"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="768.457031"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="802.734375"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="862.255859"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="910.058594"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="977.539062"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="1048.730469"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="1108.007812"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1175.830078"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1210.644531"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1256.347656"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1360.546875"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_55">
      <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" clip-path="url(#pd4631c28db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#m200f749b03" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 0.0 -->
      <g transform="translate(414.946203 318.044547) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_57">
      <path d="M 436.259015 279.379461 
L 813.937288 279.379461 
" clip-path="url(#pd4631c28db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#m200f749b03" x="436.259015" y="279.379461" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 0.2 -->
      <g transform="translate(414.946203 282.798758) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_59">
      <path d="M 436.259015 244.133672 
L 813.937288 244.133672 
" clip-path="url(#pd4631c28db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#m200f749b03" x="436.259015" y="244.133672" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 0.4 -->
      <g transform="translate(414.946203 247.552969) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_61">
      <path d="M 436.259015 208.887883 
L 813.937288 208.887883 
" clip-path="url(#pd4631c28db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#m200f749b03" x="436.259015" y="208.887883" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 0.6 -->
      <g transform="translate(414.946203 212.30718) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_63">
      <path d="M 436.259015 173.642094 
L 813.937288 173.642094 
" clip-path="url(#pd4631c28db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#m200f749b03" x="436.259015" y="173.642094" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 0.8 -->
      <g transform="translate(414.946203 177.061391) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-38" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_65">
      <path d="M 436.259015 138.396305 
L 813.937288 138.396305 
" clip-path="url(#pd4631c28db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#m200f749b03" x="436.259015" y="138.396305" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 1.0 -->
      <g transform="translate(414.946203 141.815602) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_67">
      <path d="M 436.259015 103.150516 
L 813.937288 103.150516 
" clip-path="url(#pd4631c28db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_68">
      <g>
       <use xlink:href="#m200f749b03" x="436.259015" y="103.150516" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_42">
      <!-- 1.2 -->
      <g transform="translate(414.946203 106.569812) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_69">
      <path d="M 436.259015 67.904727 
L 813.937288 67.904727 
" clip-path="url(#pd4631c28db)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_70">
      <g>
       <use xlink:href="#m200f749b03" x="436.259015" y="67.904727" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_43">
      <!-- 1.4 -->
      <g transform="translate(414.946203 71.324023) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_44">
     <!-- Neural Activation -->
     <g transform="translate(408.658547 233.746391) rotate(-90) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="line2d_71">
    <path d="M 440.975868 314.62525 
L 450.409572 57.695726 
L 459.843277 71.370252 
L 469.276982 80.72555 
L 478.710686 92.209138 
L 488.144391 81.494216 
L 497.578096 154.683853 
L 507.011801 105.411591 
L 516.445505 201.74604 
L 525.87921 246.94054 
L 535.312915 235.208431 
L 544.746619 248.048399 
L 554.180324 214.31724 
L 563.614029 263.476862 
L 573.047733 257.277122 
L 582.481438 225.391821 
L 591.915143 269.811191 
L 601.348847 273.339808 
L 610.782552 245.595007 
L 620.216257 257.649806 
L 629.649962 263.790944 
L 639.083666 287.287807 
L 648.517371 292.71495 
L 657.951076 299.156438 
L 667.38478 293.955541 
L 676.818485 293.240985 
L 686.25219 277.280825 
L 695.685894 303.0077 
L 705.119599 286.230749 
L 714.553304 285.606876 
" clip-path="url(#pd4631c28db)" style="fill: none; stroke: #e31a1c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_72">
    <path d="M 450.409572 314.62525 
L 450.409572 44.84925 
" clip-path="url(#pd4631c28db)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #e31a1c; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_73">
    <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#pd4631c28db)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_74">
    <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#pd4631c28db)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="patch_13">
    <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_14">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_45">
    <g id="patch_15">
     <path d="M 700.612124 114.0718 
L 795.053374 114.0718 
Q 798.253374 114.0718 798.253374 110.8718 
L 798.253374 58.33805 
Q 798.253374 55.13805 795.053374 55.13805 
L 700.612124 55.13805 
Q 697.412124 55.13805 697.412124 58.33805 
L 697.412124 110.8718 
Q 697.412124 114.0718 700.612124 114.0718 
z
" style="fill: #ffffff; opacity: 0.9; stroke: #808080; stroke-linejoin: miter"/>
    </g>
    <!-- Peak Distance: 0.3m -->
    <g transform="translate(712.787124 64.4168) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-3a" d="M 750 794 
L 1409 794 
L 1409 0 
L 750 0 
L 750 794 
z
M 750 3309 
L 1409 3309 
L 1409 2516 
L 750 2516 
L 750 3309 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-44" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="346.179688"/>
     <use xlink:href="#DejaVuSans-73" x="373.962891"/>
     <use xlink:href="#DejaVuSans-74" x="426.0625"/>
     <use xlink:href="#DejaVuSans-61" x="465.271484"/>
     <use xlink:href="#DejaVuSans-6e" x="526.550781"/>
     <use xlink:href="#DejaVuSans-63" x="589.929688"/>
     <use xlink:href="#DejaVuSans-65" x="644.910156"/>
     <use xlink:href="#DejaVuSans-3a" x="706.433594"/>
     <use xlink:href="#DejaVuSans-20" x="740.125"/>
     <use xlink:href="#DejaVuSans-30" x="771.912109"/>
     <use xlink:href="#DejaVuSans-2e" x="835.535156"/>
     <use xlink:href="#DejaVuSans-33" x="867.322266"/>
     <use xlink:href="#DejaVuSans-6d" x="930.945312"/>
    </g>
    <!-- Max Activation: 1.458 -->
    <g transform="translate(707.779624 73.37505) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-4d"/>
     <use xlink:href="#DejaVuSans-61" x="86.279297"/>
     <use xlink:href="#DejaVuSans-78" x="147.558594"/>
     <use xlink:href="#DejaVuSans-20" x="206.738281"/>
     <use xlink:href="#DejaVuSans-41" x="238.525391"/>
     <use xlink:href="#DejaVuSans-63" x="305.183594"/>
     <use xlink:href="#DejaVuSans-74" x="360.164062"/>
     <use xlink:href="#DejaVuSans-69" x="399.373047"/>
     <use xlink:href="#DejaVuSans-76" x="427.15625"/>
     <use xlink:href="#DejaVuSans-61" x="486.335938"/>
     <use xlink:href="#DejaVuSans-74" x="547.615234"/>
     <use xlink:href="#DejaVuSans-69" x="586.824219"/>
     <use xlink:href="#DejaVuSans-6f" x="614.607422"/>
     <use xlink:href="#DejaVuSans-6e" x="675.789062"/>
     <use xlink:href="#DejaVuSans-3a" x="739.167969"/>
     <use xlink:href="#DejaVuSans-20" x="772.859375"/>
     <use xlink:href="#DejaVuSans-31" x="804.646484"/>
     <use xlink:href="#DejaVuSans-2e" x="868.269531"/>
     <use xlink:href="#DejaVuSans-34" x="900.056641"/>
     <use xlink:href="#DejaVuSans-35" x="963.679688"/>
     <use xlink:href="#DejaVuSans-38" x="1027.302734"/>
    </g>
    <!-- Sparsity: 0.460 -->
    <g transform="translate(734.883374 82.3333) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-70" x="63.476562"/>
     <use xlink:href="#DejaVuSans-61" x="126.953125"/>
     <use xlink:href="#DejaVuSans-72" x="188.232422"/>
     <use xlink:href="#DejaVuSans-73" x="229.345703"/>
     <use xlink:href="#DejaVuSans-69" x="281.445312"/>
     <use xlink:href="#DejaVuSans-74" x="309.228516"/>
     <use xlink:href="#DejaVuSans-79" x="348.4375"/>
     <use xlink:href="#DejaVuSans-3a" x="400.367188"/>
     <use xlink:href="#DejaVuSans-20" x="434.058594"/>
     <use xlink:href="#DejaVuSans-30" x="465.845703"/>
     <use xlink:href="#DejaVuSans-2e" x="529.46875"/>
     <use xlink:href="#DejaVuSans-34" x="561.255859"/>
     <use xlink:href="#DejaVuSans-36" x="624.878906"/>
     <use xlink:href="#DejaVuSans-30" x="688.501953"/>
    </g>
    <!-- Peak Significance: 2.88 -->
    <g transform="translate(702.065874 91.29155) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-53" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="332.654297"/>
     <use xlink:href="#DejaVuSans-67" x="360.4375"/>
     <use xlink:href="#DejaVuSans-6e" x="423.914062"/>
     <use xlink:href="#DejaVuSans-69" x="487.292969"/>
     <use xlink:href="#DejaVuSans-66" x="515.076172"/>
     <use xlink:href="#DejaVuSans-69" x="550.28125"/>
     <use xlink:href="#DejaVuSans-63" x="578.064453"/>
     <use xlink:href="#DejaVuSans-61" x="633.044922"/>
     <use xlink:href="#DejaVuSans-6e" x="694.324219"/>
     <use xlink:href="#DejaVuSans-63" x="757.703125"/>
     <use xlink:href="#DejaVuSans-65" x="812.683594"/>
     <use xlink:href="#DejaVuSans-3a" x="874.207031"/>
     <use xlink:href="#DejaVuSans-20" x="907.898438"/>
     <use xlink:href="#DejaVuSans-32" x="939.685547"/>
     <use xlink:href="#DejaVuSans-2e" x="1003.308594"/>
     <use xlink:href="#DejaVuSans-38" x="1035.095703"/>
     <use xlink:href="#DejaVuSans-38" x="1098.71875"/>
    </g>
    <!-- Selectivity Index: 0.647 -->
    <g transform="translate(700.612124 100.2498) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-65" x="63.476562"/>
     <use xlink:href="#DejaVuSans-6c" x="125"/>
     <use xlink:href="#DejaVuSans-65" x="152.783203"/>
     <use xlink:href="#DejaVuSans-63" x="214.306641"/>
     <use xlink:href="#DejaVuSans-74" x="269.287109"/>
     <use xlink:href="#DejaVuSans-69" x="308.496094"/>
     <use xlink:href="#DejaVuSans-76" x="336.279297"/>
     <use xlink:href="#DejaVuSans-69" x="395.458984"/>
     <use xlink:href="#DejaVuSans-74" x="423.242188"/>
     <use xlink:href="#DejaVuSans-79" x="462.451172"/>
     <use xlink:href="#DejaVuSans-20" x="521.630859"/>
     <use xlink:href="#DejaVuSans-49" x="553.417969"/>
     <use xlink:href="#DejaVuSans-6e" x="582.910156"/>
     <use xlink:href="#DejaVuSans-64" x="646.289062"/>
     <use xlink:href="#DejaVuSans-65" x="709.765625"/>
     <use xlink:href="#DejaVuSans-78" x="769.539062"/>
     <use xlink:href="#DejaVuSans-3a" x="828.71875"/>
     <use xlink:href="#DejaVuSans-20" x="862.410156"/>
     <use xlink:href="#DejaVuSans-30" x="894.197266"/>
     <use xlink:href="#DejaVuSans-2e" x="957.820312"/>
     <use xlink:href="#DejaVuSans-36" x="989.607422"/>
     <use xlink:href="#DejaVuSans-34" x="1053.230469"/>
     <use xlink:href="#DejaVuSans-37" x="1116.853516"/>
    </g>
    <!-- Peak Width: 1.2m -->
    <g transform="translate(724.529624 109.20805) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-57" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="365.804688"/>
     <use xlink:href="#DejaVuSans-64" x="393.587891"/>
     <use xlink:href="#DejaVuSans-74" x="457.064453"/>
     <use xlink:href="#DejaVuSans-68" x="496.273438"/>
     <use xlink:href="#DejaVuSans-3a" x="559.652344"/>
     <use xlink:href="#DejaVuSans-20" x="593.34375"/>
     <use xlink:href="#DejaVuSans-31" x="625.130859"/>
     <use xlink:href="#DejaVuSans-2e" x="688.753906"/>
     <use xlink:href="#DejaVuSans-32" x="720.541016"/>
     <use xlink:href="#DejaVuSans-6d" x="784.164062"/>
    </g>
   </g>
   <g id="text_46">
    <!-- Distance Tuning Curve -->
    <g transform="translate(549.284402 16.318125) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-54" d="M 31 4666 
L 4331 4666 
L 4331 3756 
L 2784 3756 
L 2784 0 
L 1581 0 
L 1581 3756 
L 31 3756 
L 31 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-44"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
     <use xlink:href="#DejaVuSans-Bold-54" x="525.195312"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="582.408203"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="653.599609"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="724.791016"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="759.068359"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="830.259766"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="901.841797"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="936.65625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1010.044922"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1081.236328"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="1130.552734"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1195.738281"/>
    </g>
    <!-- Peak: 0.3m, Sparsity: 0.460 -->
    <g transform="translate(531.359402 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-6b" d="M 538 4863 
L 1656 4863 
L 1656 2216 
L 2944 3500 
L 4244 3500 
L 2534 1894 
L 4378 0 
L 3022 0 
L 1656 1459 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-3a" d="M 716 3500 
L 1844 3500 
L 1844 2291 
L 716 2291 
L 716 3500 
z
M 716 1209 
L 1844 1209 
L 1844 0 
L 716 0 
L 716 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2c" d="M 653 1209 
L 1778 1209 
L 1778 256 
L 1006 -909 
L 341 -909 
L 653 256 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-53" d="M 3834 4519 
L 3834 3531 
Q 3450 3703 3084 3790 
Q 2719 3878 2394 3878 
Q 1963 3878 1756 3759 
Q 1550 3641 1550 3391 
Q 1550 3203 1689 3098 
Q 1828 2994 2194 2919 
L 2706 2816 
Q 3484 2659 3812 2340 
Q 4141 2022 4141 1434 
Q 4141 663 3683 286 
Q 3225 -91 2284 -91 
Q 1841 -91 1394 -6 
Q 947 78 500 244 
L 500 1259 
Q 947 1022 1364 901 
Q 1781 781 2169 781 
Q 2563 781 2772 912 
Q 2981 1044 2981 1288 
Q 2981 1506 2839 1625 
Q 2697 1744 2272 1838 
L 1806 1941 
Q 1106 2091 782 2419 
Q 459 2747 459 3303 
Q 459 4000 909 4375 
Q 1359 4750 2203 4750 
Q 2588 4750 2994 4692 
Q 3400 4634 3834 4519 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-79" d="M 78 3500 
L 1197 3500 
L 2138 1125 
L 2938 3500 
L 4056 3500 
L 2584 -331 
Q 2363 -916 2067 -1148 
Q 1772 -1381 1288 -1381 
L 641 -1381 
L 641 -647 
L 991 -647 
Q 1275 -647 1404 -556 
Q 1534 -466 1606 -231 
L 1638 -134 
L 78 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-34" d="M 2356 3675 
L 1038 1722 
L 2356 1722 
L 2356 3675 
z
M 2156 4666 
L 3494 4666 
L 3494 1722 
L 4159 1722 
L 4159 850 
L 3494 850 
L 3494 0 
L 2356 0 
L 2356 850 
L 288 850 
L 288 1881 
L 2156 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-36" d="M 2316 2303 
Q 2000 2303 1842 2098 
Q 1684 1894 1684 1484 
Q 1684 1075 1842 870 
Q 2000 666 2316 666 
Q 2634 666 2792 870 
Q 2950 1075 2950 1484 
Q 2950 1894 2792 2098 
Q 2634 2303 2316 2303 
z
M 3803 4544 
L 3803 3681 
Q 3506 3822 3243 3889 
Q 2981 3956 2731 3956 
Q 2194 3956 1894 3657 
Q 1594 3359 1544 2772 
Q 1750 2925 1990 3001 
Q 2231 3078 2516 3078 
Q 3231 3078 3670 2659 
Q 4109 2241 4109 1563 
Q 4109 813 3618 361 
Q 3128 -91 2303 -91 
Q 1394 -91 895 523 
Q 397 1138 397 2266 
Q 397 3422 980 4083 
Q 1563 4744 2578 4744 
Q 2900 4744 3203 4694 
Q 3506 4644 3803 4544 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-50"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="73.291016"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="141.113281"/>
     <use xlink:href="#DejaVuSans-Bold-6b" x="208.59375"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="275.097656"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="315.087891"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="349.902344"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="419.482422"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="457.470703"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="527.050781"/>
     <use xlink:href="#DejaVuSans-Bold-2c" x="631.25"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="669.238281"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="704.052734"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="776.074219"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="847.65625"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="915.136719"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="964.453125"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1023.974609"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="1058.251953"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="1106.054688"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="1171.240234"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1211.230469"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1246.044922"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="1315.625"/>
     <use xlink:href="#DejaVuSans-Bold-34" x="1353.613281"/>
     <use xlink:href="#DejaVuSans-Bold-36" x="1423.193359"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1492.773438"/>
    </g>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="mf25fb2070c" d="M 0 5 
C 1.326016 5 2.597899 4.473168 3.535534 3.535534 
C 4.473168 2.597899 5 1.326016 5 0 
C 5 -1.326016 4.473168 -2.597899 3.535534 -3.535534 
C 2.597899 -4.473168 1.326016 -5 0 -5 
C -1.326016 -5 -2.597899 -4.473168 -3.535534 -3.535534 
C -4.473168 -2.597899 -5 -1.326016 -5 0 
C -5 1.326016 -4.473168 2.597899 -3.535534 3.535534 
C -2.597899 4.473168 -1.326016 5 0 5 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#pd4631c28db)">
     <use xlink:href="#mf25fb2070c" x="450.409572" y="57.695726" style="fill: #e31a1c; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="patch_16">
     <path d="M 691.429788 86.47675 
L 808.337288 86.47675 
Q 809.937288 86.47675 809.937288 84.87675 
L 809.937288 50.44925 
Q 809.937288 48.84925 808.337288 48.84925 
L 691.429788 48.84925 
Q 689.829788 48.84925 689.829788 50.44925 
L 689.829788 84.87675 
Q 689.829788 86.47675 691.429788 86.47675 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_75">
     <path d="M 693.029788 55.328 
L 701.029788 55.328 
L 709.029788 55.328 
" style="fill: none; stroke: #e31a1c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
    </g>
    <g id="text_47">
     <!-- Tuning Curve -->
     <g transform="translate(715.429788 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-54"/>
      <use xlink:href="#DejaVuSans-75" x="45.958984"/>
      <use xlink:href="#DejaVuSans-6e" x="109.337891"/>
      <use xlink:href="#DejaVuSans-69" x="172.716797"/>
      <use xlink:href="#DejaVuSans-6e" x="200.5"/>
      <use xlink:href="#DejaVuSans-67" x="263.878906"/>
      <use xlink:href="#DejaVuSans-20" x="327.355469"/>
      <use xlink:href="#DejaVuSans-43" x="359.142578"/>
      <use xlink:href="#DejaVuSans-75" x="428.966797"/>
      <use xlink:href="#DejaVuSans-72" x="492.345703"/>
      <use xlink:href="#DejaVuSans-76" x="533.458984"/>
      <use xlink:href="#DejaVuSans-65" x="592.638672"/>
     </g>
    </g>
    <g id="line2d_76">
     <path d="M 693.029788 67.0705 
L 701.029788 67.0705 
L 709.029788 67.0705 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_48">
     <!-- Close threshold (3.0m) -->
     <g transform="translate(715.429788 69.8705) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6c" x="69.824219"/>
      <use xlink:href="#DejaVuSans-6f" x="97.607422"/>
      <use xlink:href="#DejaVuSans-73" x="158.789062"/>
      <use xlink:href="#DejaVuSans-65" x="210.888672"/>
      <use xlink:href="#DejaVuSans-20" x="272.412109"/>
      <use xlink:href="#DejaVuSans-74" x="304.199219"/>
      <use xlink:href="#DejaVuSans-68" x="343.408203"/>
      <use xlink:href="#DejaVuSans-72" x="406.787109"/>
      <use xlink:href="#DejaVuSans-65" x="445.650391"/>
      <use xlink:href="#DejaVuSans-73" x="507.173828"/>
      <use xlink:href="#DejaVuSans-68" x="559.273438"/>
      <use xlink:href="#DejaVuSans-6f" x="622.652344"/>
      <use xlink:href="#DejaVuSans-6c" x="683.833984"/>
      <use xlink:href="#DejaVuSans-64" x="711.617188"/>
      <use xlink:href="#DejaVuSans-20" x="775.09375"/>
      <use xlink:href="#DejaVuSans-28" x="806.880859"/>
      <use xlink:href="#DejaVuSans-33" x="845.894531"/>
      <use xlink:href="#DejaVuSans-2e" x="909.517578"/>
      <use xlink:href="#DejaVuSans-30" x="941.304688"/>
      <use xlink:href="#DejaVuSans-6d" x="1004.927734"/>
      <use xlink:href="#DejaVuSans-29" x="1102.339844"/>
     </g>
    </g>
    <g id="line2d_77">
     <path d="M 693.029788 78.813 
L 701.029788 78.813 
L 709.029788 78.813 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_49">
     <!-- Far threshold (5.0m) -->
     <g transform="translate(715.429788 81.613) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" x="48.394531"/>
      <use xlink:href="#DejaVuSans-72" x="109.673828"/>
      <use xlink:href="#DejaVuSans-20" x="150.787109"/>
      <use xlink:href="#DejaVuSans-74" x="182.574219"/>
      <use xlink:href="#DejaVuSans-68" x="221.783203"/>
      <use xlink:href="#DejaVuSans-72" x="285.162109"/>
      <use xlink:href="#DejaVuSans-65" x="324.025391"/>
      <use xlink:href="#DejaVuSans-73" x="385.548828"/>
      <use xlink:href="#DejaVuSans-68" x="437.648438"/>
      <use xlink:href="#DejaVuSans-6f" x="501.027344"/>
      <use xlink:href="#DejaVuSans-6c" x="562.208984"/>
      <use xlink:href="#DejaVuSans-64" x="589.992188"/>
      <use xlink:href="#DejaVuSans-20" x="653.46875"/>
      <use xlink:href="#DejaVuSans-28" x="685.255859"/>
      <use xlink:href="#DejaVuSans-35" x="724.269531"/>
      <use xlink:href="#DejaVuSans-2e" x="787.892578"/>
      <use xlink:href="#DejaVuSans-30" x="819.679688"/>
      <use xlink:href="#DejaVuSans-6d" x="883.302734"/>
      <use xlink:href="#DejaVuSans-29" x="980.714844"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_17">
    <path d="M 330.77382 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 330.77382 71.82685 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAABcAAAHBCAYAAACCOiAmAAACGUlEQVR4nO2b0W0bURDEnqRz3+k7VpwCjvkcAQzIAhbEzuzpLMiPc379nBHXOddq9nnOJp9zrnO+ZsPX5u0ch7eWO1URqYqIuope811bWgtSFZGqiFRFpJ0jvVog6ip6zXue3/Ee0eOcn9mXOVPzhv9nw6/z2A0fm7+Ww3cPxfVadg/FzJmqiJgD9ZpXRcAcqNe8KgLmQL3mVREwB+o1r4qAOVCveVUEzIF6zYdVHJsPe24ONHPAe0Q9z5HMkal5a0EyR3oqIuZAM//0cO9aMkd6KiLmQDPn4bNfWs3X8p4Nzxy5nq/v3fDry7qWlzZQs/myit61PLXmJ/M7a/PlEYnXkvmdqogUKJL5v4bver5dS0dEeM2rIuIN1GteFRFvoF5z9Vo6ojtVEamKSFX8/HDvWjoiZHpE6kC95lURhpsDzfxOR4R0RIjavCrCcHOgXvOqCMPNgWZ+x3tEfSWCZI5URUQdaOY4vPeWO95Ax+bvZRW/tVV8/5kNHwe6W7nY/Hrsar7+rxxtoJnz8KoIZI5M22JeS+aAt4renRco4g3Ua16gSOZIVUQKFPF+EpnX4jXv/D893LuWqohURaRAEa+5eS1VEfCaFyiSOdKfLUjmiLeK3p0XKOIN1GteoEjmSFVErt/aQIe/hVzvfPmWK16L1nwovt65dXiBIu2ch7cWoioiVRGpiojXvECRjgipikhVRLw7967lLyPgue9DbEF4AAAAAElFTkSuQmCC" id="image8e375af017" transform="scale(1 -1) translate(0 -215.52)" x="330.72" y="-71.52" width="11.04" height="215.52"/>
   <g id="matplotlib.axis_5"/>
   <g id="matplotlib.axis_6">
    <g id="ytick_18">
     <g id="line2d_78">
      <defs>
       <path id="m4486c88de9" d="M 0 0 
L 3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m4486c88de9" x="341.56486" y="287.64765" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_50">
      <!-- 0.0 -->
      <g transform="translate(348.56486 291.066947) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_79">
      <g>
       <use xlink:href="#m4486c88de9" x="341.56486" y="244.48349" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_51">
      <!-- 0.2 -->
      <g transform="translate(348.56486 247.902787) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_80">
      <g>
       <use xlink:href="#m4486c88de9" x="341.56486" y="201.31933" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_52">
      <!-- 0.4 -->
      <g transform="translate(348.56486 204.738627) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_81">
      <g>
       <use xlink:href="#m4486c88de9" x="341.56486" y="158.15517" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_53">
      <!-- 0.6 -->
      <g transform="translate(348.56486 161.574467) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_82">
      <g>
       <use xlink:href="#m4486c88de9" x="341.56486" y="114.99101" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_54">
      <!-- 0.8 -->
      <g transform="translate(348.56486 118.410307) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-38" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_23">
     <g id="line2d_83">
      <g>
       <use xlink:href="#m4486c88de9" x="341.56486" y="71.82685" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_55">
      <!-- 1.0 -->
      <g transform="translate(348.56486 75.246147) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_56">
     <!-- Neural Activation -->
     <g transform="translate(369.519391 125.728109) rotate(-270) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_18">
    <path d="M 330.77382 287.64765 
L 336.16934 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 336.16934 71.82685 
L 330.77382 71.82685 
L 330.77382 287.64765 
z
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p9d28241054">
   <rect x="42.113906" y="44.84925" width="269.776" height="269.776"/>
  </clipPath>
  <clipPath id="pd4631c28db">
   <rect x="436.259015" y="44.84925" width="377.678273" height="269.776"/>
  </clipPath>
 </defs>
</svg>
