<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="824.000413pt" height="352.267438pt" viewBox="0 0 824.000413 352.267438" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:56:31.909606</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 352.267438 
L 824.000413 352.267438 
L 824.000413 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
L 311.889906 44.84925 
L 42.113906 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g clip-path="url(#p7aa66c28e4)">
    <image xlink:href="data:image/png;base64,
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" id="image472a57c422" transform="scale(1 -1) translate(0 -270.24)" x="42.113906" y="-44.38525" width="270.24" height="270.24"/>
   </g>
   <g id="patch_3">
    <path d="M 177.001906 230.32025 
C 190.416675 230.32025 203.283815 224.990506 212.769489 215.504832 
C 222.255162 206.019159 227.584906 193.152018 227.584906 179.73725 
C 227.584906 166.322482 222.255162 153.455341 212.769489 143.969668 
C 203.283815 134.483994 190.416675 129.15425 177.001906 129.15425 
C 163.587138 129.15425 150.719998 134.483994 141.234324 143.969668 
C 131.74865 153.455341 126.418906 166.322482 126.418906 179.73725 
C 126.418906 193.152018 131.74865 206.019159 141.234324 215.504832 
C 150.719998 224.990506 163.587138 230.32025 177.001906 230.32025 
L 177.001906 230.32025 
z
" clip-path="url(#p7aa66c28e4)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 177.001906 264.04225 
C 199.359854 264.04225 220.805087 255.159343 236.614543 239.349887 
C 252.424 223.540431 261.306906 202.095197 261.306906 179.73725 
C 261.306906 157.379303 252.424 135.934069 236.614543 120.124613 
C 220.805087 104.315157 199.359854 95.43225 177.001906 95.43225 
C 154.643959 95.43225 133.198725 104.315157 117.389269 120.124613 
C 101.579813 135.934069 92.696906 157.379303 92.696906 179.73725 
C 92.696906 202.095197 101.579813 223.540431 117.389269 239.349887 
C 133.198725 255.159343 154.643959 264.04225 177.001906 264.04225 
L 177.001906 264.04225 
z
" clip-path="url(#p7aa66c28e4)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 177.001906 314.62525 
C 212.774622 314.62525 247.086996 300.412599 272.382126 275.11747 
C 297.677256 249.82234 311.889906 215.509966 311.889906 179.73725 
C 311.889906 143.964534 297.677256 109.65216 272.382126 84.35703 
C 247.086996 59.061901 212.774622 44.84925 177.001906 44.84925 
C 141.22919 44.84925 106.916817 59.061901 81.621687 84.35703 
C 56.326557 109.65216 42.113906 143.964534 42.113906 179.73725 
C 42.113906 215.509966 56.326557 249.82234 81.621687 275.11747 
C 106.916817 300.412599 141.22919 314.62525 177.001906 314.62525 
L 177.001906 314.62525 
z
" clip-path="url(#p7aa66c28e4)" style="fill: none; opacity: 0.7; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" clip-path="url(#p7aa66c28e4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="m8557770462" d="M 0 0 
L 0 3.5 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m8557770462" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −8 -->
      <g transform="translate(35.479922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 75.835906 314.62525 
L 75.835906 44.84925 
" clip-path="url(#p7aa66c28e4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#m8557770462" x="75.835906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −6 -->
      <g transform="translate(69.201922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 109.557906 314.62525 
L 109.557906 44.84925 
" clip-path="url(#p7aa66c28e4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#m8557770462" x="109.557906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_3">
      <!-- −4 -->
      <g transform="translate(102.923922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 143.279906 314.62525 
L 143.279906 44.84925 
" clip-path="url(#p7aa66c28e4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#m8557770462" x="143.279906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_4">
      <!-- −2 -->
      <g transform="translate(136.645922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 177.001906 314.62525 
L 177.001906 44.84925 
" clip-path="url(#p7aa66c28e4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#m8557770462" x="177.001906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(174.138781 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 210.723906 314.62525 
L 210.723906 44.84925 
" clip-path="url(#p7aa66c28e4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#m8557770462" x="210.723906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 2 -->
      <g transform="translate(207.860781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 244.445906 314.62525 
L 244.445906 44.84925 
" clip-path="url(#p7aa66c28e4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#m8557770462" x="244.445906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 4 -->
      <g transform="translate(241.582781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <path d="M 278.167906 314.62525 
L 278.167906 44.84925 
" clip-path="url(#p7aa66c28e4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#m8557770462" x="278.167906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 6 -->
      <g transform="translate(275.304781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_17">
      <path d="M 311.889906 314.62525 
L 311.889906 44.84925 
" clip-path="url(#p7aa66c28e4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#m8557770462" x="311.889906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 8 -->
      <g transform="translate(309.026781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- Relative X Position (m) -->
     <g transform="translate(105.68925 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-52" d="M 2297 2597 
Q 2675 2597 2839 2737 
Q 3003 2878 3003 3200 
Q 3003 3519 2839 3656 
Q 2675 3794 2297 3794 
L 1791 3794 
L 1791 2597 
L 2297 2597 
z
M 1791 1766 
L 1791 0 
L 588 0 
L 588 4666 
L 2425 4666 
Q 3347 4666 3776 4356 
Q 4206 4047 4206 3378 
Q 4206 2916 3982 2619 
Q 3759 2322 3309 2181 
Q 3556 2125 3751 1926 
Q 3947 1728 4147 1325 
L 4800 0 
L 3519 0 
L 2950 1159 
Q 2778 1509 2601 1637 
Q 2425 1766 2131 1766 
L 1791 1766 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6c" d="M 538 4863 
L 1656 4863 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-76" d="M 97 3500 
L 1216 3500 
L 2088 1081 
L 2956 3500 
L 4078 3500 
L 2700 0 
L 1472 0 
L 97 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-58" d="M 3188 2381 
L 4806 0 
L 3553 0 
L 2463 1594 
L 1381 0 
L 122 0 
L 1741 2381 
L 184 4666 
L 1441 4666 
L 2463 3163 
L 3481 4666 
L 4744 4666 
L 3188 2381 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-50" d="M 588 4666 
L 2584 4666 
Q 3475 4666 3951 4270 
Q 4428 3875 4428 3144 
Q 4428 2409 3951 2014 
Q 3475 1619 2584 1619 
L 1791 1619 
L 1791 0 
L 588 0 
L 588 4666 
z
M 1791 3794 
L 1791 2491 
L 2456 2491 
Q 2806 2491 2997 2661 
Q 3188 2831 3188 3144 
Q 3188 3456 2997 3625 
Q 2806 3794 2456 3794 
L 1791 3794 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6f" d="M 2203 2784 
Q 1831 2784 1636 2517 
Q 1441 2250 1441 1747 
Q 1441 1244 1636 976 
Q 1831 709 2203 709 
Q 2569 709 2762 976 
Q 2956 1244 2956 1747 
Q 2956 2250 2762 2517 
Q 2569 2784 2203 2784 
z
M 2203 3584 
Q 3106 3584 3614 3096 
Q 4122 2609 4122 1747 
Q 4122 884 3614 396 
Q 3106 -91 2203 -91 
Q 1297 -91 786 396 
Q 275 884 275 1747 
Q 275 2609 786 3096 
Q 1297 3584 2203 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-28" d="M 2413 -844 
L 1484 -844 
Q 1006 -72 778 623 
Q 550 1319 550 2003 
Q 550 2688 779 3389 
Q 1009 4091 1484 4856 
L 2413 4856 
Q 2013 4116 1813 3408 
Q 1613 2700 1613 2009 
Q 1613 1319 1811 609 
Q 2009 -100 2413 -844 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6d" d="M 3781 2919 
Q 3994 3244 4286 3414 
Q 4578 3584 4928 3584 
Q 5531 3584 5847 3212 
Q 6163 2841 6163 2131 
L 6163 0 
L 5038 0 
L 5038 1825 
Q 5041 1866 5042 1909 
Q 5044 1953 5044 2034 
Q 5044 2406 4934 2573 
Q 4825 2741 4581 2741 
Q 4263 2741 4089 2478 
Q 3916 2216 3909 1719 
L 3909 0 
L 2784 0 
L 2784 1825 
Q 2784 2406 2684 2573 
Q 2584 2741 2328 2741 
Q 2006 2741 1831 2477 
Q 1656 2213 1656 1722 
L 1656 0 
L 531 0 
L 531 3500 
L 1656 3500 
L 1656 2988 
Q 1863 3284 2130 3434 
Q 2397 3584 2719 3584 
Q 3081 3584 3359 3409 
Q 3638 3234 3781 2919 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-29" d="M 513 -844 
Q 913 -100 1113 609 
Q 1313 1319 1313 2009 
Q 1313 2700 1113 3408 
Q 913 4116 513 4856 
L 1441 4856 
Q 1916 4091 2145 3389 
Q 2375 2688 2375 2003 
Q 2375 1319 2147 623 
Q 1919 -72 1441 -844 
L 513 -844 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-58" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="573.583984"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="608.398438"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="681.689453"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="750.390625"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="809.912109"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="844.189453"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="891.992188"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="926.269531"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="994.970703"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1066.162109"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1100.976562"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1146.679688"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1250.878906"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_19">
      <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" clip-path="url(#p7aa66c28e4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <defs>
       <path id="mc900eaaeae" d="M 0 0 
L -3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#mc900eaaeae" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_11">
      <!-- −8 -->
      <g transform="translate(21.845937 318.044547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_21">
      <path d="M 42.113906 280.90325 
L 311.889906 280.90325 
" clip-path="url(#p7aa66c28e4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#mc900eaaeae" x="42.113906" y="280.90325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_12">
      <!-- −6 -->
      <g transform="translate(21.845937 284.322547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_23">
      <path d="M 42.113906 247.18125 
L 311.889906 247.18125 
" clip-path="url(#p7aa66c28e4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#mc900eaaeae" x="42.113906" y="247.18125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_13">
      <!-- −4 -->
      <g transform="translate(21.845937 250.600547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_25">
      <path d="M 42.113906 213.45925 
L 311.889906 213.45925 
" clip-path="url(#p7aa66c28e4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#mc900eaaeae" x="42.113906" y="213.45925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_14">
      <!-- −2 -->
      <g transform="translate(21.845937 216.878547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_27">
      <path d="M 42.113906 179.73725 
L 311.889906 179.73725 
" clip-path="url(#p7aa66c28e4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#mc900eaaeae" x="42.113906" y="179.73725" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 0 -->
      <g transform="translate(29.387656 183.156547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_29">
      <path d="M 42.113906 146.01525 
L 311.889906 146.01525 
" clip-path="url(#p7aa66c28e4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#mc900eaaeae" x="42.113906" y="146.01525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 2 -->
      <g transform="translate(29.387656 149.434547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_31">
      <path d="M 42.113906 112.29325 
L 311.889906 112.29325 
" clip-path="url(#p7aa66c28e4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#mc900eaaeae" x="42.113906" y="112.29325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 4 -->
      <g transform="translate(29.387656 115.712547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_33">
      <path d="M 42.113906 78.57125 
L 311.889906 78.57125 
" clip-path="url(#p7aa66c28e4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#mc900eaaeae" x="42.113906" y="78.57125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 6 -->
      <g transform="translate(29.387656 81.990547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_35">
      <path d="M 42.113906 44.84925 
L 311.889906 44.84925 
" clip-path="url(#p7aa66c28e4)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#mc900eaaeae" x="42.113906" y="44.84925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 8 -->
      <g transform="translate(29.387656 48.268547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_20">
     <!-- Relative Y Position (m) -->
     <g transform="translate(15.558281 250.792094) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-59" d="M -63 4666 
L 1253 4666 
L 2316 3003 
L 3378 4666 
L 4697 4666 
L 2919 1966 
L 2919 0 
L 1716 0 
L 1716 1966 
L -63 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-59" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="568.896484"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="603.710938"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="677.001953"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="745.703125"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="805.224609"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="839.501953"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="887.304688"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="921.582031"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="990.283203"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1061.474609"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1096.289062"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1141.992188"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1246.191406"/>
     </g>
    </g>
   </g>
   <g id="patch_6">
    <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_21">
    <g id="patch_8">
     <path d="M 200.10902 150.124824 
L 225.429957 150.124824 
Q 227.229957 150.124824 227.229957 148.324824 
L 227.229957 139.614511 
Q 227.229957 137.814511 225.429957 137.814511 
L 200.10902 137.814511 
Q 198.30902 137.814511 198.30902 139.614511 
L 198.30902 148.324824 
Q 198.30902 150.124824 200.10902 150.124824 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 3.0m -->
    <g style="fill: #ffffff" transform="translate(200.10902 146.453105) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-33" d="M 2981 2516 
Q 3453 2394 3698 2092 
Q 3944 1791 3944 1325 
Q 3944 631 3412 270 
Q 2881 -91 1863 -91 
Q 1503 -91 1142 -33 
Q 781 25 428 141 
L 428 1069 
Q 766 900 1098 814 
Q 1431 728 1753 728 
Q 2231 728 2486 893 
Q 2741 1059 2741 1369 
Q 2741 1688 2480 1852 
Q 2219 2016 1709 2016 
L 1228 2016 
L 1228 2791 
L 1734 2791 
Q 2188 2791 2409 2933 
Q 2631 3075 2631 3366 
Q 2631 3634 2415 3781 
Q 2200 3928 1806 3928 
Q 1516 3928 1219 3862 
Q 922 3797 628 3669 
L 628 4550 
Q 984 4650 1334 4700 
Q 1684 4750 2022 4750 
Q 2931 4750 3382 4451 
Q 3834 4153 3834 3553 
Q 3834 3144 3618 2883 
Q 3403 2622 2981 2516 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2e" d="M 653 1209 
L 1778 1209 
L 1778 0 
L 653 0 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-30" d="M 2944 2338 
Q 2944 3213 2780 3570 
Q 2616 3928 2228 3928 
Q 1841 3928 1675 3570 
Q 1509 3213 1509 2338 
Q 1509 1453 1675 1090 
Q 1841 728 2228 728 
Q 2613 728 2778 1090 
Q 2944 1453 2944 2338 
z
M 4147 2328 
Q 4147 1169 3647 539 
Q 3147 -91 2228 -91 
Q 1306 -91 806 539 
Q 306 1169 306 2328 
Q 306 3491 806 4120 
Q 1306 4750 2228 4750 
Q 3147 4750 3647 4120 
Q 4147 3491 4147 2328 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-33"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_22">
    <g id="patch_9">
     <path d="M 223.954075 126.279769 
L 249.275012 126.279769 
Q 251.075012 126.279769 251.075012 124.479769 
L 251.075012 115.769457 
Q 251.075012 113.969457 249.275012 113.969457 
L 223.954075 113.969457 
Q 222.154075 113.969457 222.154075 115.769457 
L 222.154075 124.479769 
Q 222.154075 126.279769 223.954075 126.279769 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 5.0m -->
    <g style="fill: #ffffff" transform="translate(223.954075 122.60805) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-35" d="M 678 4666 
L 3669 4666 
L 3669 3781 
L 1638 3781 
L 1638 3059 
Q 1775 3097 1914 3117 
Q 2053 3138 2203 3138 
Q 3056 3138 3531 2711 
Q 4006 2284 4006 1522 
Q 4006 766 3489 337 
Q 2972 -91 2053 -91 
Q 1656 -91 1267 -14 
Q 878 63 494 219 
L 494 1166 
Q 875 947 1217 837 
Q 1559 728 1863 728 
Q 2300 728 2551 942 
Q 2803 1156 2803 1522 
Q 2803 1891 2551 2103 
Q 2300 2316 1863 2316 
Q 1603 2316 1309 2248 
Q 1016 2181 678 2041 
L 678 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-35"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_23">
    <g id="patch_10">
     <path d="M 259.721657 90.512187 
L 285.042595 90.512187 
Q 286.842595 90.512187 286.842595 88.712187 
L 286.842595 80.001874 
Q 286.842595 78.201874 285.042595 78.201874 
L 259.721657 78.201874 
Q 257.921657 78.201874 257.921657 80.001874 
L 257.921657 88.712187 
Q 257.921657 90.512187 259.721657 90.512187 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 8.0m -->
    <g style="fill: #ffffff" transform="translate(259.721657 86.840468) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-38" d="M 2228 2088 
Q 1891 2088 1709 1903 
Q 1528 1719 1528 1375 
Q 1528 1031 1709 848 
Q 1891 666 2228 666 
Q 2563 666 2741 848 
Q 2919 1031 2919 1375 
Q 2919 1722 2741 1905 
Q 2563 2088 2228 2088 
z
M 1350 2484 
Q 925 2613 709 2878 
Q 494 3144 494 3541 
Q 494 4131 934 4440 
Q 1375 4750 2228 4750 
Q 3075 4750 3515 4442 
Q 3956 4134 3956 3541 
Q 3956 3144 3739 2878 
Q 3522 2613 3097 2484 
Q 3572 2353 3814 2058 
Q 4056 1763 4056 1313 
Q 4056 619 3595 264 
Q 3134 -91 2228 -91 
Q 1319 -91 855 264 
Q 391 619 391 1313 
Q 391 1763 633 2058 
Q 875 2353 1350 2484 
z
M 1631 3419 
Q 1631 3141 1786 2991 
Q 1941 2841 2228 2841 
Q 2509 2841 2662 2991 
Q 2816 3141 2816 3419 
Q 2816 3697 2662 3845 
Q 2509 3994 2228 3994 
Q 1941 3994 1786 3844 
Q 1631 3694 1631 3419 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-38"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_24">
    <!-- Far-Distance Cell (Neuron 3) -->
    <g transform="translate(81.107844 16.411875) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-46" d="M 588 4666 
L 3834 4666 
L 3834 3756 
L 1791 3756 
L 1791 2888 
L 3713 2888 
L 3713 1978 
L 1791 1978 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2d" d="M 347 2297 
L 2309 2297 
L 2309 1388 
L 347 1388 
L 347 2297 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-44" d="M 1791 3756 
L 1791 909 
L 2222 909 
Q 2959 909 3348 1275 
Q 3738 1641 3738 2338 
Q 3738 3031 3350 3393 
Q 2963 3756 2222 3756 
L 1791 3756 
z
M 588 4666 
L 1856 4666 
Q 2919 4666 3439 4514 
Q 3959 4363 4331 4000 
Q 4659 3684 4818 3271 
Q 4978 2859 4978 2338 
Q 4978 1809 4818 1395 
Q 4659 981 4331 666 
Q 3956 303 3431 151 
Q 2906 0 1856 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4e" d="M 588 4666 
L 1931 4666 
L 3628 1466 
L 3628 4666 
L 4769 4666 
L 4769 0 
L 3425 0 
L 1728 3200 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-75" d="M 500 1363 
L 500 3500 
L 1625 3500 
L 1625 3150 
Q 1625 2866 1622 2436 
Q 1619 2006 1619 1863 
Q 1619 1441 1641 1255 
Q 1663 1069 1716 984 
Q 1784 875 1895 815 
Q 2006 756 2150 756 
Q 2500 756 2700 1025 
Q 2900 1294 2900 1772 
L 2900 3500 
L 4019 3500 
L 4019 0 
L 2900 0 
L 2900 506 
Q 2647 200 2364 54 
Q 2081 -91 1741 -91 
Q 1134 -91 817 281 
Q 500 653 500 1363 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-46"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="62.435547"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="129.916016"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="179.232422"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="220.736328"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="303.744141"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="338.021484"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="397.542969"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="445.345703"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="512.826172"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="584.017578"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="643.294922"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="711.117188"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="745.931641"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="819.320312"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="887.142578"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="921.419922"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="955.697266"/>
     <use xlink:href="#DejaVuSans-Bold-28" x="990.511719"/>
     <use xlink:href="#DejaVuSans-Bold-4e" x="1036.214844"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1119.90625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1187.728516"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1258.919922"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="1308.236328"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1376.9375"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1448.128906"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="1482.943359"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="1552.523438"/>
    </g>
    <!-- 2D Activation Map -->
    <g transform="translate(114.950656 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-32" d="M 1844 884 
L 3897 884 
L 3897 0 
L 506 0 
L 506 884 
L 2209 2388 
Q 2438 2594 2547 2791 
Q 2656 2988 2656 3200 
Q 2656 3528 2436 3728 
Q 2216 3928 1850 3928 
Q 1569 3928 1234 3808 
Q 900 3688 519 3450 
L 519 4475 
Q 925 4609 1322 4679 
Q 1719 4750 2100 4750 
Q 2938 4750 3402 4381 
Q 3866 4013 3866 3353 
Q 3866 2972 3669 2642 
Q 3472 2313 2841 1759 
L 1844 884 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-41" d="M 3419 850 
L 1538 850 
L 1241 0 
L 31 0 
L 1759 4666 
L 3194 4666 
L 4922 0 
L 3713 0 
L 3419 850 
z
M 1838 1716 
L 3116 1716 
L 2478 3572 
L 1838 1716 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4d" d="M 588 4666 
L 2119 4666 
L 3181 2169 
L 4250 4666 
L 5778 4666 
L 5778 0 
L 4641 0 
L 4641 3413 
L 3566 897 
L 2803 897 
L 1728 3413 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-32"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="152.587891"/>
     <use xlink:href="#DejaVuSans-Bold-41" x="187.402344"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="264.794922"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="324.072266"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="371.875"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="406.152344"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.337891"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="538.818359"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="586.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="620.898438"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="689.599609"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="760.791016"/>
     <use xlink:href="#DejaVuSans-Bold-4d" x="795.605469"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="895.117188"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="962.597656"/>
    </g>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="me6a1e1c50d" d="M 0 -5.477226 
L -1.229714 -1.692556 
L -5.209151 -1.692556 
L -1.989719 0.646499 
L -3.219432 4.431169 
L -0 2.092114 
L 3.219432 4.431169 
L 1.989719 0.646499 
L 5.209151 -1.692556 
L 1.229714 -1.692556 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#p7aa66c28e4)">
     <use xlink:href="#me6a1e1c50d" x="177.001906" y="179.73725" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_11">
     <path d="M 242.388656 62.99175 
L 307.889906 62.99175 
L 307.889906 48.84925 
L 242.388656 48.84925 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="PathCollection_2">
     <g>
      <use xlink:href="#me6a1e1c50d" x="253.588656" y="56.028" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
     </g>
    </g>
    <g id="text_25">
     <!-- Observer -->
     <g transform="translate(267.988656 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4f"/>
      <use xlink:href="#DejaVuSans-62" x="78.710938"/>
      <use xlink:href="#DejaVuSans-73" x="142.1875"/>
      <use xlink:href="#DejaVuSans-65" x="194.287109"/>
      <use xlink:href="#DejaVuSans-72" x="255.810547"/>
      <use xlink:href="#DejaVuSans-76" x="296.923828"/>
      <use xlink:href="#DejaVuSans-65" x="356.103516"/>
      <use xlink:href="#DejaVuSans-72" x="417.626953"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_12">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
L 813.937288 44.84925 
L 436.259015 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="m7ebe587fc9" d="M 440.975868 -37.642188 
L 440.975868 -37.642188 
L 450.409572 -40.156103 
L 459.843277 -39.918281 
L 469.276982 -39.817063 
L 478.710686 -39.858747 
L 488.144391 -39.926199 
L 497.578096 -39.623518 
L 507.011801 -39.873216 
L 516.445505 -41.266392 
L 525.87921 -41.685147 
L 535.312915 -44.21416 
L 544.746619 -44.067376 
L 554.180324 -42.706993 
L 563.614029 -43.588956 
L 573.047733 -49.791635 
L 582.481438 -58.418067 
L 591.915143 -87.378116 
L 601.348847 -80.390547 
L 610.782552 -114.005136 
L 620.216257 -84.768621 
L 629.649962 -139.401887 
L 639.083666 -137.273172 
L 648.517371 -155.024393 
L 657.951076 -213.342707 
L 667.38478 -215.24715 
L 676.818485 -237.864973 
L 686.25219 -223.953312 
L 695.685894 -275.288214 
L 705.119599 -264.227783 
L 714.553304 -294.571711 
L 714.553304 -37.642188 
L 714.553304 -37.642188 
L 705.119599 -37.642188 
L 695.685894 -37.642188 
L 686.25219 -37.642188 
L 676.818485 -37.642188 
L 667.38478 -37.642188 
L 657.951076 -37.642188 
L 648.517371 -37.642188 
L 639.083666 -37.642188 
L 629.649962 -37.642188 
L 620.216257 -37.642188 
L 610.782552 -37.642188 
L 601.348847 -37.642188 
L 591.915143 -37.642188 
L 582.481438 -37.642188 
L 573.047733 -37.642188 
L 563.614029 -37.642188 
L 554.180324 -37.642188 
L 544.746619 -37.642188 
L 535.312915 -37.642188 
L 525.87921 -37.642188 
L 516.445505 -37.642188 
L 507.011801 -37.642188 
L 497.578096 -37.642188 
L 488.144391 -37.642188 
L 478.710686 -37.642188 
L 469.276982 -37.642188 
L 459.843277 -37.642188 
L 450.409572 -37.642188 
L 440.975868 -37.642188 
z
" style="stroke: #33a02c; stroke-opacity: 0.3"/>
    </defs>
    <g clip-path="url(#pf65ee60124)">
     <use xlink:href="#m7ebe587fc9" x="0" y="352.267438" style="fill: #33a02c; fill-opacity: 0.3; stroke: #33a02c; stroke-opacity: 0.3"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10">
     <g id="line2d_37">
      <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" clip-path="url(#pf65ee60124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#m8557770462" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 0 -->
      <g transform="translate(433.39589 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_39">
      <path d="M 483.468799 314.62525 
L 483.468799 44.84925 
" clip-path="url(#pf65ee60124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#m8557770462" x="483.468799" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 1 -->
      <g transform="translate(480.605674 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_41">
      <path d="M 530.678584 314.62525 
L 530.678584 44.84925 
" clip-path="url(#pf65ee60124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#m8557770462" x="530.678584" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 2 -->
      <g transform="translate(527.815459 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_43">
      <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#pf65ee60124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#m8557770462" x="577.888368" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 3 -->
      <g transform="translate(575.025243 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_45">
      <path d="M 625.098152 314.62525 
L 625.098152 44.84925 
" clip-path="url(#pf65ee60124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#m8557770462" x="625.098152" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 4 -->
      <g transform="translate(622.235027 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_47">
      <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#pf65ee60124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#m8557770462" x="672.307936" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 5 -->
      <g transform="translate(669.444811 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_49">
      <path d="M 719.51772 314.62525 
L 719.51772 44.84925 
" clip-path="url(#pf65ee60124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#m8557770462" x="719.51772" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 6 -->
      <g transform="translate(716.654595 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_51">
      <path d="M 766.727504 314.62525 
L 766.727504 44.84925 
" clip-path="url(#pf65ee60124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#m8557770462" x="766.727504" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 7 -->
      <g transform="translate(763.864379 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-37"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_53">
      <path d="M 813.937288 314.62525 
L 813.937288 44.84925 
" clip-path="url(#pf65ee60124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#m8557770462" x="813.937288" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 8 -->
      <g transform="translate(811.074163 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_35">
     <!-- Inter-Agent Distance (m) -->
     <g transform="translate(547.755261 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-49" d="M 588 4666 
L 1791 4666 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-49"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="37.207031"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="108.398438"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="156.201172"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="224.023438"/>
      <use xlink:href="#DejaVuSans-Bold-2d" x="273.339844"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="314.84375"/>
      <use xlink:href="#DejaVuSans-Bold-67" x="392.236328"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="463.818359"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="531.640625"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="602.832031"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="650.634766"/>
      <use xlink:href="#DejaVuSans-Bold-44" x="685.449219"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="768.457031"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="802.734375"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="862.255859"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="910.058594"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="977.539062"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="1048.730469"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="1108.007812"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1175.830078"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1210.644531"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1256.347656"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1360.546875"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_55">
      <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" clip-path="url(#pf65ee60124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#mc900eaaeae" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 0.0 -->
      <g transform="translate(414.946203 318.044547) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_57">
      <path d="M 436.259015 275.916647 
L 813.937288 275.916647 
" clip-path="url(#pf65ee60124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#mc900eaaeae" x="436.259015" y="275.916647" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 0.2 -->
      <g transform="translate(414.946203 279.335944) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_59">
      <path d="M 436.259015 237.208045 
L 813.937288 237.208045 
" clip-path="url(#pf65ee60124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#mc900eaaeae" x="436.259015" y="237.208045" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 0.4 -->
      <g transform="translate(414.946203 240.627342) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_61">
      <path d="M 436.259015 198.499442 
L 813.937288 198.499442 
" clip-path="url(#pf65ee60124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#mc900eaaeae" x="436.259015" y="198.499442" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 0.6 -->
      <g transform="translate(414.946203 201.918739) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_63">
      <path d="M 436.259015 159.79084 
L 813.937288 159.79084 
" clip-path="url(#pf65ee60124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#mc900eaaeae" x="436.259015" y="159.79084" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 0.8 -->
      <g transform="translate(414.946203 163.210136) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-38" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_65">
      <path d="M 436.259015 121.082237 
L 813.937288 121.082237 
" clip-path="url(#pf65ee60124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#mc900eaaeae" x="436.259015" y="121.082237" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 1.0 -->
      <g transform="translate(414.946203 124.501534) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_67">
      <path d="M 436.259015 82.373634 
L 813.937288 82.373634 
" clip-path="url(#pf65ee60124)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_68">
      <g>
       <use xlink:href="#mc900eaaeae" x="436.259015" y="82.373634" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_42">
      <!-- 1.2 -->
      <g transform="translate(414.946203 85.792931) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_43">
     <!-- Neural Activation -->
     <g transform="translate(408.658547 233.746391) rotate(-90) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="line2d_69">
    <path d="M 440.975868 314.62525 
L 450.409572 312.111334 
L 459.843277 312.349156 
L 469.276982 312.450374 
L 478.710686 312.408691 
L 488.144391 312.341238 
L 497.578096 312.643919 
L 507.011801 312.394222 
L 516.445505 311.001046 
L 525.87921 310.582291 
L 535.312915 308.053277 
L 544.746619 308.200062 
L 554.180324 309.560445 
L 563.614029 308.678481 
L 573.047733 302.475802 
L 582.481438 293.849371 
L 591.915143 264.889322 
L 601.348847 271.876891 
L 610.782552 238.262301 
L 620.216257 267.498817 
L 629.649962 212.86555 
L 639.083666 214.994265 
L 648.517371 197.243045 
L 657.951076 138.92473 
L 667.38478 137.020288 
L 676.818485 114.402464 
L 686.25219 128.314125 
L 695.685894 76.979224 
L 705.119599 88.039654 
L 714.553304 57.695726 
" clip-path="url(#pf65ee60124)" style="fill: none; stroke: #33a02c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_70">
    <path d="M 714.553304 314.62525 
L 714.553304 44.84925 
" clip-path="url(#pf65ee60124)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #33a02c; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_71">
    <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#pf65ee60124)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_72">
    <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#pf65ee60124)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="patch_13">
    <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_14">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_44">
    <g id="patch_15">
     <path d="M 700.612124 114.0718 
L 795.053374 114.0718 
Q 798.253374 114.0718 798.253374 110.8718 
L 798.253374 58.33805 
Q 798.253374 55.13805 795.053374 55.13805 
L 700.612124 55.13805 
Q 697.412124 55.13805 697.412124 58.33805 
L 697.412124 110.8718 
Q 697.412124 114.0718 700.612124 114.0718 
z
" style="fill: #ffffff; opacity: 0.9; stroke: #808080; stroke-linejoin: miter"/>
    </g>
    <!-- Peak Distance: 5.9m -->
    <g transform="translate(712.787124 64.4168) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-3a" d="M 750 794 
L 1409 794 
L 1409 0 
L 750 0 
L 750 794 
z
M 750 3309 
L 1409 3309 
L 1409 2516 
L 750 2516 
L 750 3309 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-44" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="346.179688"/>
     <use xlink:href="#DejaVuSans-73" x="373.962891"/>
     <use xlink:href="#DejaVuSans-74" x="426.0625"/>
     <use xlink:href="#DejaVuSans-61" x="465.271484"/>
     <use xlink:href="#DejaVuSans-6e" x="526.550781"/>
     <use xlink:href="#DejaVuSans-63" x="589.929688"/>
     <use xlink:href="#DejaVuSans-65" x="644.910156"/>
     <use xlink:href="#DejaVuSans-3a" x="706.433594"/>
     <use xlink:href="#DejaVuSans-20" x="740.125"/>
     <use xlink:href="#DejaVuSans-35" x="771.912109"/>
     <use xlink:href="#DejaVuSans-2e" x="835.535156"/>
     <use xlink:href="#DejaVuSans-39" x="867.322266"/>
     <use xlink:href="#DejaVuSans-6d" x="930.945312"/>
    </g>
    <!-- Max Activation: 1.328 -->
    <g transform="translate(707.779624 73.37505) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-4d"/>
     <use xlink:href="#DejaVuSans-61" x="86.279297"/>
     <use xlink:href="#DejaVuSans-78" x="147.558594"/>
     <use xlink:href="#DejaVuSans-20" x="206.738281"/>
     <use xlink:href="#DejaVuSans-41" x="238.525391"/>
     <use xlink:href="#DejaVuSans-63" x="305.183594"/>
     <use xlink:href="#DejaVuSans-74" x="360.164062"/>
     <use xlink:href="#DejaVuSans-69" x="399.373047"/>
     <use xlink:href="#DejaVuSans-76" x="427.15625"/>
     <use xlink:href="#DejaVuSans-61" x="486.335938"/>
     <use xlink:href="#DejaVuSans-74" x="547.615234"/>
     <use xlink:href="#DejaVuSans-69" x="586.824219"/>
     <use xlink:href="#DejaVuSans-6f" x="614.607422"/>
     <use xlink:href="#DejaVuSans-6e" x="675.789062"/>
     <use xlink:href="#DejaVuSans-3a" x="739.167969"/>
     <use xlink:href="#DejaVuSans-20" x="772.859375"/>
     <use xlink:href="#DejaVuSans-31" x="804.646484"/>
     <use xlink:href="#DejaVuSans-2e" x="868.269531"/>
     <use xlink:href="#DejaVuSans-33" x="900.056641"/>
     <use xlink:href="#DejaVuSans-32" x="963.679688"/>
     <use xlink:href="#DejaVuSans-38" x="1027.302734"/>
    </g>
    <!-- Sparsity: 0.620 -->
    <g transform="translate(734.883374 82.3333) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-70" x="63.476562"/>
     <use xlink:href="#DejaVuSans-61" x="126.953125"/>
     <use xlink:href="#DejaVuSans-72" x="188.232422"/>
     <use xlink:href="#DejaVuSans-73" x="229.345703"/>
     <use xlink:href="#DejaVuSans-69" x="281.445312"/>
     <use xlink:href="#DejaVuSans-74" x="309.228516"/>
     <use xlink:href="#DejaVuSans-79" x="348.4375"/>
     <use xlink:href="#DejaVuSans-3a" x="400.367188"/>
     <use xlink:href="#DejaVuSans-20" x="434.058594"/>
     <use xlink:href="#DejaVuSans-30" x="465.845703"/>
     <use xlink:href="#DejaVuSans-2e" x="529.46875"/>
     <use xlink:href="#DejaVuSans-36" x="561.255859"/>
     <use xlink:href="#DejaVuSans-32" x="624.878906"/>
     <use xlink:href="#DejaVuSans-30" x="688.501953"/>
    </g>
    <!-- Peak Significance: 3.00 -->
    <g transform="translate(702.065874 91.29155) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-53" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="332.654297"/>
     <use xlink:href="#DejaVuSans-67" x="360.4375"/>
     <use xlink:href="#DejaVuSans-6e" x="423.914062"/>
     <use xlink:href="#DejaVuSans-69" x="487.292969"/>
     <use xlink:href="#DejaVuSans-66" x="515.076172"/>
     <use xlink:href="#DejaVuSans-69" x="550.28125"/>
     <use xlink:href="#DejaVuSans-63" x="578.064453"/>
     <use xlink:href="#DejaVuSans-61" x="633.044922"/>
     <use xlink:href="#DejaVuSans-6e" x="694.324219"/>
     <use xlink:href="#DejaVuSans-63" x="757.703125"/>
     <use xlink:href="#DejaVuSans-65" x="812.683594"/>
     <use xlink:href="#DejaVuSans-3a" x="874.207031"/>
     <use xlink:href="#DejaVuSans-20" x="907.898438"/>
     <use xlink:href="#DejaVuSans-33" x="939.685547"/>
     <use xlink:href="#DejaVuSans-2e" x="1003.308594"/>
     <use xlink:href="#DejaVuSans-30" x="1035.095703"/>
     <use xlink:href="#DejaVuSans-30" x="1098.71875"/>
    </g>
    <!-- Selectivity Index: 0.807 -->
    <g transform="translate(700.612124 100.2498) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-65" x="63.476562"/>
     <use xlink:href="#DejaVuSans-6c" x="125"/>
     <use xlink:href="#DejaVuSans-65" x="152.783203"/>
     <use xlink:href="#DejaVuSans-63" x="214.306641"/>
     <use xlink:href="#DejaVuSans-74" x="269.287109"/>
     <use xlink:href="#DejaVuSans-69" x="308.496094"/>
     <use xlink:href="#DejaVuSans-76" x="336.279297"/>
     <use xlink:href="#DejaVuSans-69" x="395.458984"/>
     <use xlink:href="#DejaVuSans-74" x="423.242188"/>
     <use xlink:href="#DejaVuSans-79" x="462.451172"/>
     <use xlink:href="#DejaVuSans-20" x="521.630859"/>
     <use xlink:href="#DejaVuSans-49" x="553.417969"/>
     <use xlink:href="#DejaVuSans-6e" x="582.910156"/>
     <use xlink:href="#DejaVuSans-64" x="646.289062"/>
     <use xlink:href="#DejaVuSans-65" x="709.765625"/>
     <use xlink:href="#DejaVuSans-78" x="769.539062"/>
     <use xlink:href="#DejaVuSans-3a" x="828.71875"/>
     <use xlink:href="#DejaVuSans-20" x="862.410156"/>
     <use xlink:href="#DejaVuSans-30" x="894.197266"/>
     <use xlink:href="#DejaVuSans-2e" x="957.820312"/>
     <use xlink:href="#DejaVuSans-38" x="989.607422"/>
     <use xlink:href="#DejaVuSans-30" x="1053.230469"/>
     <use xlink:href="#DejaVuSans-37" x="1116.853516"/>
    </g>
    <!-- Peak Width: 1.2m -->
    <g transform="translate(724.529624 109.20805) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-57" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="365.804688"/>
     <use xlink:href="#DejaVuSans-64" x="393.587891"/>
     <use xlink:href="#DejaVuSans-74" x="457.064453"/>
     <use xlink:href="#DejaVuSans-68" x="496.273438"/>
     <use xlink:href="#DejaVuSans-3a" x="559.652344"/>
     <use xlink:href="#DejaVuSans-20" x="593.34375"/>
     <use xlink:href="#DejaVuSans-31" x="625.130859"/>
     <use xlink:href="#DejaVuSans-2e" x="688.753906"/>
     <use xlink:href="#DejaVuSans-32" x="720.541016"/>
     <use xlink:href="#DejaVuSans-6d" x="784.164062"/>
    </g>
   </g>
   <g id="text_45">
    <!-- Distance Tuning Curve -->
    <g transform="translate(549.284402 16.318125) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-54" d="M 31 4666 
L 4331 4666 
L 4331 3756 
L 2784 3756 
L 2784 0 
L 1581 0 
L 1581 3756 
L 31 3756 
L 31 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-44"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
     <use xlink:href="#DejaVuSans-Bold-54" x="525.195312"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="582.408203"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="653.599609"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="724.791016"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="759.068359"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="830.259766"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="901.841797"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="936.65625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1010.044922"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1081.236328"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="1130.552734"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1195.738281"/>
    </g>
    <!-- Peak: 5.9m, Sparsity: 0.620 -->
    <g transform="translate(531.359402 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-6b" d="M 538 4863 
L 1656 4863 
L 1656 2216 
L 2944 3500 
L 4244 3500 
L 2534 1894 
L 4378 0 
L 3022 0 
L 1656 1459 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-3a" d="M 716 3500 
L 1844 3500 
L 1844 2291 
L 716 2291 
L 716 3500 
z
M 716 1209 
L 1844 1209 
L 1844 0 
L 716 0 
L 716 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-39" d="M 641 103 
L 641 966 
Q 928 831 1190 764 
Q 1453 697 1709 697 
Q 2247 697 2547 995 
Q 2847 1294 2900 1881 
Q 2688 1725 2447 1647 
Q 2206 1569 1925 1569 
Q 1209 1569 770 1986 
Q 331 2403 331 3084 
Q 331 3838 820 4291 
Q 1309 4744 2131 4744 
Q 3044 4744 3544 4128 
Q 4044 3513 4044 2388 
Q 4044 1231 3459 570 
Q 2875 -91 1856 -91 
Q 1528 -91 1228 -42 
Q 928 6 641 103 
z
M 2125 2350 
Q 2441 2350 2600 2554 
Q 2759 2759 2759 3169 
Q 2759 3575 2600 3781 
Q 2441 3988 2125 3988 
Q 1809 3988 1650 3781 
Q 1491 3575 1491 3169 
Q 1491 2759 1650 2554 
Q 1809 2350 2125 2350 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2c" d="M 653 1209 
L 1778 1209 
L 1778 256 
L 1006 -909 
L 341 -909 
L 653 256 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-53" d="M 3834 4519 
L 3834 3531 
Q 3450 3703 3084 3790 
Q 2719 3878 2394 3878 
Q 1963 3878 1756 3759 
Q 1550 3641 1550 3391 
Q 1550 3203 1689 3098 
Q 1828 2994 2194 2919 
L 2706 2816 
Q 3484 2659 3812 2340 
Q 4141 2022 4141 1434 
Q 4141 663 3683 286 
Q 3225 -91 2284 -91 
Q 1841 -91 1394 -6 
Q 947 78 500 244 
L 500 1259 
Q 947 1022 1364 901 
Q 1781 781 2169 781 
Q 2563 781 2772 912 
Q 2981 1044 2981 1288 
Q 2981 1506 2839 1625 
Q 2697 1744 2272 1838 
L 1806 1941 
Q 1106 2091 782 2419 
Q 459 2747 459 3303 
Q 459 4000 909 4375 
Q 1359 4750 2203 4750 
Q 2588 4750 2994 4692 
Q 3400 4634 3834 4519 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-79" d="M 78 3500 
L 1197 3500 
L 2138 1125 
L 2938 3500 
L 4056 3500 
L 2584 -331 
Q 2363 -916 2067 -1148 
Q 1772 -1381 1288 -1381 
L 641 -1381 
L 641 -647 
L 991 -647 
Q 1275 -647 1404 -556 
Q 1534 -466 1606 -231 
L 1638 -134 
L 78 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-36" d="M 2316 2303 
Q 2000 2303 1842 2098 
Q 1684 1894 1684 1484 
Q 1684 1075 1842 870 
Q 2000 666 2316 666 
Q 2634 666 2792 870 
Q 2950 1075 2950 1484 
Q 2950 1894 2792 2098 
Q 2634 2303 2316 2303 
z
M 3803 4544 
L 3803 3681 
Q 3506 3822 3243 3889 
Q 2981 3956 2731 3956 
Q 2194 3956 1894 3657 
Q 1594 3359 1544 2772 
Q 1750 2925 1990 3001 
Q 2231 3078 2516 3078 
Q 3231 3078 3670 2659 
Q 4109 2241 4109 1563 
Q 4109 813 3618 361 
Q 3128 -91 2303 -91 
Q 1394 -91 895 523 
Q 397 1138 397 2266 
Q 397 3422 980 4083 
Q 1563 4744 2578 4744 
Q 2900 4744 3203 4694 
Q 3506 4644 3803 4544 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-50"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="73.291016"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="141.113281"/>
     <use xlink:href="#DejaVuSans-Bold-6b" x="208.59375"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="275.097656"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="315.087891"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="349.902344"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="419.482422"/>
     <use xlink:href="#DejaVuSans-Bold-39" x="457.470703"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="527.050781"/>
     <use xlink:href="#DejaVuSans-Bold-2c" x="631.25"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="669.238281"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="704.052734"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="776.074219"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="847.65625"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="915.136719"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="964.453125"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1023.974609"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="1058.251953"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="1106.054688"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="1171.240234"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1211.230469"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1246.044922"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="1315.625"/>
     <use xlink:href="#DejaVuSans-Bold-36" x="1353.613281"/>
     <use xlink:href="#DejaVuSans-Bold-32" x="1423.193359"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1492.773438"/>
    </g>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="ma4c85128d1" d="M 0 5 
C 1.326016 5 2.597899 4.473168 3.535534 3.535534 
C 4.473168 2.597899 5 1.326016 5 0 
C 5 -1.326016 4.473168 -2.597899 3.535534 -3.535534 
C 2.597899 -4.473168 1.326016 -5 0 -5 
C -1.326016 -5 -2.597899 -4.473168 -3.535534 -3.535534 
C -4.473168 -2.597899 -5 -1.326016 -5 0 
C -5 1.326016 -4.473168 2.597899 -3.535534 3.535534 
C -2.597899 4.473168 -1.326016 5 0 5 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#pf65ee60124)">
     <use xlink:href="#ma4c85128d1" x="714.553304" y="57.695726" style="fill: #33a02c; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="patch_16">
     <path d="M 441.859015 86.47675 
L 558.766515 86.47675 
Q 560.366515 86.47675 560.366515 84.87675 
L 560.366515 50.44925 
Q 560.366515 48.84925 558.766515 48.84925 
L 441.859015 48.84925 
Q 440.259015 48.84925 440.259015 50.44925 
L 440.259015 84.87675 
Q 440.259015 86.47675 441.859015 86.47675 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_73">
     <path d="M 443.459015 55.328 
L 451.459015 55.328 
L 459.459015 55.328 
" style="fill: none; stroke: #33a02c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
    </g>
    <g id="text_46">
     <!-- Tuning Curve -->
     <g transform="translate(465.859015 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-54"/>
      <use xlink:href="#DejaVuSans-75" x="45.958984"/>
      <use xlink:href="#DejaVuSans-6e" x="109.337891"/>
      <use xlink:href="#DejaVuSans-69" x="172.716797"/>
      <use xlink:href="#DejaVuSans-6e" x="200.5"/>
      <use xlink:href="#DejaVuSans-67" x="263.878906"/>
      <use xlink:href="#DejaVuSans-20" x="327.355469"/>
      <use xlink:href="#DejaVuSans-43" x="359.142578"/>
      <use xlink:href="#DejaVuSans-75" x="428.966797"/>
      <use xlink:href="#DejaVuSans-72" x="492.345703"/>
      <use xlink:href="#DejaVuSans-76" x="533.458984"/>
      <use xlink:href="#DejaVuSans-65" x="592.638672"/>
     </g>
    </g>
    <g id="line2d_74">
     <path d="M 443.459015 67.0705 
L 451.459015 67.0705 
L 459.459015 67.0705 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_47">
     <!-- Close threshold (3.0m) -->
     <g transform="translate(465.859015 69.8705) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6c" x="69.824219"/>
      <use xlink:href="#DejaVuSans-6f" x="97.607422"/>
      <use xlink:href="#DejaVuSans-73" x="158.789062"/>
      <use xlink:href="#DejaVuSans-65" x="210.888672"/>
      <use xlink:href="#DejaVuSans-20" x="272.412109"/>
      <use xlink:href="#DejaVuSans-74" x="304.199219"/>
      <use xlink:href="#DejaVuSans-68" x="343.408203"/>
      <use xlink:href="#DejaVuSans-72" x="406.787109"/>
      <use xlink:href="#DejaVuSans-65" x="445.650391"/>
      <use xlink:href="#DejaVuSans-73" x="507.173828"/>
      <use xlink:href="#DejaVuSans-68" x="559.273438"/>
      <use xlink:href="#DejaVuSans-6f" x="622.652344"/>
      <use xlink:href="#DejaVuSans-6c" x="683.833984"/>
      <use xlink:href="#DejaVuSans-64" x="711.617188"/>
      <use xlink:href="#DejaVuSans-20" x="775.09375"/>
      <use xlink:href="#DejaVuSans-28" x="806.880859"/>
      <use xlink:href="#DejaVuSans-33" x="845.894531"/>
      <use xlink:href="#DejaVuSans-2e" x="909.517578"/>
      <use xlink:href="#DejaVuSans-30" x="941.304688"/>
      <use xlink:href="#DejaVuSans-6d" x="1004.927734"/>
      <use xlink:href="#DejaVuSans-29" x="1102.339844"/>
     </g>
    </g>
    <g id="line2d_75">
     <path d="M 443.459015 78.813 
L 451.459015 78.813 
L 459.459015 78.813 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_48">
     <!-- Far threshold (5.0m) -->
     <g transform="translate(465.859015 81.613) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" x="48.394531"/>
      <use xlink:href="#DejaVuSans-72" x="109.673828"/>
      <use xlink:href="#DejaVuSans-20" x="150.787109"/>
      <use xlink:href="#DejaVuSans-74" x="182.574219"/>
      <use xlink:href="#DejaVuSans-68" x="221.783203"/>
      <use xlink:href="#DejaVuSans-72" x="285.162109"/>
      <use xlink:href="#DejaVuSans-65" x="324.025391"/>
      <use xlink:href="#DejaVuSans-73" x="385.548828"/>
      <use xlink:href="#DejaVuSans-68" x="437.648438"/>
      <use xlink:href="#DejaVuSans-6f" x="501.027344"/>
      <use xlink:href="#DejaVuSans-6c" x="562.208984"/>
      <use xlink:href="#DejaVuSans-64" x="589.992188"/>
      <use xlink:href="#DejaVuSans-20" x="653.46875"/>
      <use xlink:href="#DejaVuSans-28" x="685.255859"/>
      <use xlink:href="#DejaVuSans-35" x="724.269531"/>
      <use xlink:href="#DejaVuSans-2e" x="787.892578"/>
      <use xlink:href="#DejaVuSans-30" x="819.679688"/>
      <use xlink:href="#DejaVuSans-6d" x="883.302734"/>
      <use xlink:href="#DejaVuSans-29" x="980.714844"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_17">
    <path d="M 330.77382 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 330.77382 71.82685 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAABcAAAHBCAYAAACCOiAmAAACGUlEQVR4nO2b0W0bURDEnqRz3+k7VpwCjvkcAQzIAhbEzuzpLMiPc379nBHXOddq9nnOJp9zrnO+ZsPX5u0ch7eWO1URqYqIuope811bWgtSFZGqiFRFpJ0jvVog6ip6zXue3/Ee0eOcn9mXOVPzhv9nw6/z2A0fm7+Ww3cPxfVadg/FzJmqiJgD9ZpXRcAcqNe8KgLmQL3mVREwB+o1r4qAOVCveVUEzIF6zYdVHJsPe24ONHPAe0Q9z5HMkal5a0EyR3oqIuZAM//0cO9aMkd6KiLmQDPn4bNfWs3X8p4Nzxy5nq/v3fDry7qWlzZQs/myit61PLXmJ/M7a/PlEYnXkvmdqogUKJL5v4bver5dS0dEeM2rIuIN1GteFRFvoF5z9Vo6ojtVEamKSFX8/HDvWjoiZHpE6kC95lURhpsDzfxOR4R0RIjavCrCcHOgXvOqCMPNgWZ+x3tEfSWCZI5URUQdaOY4vPeWO95Ax+bvZRW/tVV8/5kNHwe6W7nY/Hrsar7+rxxtoJnz8KoIZI5M22JeS+aAt4renRco4g3Ua16gSOZIVUQKFPF+EpnX4jXv/D893LuWqohURaRAEa+5eS1VEfCaFyiSOdKfLUjmiLeK3p0XKOIN1GteoEjmSFVErt/aQIe/hVzvfPmWK16L1nwovt65dXiBIu2ch7cWoioiVRGpiojXvECRjgipikhVRLw7967lLyPgue9DbEF4AAAAAElFTkSuQmCC" id="image227c7b6e8b" transform="scale(1 -1) translate(0 -215.52)" x="330.72" y="-71.52" width="11.04" height="215.52"/>
   <g id="matplotlib.axis_5"/>
   <g id="matplotlib.axis_6">
    <g id="ytick_17">
     <g id="line2d_76">
      <defs>
       <path id="m15a741c567" d="M 0 0 
L 3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m15a741c567" x="341.56486" y="287.64765" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_49">
      <!-- 0.0 -->
      <g transform="translate(348.56486 291.066947) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_77">
      <g>
       <use xlink:href="#m15a741c567" x="341.56486" y="244.48349" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_50">
      <!-- 0.2 -->
      <g transform="translate(348.56486 247.902787) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_78">
      <g>
       <use xlink:href="#m15a741c567" x="341.56486" y="201.31933" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_51">
      <!-- 0.4 -->
      <g transform="translate(348.56486 204.738627) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_79">
      <g>
       <use xlink:href="#m15a741c567" x="341.56486" y="158.15517" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_52">
      <!-- 0.6 -->
      <g transform="translate(348.56486 161.574467) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_80">
      <g>
       <use xlink:href="#m15a741c567" x="341.56486" y="114.99101" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_53">
      <!-- 0.8 -->
      <g transform="translate(348.56486 118.410307) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-38" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_81">
      <g>
       <use xlink:href="#m15a741c567" x="341.56486" y="71.82685" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_54">
      <!-- 1.0 -->
      <g transform="translate(348.56486 75.246147) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_55">
     <!-- Neural Activation -->
     <g transform="translate(369.519391 125.728109) rotate(-270) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_18">
    <path d="M 330.77382 287.64765 
L 336.16934 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 336.16934 71.82685 
L 330.77382 71.82685 
L 330.77382 287.64765 
z
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p7aa66c28e4">
   <rect x="42.113906" y="44.84925" width="269.776" height="269.776"/>
  </clipPath>
  <clipPath id="pf65ee60124">
   <rect x="436.259015" y="44.84925" width="377.678273" height="269.776"/>
  </clipPath>
 </defs>
</svg>
