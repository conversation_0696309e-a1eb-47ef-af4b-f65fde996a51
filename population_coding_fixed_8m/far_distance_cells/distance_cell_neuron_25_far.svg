<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="826.256413pt" height="352.267438pt" viewBox="0 0 826.256413 352.267438" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:56:29.666992</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 352.267438 
L 826.256413 352.267438 
L 826.256413 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
L 311.889906 44.84925 
L 42.113906 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g clip-path="url(#p43758ec6d2)">
    <image xlink:href="data:image/png;base64,
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" id="imagec2b116d222" transform="scale(1 -1) translate(0 -270.24)" x="42.113906" y="-44.38525" width="270.24" height="270.24"/>
   </g>
   <g id="patch_3">
    <path d="M 177.001906 230.32025 
C 190.416675 230.32025 203.283815 224.990506 212.769489 215.504832 
C 222.255162 206.019159 227.584906 193.152018 227.584906 179.73725 
C 227.584906 166.322482 222.255162 153.455341 212.769489 143.969668 
C 203.283815 134.483994 190.416675 129.15425 177.001906 129.15425 
C 163.587138 129.15425 150.719998 134.483994 141.234324 143.969668 
C 131.74865 153.455341 126.418906 166.322482 126.418906 179.73725 
C 126.418906 193.152018 131.74865 206.019159 141.234324 215.504832 
C 150.719998 224.990506 163.587138 230.32025 177.001906 230.32025 
L 177.001906 230.32025 
z
" clip-path="url(#p43758ec6d2)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 177.001906 264.04225 
C 199.359854 264.04225 220.805087 255.159343 236.614543 239.349887 
C 252.424 223.540431 261.306906 202.095197 261.306906 179.73725 
C 261.306906 157.379303 252.424 135.934069 236.614543 120.124613 
C 220.805087 104.315157 199.359854 95.43225 177.001906 95.43225 
C 154.643959 95.43225 133.198725 104.315157 117.389269 120.124613 
C 101.579813 135.934069 92.696906 157.379303 92.696906 179.73725 
C 92.696906 202.095197 101.579813 223.540431 117.389269 239.349887 
C 133.198725 255.159343 154.643959 264.04225 177.001906 264.04225 
L 177.001906 264.04225 
z
" clip-path="url(#p43758ec6d2)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 177.001906 314.62525 
C 212.774622 314.62525 247.086996 300.412599 272.382126 275.11747 
C 297.677256 249.82234 311.889906 215.509966 311.889906 179.73725 
C 311.889906 143.964534 297.677256 109.65216 272.382126 84.35703 
C 247.086996 59.061901 212.774622 44.84925 177.001906 44.84925 
C 141.22919 44.84925 106.916817 59.061901 81.621687 84.35703 
C 56.326557 109.65216 42.113906 143.964534 42.113906 179.73725 
C 42.113906 215.509966 56.326557 249.82234 81.621687 275.11747 
C 106.916817 300.412599 141.22919 314.62525 177.001906 314.62525 
L 177.001906 314.62525 
z
" clip-path="url(#p43758ec6d2)" style="fill: none; opacity: 0.7; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" clip-path="url(#p43758ec6d2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="m5b5937f554" d="M 0 0 
L 0 3.5 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m5b5937f554" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −8 -->
      <g transform="translate(35.479922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 75.835906 314.62525 
L 75.835906 44.84925 
" clip-path="url(#p43758ec6d2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#m5b5937f554" x="75.835906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −6 -->
      <g transform="translate(69.201922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 109.557906 314.62525 
L 109.557906 44.84925 
" clip-path="url(#p43758ec6d2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#m5b5937f554" x="109.557906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_3">
      <!-- −4 -->
      <g transform="translate(102.923922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 143.279906 314.62525 
L 143.279906 44.84925 
" clip-path="url(#p43758ec6d2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#m5b5937f554" x="143.279906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_4">
      <!-- −2 -->
      <g transform="translate(136.645922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 177.001906 314.62525 
L 177.001906 44.84925 
" clip-path="url(#p43758ec6d2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#m5b5937f554" x="177.001906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(174.138781 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 210.723906 314.62525 
L 210.723906 44.84925 
" clip-path="url(#p43758ec6d2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#m5b5937f554" x="210.723906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 2 -->
      <g transform="translate(207.860781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 244.445906 314.62525 
L 244.445906 44.84925 
" clip-path="url(#p43758ec6d2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#m5b5937f554" x="244.445906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 4 -->
      <g transform="translate(241.582781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <path d="M 278.167906 314.62525 
L 278.167906 44.84925 
" clip-path="url(#p43758ec6d2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#m5b5937f554" x="278.167906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 6 -->
      <g transform="translate(275.304781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_17">
      <path d="M 311.889906 314.62525 
L 311.889906 44.84925 
" clip-path="url(#p43758ec6d2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#m5b5937f554" x="311.889906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 8 -->
      <g transform="translate(309.026781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- Relative X Position (m) -->
     <g transform="translate(105.68925 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-52" d="M 2297 2597 
Q 2675 2597 2839 2737 
Q 3003 2878 3003 3200 
Q 3003 3519 2839 3656 
Q 2675 3794 2297 3794 
L 1791 3794 
L 1791 2597 
L 2297 2597 
z
M 1791 1766 
L 1791 0 
L 588 0 
L 588 4666 
L 2425 4666 
Q 3347 4666 3776 4356 
Q 4206 4047 4206 3378 
Q 4206 2916 3982 2619 
Q 3759 2322 3309 2181 
Q 3556 2125 3751 1926 
Q 3947 1728 4147 1325 
L 4800 0 
L 3519 0 
L 2950 1159 
Q 2778 1509 2601 1637 
Q 2425 1766 2131 1766 
L 1791 1766 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6c" d="M 538 4863 
L 1656 4863 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-76" d="M 97 3500 
L 1216 3500 
L 2088 1081 
L 2956 3500 
L 4078 3500 
L 2700 0 
L 1472 0 
L 97 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-58" d="M 3188 2381 
L 4806 0 
L 3553 0 
L 2463 1594 
L 1381 0 
L 122 0 
L 1741 2381 
L 184 4666 
L 1441 4666 
L 2463 3163 
L 3481 4666 
L 4744 4666 
L 3188 2381 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-50" d="M 588 4666 
L 2584 4666 
Q 3475 4666 3951 4270 
Q 4428 3875 4428 3144 
Q 4428 2409 3951 2014 
Q 3475 1619 2584 1619 
L 1791 1619 
L 1791 0 
L 588 0 
L 588 4666 
z
M 1791 3794 
L 1791 2491 
L 2456 2491 
Q 2806 2491 2997 2661 
Q 3188 2831 3188 3144 
Q 3188 3456 2997 3625 
Q 2806 3794 2456 3794 
L 1791 3794 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6f" d="M 2203 2784 
Q 1831 2784 1636 2517 
Q 1441 2250 1441 1747 
Q 1441 1244 1636 976 
Q 1831 709 2203 709 
Q 2569 709 2762 976 
Q 2956 1244 2956 1747 
Q 2956 2250 2762 2517 
Q 2569 2784 2203 2784 
z
M 2203 3584 
Q 3106 3584 3614 3096 
Q 4122 2609 4122 1747 
Q 4122 884 3614 396 
Q 3106 -91 2203 -91 
Q 1297 -91 786 396 
Q 275 884 275 1747 
Q 275 2609 786 3096 
Q 1297 3584 2203 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-28" d="M 2413 -844 
L 1484 -844 
Q 1006 -72 778 623 
Q 550 1319 550 2003 
Q 550 2688 779 3389 
Q 1009 4091 1484 4856 
L 2413 4856 
Q 2013 4116 1813 3408 
Q 1613 2700 1613 2009 
Q 1613 1319 1811 609 
Q 2009 -100 2413 -844 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6d" d="M 3781 2919 
Q 3994 3244 4286 3414 
Q 4578 3584 4928 3584 
Q 5531 3584 5847 3212 
Q 6163 2841 6163 2131 
L 6163 0 
L 5038 0 
L 5038 1825 
Q 5041 1866 5042 1909 
Q 5044 1953 5044 2034 
Q 5044 2406 4934 2573 
Q 4825 2741 4581 2741 
Q 4263 2741 4089 2478 
Q 3916 2216 3909 1719 
L 3909 0 
L 2784 0 
L 2784 1825 
Q 2784 2406 2684 2573 
Q 2584 2741 2328 2741 
Q 2006 2741 1831 2477 
Q 1656 2213 1656 1722 
L 1656 0 
L 531 0 
L 531 3500 
L 1656 3500 
L 1656 2988 
Q 1863 3284 2130 3434 
Q 2397 3584 2719 3584 
Q 3081 3584 3359 3409 
Q 3638 3234 3781 2919 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-29" d="M 513 -844 
Q 913 -100 1113 609 
Q 1313 1319 1313 2009 
Q 1313 2700 1113 3408 
Q 913 4116 513 4856 
L 1441 4856 
Q 1916 4091 2145 3389 
Q 2375 2688 2375 2003 
Q 2375 1319 2147 623 
Q 1919 -72 1441 -844 
L 513 -844 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-58" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="573.583984"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="608.398438"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="681.689453"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="750.390625"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="809.912109"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="844.189453"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="891.992188"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="926.269531"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="994.970703"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1066.162109"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1100.976562"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1146.679688"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1250.878906"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_19">
      <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" clip-path="url(#p43758ec6d2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <defs>
       <path id="mda3323f291" d="M 0 0 
L -3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#mda3323f291" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_11">
      <!-- −8 -->
      <g transform="translate(21.845938 318.044547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_21">
      <path d="M 42.113906 280.90325 
L 311.889906 280.90325 
" clip-path="url(#p43758ec6d2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#mda3323f291" x="42.113906" y="280.90325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_12">
      <!-- −6 -->
      <g transform="translate(21.845938 284.322547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_23">
      <path d="M 42.113906 247.18125 
L 311.889906 247.18125 
" clip-path="url(#p43758ec6d2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#mda3323f291" x="42.113906" y="247.18125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_13">
      <!-- −4 -->
      <g transform="translate(21.845938 250.600547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_25">
      <path d="M 42.113906 213.45925 
L 311.889906 213.45925 
" clip-path="url(#p43758ec6d2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#mda3323f291" x="42.113906" y="213.45925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_14">
      <!-- −2 -->
      <g transform="translate(21.845938 216.878547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_27">
      <path d="M 42.113906 179.73725 
L 311.889906 179.73725 
" clip-path="url(#p43758ec6d2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#mda3323f291" x="42.113906" y="179.73725" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 0 -->
      <g transform="translate(29.387656 183.156547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_29">
      <path d="M 42.113906 146.01525 
L 311.889906 146.01525 
" clip-path="url(#p43758ec6d2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#mda3323f291" x="42.113906" y="146.01525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 2 -->
      <g transform="translate(29.387656 149.434547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_31">
      <path d="M 42.113906 112.29325 
L 311.889906 112.29325 
" clip-path="url(#p43758ec6d2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#mda3323f291" x="42.113906" y="112.29325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 4 -->
      <g transform="translate(29.387656 115.712547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_33">
      <path d="M 42.113906 78.57125 
L 311.889906 78.57125 
" clip-path="url(#p43758ec6d2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#mda3323f291" x="42.113906" y="78.57125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 6 -->
      <g transform="translate(29.387656 81.990547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_35">
      <path d="M 42.113906 44.84925 
L 311.889906 44.84925 
" clip-path="url(#p43758ec6d2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#mda3323f291" x="42.113906" y="44.84925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 8 -->
      <g transform="translate(29.387656 48.268547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_20">
     <!-- Relative Y Position (m) -->
     <g transform="translate(15.558281 250.792094) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-59" d="M -63 4666 
L 1253 4666 
L 2316 3003 
L 3378 4666 
L 4697 4666 
L 2919 1966 
L 2919 0 
L 1716 0 
L 1716 1966 
L -63 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-59" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="568.896484"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="603.710938"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="677.001953"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="745.703125"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="805.224609"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="839.501953"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="887.304688"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="921.582031"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="990.283203"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1061.474609"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1096.289062"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1141.992188"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1246.191406"/>
     </g>
    </g>
   </g>
   <g id="patch_6">
    <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_21">
    <g id="patch_8">
     <path d="M 200.10902 150.124824 
L 225.429957 150.124824 
Q 227.229957 150.124824 227.229957 148.324824 
L 227.229957 139.614511 
Q 227.229957 137.814511 225.429957 137.814511 
L 200.10902 137.814511 
Q 198.30902 137.814511 198.30902 139.614511 
L 198.30902 148.324824 
Q 198.30902 150.124824 200.10902 150.124824 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 3.0m -->
    <g style="fill: #ffffff" transform="translate(200.10902 146.453105) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-33" d="M 2981 2516 
Q 3453 2394 3698 2092 
Q 3944 1791 3944 1325 
Q 3944 631 3412 270 
Q 2881 -91 1863 -91 
Q 1503 -91 1142 -33 
Q 781 25 428 141 
L 428 1069 
Q 766 900 1098 814 
Q 1431 728 1753 728 
Q 2231 728 2486 893 
Q 2741 1059 2741 1369 
Q 2741 1688 2480 1852 
Q 2219 2016 1709 2016 
L 1228 2016 
L 1228 2791 
L 1734 2791 
Q 2188 2791 2409 2933 
Q 2631 3075 2631 3366 
Q 2631 3634 2415 3781 
Q 2200 3928 1806 3928 
Q 1516 3928 1219 3862 
Q 922 3797 628 3669 
L 628 4550 
Q 984 4650 1334 4700 
Q 1684 4750 2022 4750 
Q 2931 4750 3382 4451 
Q 3834 4153 3834 3553 
Q 3834 3144 3618 2883 
Q 3403 2622 2981 2516 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2e" d="M 653 1209 
L 1778 1209 
L 1778 0 
L 653 0 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-30" d="M 2944 2338 
Q 2944 3213 2780 3570 
Q 2616 3928 2228 3928 
Q 1841 3928 1675 3570 
Q 1509 3213 1509 2338 
Q 1509 1453 1675 1090 
Q 1841 728 2228 728 
Q 2613 728 2778 1090 
Q 2944 1453 2944 2338 
z
M 4147 2328 
Q 4147 1169 3647 539 
Q 3147 -91 2228 -91 
Q 1306 -91 806 539 
Q 306 1169 306 2328 
Q 306 3491 806 4120 
Q 1306 4750 2228 4750 
Q 3147 4750 3647 4120 
Q 4147 3491 4147 2328 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-33"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_22">
    <g id="patch_9">
     <path d="M 223.954075 126.279769 
L 249.275012 126.279769 
Q 251.075012 126.279769 251.075012 124.479769 
L 251.075012 115.769457 
Q 251.075012 113.969457 249.275012 113.969457 
L 223.954075 113.969457 
Q 222.154075 113.969457 222.154075 115.769457 
L 222.154075 124.479769 
Q 222.154075 126.279769 223.954075 126.279769 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 5.0m -->
    <g style="fill: #ffffff" transform="translate(223.954075 122.60805) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-35" d="M 678 4666 
L 3669 4666 
L 3669 3781 
L 1638 3781 
L 1638 3059 
Q 1775 3097 1914 3117 
Q 2053 3138 2203 3138 
Q 3056 3138 3531 2711 
Q 4006 2284 4006 1522 
Q 4006 766 3489 337 
Q 2972 -91 2053 -91 
Q 1656 -91 1267 -14 
Q 878 63 494 219 
L 494 1166 
Q 875 947 1217 837 
Q 1559 728 1863 728 
Q 2300 728 2551 942 
Q 2803 1156 2803 1522 
Q 2803 1891 2551 2103 
Q 2300 2316 1863 2316 
Q 1603 2316 1309 2248 
Q 1016 2181 678 2041 
L 678 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-35"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_23">
    <g id="patch_10">
     <path d="M 259.721657 90.512187 
L 285.042595 90.512187 
Q 286.842595 90.512187 286.842595 88.712187 
L 286.842595 80.001874 
Q 286.842595 78.201874 285.042595 78.201874 
L 259.721657 78.201874 
Q 257.921657 78.201874 257.921657 80.001874 
L 257.921657 88.712187 
Q 257.921657 90.512187 259.721657 90.512187 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 8.0m -->
    <g style="fill: #ffffff" transform="translate(259.721657 86.840468) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-38" d="M 2228 2088 
Q 1891 2088 1709 1903 
Q 1528 1719 1528 1375 
Q 1528 1031 1709 848 
Q 1891 666 2228 666 
Q 2563 666 2741 848 
Q 2919 1031 2919 1375 
Q 2919 1722 2741 1905 
Q 2563 2088 2228 2088 
z
M 1350 2484 
Q 925 2613 709 2878 
Q 494 3144 494 3541 
Q 494 4131 934 4440 
Q 1375 4750 2228 4750 
Q 3075 4750 3515 4442 
Q 3956 4134 3956 3541 
Q 3956 3144 3739 2878 
Q 3522 2613 3097 2484 
Q 3572 2353 3814 2058 
Q 4056 1763 4056 1313 
Q 4056 619 3595 264 
Q 3134 -91 2228 -91 
Q 1319 -91 855 264 
Q 391 619 391 1313 
Q 391 1763 633 2058 
Q 875 2353 1350 2484 
z
M 1631 3419 
Q 1631 3141 1786 2991 
Q 1941 2841 2228 2841 
Q 2509 2841 2662 2991 
Q 2816 3141 2816 3419 
Q 2816 3697 2662 3845 
Q 2509 3994 2228 3994 
Q 1941 3994 1786 3844 
Q 1631 3694 1631 3419 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-38"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_24">
    <!-- Far-Distance Cell (Neuron 25) -->
    <g transform="translate(76.933156 16.411875) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-46" d="M 588 4666 
L 3834 4666 
L 3834 3756 
L 1791 3756 
L 1791 2888 
L 3713 2888 
L 3713 1978 
L 1791 1978 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2d" d="M 347 2297 
L 2309 2297 
L 2309 1388 
L 347 1388 
L 347 2297 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-44" d="M 1791 3756 
L 1791 909 
L 2222 909 
Q 2959 909 3348 1275 
Q 3738 1641 3738 2338 
Q 3738 3031 3350 3393 
Q 2963 3756 2222 3756 
L 1791 3756 
z
M 588 4666 
L 1856 4666 
Q 2919 4666 3439 4514 
Q 3959 4363 4331 4000 
Q 4659 3684 4818 3271 
Q 4978 2859 4978 2338 
Q 4978 1809 4818 1395 
Q 4659 981 4331 666 
Q 3956 303 3431 151 
Q 2906 0 1856 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4e" d="M 588 4666 
L 1931 4666 
L 3628 1466 
L 3628 4666 
L 4769 4666 
L 4769 0 
L 3425 0 
L 1728 3200 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-75" d="M 500 1363 
L 500 3500 
L 1625 3500 
L 1625 3150 
Q 1625 2866 1622 2436 
Q 1619 2006 1619 1863 
Q 1619 1441 1641 1255 
Q 1663 1069 1716 984 
Q 1784 875 1895 815 
Q 2006 756 2150 756 
Q 2500 756 2700 1025 
Q 2900 1294 2900 1772 
L 2900 3500 
L 4019 3500 
L 4019 0 
L 2900 0 
L 2900 506 
Q 2647 200 2364 54 
Q 2081 -91 1741 -91 
Q 1134 -91 817 281 
Q 500 653 500 1363 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-32" d="M 1844 884 
L 3897 884 
L 3897 0 
L 506 0 
L 506 884 
L 2209 2388 
Q 2438 2594 2547 2791 
Q 2656 2988 2656 3200 
Q 2656 3528 2436 3728 
Q 2216 3928 1850 3928 
Q 1569 3928 1234 3808 
Q 900 3688 519 3450 
L 519 4475 
Q 925 4609 1322 4679 
Q 1719 4750 2100 4750 
Q 2938 4750 3402 4381 
Q 3866 4013 3866 3353 
Q 3866 2972 3669 2642 
Q 3472 2313 2841 1759 
L 1844 884 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-46"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="62.435547"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="129.916016"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="179.232422"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="220.736328"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="303.744141"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="338.021484"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="397.542969"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="445.345703"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="512.826172"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="584.017578"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="643.294922"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="711.117188"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="745.931641"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="819.320312"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="887.142578"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="921.419922"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="955.697266"/>
     <use xlink:href="#DejaVuSans-Bold-28" x="990.511719"/>
     <use xlink:href="#DejaVuSans-Bold-4e" x="1036.214844"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1119.90625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1187.728516"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1258.919922"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="1308.236328"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1376.9375"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1448.128906"/>
     <use xlink:href="#DejaVuSans-Bold-32" x="1482.943359"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="1552.523438"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="1622.103516"/>
    </g>
    <!-- 2D Activation Map -->
    <g transform="translate(114.950656 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-41" d="M 3419 850 
L 1538 850 
L 1241 0 
L 31 0 
L 1759 4666 
L 3194 4666 
L 4922 0 
L 3713 0 
L 3419 850 
z
M 1838 1716 
L 3116 1716 
L 2478 3572 
L 1838 1716 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4d" d="M 588 4666 
L 2119 4666 
L 3181 2169 
L 4250 4666 
L 5778 4666 
L 5778 0 
L 4641 0 
L 4641 3413 
L 3566 897 
L 2803 897 
L 1728 3413 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-32"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="152.587891"/>
     <use xlink:href="#DejaVuSans-Bold-41" x="187.402344"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="264.794922"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="324.072266"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="371.875"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="406.152344"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.337891"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="538.818359"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="586.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="620.898438"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="689.599609"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="760.791016"/>
     <use xlink:href="#DejaVuSans-Bold-4d" x="795.605469"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="895.117188"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="962.597656"/>
    </g>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="m14829549a9" d="M 0 -5.477226 
L -1.229714 -1.692556 
L -5.209151 -1.692556 
L -1.989719 0.646499 
L -3.219432 4.431169 
L -0 2.092114 
L 3.219432 4.431169 
L 1.989719 0.646499 
L 5.209151 -1.692556 
L 1.229714 -1.692556 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#p43758ec6d2)">
     <use xlink:href="#m14829549a9" x="177.001906" y="179.73725" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_11">
     <path d="M 242.388656 62.99175 
L 307.889906 62.99175 
L 307.889906 48.84925 
L 242.388656 48.84925 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="PathCollection_2">
     <g>
      <use xlink:href="#m14829549a9" x="253.588656" y="56.028" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
     </g>
    </g>
    <g id="text_25">
     <!-- Observer -->
     <g transform="translate(267.988656 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4f"/>
      <use xlink:href="#DejaVuSans-62" x="78.710938"/>
      <use xlink:href="#DejaVuSans-73" x="142.1875"/>
      <use xlink:href="#DejaVuSans-65" x="194.287109"/>
      <use xlink:href="#DejaVuSans-72" x="255.810547"/>
      <use xlink:href="#DejaVuSans-76" x="296.923828"/>
      <use xlink:href="#DejaVuSans-65" x="356.103516"/>
      <use xlink:href="#DejaVuSans-72" x="417.626953"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_12">
    <path d="M 441.335015 314.62525 
L 816.193288 314.62525 
L 816.193288 44.84925 
L 441.335015 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="m4896fc5e34" d="M 446.016648 -37.642188 
L 446.016648 -37.642188 
L 455.379915 -37.642188 
L 464.743181 -37.642188 
L 474.106447 -37.642188 
L 483.469714 -37.642188 
L 492.83298 -37.642188 
L 502.196246 -37.642188 
L 511.559513 -37.642188 
L 520.922779 -37.752875 
L 530.286045 -37.775048 
L 539.649312 -38.061454 
L 549.012578 -37.766184 
L 558.375844 -38.005771 
L 567.739111 -38.176269 
L 577.102377 -38.63895 
L 586.465643 -39.045491 
L 595.828909 -40.611296 
L 605.192176 -41.556616 
L 614.555442 -62.506985 
L 623.918708 -51.432966 
L 633.281975 -89.408705 
L 642.645241 -82.335745 
L 652.008507 -102.041526 
L 661.371774 -163.431921 
L 670.73504 -185.16417 
L 680.098306 -191.901938 
L 689.461573 -222.921808 
L 698.824839 -235.613267 
L 708.188105 -147.135725 
L 717.551371 -294.571711 
L 717.551371 -37.642188 
L 717.551371 -37.642188 
L 708.188105 -37.642188 
L 698.824839 -37.642188 
L 689.461573 -37.642188 
L 680.098306 -37.642188 
L 670.73504 -37.642188 
L 661.371774 -37.642188 
L 652.008507 -37.642188 
L 642.645241 -37.642188 
L 633.281975 -37.642188 
L 623.918708 -37.642188 
L 614.555442 -37.642188 
L 605.192176 -37.642188 
L 595.828909 -37.642188 
L 586.465643 -37.642188 
L 577.102377 -37.642188 
L 567.739111 -37.642188 
L 558.375844 -37.642188 
L 549.012578 -37.642188 
L 539.649312 -37.642188 
L 530.286045 -37.642188 
L 520.922779 -37.642188 
L 511.559513 -37.642188 
L 502.196246 -37.642188 
L 492.83298 -37.642188 
L 483.469714 -37.642188 
L 474.106447 -37.642188 
L 464.743181 -37.642188 
L 455.379915 -37.642188 
L 446.016648 -37.642188 
z
" style="stroke: #33a02c; stroke-opacity: 0.3"/>
    </defs>
    <g clip-path="url(#pa1a10cbcac)">
     <use xlink:href="#m4896fc5e34" x="0" y="352.267438" style="fill: #33a02c; fill-opacity: 0.3; stroke: #33a02c; stroke-opacity: 0.3"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10">
     <g id="line2d_37">
      <path d="M 441.335015 314.62525 
L 441.335015 44.84925 
" clip-path="url(#pa1a10cbcac)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#m5b5937f554" x="441.335015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 0 -->
      <g transform="translate(438.47189 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_39">
      <path d="M 488.192299 314.62525 
L 488.192299 44.84925 
" clip-path="url(#pa1a10cbcac)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#m5b5937f554" x="488.192299" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 1 -->
      <g transform="translate(485.329174 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_41">
      <path d="M 535.049584 314.62525 
L 535.049584 44.84925 
" clip-path="url(#pa1a10cbcac)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#m5b5937f554" x="535.049584" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 2 -->
      <g transform="translate(532.186459 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_43">
      <path d="M 581.906868 314.62525 
L 581.906868 44.84925 
" clip-path="url(#pa1a10cbcac)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#m5b5937f554" x="581.906868" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 3 -->
      <g transform="translate(579.043743 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_45">
      <path d="M 628.764152 314.62525 
L 628.764152 44.84925 
" clip-path="url(#pa1a10cbcac)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#m5b5937f554" x="628.764152" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 4 -->
      <g transform="translate(625.901027 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_47">
      <path d="M 675.621436 314.62525 
L 675.621436 44.84925 
" clip-path="url(#pa1a10cbcac)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#m5b5937f554" x="675.621436" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 5 -->
      <g transform="translate(672.758311 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_49">
      <path d="M 722.47872 314.62525 
L 722.47872 44.84925 
" clip-path="url(#pa1a10cbcac)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#m5b5937f554" x="722.47872" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 6 -->
      <g transform="translate(719.615595 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_51">
      <path d="M 769.336004 314.62525 
L 769.336004 44.84925 
" clip-path="url(#pa1a10cbcac)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#m5b5937f554" x="769.336004" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 7 -->
      <g transform="translate(766.472879 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-37"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_53">
      <path d="M 816.193288 314.62525 
L 816.193288 44.84925 
" clip-path="url(#pa1a10cbcac)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#m5b5937f554" x="816.193288" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 8 -->
      <g transform="translate(813.330163 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_35">
     <!-- Inter-Agent Distance (m) -->
     <g transform="translate(551.421261 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-49" d="M 588 4666 
L 1791 4666 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-49"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="37.207031"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="108.398438"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="156.201172"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="224.023438"/>
      <use xlink:href="#DejaVuSans-Bold-2d" x="273.339844"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="314.84375"/>
      <use xlink:href="#DejaVuSans-Bold-67" x="392.236328"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="463.818359"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="531.640625"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="602.832031"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="650.634766"/>
      <use xlink:href="#DejaVuSans-Bold-44" x="685.449219"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="768.457031"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="802.734375"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="862.255859"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="910.058594"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="977.539062"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="1048.730469"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="1108.007812"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1175.830078"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1210.644531"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1256.347656"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1360.546875"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_55">
      <path d="M 441.335015 314.62525 
L 816.193288 314.62525 
" clip-path="url(#pa1a10cbcac)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#mda3323f291" x="441.335015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 0.00 -->
      <g transform="translate(414.295953 318.044547) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_57">
      <path d="M 441.335015 260.920878 
L 816.193288 260.920878 
" clip-path="url(#pa1a10cbcac)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#mda3323f291" x="441.335015" y="260.920878" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 0.05 -->
      <g transform="translate(414.295953 264.340174) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_59">
      <path d="M 441.335015 207.216505 
L 816.193288 207.216505 
" clip-path="url(#pa1a10cbcac)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#mda3323f291" x="441.335015" y="207.216505" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 0.10 -->
      <g transform="translate(414.295953 210.635802) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_61">
      <path d="M 441.335015 153.512133 
L 816.193288 153.512133 
" clip-path="url(#pa1a10cbcac)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#mda3323f291" x="441.335015" y="153.512133" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 0.15 -->
      <g transform="translate(414.295953 156.93143) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_63">
      <path d="M 441.335015 99.80776 
L 816.193288 99.80776 
" clip-path="url(#pa1a10cbcac)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#mda3323f291" x="441.335015" y="99.80776" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 0.20 -->
      <g transform="translate(414.295953 103.227057) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_65">
      <path d="M 441.335015 46.103388 
L 816.193288 46.103388 
" clip-path="url(#pa1a10cbcac)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#mda3323f291" x="441.335015" y="46.103388" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 0.25 -->
      <g transform="translate(414.295953 49.522685) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="text_42">
     <!-- Neural Activation -->
     <g transform="translate(408.008297 233.746391) rotate(-90) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="line2d_67">
    <path d="M 446.016648 314.62525 
L 455.379915 314.62525 
L 464.743181 314.62525 
L 474.106447 314.62525 
L 483.469714 314.62525 
L 492.83298 314.62525 
L 502.196246 314.62525 
L 511.559513 314.62525 
L 520.922779 314.514563 
L 530.286045 314.492389 
L 539.649312 314.205984 
L 549.012578 314.501254 
L 558.375844 314.261666 
L 567.739111 314.091169 
L 577.102377 313.628488 
L 586.465643 313.221947 
L 595.828909 311.656142 
L 605.192176 310.710821 
L 614.555442 289.760453 
L 623.918708 300.834471 
L 633.281975 262.858732 
L 642.645241 269.931692 
L 652.008507 250.225911 
L 661.371774 188.835517 
L 670.73504 167.103268 
L 680.098306 160.3655 
L 689.461573 129.34563 
L 698.824839 116.65417 
L 708.188105 205.131713 
L 717.551371 57.695726 
" clip-path="url(#pa1a10cbcac)" style="fill: none; stroke: #33a02c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_68">
    <path d="M 717.551371 314.62525 
L 717.551371 44.84925 
" clip-path="url(#pa1a10cbcac)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #33a02c; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_69">
    <path d="M 581.906868 314.62525 
L 581.906868 44.84925 
" clip-path="url(#pa1a10cbcac)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_70">
    <path d="M 675.621436 314.62525 
L 675.621436 44.84925 
" clip-path="url(#pa1a10cbcac)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="patch_13">
    <path d="M 441.335015 314.62525 
L 441.335015 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_14">
    <path d="M 441.335015 314.62525 
L 816.193288 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_43">
    <g id="patch_15">
     <path d="M 703.009124 114.0718 
L 797.450374 114.0718 
Q 800.650374 114.0718 800.650374 110.8718 
L 800.650374 58.33805 
Q 800.650374 55.13805 797.450374 55.13805 
L 703.009124 55.13805 
Q 699.809124 55.13805 699.809124 58.33805 
L 699.809124 110.8718 
Q 699.809124 114.0718 703.009124 114.0718 
z
" style="fill: #ffffff; opacity: 0.9; stroke: #808080; stroke-linejoin: miter"/>
    </g>
    <!-- Peak Distance: 5.9m -->
    <g transform="translate(715.184124 64.4168) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-3a" d="M 750 794 
L 1409 794 
L 1409 0 
L 750 0 
L 750 794 
z
M 750 3309 
L 1409 3309 
L 1409 2516 
L 750 2516 
L 750 3309 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-44" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="346.179688"/>
     <use xlink:href="#DejaVuSans-73" x="373.962891"/>
     <use xlink:href="#DejaVuSans-74" x="426.0625"/>
     <use xlink:href="#DejaVuSans-61" x="465.271484"/>
     <use xlink:href="#DejaVuSans-6e" x="526.550781"/>
     <use xlink:href="#DejaVuSans-63" x="589.929688"/>
     <use xlink:href="#DejaVuSans-65" x="644.910156"/>
     <use xlink:href="#DejaVuSans-3a" x="706.433594"/>
     <use xlink:href="#DejaVuSans-20" x="740.125"/>
     <use xlink:href="#DejaVuSans-35" x="771.912109"/>
     <use xlink:href="#DejaVuSans-2e" x="835.535156"/>
     <use xlink:href="#DejaVuSans-39" x="867.322266"/>
     <use xlink:href="#DejaVuSans-6d" x="930.945312"/>
    </g>
    <!-- Max Activation: 0.239 -->
    <g transform="translate(710.176624 73.37505) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-4d"/>
     <use xlink:href="#DejaVuSans-61" x="86.279297"/>
     <use xlink:href="#DejaVuSans-78" x="147.558594"/>
     <use xlink:href="#DejaVuSans-20" x="206.738281"/>
     <use xlink:href="#DejaVuSans-41" x="238.525391"/>
     <use xlink:href="#DejaVuSans-63" x="305.183594"/>
     <use xlink:href="#DejaVuSans-74" x="360.164062"/>
     <use xlink:href="#DejaVuSans-69" x="399.373047"/>
     <use xlink:href="#DejaVuSans-76" x="427.15625"/>
     <use xlink:href="#DejaVuSans-61" x="486.335938"/>
     <use xlink:href="#DejaVuSans-74" x="547.615234"/>
     <use xlink:href="#DejaVuSans-69" x="586.824219"/>
     <use xlink:href="#DejaVuSans-6f" x="614.607422"/>
     <use xlink:href="#DejaVuSans-6e" x="675.789062"/>
     <use xlink:href="#DejaVuSans-3a" x="739.167969"/>
     <use xlink:href="#DejaVuSans-20" x="772.859375"/>
     <use xlink:href="#DejaVuSans-30" x="804.646484"/>
     <use xlink:href="#DejaVuSans-2e" x="868.269531"/>
     <use xlink:href="#DejaVuSans-32" x="900.056641"/>
     <use xlink:href="#DejaVuSans-33" x="963.679688"/>
     <use xlink:href="#DejaVuSans-39" x="1027.302734"/>
    </g>
    <!-- Sparsity: 0.736 -->
    <g transform="translate(737.280374 82.3333) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-70" x="63.476562"/>
     <use xlink:href="#DejaVuSans-61" x="126.953125"/>
     <use xlink:href="#DejaVuSans-72" x="188.232422"/>
     <use xlink:href="#DejaVuSans-73" x="229.345703"/>
     <use xlink:href="#DejaVuSans-69" x="281.445312"/>
     <use xlink:href="#DejaVuSans-74" x="309.228516"/>
     <use xlink:href="#DejaVuSans-79" x="348.4375"/>
     <use xlink:href="#DejaVuSans-3a" x="400.367188"/>
     <use xlink:href="#DejaVuSans-20" x="434.058594"/>
     <use xlink:href="#DejaVuSans-30" x="465.845703"/>
     <use xlink:href="#DejaVuSans-2e" x="529.46875"/>
     <use xlink:href="#DejaVuSans-37" x="561.255859"/>
     <use xlink:href="#DejaVuSans-33" x="624.878906"/>
     <use xlink:href="#DejaVuSans-36" x="688.501953"/>
    </g>
    <!-- Peak Significance: 3.54 -->
    <g transform="translate(704.462874 91.29155) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-53" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="332.654297"/>
     <use xlink:href="#DejaVuSans-67" x="360.4375"/>
     <use xlink:href="#DejaVuSans-6e" x="423.914062"/>
     <use xlink:href="#DejaVuSans-69" x="487.292969"/>
     <use xlink:href="#DejaVuSans-66" x="515.076172"/>
     <use xlink:href="#DejaVuSans-69" x="550.28125"/>
     <use xlink:href="#DejaVuSans-63" x="578.064453"/>
     <use xlink:href="#DejaVuSans-61" x="633.044922"/>
     <use xlink:href="#DejaVuSans-6e" x="694.324219"/>
     <use xlink:href="#DejaVuSans-63" x="757.703125"/>
     <use xlink:href="#DejaVuSans-65" x="812.683594"/>
     <use xlink:href="#DejaVuSans-3a" x="874.207031"/>
     <use xlink:href="#DejaVuSans-20" x="907.898438"/>
     <use xlink:href="#DejaVuSans-33" x="939.685547"/>
     <use xlink:href="#DejaVuSans-2e" x="1003.308594"/>
     <use xlink:href="#DejaVuSans-35" x="1035.095703"/>
     <use xlink:href="#DejaVuSans-34" x="1098.71875"/>
    </g>
    <!-- Selectivity Index: 0.949 -->
    <g transform="translate(703.009124 100.2498) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-65" x="63.476562"/>
     <use xlink:href="#DejaVuSans-6c" x="125"/>
     <use xlink:href="#DejaVuSans-65" x="152.783203"/>
     <use xlink:href="#DejaVuSans-63" x="214.306641"/>
     <use xlink:href="#DejaVuSans-74" x="269.287109"/>
     <use xlink:href="#DejaVuSans-69" x="308.496094"/>
     <use xlink:href="#DejaVuSans-76" x="336.279297"/>
     <use xlink:href="#DejaVuSans-69" x="395.458984"/>
     <use xlink:href="#DejaVuSans-74" x="423.242188"/>
     <use xlink:href="#DejaVuSans-79" x="462.451172"/>
     <use xlink:href="#DejaVuSans-20" x="521.630859"/>
     <use xlink:href="#DejaVuSans-49" x="553.417969"/>
     <use xlink:href="#DejaVuSans-6e" x="582.910156"/>
     <use xlink:href="#DejaVuSans-64" x="646.289062"/>
     <use xlink:href="#DejaVuSans-65" x="709.765625"/>
     <use xlink:href="#DejaVuSans-78" x="769.539062"/>
     <use xlink:href="#DejaVuSans-3a" x="828.71875"/>
     <use xlink:href="#DejaVuSans-20" x="862.410156"/>
     <use xlink:href="#DejaVuSans-30" x="894.197266"/>
     <use xlink:href="#DejaVuSans-2e" x="957.820312"/>
     <use xlink:href="#DejaVuSans-39" x="989.607422"/>
     <use xlink:href="#DejaVuSans-34" x="1053.230469"/>
     <use xlink:href="#DejaVuSans-39" x="1116.853516"/>
    </g>
    <!-- Peak Width: 1.0m -->
    <g transform="translate(726.926624 109.20805) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-57" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="365.804688"/>
     <use xlink:href="#DejaVuSans-64" x="393.587891"/>
     <use xlink:href="#DejaVuSans-74" x="457.064453"/>
     <use xlink:href="#DejaVuSans-68" x="496.273438"/>
     <use xlink:href="#DejaVuSans-3a" x="559.652344"/>
     <use xlink:href="#DejaVuSans-20" x="593.34375"/>
     <use xlink:href="#DejaVuSans-31" x="625.130859"/>
     <use xlink:href="#DejaVuSans-2e" x="688.753906"/>
     <use xlink:href="#DejaVuSans-30" x="720.541016"/>
     <use xlink:href="#DejaVuSans-6d" x="784.164062"/>
    </g>
   </g>
   <g id="text_44">
    <!-- Distance Tuning Curve -->
    <g transform="translate(552.950402 16.318125) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-54" d="M 31 4666 
L 4331 4666 
L 4331 3756 
L 2784 3756 
L 2784 0 
L 1581 0 
L 1581 3756 
L 31 3756 
L 31 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-44"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
     <use xlink:href="#DejaVuSans-Bold-54" x="525.195312"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="582.408203"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="653.599609"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="724.791016"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="759.068359"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="830.259766"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="901.841797"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="936.65625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1010.044922"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1081.236328"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="1130.552734"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1195.738281"/>
    </g>
    <!-- Peak: 5.9m, Sparsity: 0.736 -->
    <g transform="translate(535.025402 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-6b" d="M 538 4863 
L 1656 4863 
L 1656 2216 
L 2944 3500 
L 4244 3500 
L 2534 1894 
L 4378 0 
L 3022 0 
L 1656 1459 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-3a" d="M 716 3500 
L 1844 3500 
L 1844 2291 
L 716 2291 
L 716 3500 
z
M 716 1209 
L 1844 1209 
L 1844 0 
L 716 0 
L 716 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-39" d="M 641 103 
L 641 966 
Q 928 831 1190 764 
Q 1453 697 1709 697 
Q 2247 697 2547 995 
Q 2847 1294 2900 1881 
Q 2688 1725 2447 1647 
Q 2206 1569 1925 1569 
Q 1209 1569 770 1986 
Q 331 2403 331 3084 
Q 331 3838 820 4291 
Q 1309 4744 2131 4744 
Q 3044 4744 3544 4128 
Q 4044 3513 4044 2388 
Q 4044 1231 3459 570 
Q 2875 -91 1856 -91 
Q 1528 -91 1228 -42 
Q 928 6 641 103 
z
M 2125 2350 
Q 2441 2350 2600 2554 
Q 2759 2759 2759 3169 
Q 2759 3575 2600 3781 
Q 2441 3988 2125 3988 
Q 1809 3988 1650 3781 
Q 1491 3575 1491 3169 
Q 1491 2759 1650 2554 
Q 1809 2350 2125 2350 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2c" d="M 653 1209 
L 1778 1209 
L 1778 256 
L 1006 -909 
L 341 -909 
L 653 256 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-53" d="M 3834 4519 
L 3834 3531 
Q 3450 3703 3084 3790 
Q 2719 3878 2394 3878 
Q 1963 3878 1756 3759 
Q 1550 3641 1550 3391 
Q 1550 3203 1689 3098 
Q 1828 2994 2194 2919 
L 2706 2816 
Q 3484 2659 3812 2340 
Q 4141 2022 4141 1434 
Q 4141 663 3683 286 
Q 3225 -91 2284 -91 
Q 1841 -91 1394 -6 
Q 947 78 500 244 
L 500 1259 
Q 947 1022 1364 901 
Q 1781 781 2169 781 
Q 2563 781 2772 912 
Q 2981 1044 2981 1288 
Q 2981 1506 2839 1625 
Q 2697 1744 2272 1838 
L 1806 1941 
Q 1106 2091 782 2419 
Q 459 2747 459 3303 
Q 459 4000 909 4375 
Q 1359 4750 2203 4750 
Q 2588 4750 2994 4692 
Q 3400 4634 3834 4519 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-79" d="M 78 3500 
L 1197 3500 
L 2138 1125 
L 2938 3500 
L 4056 3500 
L 2584 -331 
Q 2363 -916 2067 -1148 
Q 1772 -1381 1288 -1381 
L 641 -1381 
L 641 -647 
L 991 -647 
Q 1275 -647 1404 -556 
Q 1534 -466 1606 -231 
L 1638 -134 
L 78 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-37" d="M 428 4666 
L 3944 4666 
L 3944 3988 
L 2125 0 
L 953 0 
L 2675 3781 
L 428 3781 
L 428 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-36" d="M 2316 2303 
Q 2000 2303 1842 2098 
Q 1684 1894 1684 1484 
Q 1684 1075 1842 870 
Q 2000 666 2316 666 
Q 2634 666 2792 870 
Q 2950 1075 2950 1484 
Q 2950 1894 2792 2098 
Q 2634 2303 2316 2303 
z
M 3803 4544 
L 3803 3681 
Q 3506 3822 3243 3889 
Q 2981 3956 2731 3956 
Q 2194 3956 1894 3657 
Q 1594 3359 1544 2772 
Q 1750 2925 1990 3001 
Q 2231 3078 2516 3078 
Q 3231 3078 3670 2659 
Q 4109 2241 4109 1563 
Q 4109 813 3618 361 
Q 3128 -91 2303 -91 
Q 1394 -91 895 523 
Q 397 1138 397 2266 
Q 397 3422 980 4083 
Q 1563 4744 2578 4744 
Q 2900 4744 3203 4694 
Q 3506 4644 3803 4544 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-50"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="73.291016"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="141.113281"/>
     <use xlink:href="#DejaVuSans-Bold-6b" x="208.59375"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="275.097656"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="315.087891"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="349.902344"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="419.482422"/>
     <use xlink:href="#DejaVuSans-Bold-39" x="457.470703"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="527.050781"/>
     <use xlink:href="#DejaVuSans-Bold-2c" x="631.25"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="669.238281"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="704.052734"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="776.074219"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="847.65625"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="915.136719"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="964.453125"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1023.974609"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="1058.251953"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="1106.054688"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="1171.240234"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1211.230469"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1246.044922"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="1315.625"/>
     <use xlink:href="#DejaVuSans-Bold-37" x="1353.613281"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="1423.193359"/>
     <use xlink:href="#DejaVuSans-Bold-36" x="1492.773438"/>
    </g>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="m5a81f9e5fb" d="M 0 5 
C 1.326016 5 2.597899 4.473168 3.535534 3.535534 
C 4.473168 2.597899 5 1.326016 5 0 
C 5 -1.326016 4.473168 -2.597899 3.535534 -3.535534 
C 2.597899 -4.473168 1.326016 -5 0 -5 
C -1.326016 -5 -2.597899 -4.473168 -3.535534 -3.535534 
C -4.473168 -2.597899 -5 -1.326016 -5 0 
C -5 1.326016 -4.473168 2.597899 -3.535534 3.535534 
C -2.597899 4.473168 -1.326016 5 0 5 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#pa1a10cbcac)">
     <use xlink:href="#m5a81f9e5fb" x="717.551371" y="57.695726" style="fill: #33a02c; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="patch_16">
     <path d="M 446.935015 86.47675 
L 563.842515 86.47675 
Q 565.442515 86.47675 565.442515 84.87675 
L 565.442515 50.44925 
Q 565.442515 48.84925 563.842515 48.84925 
L 446.935015 48.84925 
Q 445.335015 48.84925 445.335015 50.44925 
L 445.335015 84.87675 
Q 445.335015 86.47675 446.935015 86.47675 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_71">
     <path d="M 448.535015 55.328 
L 456.535015 55.328 
L 464.535015 55.328 
" style="fill: none; stroke: #33a02c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
    </g>
    <g id="text_45">
     <!-- Tuning Curve -->
     <g transform="translate(470.935015 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-54"/>
      <use xlink:href="#DejaVuSans-75" x="45.958984"/>
      <use xlink:href="#DejaVuSans-6e" x="109.337891"/>
      <use xlink:href="#DejaVuSans-69" x="172.716797"/>
      <use xlink:href="#DejaVuSans-6e" x="200.5"/>
      <use xlink:href="#DejaVuSans-67" x="263.878906"/>
      <use xlink:href="#DejaVuSans-20" x="327.355469"/>
      <use xlink:href="#DejaVuSans-43" x="359.142578"/>
      <use xlink:href="#DejaVuSans-75" x="428.966797"/>
      <use xlink:href="#DejaVuSans-72" x="492.345703"/>
      <use xlink:href="#DejaVuSans-76" x="533.458984"/>
      <use xlink:href="#DejaVuSans-65" x="592.638672"/>
     </g>
    </g>
    <g id="line2d_72">
     <path d="M 448.535015 67.0705 
L 456.535015 67.0705 
L 464.535015 67.0705 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_46">
     <!-- Close threshold (3.0m) -->
     <g transform="translate(470.935015 69.8705) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6c" x="69.824219"/>
      <use xlink:href="#DejaVuSans-6f" x="97.607422"/>
      <use xlink:href="#DejaVuSans-73" x="158.789062"/>
      <use xlink:href="#DejaVuSans-65" x="210.888672"/>
      <use xlink:href="#DejaVuSans-20" x="272.412109"/>
      <use xlink:href="#DejaVuSans-74" x="304.199219"/>
      <use xlink:href="#DejaVuSans-68" x="343.408203"/>
      <use xlink:href="#DejaVuSans-72" x="406.787109"/>
      <use xlink:href="#DejaVuSans-65" x="445.650391"/>
      <use xlink:href="#DejaVuSans-73" x="507.173828"/>
      <use xlink:href="#DejaVuSans-68" x="559.273438"/>
      <use xlink:href="#DejaVuSans-6f" x="622.652344"/>
      <use xlink:href="#DejaVuSans-6c" x="683.833984"/>
      <use xlink:href="#DejaVuSans-64" x="711.617188"/>
      <use xlink:href="#DejaVuSans-20" x="775.09375"/>
      <use xlink:href="#DejaVuSans-28" x="806.880859"/>
      <use xlink:href="#DejaVuSans-33" x="845.894531"/>
      <use xlink:href="#DejaVuSans-2e" x="909.517578"/>
      <use xlink:href="#DejaVuSans-30" x="941.304688"/>
      <use xlink:href="#DejaVuSans-6d" x="1004.927734"/>
      <use xlink:href="#DejaVuSans-29" x="1102.339844"/>
     </g>
    </g>
    <g id="line2d_73">
     <path d="M 448.535015 78.813 
L 456.535015 78.813 
L 464.535015 78.813 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_47">
     <!-- Far threshold (5.0m) -->
     <g transform="translate(470.935015 81.613) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" x="48.394531"/>
      <use xlink:href="#DejaVuSans-72" x="109.673828"/>
      <use xlink:href="#DejaVuSans-20" x="150.787109"/>
      <use xlink:href="#DejaVuSans-74" x="182.574219"/>
      <use xlink:href="#DejaVuSans-68" x="221.783203"/>
      <use xlink:href="#DejaVuSans-72" x="285.162109"/>
      <use xlink:href="#DejaVuSans-65" x="324.025391"/>
      <use xlink:href="#DejaVuSans-73" x="385.548828"/>
      <use xlink:href="#DejaVuSans-68" x="437.648438"/>
      <use xlink:href="#DejaVuSans-6f" x="501.027344"/>
      <use xlink:href="#DejaVuSans-6c" x="562.208984"/>
      <use xlink:href="#DejaVuSans-64" x="589.992188"/>
      <use xlink:href="#DejaVuSans-20" x="653.46875"/>
      <use xlink:href="#DejaVuSans-28" x="685.255859"/>
      <use xlink:href="#DejaVuSans-35" x="724.269531"/>
      <use xlink:href="#DejaVuSans-2e" x="787.892578"/>
      <use xlink:href="#DejaVuSans-30" x="819.679688"/>
      <use xlink:href="#DejaVuSans-6d" x="883.302734"/>
      <use xlink:href="#DejaVuSans-29" x="980.714844"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_17">
    <path d="M 330.63282 287.64765 
L 341.42386 287.64765 
L 341.42386 71.82685 
L 330.63282 71.82685 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAABYAAAHBCAYAAABt+EsYAAACDUlEQVR4nO2bS25CQQwEDUzunXsHQhYz9MsFelFS1QGskrv9+AhuM9/vKbBmVmPu3CtTZ2bNfFUGN43dcQa7io11C9YtIOvGM+60wlUE6xasW7BuwR0HX/4Dsm48Y5/HG96B3GbelS9CasYOBg9ec+sMLho/WoM7D7fmKjoPN40vrFsghscztm4HYng8Y+t2IIbHM7ZuB2J4PGPrdiCGxzO2bgdieDzjUt2KxqUeE8PT+MA7EJ/HQeNQM3YVQePg0y0Qw9O4PZi3Co2DT7dADE/ja3Dl1zzVVbwqgzUO6/54dgavL9oqHrjwiMatuvFWcccZj8abpnHrQICr0Hhj3YLhBY3/D+70uLcKD+QDz9i6BV54PGPrFnjh8YyRq/BANtYtWLdg3fqDeavwQELtQJDh8Yyt2xlMDE/jjQcSPJCANLZuZzAxPJ6xdTuDieFpvOEdiF8rBI2DdQvI8DTOYN9XbHjhFY1frbo9cXV7/VYGF8PrrBhovG6dGjf/XYELT+NrsHU7aBxqrSCuQuMDr268HRte4IXHMza8oHGwbsHwAu8VhLgKnrEn3R7MW4V1C9YtGF7gGRNXYd0OPGPDCxoHPyoEjQOvbrwdG17ghcczNrygcbBuYf3gwiv9jq6549a7TeAqcMYl4eaOaYMNL7jja7Cr+GDdgnUL1i3wjA0veCDBugXrFng75q3iD9EGue+xdPB4AAAAAElFTkSuQmCC" id="image4ee1f3657d" transform="scale(1 -1) translate(0 -215.52)" x="330.72" y="-71.52" width="10.56" height="215.52"/>
   <g id="matplotlib.axis_5"/>
   <g id="matplotlib.axis_6">
    <g id="ytick_16">
     <g id="line2d_74">
      <defs>
       <path id="mf1b9899c52" d="M 0 0 
L 3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#mf1b9899c52" x="341.42386" y="287.64765" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_48">
      <!-- 0.0 -->
      <g transform="translate(348.42386 291.066947) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_75">
      <g>
       <use xlink:href="#mf1b9899c52" x="341.42386" y="244.48349" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_49">
      <!-- 0.2 -->
      <g transform="translate(348.42386 247.902787) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_76">
      <g>
       <use xlink:href="#mf1b9899c52" x="341.42386" y="201.31933" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_50">
      <!-- 0.4 -->
      <g transform="translate(348.42386 204.738627) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_77">
      <g>
       <use xlink:href="#mf1b9899c52" x="341.42386" y="158.15517" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_51">
      <!-- 0.6 -->
      <g transform="translate(348.42386 161.574467) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_78">
      <g>
       <use xlink:href="#mf1b9899c52" x="341.42386" y="114.99101" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_52">
      <!-- 0.8 -->
      <g transform="translate(348.42386 118.410307) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-38" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_79">
      <g>
       <use xlink:href="#mf1b9899c52" x="341.42386" y="71.82685" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_53">
      <!-- 1.0 -->
      <g transform="translate(348.42386 75.246147) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_54">
     <!-- Neural Activation -->
     <g transform="translate(369.378391 125.728109) rotate(-270) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_18">
    <path d="M 330.63282 287.64765 
L 336.02834 287.64765 
L 341.42386 287.64765 
L 341.42386 71.82685 
L 336.02834 71.82685 
L 330.63282 71.82685 
L 330.63282 287.64765 
z
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p43758ec6d2">
   <rect x="42.113906" y="44.84925" width="269.776" height="269.776"/>
  </clipPath>
  <clipPath id="pa1a10cbcac">
   <rect x="441.335015" y="44.84925" width="374.858273" height="269.776"/>
  </clipPath>
 </defs>
</svg>
