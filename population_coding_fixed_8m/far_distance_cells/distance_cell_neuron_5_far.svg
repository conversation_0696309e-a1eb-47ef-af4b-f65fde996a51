<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="826.280413pt" height="352.267438pt" viewBox="0 0 826.280413 352.267438" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:56:28.553954</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 352.267438 
L 826.280413 352.267438 
L 826.280413 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
L 311.889906 44.84925 
L 42.113906 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g clip-path="url(#p60638cf039)">
    <image xlink:href="data:image/png;base64,
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" id="image52346e3996" transform="scale(1 -1) translate(0 -270.24)" x="42.113906" y="-44.38525" width="270.24" height="270.24"/>
   </g>
   <g id="patch_3">
    <path d="M 177.001906 230.32025 
C 190.416675 230.32025 203.283815 224.990506 212.769489 215.504832 
C 222.255162 206.019159 227.584906 193.152018 227.584906 179.73725 
C 227.584906 166.322482 222.255162 153.455341 212.769489 143.969668 
C 203.283815 134.483994 190.416675 129.15425 177.001906 129.15425 
C 163.587138 129.15425 150.719998 134.483994 141.234324 143.969668 
C 131.74865 153.455341 126.418906 166.322482 126.418906 179.73725 
C 126.418906 193.152018 131.74865 206.019159 141.234324 215.504832 
C 150.719998 224.990506 163.587138 230.32025 177.001906 230.32025 
L 177.001906 230.32025 
z
" clip-path="url(#p60638cf039)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 177.001906 264.04225 
C 199.359854 264.04225 220.805087 255.159343 236.614543 239.349887 
C 252.424 223.540431 261.306906 202.095197 261.306906 179.73725 
C 261.306906 157.379303 252.424 135.934069 236.614543 120.124613 
C 220.805087 104.315157 199.359854 95.43225 177.001906 95.43225 
C 154.643959 95.43225 133.198725 104.315157 117.389269 120.124613 
C 101.579813 135.934069 92.696906 157.379303 92.696906 179.73725 
C 92.696906 202.095197 101.579813 223.540431 117.389269 239.349887 
C 133.198725 255.159343 154.643959 264.04225 177.001906 264.04225 
L 177.001906 264.04225 
z
" clip-path="url(#p60638cf039)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 177.001906 314.62525 
C 212.774622 314.62525 247.086996 300.412599 272.382126 275.11747 
C 297.677256 249.82234 311.889906 215.509966 311.889906 179.73725 
C 311.889906 143.964534 297.677256 109.65216 272.382126 84.35703 
C 247.086996 59.061901 212.774622 44.84925 177.001906 44.84925 
C 141.22919 44.84925 106.916817 59.061901 81.621687 84.35703 
C 56.326557 109.65216 42.113906 143.964534 42.113906 179.73725 
C 42.113906 215.509966 56.326557 249.82234 81.621687 275.11747 
C 106.916817 300.412599 141.22919 314.62525 177.001906 314.62525 
L 177.001906 314.62525 
z
" clip-path="url(#p60638cf039)" style="fill: none; opacity: 0.7; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" clip-path="url(#p60638cf039)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="m1dea598553" d="M 0 0 
L 0 3.5 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m1dea598553" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −8 -->
      <g transform="translate(35.479922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 75.835906 314.62525 
L 75.835906 44.84925 
" clip-path="url(#p60638cf039)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#m1dea598553" x="75.835906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −6 -->
      <g transform="translate(69.201922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 109.557906 314.62525 
L 109.557906 44.84925 
" clip-path="url(#p60638cf039)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#m1dea598553" x="109.557906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_3">
      <!-- −4 -->
      <g transform="translate(102.923922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 143.279906 314.62525 
L 143.279906 44.84925 
" clip-path="url(#p60638cf039)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#m1dea598553" x="143.279906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_4">
      <!-- −2 -->
      <g transform="translate(136.645922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 177.001906 314.62525 
L 177.001906 44.84925 
" clip-path="url(#p60638cf039)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#m1dea598553" x="177.001906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(174.138781 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 210.723906 314.62525 
L 210.723906 44.84925 
" clip-path="url(#p60638cf039)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#m1dea598553" x="210.723906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 2 -->
      <g transform="translate(207.860781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 244.445906 314.62525 
L 244.445906 44.84925 
" clip-path="url(#p60638cf039)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#m1dea598553" x="244.445906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 4 -->
      <g transform="translate(241.582781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <path d="M 278.167906 314.62525 
L 278.167906 44.84925 
" clip-path="url(#p60638cf039)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#m1dea598553" x="278.167906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 6 -->
      <g transform="translate(275.304781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_17">
      <path d="M 311.889906 314.62525 
L 311.889906 44.84925 
" clip-path="url(#p60638cf039)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#m1dea598553" x="311.889906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 8 -->
      <g transform="translate(309.026781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- Relative X Position (m) -->
     <g transform="translate(105.68925 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-52" d="M 2297 2597 
Q 2675 2597 2839 2737 
Q 3003 2878 3003 3200 
Q 3003 3519 2839 3656 
Q 2675 3794 2297 3794 
L 1791 3794 
L 1791 2597 
L 2297 2597 
z
M 1791 1766 
L 1791 0 
L 588 0 
L 588 4666 
L 2425 4666 
Q 3347 4666 3776 4356 
Q 4206 4047 4206 3378 
Q 4206 2916 3982 2619 
Q 3759 2322 3309 2181 
Q 3556 2125 3751 1926 
Q 3947 1728 4147 1325 
L 4800 0 
L 3519 0 
L 2950 1159 
Q 2778 1509 2601 1637 
Q 2425 1766 2131 1766 
L 1791 1766 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6c" d="M 538 4863 
L 1656 4863 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-76" d="M 97 3500 
L 1216 3500 
L 2088 1081 
L 2956 3500 
L 4078 3500 
L 2700 0 
L 1472 0 
L 97 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-58" d="M 3188 2381 
L 4806 0 
L 3553 0 
L 2463 1594 
L 1381 0 
L 122 0 
L 1741 2381 
L 184 4666 
L 1441 4666 
L 2463 3163 
L 3481 4666 
L 4744 4666 
L 3188 2381 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-50" d="M 588 4666 
L 2584 4666 
Q 3475 4666 3951 4270 
Q 4428 3875 4428 3144 
Q 4428 2409 3951 2014 
Q 3475 1619 2584 1619 
L 1791 1619 
L 1791 0 
L 588 0 
L 588 4666 
z
M 1791 3794 
L 1791 2491 
L 2456 2491 
Q 2806 2491 2997 2661 
Q 3188 2831 3188 3144 
Q 3188 3456 2997 3625 
Q 2806 3794 2456 3794 
L 1791 3794 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6f" d="M 2203 2784 
Q 1831 2784 1636 2517 
Q 1441 2250 1441 1747 
Q 1441 1244 1636 976 
Q 1831 709 2203 709 
Q 2569 709 2762 976 
Q 2956 1244 2956 1747 
Q 2956 2250 2762 2517 
Q 2569 2784 2203 2784 
z
M 2203 3584 
Q 3106 3584 3614 3096 
Q 4122 2609 4122 1747 
Q 4122 884 3614 396 
Q 3106 -91 2203 -91 
Q 1297 -91 786 396 
Q 275 884 275 1747 
Q 275 2609 786 3096 
Q 1297 3584 2203 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-28" d="M 2413 -844 
L 1484 -844 
Q 1006 -72 778 623 
Q 550 1319 550 2003 
Q 550 2688 779 3389 
Q 1009 4091 1484 4856 
L 2413 4856 
Q 2013 4116 1813 3408 
Q 1613 2700 1613 2009 
Q 1613 1319 1811 609 
Q 2009 -100 2413 -844 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6d" d="M 3781 2919 
Q 3994 3244 4286 3414 
Q 4578 3584 4928 3584 
Q 5531 3584 5847 3212 
Q 6163 2841 6163 2131 
L 6163 0 
L 5038 0 
L 5038 1825 
Q 5041 1866 5042 1909 
Q 5044 1953 5044 2034 
Q 5044 2406 4934 2573 
Q 4825 2741 4581 2741 
Q 4263 2741 4089 2478 
Q 3916 2216 3909 1719 
L 3909 0 
L 2784 0 
L 2784 1825 
Q 2784 2406 2684 2573 
Q 2584 2741 2328 2741 
Q 2006 2741 1831 2477 
Q 1656 2213 1656 1722 
L 1656 0 
L 531 0 
L 531 3500 
L 1656 3500 
L 1656 2988 
Q 1863 3284 2130 3434 
Q 2397 3584 2719 3584 
Q 3081 3584 3359 3409 
Q 3638 3234 3781 2919 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-29" d="M 513 -844 
Q 913 -100 1113 609 
Q 1313 1319 1313 2009 
Q 1313 2700 1113 3408 
Q 913 4116 513 4856 
L 1441 4856 
Q 1916 4091 2145 3389 
Q 2375 2688 2375 2003 
Q 2375 1319 2147 623 
Q 1919 -72 1441 -844 
L 513 -844 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-58" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="573.583984"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="608.398438"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="681.689453"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="750.390625"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="809.912109"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="844.189453"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="891.992188"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="926.269531"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="994.970703"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1066.162109"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1100.976562"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1146.679688"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1250.878906"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_19">
      <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" clip-path="url(#p60638cf039)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <defs>
       <path id="m03936d7d41" d="M 0 0 
L -3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m03936d7d41" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_11">
      <!-- −8 -->
      <g transform="translate(21.845937 318.044547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_21">
      <path d="M 42.113906 280.90325 
L 311.889906 280.90325 
" clip-path="url(#p60638cf039)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#m03936d7d41" x="42.113906" y="280.90325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_12">
      <!-- −6 -->
      <g transform="translate(21.845937 284.322547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_23">
      <path d="M 42.113906 247.18125 
L 311.889906 247.18125 
" clip-path="url(#p60638cf039)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#m03936d7d41" x="42.113906" y="247.18125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_13">
      <!-- −4 -->
      <g transform="translate(21.845937 250.600547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_25">
      <path d="M 42.113906 213.45925 
L 311.889906 213.45925 
" clip-path="url(#p60638cf039)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#m03936d7d41" x="42.113906" y="213.45925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_14">
      <!-- −2 -->
      <g transform="translate(21.845937 216.878547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_27">
      <path d="M 42.113906 179.73725 
L 311.889906 179.73725 
" clip-path="url(#p60638cf039)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#m03936d7d41" x="42.113906" y="179.73725" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 0 -->
      <g transform="translate(29.387656 183.156547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_29">
      <path d="M 42.113906 146.01525 
L 311.889906 146.01525 
" clip-path="url(#p60638cf039)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#m03936d7d41" x="42.113906" y="146.01525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 2 -->
      <g transform="translate(29.387656 149.434547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_31">
      <path d="M 42.113906 112.29325 
L 311.889906 112.29325 
" clip-path="url(#p60638cf039)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#m03936d7d41" x="42.113906" y="112.29325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 4 -->
      <g transform="translate(29.387656 115.712547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_33">
      <path d="M 42.113906 78.57125 
L 311.889906 78.57125 
" clip-path="url(#p60638cf039)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#m03936d7d41" x="42.113906" y="78.57125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 6 -->
      <g transform="translate(29.387656 81.990547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_35">
      <path d="M 42.113906 44.84925 
L 311.889906 44.84925 
" clip-path="url(#p60638cf039)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#m03936d7d41" x="42.113906" y="44.84925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 8 -->
      <g transform="translate(29.387656 48.268547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_20">
     <!-- Relative Y Position (m) -->
     <g transform="translate(15.558281 250.792094) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-59" d="M -63 4666 
L 1253 4666 
L 2316 3003 
L 3378 4666 
L 4697 4666 
L 2919 1966 
L 2919 0 
L 1716 0 
L 1716 1966 
L -63 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-59" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="568.896484"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="603.710938"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="677.001953"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="745.703125"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="805.224609"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="839.501953"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="887.304688"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="921.582031"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="990.283203"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1061.474609"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1096.289062"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1141.992188"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1246.191406"/>
     </g>
    </g>
   </g>
   <g id="patch_6">
    <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_21">
    <g id="patch_8">
     <path d="M 200.10902 150.124824 
L 225.429957 150.124824 
Q 227.229957 150.124824 227.229957 148.324824 
L 227.229957 139.614511 
Q 227.229957 137.814511 225.429957 137.814511 
L 200.10902 137.814511 
Q 198.30902 137.814511 198.30902 139.614511 
L 198.30902 148.324824 
Q 198.30902 150.124824 200.10902 150.124824 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 3.0m -->
    <g style="fill: #ffffff" transform="translate(200.10902 146.453105) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-33" d="M 2981 2516 
Q 3453 2394 3698 2092 
Q 3944 1791 3944 1325 
Q 3944 631 3412 270 
Q 2881 -91 1863 -91 
Q 1503 -91 1142 -33 
Q 781 25 428 141 
L 428 1069 
Q 766 900 1098 814 
Q 1431 728 1753 728 
Q 2231 728 2486 893 
Q 2741 1059 2741 1369 
Q 2741 1688 2480 1852 
Q 2219 2016 1709 2016 
L 1228 2016 
L 1228 2791 
L 1734 2791 
Q 2188 2791 2409 2933 
Q 2631 3075 2631 3366 
Q 2631 3634 2415 3781 
Q 2200 3928 1806 3928 
Q 1516 3928 1219 3862 
Q 922 3797 628 3669 
L 628 4550 
Q 984 4650 1334 4700 
Q 1684 4750 2022 4750 
Q 2931 4750 3382 4451 
Q 3834 4153 3834 3553 
Q 3834 3144 3618 2883 
Q 3403 2622 2981 2516 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2e" d="M 653 1209 
L 1778 1209 
L 1778 0 
L 653 0 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-30" d="M 2944 2338 
Q 2944 3213 2780 3570 
Q 2616 3928 2228 3928 
Q 1841 3928 1675 3570 
Q 1509 3213 1509 2338 
Q 1509 1453 1675 1090 
Q 1841 728 2228 728 
Q 2613 728 2778 1090 
Q 2944 1453 2944 2338 
z
M 4147 2328 
Q 4147 1169 3647 539 
Q 3147 -91 2228 -91 
Q 1306 -91 806 539 
Q 306 1169 306 2328 
Q 306 3491 806 4120 
Q 1306 4750 2228 4750 
Q 3147 4750 3647 4120 
Q 4147 3491 4147 2328 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-33"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_22">
    <g id="patch_9">
     <path d="M 223.954075 126.279769 
L 249.275012 126.279769 
Q 251.075012 126.279769 251.075012 124.479769 
L 251.075012 115.769457 
Q 251.075012 113.969457 249.275012 113.969457 
L 223.954075 113.969457 
Q 222.154075 113.969457 222.154075 115.769457 
L 222.154075 124.479769 
Q 222.154075 126.279769 223.954075 126.279769 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 5.0m -->
    <g style="fill: #ffffff" transform="translate(223.954075 122.60805) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-35" d="M 678 4666 
L 3669 4666 
L 3669 3781 
L 1638 3781 
L 1638 3059 
Q 1775 3097 1914 3117 
Q 2053 3138 2203 3138 
Q 3056 3138 3531 2711 
Q 4006 2284 4006 1522 
Q 4006 766 3489 337 
Q 2972 -91 2053 -91 
Q 1656 -91 1267 -14 
Q 878 63 494 219 
L 494 1166 
Q 875 947 1217 837 
Q 1559 728 1863 728 
Q 2300 728 2551 942 
Q 2803 1156 2803 1522 
Q 2803 1891 2551 2103 
Q 2300 2316 1863 2316 
Q 1603 2316 1309 2248 
Q 1016 2181 678 2041 
L 678 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-35"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_23">
    <g id="patch_10">
     <path d="M 259.721657 90.512187 
L 285.042595 90.512187 
Q 286.842595 90.512187 286.842595 88.712187 
L 286.842595 80.001874 
Q 286.842595 78.201874 285.042595 78.201874 
L 259.721657 78.201874 
Q 257.921657 78.201874 257.921657 80.001874 
L 257.921657 88.712187 
Q 257.921657 90.512187 259.721657 90.512187 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 8.0m -->
    <g style="fill: #ffffff" transform="translate(259.721657 86.840468) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-38" d="M 2228 2088 
Q 1891 2088 1709 1903 
Q 1528 1719 1528 1375 
Q 1528 1031 1709 848 
Q 1891 666 2228 666 
Q 2563 666 2741 848 
Q 2919 1031 2919 1375 
Q 2919 1722 2741 1905 
Q 2563 2088 2228 2088 
z
M 1350 2484 
Q 925 2613 709 2878 
Q 494 3144 494 3541 
Q 494 4131 934 4440 
Q 1375 4750 2228 4750 
Q 3075 4750 3515 4442 
Q 3956 4134 3956 3541 
Q 3956 3144 3739 2878 
Q 3522 2613 3097 2484 
Q 3572 2353 3814 2058 
Q 4056 1763 4056 1313 
Q 4056 619 3595 264 
Q 3134 -91 2228 -91 
Q 1319 -91 855 264 
Q 391 619 391 1313 
Q 391 1763 633 2058 
Q 875 2353 1350 2484 
z
M 1631 3419 
Q 1631 3141 1786 2991 
Q 1941 2841 2228 2841 
Q 2509 2841 2662 2991 
Q 2816 3141 2816 3419 
Q 2816 3697 2662 3845 
Q 2509 3994 2228 3994 
Q 1941 3994 1786 3844 
Q 1631 3694 1631 3419 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-38"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_24">
    <!-- Far-Distance Cell (Neuron 5) -->
    <g transform="translate(81.107844 16.411875) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-46" d="M 588 4666 
L 3834 4666 
L 3834 3756 
L 1791 3756 
L 1791 2888 
L 3713 2888 
L 3713 1978 
L 1791 1978 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2d" d="M 347 2297 
L 2309 2297 
L 2309 1388 
L 347 1388 
L 347 2297 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-44" d="M 1791 3756 
L 1791 909 
L 2222 909 
Q 2959 909 3348 1275 
Q 3738 1641 3738 2338 
Q 3738 3031 3350 3393 
Q 2963 3756 2222 3756 
L 1791 3756 
z
M 588 4666 
L 1856 4666 
Q 2919 4666 3439 4514 
Q 3959 4363 4331 4000 
Q 4659 3684 4818 3271 
Q 4978 2859 4978 2338 
Q 4978 1809 4818 1395 
Q 4659 981 4331 666 
Q 3956 303 3431 151 
Q 2906 0 1856 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4e" d="M 588 4666 
L 1931 4666 
L 3628 1466 
L 3628 4666 
L 4769 4666 
L 4769 0 
L 3425 0 
L 1728 3200 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-75" d="M 500 1363 
L 500 3500 
L 1625 3500 
L 1625 3150 
Q 1625 2866 1622 2436 
Q 1619 2006 1619 1863 
Q 1619 1441 1641 1255 
Q 1663 1069 1716 984 
Q 1784 875 1895 815 
Q 2006 756 2150 756 
Q 2500 756 2700 1025 
Q 2900 1294 2900 1772 
L 2900 3500 
L 4019 3500 
L 4019 0 
L 2900 0 
L 2900 506 
Q 2647 200 2364 54 
Q 2081 -91 1741 -91 
Q 1134 -91 817 281 
Q 500 653 500 1363 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-46"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="62.435547"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="129.916016"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="179.232422"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="220.736328"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="303.744141"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="338.021484"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="397.542969"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="445.345703"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="512.826172"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="584.017578"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="643.294922"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="711.117188"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="745.931641"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="819.320312"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="887.142578"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="921.419922"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="955.697266"/>
     <use xlink:href="#DejaVuSans-Bold-28" x="990.511719"/>
     <use xlink:href="#DejaVuSans-Bold-4e" x="1036.214844"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1119.90625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1187.728516"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1258.919922"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="1308.236328"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1376.9375"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1448.128906"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="1482.943359"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="1552.523438"/>
    </g>
    <!-- 2D Activation Map -->
    <g transform="translate(114.950656 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-32" d="M 1844 884 
L 3897 884 
L 3897 0 
L 506 0 
L 506 884 
L 2209 2388 
Q 2438 2594 2547 2791 
Q 2656 2988 2656 3200 
Q 2656 3528 2436 3728 
Q 2216 3928 1850 3928 
Q 1569 3928 1234 3808 
Q 900 3688 519 3450 
L 519 4475 
Q 925 4609 1322 4679 
Q 1719 4750 2100 4750 
Q 2938 4750 3402 4381 
Q 3866 4013 3866 3353 
Q 3866 2972 3669 2642 
Q 3472 2313 2841 1759 
L 1844 884 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-41" d="M 3419 850 
L 1538 850 
L 1241 0 
L 31 0 
L 1759 4666 
L 3194 4666 
L 4922 0 
L 3713 0 
L 3419 850 
z
M 1838 1716 
L 3116 1716 
L 2478 3572 
L 1838 1716 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4d" d="M 588 4666 
L 2119 4666 
L 3181 2169 
L 4250 4666 
L 5778 4666 
L 5778 0 
L 4641 0 
L 4641 3413 
L 3566 897 
L 2803 897 
L 1728 3413 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-32"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="152.587891"/>
     <use xlink:href="#DejaVuSans-Bold-41" x="187.402344"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="264.794922"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="324.072266"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="371.875"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="406.152344"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.337891"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="538.818359"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="586.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="620.898438"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="689.599609"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="760.791016"/>
     <use xlink:href="#DejaVuSans-Bold-4d" x="795.605469"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="895.117188"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="962.597656"/>
    </g>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="md0a251dd45" d="M 0 -5.477226 
L -1.229714 -1.692556 
L -5.209151 -1.692556 
L -1.989719 0.646499 
L -3.219432 4.431169 
L -0 2.092114 
L 3.219432 4.431169 
L 1.989719 0.646499 
L 5.209151 -1.692556 
L 1.229714 -1.692556 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#p60638cf039)">
     <use xlink:href="#md0a251dd45" x="177.001906" y="179.73725" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_11">
     <path d="M 242.388656 62.99175 
L 307.889906 62.99175 
L 307.889906 48.84925 
L 242.388656 48.84925 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="PathCollection_2">
     <g>
      <use xlink:href="#md0a251dd45" x="253.588656" y="56.028" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
     </g>
    </g>
    <g id="text_25">
     <!-- Observer -->
     <g transform="translate(267.988656 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4f"/>
      <use xlink:href="#DejaVuSans-62" x="78.710938"/>
      <use xlink:href="#DejaVuSans-73" x="142.1875"/>
      <use xlink:href="#DejaVuSans-65" x="194.287109"/>
      <use xlink:href="#DejaVuSans-72" x="255.810547"/>
      <use xlink:href="#DejaVuSans-76" x="296.923828"/>
      <use xlink:href="#DejaVuSans-65" x="356.103516"/>
      <use xlink:href="#DejaVuSans-72" x="417.626953"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_12">
    <path d="M 441.389015 314.62525 
L 816.217288 314.62525 
L 816.217288 44.84925 
L 441.389015 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="m9ad0971fb9" d="M 446.070274 -37.642188 
L 446.070274 -37.642188 
L 455.432791 -37.642188 
L 464.795308 -37.642188 
L 474.157825 -37.642188 
L 483.520342 -37.642188 
L 492.882859 -37.642188 
L 502.245376 -37.642188 
L 511.607893 -37.642188 
L 520.97041 -37.642188 
L 530.332927 -37.642188 
L 539.695443 -37.642188 
L 549.05796 -37.642188 
L 558.420477 -37.654545 
L 567.782994 -37.669554 
L 577.145511 -37.685415 
L 586.508028 -37.733506 
L 595.870545 -41.103215 
L 605.233062 -40.103206 
L 614.595579 -66.674593 
L 623.958096 -47.906386 
L 633.320613 -92.913889 
L 642.68313 -84.872692 
L 652.045647 -90.759734 
L 661.408164 -150.519662 
L 670.770681 -179.092626 
L 680.133198 -190.207578 
L 689.495715 -189.63772 
L 698.858232 -219.634865 
L 708.220749 -155.650463 
L 717.583266 -294.571711 
L 717.583266 -37.642188 
L 717.583266 -37.642188 
L 708.220749 -37.642188 
L 698.858232 -37.642188 
L 689.495715 -37.642188 
L 680.133198 -37.642188 
L 670.770681 -37.642188 
L 661.408164 -37.642188 
L 652.045647 -37.642188 
L 642.68313 -37.642188 
L 633.320613 -37.642188 
L 623.958096 -37.642188 
L 614.595579 -37.642188 
L 605.233062 -37.642188 
L 595.870545 -37.642188 
L 586.508028 -37.642188 
L 577.145511 -37.642188 
L 567.782994 -37.642188 
L 558.420477 -37.642188 
L 549.05796 -37.642188 
L 539.695443 -37.642188 
L 530.332927 -37.642188 
L 520.97041 -37.642188 
L 511.607893 -37.642188 
L 502.245376 -37.642188 
L 492.882859 -37.642188 
L 483.520342 -37.642188 
L 474.157825 -37.642188 
L 464.795308 -37.642188 
L 455.432791 -37.642188 
L 446.070274 -37.642188 
z
" style="stroke: #33a02c; stroke-opacity: 0.3"/>
    </defs>
    <g clip-path="url(#pc3c6d525f6)">
     <use xlink:href="#m9ad0971fb9" x="0" y="352.267438" style="fill: #33a02c; fill-opacity: 0.3; stroke: #33a02c; stroke-opacity: 0.3"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10">
     <g id="line2d_37">
      <path d="M 441.389015 314.62525 
L 441.389015 44.84925 
" clip-path="url(#pc3c6d525f6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#m1dea598553" x="441.389015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 0 -->
      <g transform="translate(438.52589 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_39">
      <path d="M 488.242549 314.62525 
L 488.242549 44.84925 
" clip-path="url(#pc3c6d525f6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#m1dea598553" x="488.242549" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 1 -->
      <g transform="translate(485.379424 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_41">
      <path d="M 535.096084 314.62525 
L 535.096084 44.84925 
" clip-path="url(#pc3c6d525f6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#m1dea598553" x="535.096084" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 2 -->
      <g transform="translate(532.232959 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_43">
      <path d="M 581.949618 314.62525 
L 581.949618 44.84925 
" clip-path="url(#pc3c6d525f6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#m1dea598553" x="581.949618" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 3 -->
      <g transform="translate(579.086493 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_45">
      <path d="M 628.803152 314.62525 
L 628.803152 44.84925 
" clip-path="url(#pc3c6d525f6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#m1dea598553" x="628.803152" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 4 -->
      <g transform="translate(625.940027 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_47">
      <path d="M 675.656686 314.62525 
L 675.656686 44.84925 
" clip-path="url(#pc3c6d525f6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#m1dea598553" x="675.656686" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 5 -->
      <g transform="translate(672.793561 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_49">
      <path d="M 722.51022 314.62525 
L 722.51022 44.84925 
" clip-path="url(#pc3c6d525f6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#m1dea598553" x="722.51022" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 6 -->
      <g transform="translate(719.647095 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_51">
      <path d="M 769.363754 314.62525 
L 769.363754 44.84925 
" clip-path="url(#pc3c6d525f6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#m1dea598553" x="769.363754" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 7 -->
      <g transform="translate(766.500629 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-37"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_53">
      <path d="M 816.217288 314.62525 
L 816.217288 44.84925 
" clip-path="url(#pc3c6d525f6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#m1dea598553" x="816.217288" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 8 -->
      <g transform="translate(813.354163 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_35">
     <!-- Inter-Agent Distance (m) -->
     <g transform="translate(551.460261 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-49" d="M 588 4666 
L 1791 4666 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-49"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="37.207031"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="108.398438"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="156.201172"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="224.023438"/>
      <use xlink:href="#DejaVuSans-Bold-2d" x="273.339844"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="314.84375"/>
      <use xlink:href="#DejaVuSans-Bold-67" x="392.236328"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="463.818359"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="531.640625"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="602.832031"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="650.634766"/>
      <use xlink:href="#DejaVuSans-Bold-44" x="685.449219"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="768.457031"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="802.734375"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="862.255859"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="910.058594"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="977.539062"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="1048.730469"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="1108.007812"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1175.830078"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1210.644531"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1256.347656"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1360.546875"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_55">
      <path d="M 441.389015 314.62525 
L 816.217288 314.62525 
" clip-path="url(#pc3c6d525f6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#m03936d7d41" x="441.389015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 0.00 -->
      <g transform="translate(414.349953 318.044547) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_57">
      <path d="M 441.389015 281.529913 
L 816.217288 281.529913 
" clip-path="url(#pc3c6d525f6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#m03936d7d41" x="441.389015" y="281.529913" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 0.02 -->
      <g transform="translate(414.349953 284.94921) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-32" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_59">
      <path d="M 441.389015 248.434577 
L 816.217288 248.434577 
" clip-path="url(#pc3c6d525f6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#m03936d7d41" x="441.389015" y="248.434577" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 0.04 -->
      <g transform="translate(414.349953 251.853873) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-34" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_61">
      <path d="M 441.389015 215.33924 
L 816.217288 215.33924 
" clip-path="url(#pc3c6d525f6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#m03936d7d41" x="441.389015" y="215.33924" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 0.06 -->
      <g transform="translate(414.349953 218.758537) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-36" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_63">
      <path d="M 441.389015 182.243903 
L 816.217288 182.243903 
" clip-path="url(#pc3c6d525f6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#m03936d7d41" x="441.389015" y="182.243903" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 0.08 -->
      <g transform="translate(414.349953 185.6632) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-38" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_65">
      <path d="M 441.389015 149.148566 
L 816.217288 149.148566 
" clip-path="url(#pc3c6d525f6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#m03936d7d41" x="441.389015" y="149.148566" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 0.10 -->
      <g transform="translate(414.349953 152.567863) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_67">
      <path d="M 441.389015 116.05323 
L 816.217288 116.05323 
" clip-path="url(#pc3c6d525f6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_68">
      <g>
       <use xlink:href="#m03936d7d41" x="441.389015" y="116.05323" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_42">
      <!-- 0.12 -->
      <g transform="translate(414.349953 119.472526) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
       <use xlink:href="#DejaVuSans-32" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_69">
      <path d="M 441.389015 82.957893 
L 816.217288 82.957893 
" clip-path="url(#pc3c6d525f6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_70">
      <g>
       <use xlink:href="#m03936d7d41" x="441.389015" y="82.957893" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_43">
      <!-- 0.14 -->
      <g transform="translate(414.349953 86.37719) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
       <use xlink:href="#DejaVuSans-34" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_71">
      <path d="M 441.389015 49.862556 
L 816.217288 49.862556 
" clip-path="url(#pc3c6d525f6)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_72">
      <g>
       <use xlink:href="#m03936d7d41" x="441.389015" y="49.862556" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_44">
      <!-- 0.16 -->
      <g transform="translate(414.349953 53.281853) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
       <use xlink:href="#DejaVuSans-36" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="text_45">
     <!-- Neural Activation -->
     <g transform="translate(408.062297 233.746391) rotate(-90) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="line2d_73">
    <path d="M 446.070274 314.62525 
L 455.432791 314.62525 
L 464.795308 314.62525 
L 474.157825 314.62525 
L 483.520342 314.62525 
L 492.882859 314.62525 
L 502.245376 314.62525 
L 511.607893 314.62525 
L 520.97041 314.62525 
L 530.332927 314.62525 
L 539.695443 314.62525 
L 549.05796 314.62525 
L 558.420477 314.612893 
L 567.782994 314.597884 
L 577.145511 314.582023 
L 586.508028 314.533931 
L 595.870545 311.164222 
L 605.233062 312.164232 
L 614.595579 285.592844 
L 623.958096 304.361051 
L 633.320613 259.353549 
L 642.68313 267.394746 
L 652.045647 261.507704 
L 661.408164 201.747775 
L 670.770681 173.174812 
L 680.133198 162.05986 
L 689.495715 162.629717 
L 698.858232 132.632572 
L 708.220749 196.616974 
L 717.583266 57.695726 
" clip-path="url(#pc3c6d525f6)" style="fill: none; stroke: #33a02c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_74">
    <path d="M 717.583266 314.62525 
L 717.583266 44.84925 
" clip-path="url(#pc3c6d525f6)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #33a02c; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_75">
    <path d="M 581.949618 314.62525 
L 581.949618 44.84925 
" clip-path="url(#pc3c6d525f6)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_76">
    <path d="M 675.656686 314.62525 
L 675.656686 44.84925 
" clip-path="url(#pc3c6d525f6)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="patch_13">
    <path d="M 441.389015 314.62525 
L 441.389015 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_14">
    <path d="M 441.389015 314.62525 
L 816.217288 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_46">
    <g id="patch_15">
     <path d="M 703.034624 114.0718 
L 797.475874 114.0718 
Q 800.675874 114.0718 800.675874 110.8718 
L 800.675874 58.33805 
Q 800.675874 55.13805 797.475874 55.13805 
L 703.034624 55.13805 
Q 699.834624 55.13805 699.834624 58.33805 
L 699.834624 110.8718 
Q 699.834624 114.0718 703.034624 114.0718 
z
" style="fill: #ffffff; opacity: 0.9; stroke: #808080; stroke-linejoin: miter"/>
    </g>
    <!-- Peak Distance: 5.9m -->
    <g transform="translate(715.209624 64.4168) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-3a" d="M 750 794 
L 1409 794 
L 1409 0 
L 750 0 
L 750 794 
z
M 750 3309 
L 1409 3309 
L 1409 2516 
L 750 2516 
L 750 3309 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-44" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="346.179688"/>
     <use xlink:href="#DejaVuSans-73" x="373.962891"/>
     <use xlink:href="#DejaVuSans-74" x="426.0625"/>
     <use xlink:href="#DejaVuSans-61" x="465.271484"/>
     <use xlink:href="#DejaVuSans-6e" x="526.550781"/>
     <use xlink:href="#DejaVuSans-63" x="589.929688"/>
     <use xlink:href="#DejaVuSans-65" x="644.910156"/>
     <use xlink:href="#DejaVuSans-3a" x="706.433594"/>
     <use xlink:href="#DejaVuSans-20" x="740.125"/>
     <use xlink:href="#DejaVuSans-35" x="771.912109"/>
     <use xlink:href="#DejaVuSans-2e" x="835.535156"/>
     <use xlink:href="#DejaVuSans-39" x="867.322266"/>
     <use xlink:href="#DejaVuSans-6d" x="930.945312"/>
    </g>
    <!-- Max Activation: 0.155 -->
    <g transform="translate(710.202124 73.37505) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-4d"/>
     <use xlink:href="#DejaVuSans-61" x="86.279297"/>
     <use xlink:href="#DejaVuSans-78" x="147.558594"/>
     <use xlink:href="#DejaVuSans-20" x="206.738281"/>
     <use xlink:href="#DejaVuSans-41" x="238.525391"/>
     <use xlink:href="#DejaVuSans-63" x="305.183594"/>
     <use xlink:href="#DejaVuSans-74" x="360.164062"/>
     <use xlink:href="#DejaVuSans-69" x="399.373047"/>
     <use xlink:href="#DejaVuSans-76" x="427.15625"/>
     <use xlink:href="#DejaVuSans-61" x="486.335938"/>
     <use xlink:href="#DejaVuSans-74" x="547.615234"/>
     <use xlink:href="#DejaVuSans-69" x="586.824219"/>
     <use xlink:href="#DejaVuSans-6f" x="614.607422"/>
     <use xlink:href="#DejaVuSans-6e" x="675.789062"/>
     <use xlink:href="#DejaVuSans-3a" x="739.167969"/>
     <use xlink:href="#DejaVuSans-20" x="772.859375"/>
     <use xlink:href="#DejaVuSans-30" x="804.646484"/>
     <use xlink:href="#DejaVuSans-2e" x="868.269531"/>
     <use xlink:href="#DejaVuSans-31" x="900.056641"/>
     <use xlink:href="#DejaVuSans-35" x="963.679688"/>
     <use xlink:href="#DejaVuSans-35" x="1027.302734"/>
    </g>
    <!-- Sparsity: 0.737 -->
    <g transform="translate(737.305874 82.3333) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-70" x="63.476562"/>
     <use xlink:href="#DejaVuSans-61" x="126.953125"/>
     <use xlink:href="#DejaVuSans-72" x="188.232422"/>
     <use xlink:href="#DejaVuSans-73" x="229.345703"/>
     <use xlink:href="#DejaVuSans-69" x="281.445312"/>
     <use xlink:href="#DejaVuSans-74" x="309.228516"/>
     <use xlink:href="#DejaVuSans-79" x="348.4375"/>
     <use xlink:href="#DejaVuSans-3a" x="400.367188"/>
     <use xlink:href="#DejaVuSans-20" x="434.058594"/>
     <use xlink:href="#DejaVuSans-30" x="465.845703"/>
     <use xlink:href="#DejaVuSans-2e" x="529.46875"/>
     <use xlink:href="#DejaVuSans-37" x="561.255859"/>
     <use xlink:href="#DejaVuSans-33" x="624.878906"/>
     <use xlink:href="#DejaVuSans-37" x="688.501953"/>
    </g>
    <!-- Peak Significance: 3.72 -->
    <g transform="translate(704.488374 91.29155) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-53" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="332.654297"/>
     <use xlink:href="#DejaVuSans-67" x="360.4375"/>
     <use xlink:href="#DejaVuSans-6e" x="423.914062"/>
     <use xlink:href="#DejaVuSans-69" x="487.292969"/>
     <use xlink:href="#DejaVuSans-66" x="515.076172"/>
     <use xlink:href="#DejaVuSans-69" x="550.28125"/>
     <use xlink:href="#DejaVuSans-63" x="578.064453"/>
     <use xlink:href="#DejaVuSans-61" x="633.044922"/>
     <use xlink:href="#DejaVuSans-6e" x="694.324219"/>
     <use xlink:href="#DejaVuSans-63" x="757.703125"/>
     <use xlink:href="#DejaVuSans-65" x="812.683594"/>
     <use xlink:href="#DejaVuSans-3a" x="874.207031"/>
     <use xlink:href="#DejaVuSans-20" x="907.898438"/>
     <use xlink:href="#DejaVuSans-33" x="939.685547"/>
     <use xlink:href="#DejaVuSans-2e" x="1003.308594"/>
     <use xlink:href="#DejaVuSans-37" x="1035.095703"/>
     <use xlink:href="#DejaVuSans-32" x="1098.71875"/>
    </g>
    <!-- Selectivity Index: 0.953 -->
    <g transform="translate(703.034624 100.2498) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-65" x="63.476562"/>
     <use xlink:href="#DejaVuSans-6c" x="125"/>
     <use xlink:href="#DejaVuSans-65" x="152.783203"/>
     <use xlink:href="#DejaVuSans-63" x="214.306641"/>
     <use xlink:href="#DejaVuSans-74" x="269.287109"/>
     <use xlink:href="#DejaVuSans-69" x="308.496094"/>
     <use xlink:href="#DejaVuSans-76" x="336.279297"/>
     <use xlink:href="#DejaVuSans-69" x="395.458984"/>
     <use xlink:href="#DejaVuSans-74" x="423.242188"/>
     <use xlink:href="#DejaVuSans-79" x="462.451172"/>
     <use xlink:href="#DejaVuSans-20" x="521.630859"/>
     <use xlink:href="#DejaVuSans-49" x="553.417969"/>
     <use xlink:href="#DejaVuSans-6e" x="582.910156"/>
     <use xlink:href="#DejaVuSans-64" x="646.289062"/>
     <use xlink:href="#DejaVuSans-65" x="709.765625"/>
     <use xlink:href="#DejaVuSans-78" x="769.539062"/>
     <use xlink:href="#DejaVuSans-3a" x="828.71875"/>
     <use xlink:href="#DejaVuSans-20" x="862.410156"/>
     <use xlink:href="#DejaVuSans-30" x="894.197266"/>
     <use xlink:href="#DejaVuSans-2e" x="957.820312"/>
     <use xlink:href="#DejaVuSans-39" x="989.607422"/>
     <use xlink:href="#DejaVuSans-35" x="1053.230469"/>
     <use xlink:href="#DejaVuSans-33" x="1116.853516"/>
    </g>
    <!-- Peak Width: 1.0m -->
    <g transform="translate(726.952124 109.20805) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-57" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="365.804688"/>
     <use xlink:href="#DejaVuSans-64" x="393.587891"/>
     <use xlink:href="#DejaVuSans-74" x="457.064453"/>
     <use xlink:href="#DejaVuSans-68" x="496.273438"/>
     <use xlink:href="#DejaVuSans-3a" x="559.652344"/>
     <use xlink:href="#DejaVuSans-20" x="593.34375"/>
     <use xlink:href="#DejaVuSans-31" x="625.130859"/>
     <use xlink:href="#DejaVuSans-2e" x="688.753906"/>
     <use xlink:href="#DejaVuSans-30" x="720.541016"/>
     <use xlink:href="#DejaVuSans-6d" x="784.164062"/>
    </g>
   </g>
   <g id="text_47">
    <!-- Distance Tuning Curve -->
    <g transform="translate(552.989402 16.318125) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-54" d="M 31 4666 
L 4331 4666 
L 4331 3756 
L 2784 3756 
L 2784 0 
L 1581 0 
L 1581 3756 
L 31 3756 
L 31 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-44"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
     <use xlink:href="#DejaVuSans-Bold-54" x="525.195312"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="582.408203"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="653.599609"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="724.791016"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="759.068359"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="830.259766"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="901.841797"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="936.65625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1010.044922"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1081.236328"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="1130.552734"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1195.738281"/>
    </g>
    <!-- Peak: 5.9m, Sparsity: 0.737 -->
    <g transform="translate(535.064402 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-6b" d="M 538 4863 
L 1656 4863 
L 1656 2216 
L 2944 3500 
L 4244 3500 
L 2534 1894 
L 4378 0 
L 3022 0 
L 1656 1459 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-3a" d="M 716 3500 
L 1844 3500 
L 1844 2291 
L 716 2291 
L 716 3500 
z
M 716 1209 
L 1844 1209 
L 1844 0 
L 716 0 
L 716 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-39" d="M 641 103 
L 641 966 
Q 928 831 1190 764 
Q 1453 697 1709 697 
Q 2247 697 2547 995 
Q 2847 1294 2900 1881 
Q 2688 1725 2447 1647 
Q 2206 1569 1925 1569 
Q 1209 1569 770 1986 
Q 331 2403 331 3084 
Q 331 3838 820 4291 
Q 1309 4744 2131 4744 
Q 3044 4744 3544 4128 
Q 4044 3513 4044 2388 
Q 4044 1231 3459 570 
Q 2875 -91 1856 -91 
Q 1528 -91 1228 -42 
Q 928 6 641 103 
z
M 2125 2350 
Q 2441 2350 2600 2554 
Q 2759 2759 2759 3169 
Q 2759 3575 2600 3781 
Q 2441 3988 2125 3988 
Q 1809 3988 1650 3781 
Q 1491 3575 1491 3169 
Q 1491 2759 1650 2554 
Q 1809 2350 2125 2350 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2c" d="M 653 1209 
L 1778 1209 
L 1778 256 
L 1006 -909 
L 341 -909 
L 653 256 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-53" d="M 3834 4519 
L 3834 3531 
Q 3450 3703 3084 3790 
Q 2719 3878 2394 3878 
Q 1963 3878 1756 3759 
Q 1550 3641 1550 3391 
Q 1550 3203 1689 3098 
Q 1828 2994 2194 2919 
L 2706 2816 
Q 3484 2659 3812 2340 
Q 4141 2022 4141 1434 
Q 4141 663 3683 286 
Q 3225 -91 2284 -91 
Q 1841 -91 1394 -6 
Q 947 78 500 244 
L 500 1259 
Q 947 1022 1364 901 
Q 1781 781 2169 781 
Q 2563 781 2772 912 
Q 2981 1044 2981 1288 
Q 2981 1506 2839 1625 
Q 2697 1744 2272 1838 
L 1806 1941 
Q 1106 2091 782 2419 
Q 459 2747 459 3303 
Q 459 4000 909 4375 
Q 1359 4750 2203 4750 
Q 2588 4750 2994 4692 
Q 3400 4634 3834 4519 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-79" d="M 78 3500 
L 1197 3500 
L 2138 1125 
L 2938 3500 
L 4056 3500 
L 2584 -331 
Q 2363 -916 2067 -1148 
Q 1772 -1381 1288 -1381 
L 641 -1381 
L 641 -647 
L 991 -647 
Q 1275 -647 1404 -556 
Q 1534 -466 1606 -231 
L 1638 -134 
L 78 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-37" d="M 428 4666 
L 3944 4666 
L 3944 3988 
L 2125 0 
L 953 0 
L 2675 3781 
L 428 3781 
L 428 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-50"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="73.291016"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="141.113281"/>
     <use xlink:href="#DejaVuSans-Bold-6b" x="208.59375"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="275.097656"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="315.087891"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="349.902344"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="419.482422"/>
     <use xlink:href="#DejaVuSans-Bold-39" x="457.470703"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="527.050781"/>
     <use xlink:href="#DejaVuSans-Bold-2c" x="631.25"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="669.238281"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="704.052734"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="776.074219"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="847.65625"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="915.136719"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="964.453125"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1023.974609"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="1058.251953"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="1106.054688"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="1171.240234"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1211.230469"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1246.044922"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="1315.625"/>
     <use xlink:href="#DejaVuSans-Bold-37" x="1353.613281"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="1423.193359"/>
     <use xlink:href="#DejaVuSans-Bold-37" x="1492.773438"/>
    </g>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="mc6ec0dadf6" d="M 0 5 
C 1.326016 5 2.597899 4.473168 3.535534 3.535534 
C 4.473168 2.597899 5 1.326016 5 0 
C 5 -1.326016 4.473168 -2.597899 3.535534 -3.535534 
C 2.597899 -4.473168 1.326016 -5 0 -5 
C -1.326016 -5 -2.597899 -4.473168 -3.535534 -3.535534 
C -4.473168 -2.597899 -5 -1.326016 -5 0 
C -5 1.326016 -4.473168 2.597899 -3.535534 3.535534 
C -2.597899 4.473168 -1.326016 5 0 5 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#pc3c6d525f6)">
     <use xlink:href="#mc6ec0dadf6" x="717.583266" y="57.695726" style="fill: #33a02c; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="patch_16">
     <path d="M 446.989015 86.47675 
L 563.896515 86.47675 
Q 565.496515 86.47675 565.496515 84.87675 
L 565.496515 50.44925 
Q 565.496515 48.84925 563.896515 48.84925 
L 446.989015 48.84925 
Q 445.389015 48.84925 445.389015 50.44925 
L 445.389015 84.87675 
Q 445.389015 86.47675 446.989015 86.47675 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_77">
     <path d="M 448.589015 55.328 
L 456.589015 55.328 
L 464.589015 55.328 
" style="fill: none; stroke: #33a02c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
    </g>
    <g id="text_48">
     <!-- Tuning Curve -->
     <g transform="translate(470.989015 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-54"/>
      <use xlink:href="#DejaVuSans-75" x="45.958984"/>
      <use xlink:href="#DejaVuSans-6e" x="109.337891"/>
      <use xlink:href="#DejaVuSans-69" x="172.716797"/>
      <use xlink:href="#DejaVuSans-6e" x="200.5"/>
      <use xlink:href="#DejaVuSans-67" x="263.878906"/>
      <use xlink:href="#DejaVuSans-20" x="327.355469"/>
      <use xlink:href="#DejaVuSans-43" x="359.142578"/>
      <use xlink:href="#DejaVuSans-75" x="428.966797"/>
      <use xlink:href="#DejaVuSans-72" x="492.345703"/>
      <use xlink:href="#DejaVuSans-76" x="533.458984"/>
      <use xlink:href="#DejaVuSans-65" x="592.638672"/>
     </g>
    </g>
    <g id="line2d_78">
     <path d="M 448.589015 67.0705 
L 456.589015 67.0705 
L 464.589015 67.0705 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_49">
     <!-- Close threshold (3.0m) -->
     <g transform="translate(470.989015 69.8705) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6c" x="69.824219"/>
      <use xlink:href="#DejaVuSans-6f" x="97.607422"/>
      <use xlink:href="#DejaVuSans-73" x="158.789062"/>
      <use xlink:href="#DejaVuSans-65" x="210.888672"/>
      <use xlink:href="#DejaVuSans-20" x="272.412109"/>
      <use xlink:href="#DejaVuSans-74" x="304.199219"/>
      <use xlink:href="#DejaVuSans-68" x="343.408203"/>
      <use xlink:href="#DejaVuSans-72" x="406.787109"/>
      <use xlink:href="#DejaVuSans-65" x="445.650391"/>
      <use xlink:href="#DejaVuSans-73" x="507.173828"/>
      <use xlink:href="#DejaVuSans-68" x="559.273438"/>
      <use xlink:href="#DejaVuSans-6f" x="622.652344"/>
      <use xlink:href="#DejaVuSans-6c" x="683.833984"/>
      <use xlink:href="#DejaVuSans-64" x="711.617188"/>
      <use xlink:href="#DejaVuSans-20" x="775.09375"/>
      <use xlink:href="#DejaVuSans-28" x="806.880859"/>
      <use xlink:href="#DejaVuSans-33" x="845.894531"/>
      <use xlink:href="#DejaVuSans-2e" x="909.517578"/>
      <use xlink:href="#DejaVuSans-30" x="941.304688"/>
      <use xlink:href="#DejaVuSans-6d" x="1004.927734"/>
      <use xlink:href="#DejaVuSans-29" x="1102.339844"/>
     </g>
    </g>
    <g id="line2d_79">
     <path d="M 448.589015 78.813 
L 456.589015 78.813 
L 464.589015 78.813 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_50">
     <!-- Far threshold (5.0m) -->
     <g transform="translate(470.989015 81.613) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" x="48.394531"/>
      <use xlink:href="#DejaVuSans-72" x="109.673828"/>
      <use xlink:href="#DejaVuSans-20" x="150.787109"/>
      <use xlink:href="#DejaVuSans-74" x="182.574219"/>
      <use xlink:href="#DejaVuSans-68" x="221.783203"/>
      <use xlink:href="#DejaVuSans-72" x="285.162109"/>
      <use xlink:href="#DejaVuSans-65" x="324.025391"/>
      <use xlink:href="#DejaVuSans-73" x="385.548828"/>
      <use xlink:href="#DejaVuSans-68" x="437.648438"/>
      <use xlink:href="#DejaVuSans-6f" x="501.027344"/>
      <use xlink:href="#DejaVuSans-6c" x="562.208984"/>
      <use xlink:href="#DejaVuSans-64" x="589.992188"/>
      <use xlink:href="#DejaVuSans-20" x="653.46875"/>
      <use xlink:href="#DejaVuSans-28" x="685.255859"/>
      <use xlink:href="#DejaVuSans-35" x="724.269531"/>
      <use xlink:href="#DejaVuSans-2e" x="787.892578"/>
      <use xlink:href="#DejaVuSans-30" x="819.679688"/>
      <use xlink:href="#DejaVuSans-6d" x="883.302734"/>
      <use xlink:href="#DejaVuSans-29" x="980.714844"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_17">
    <path d="M 330.63132 287.64765 
L 341.42236 287.64765 
L 341.42236 71.82685 
L 330.63132 71.82685 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAABYAAAHBCAYAAABt+EsYAAACDUlEQVR4nO2bS25CQQwEDUzunXsHQhYz9MsFelFS1QGskrv9+AhuM9/vKbBmVmPu3CtTZ2bNfFUGN43dcQa7io11C9YtIOvGM+60wlUE6xasW7BuwR0HX/4Dsm48Y5/HG96B3GbelS9CasYOBg9ec+sMLho/WoM7D7fmKjoPN40vrFsghscztm4HYng8Y+t2IIbHM7ZuB2J4PGPrdiCGxzO2bgdieDzjUt2KxqUeE8PT+MA7EJ/HQeNQM3YVQePg0y0Qw9O4PZi3Co2DT7dADE/ja3Dl1zzVVbwqgzUO6/54dgavL9oqHrjwiMatuvFWcccZj8abpnHrQICr0Hhj3YLhBY3/D+70uLcKD+QDz9i6BV54PGPrFnjh8YyRq/BANtYtWLdg3fqDeavwQELtQJDh8Yyt2xlMDE/jjQcSPJCANLZuZzAxPJ6xdTuDieFpvOEdiF8rBI2DdQvI8DTOYN9XbHjhFY1frbo9cXV7/VYGF8PrrBhovG6dGjf/XYELT+NrsHU7aBxqrSCuQuMDr268HRte4IXHMza8oHGwbsHwAu8VhLgKnrEn3R7MW4V1C9YtGF7gGRNXYd0OPGPDCxoHPyoEjQOvbrwdG17ghcczNrygcbBuYf3gwiv9jq6549a7TeAqcMYl4eaOaYMNL7jja7Cr+GDdgnUL1i3wjA0veCDBugXrFng75q3iD9EGue+xdPB4AAAAAElFTkSuQmCC" id="image8ca402a9fc" transform="scale(1 -1) translate(0 -215.52)" x="330.72" y="-71.52" width="10.56" height="215.52"/>
   <g id="matplotlib.axis_5"/>
   <g id="matplotlib.axis_6">
    <g id="ytick_19">
     <g id="line2d_80">
      <defs>
       <path id="m38ece00b16" d="M 0 0 
L 3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m38ece00b16" x="341.42236" y="287.64765" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_51">
      <!-- 0.0 -->
      <g transform="translate(348.42236 291.066947) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_81">
      <g>
       <use xlink:href="#m38ece00b16" x="341.42236" y="244.48349" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_52">
      <!-- 0.2 -->
      <g transform="translate(348.42236 247.902787) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_82">
      <g>
       <use xlink:href="#m38ece00b16" x="341.42236" y="201.31933" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_53">
      <!-- 0.4 -->
      <g transform="translate(348.42236 204.738627) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_83">
      <g>
       <use xlink:href="#m38ece00b16" x="341.42236" y="158.15517" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_54">
      <!-- 0.6 -->
      <g transform="translate(348.42236 161.574467) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_23">
     <g id="line2d_84">
      <g>
       <use xlink:href="#m38ece00b16" x="341.42236" y="114.99101" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_55">
      <!-- 0.8 -->
      <g transform="translate(348.42236 118.410307) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-38" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_24">
     <g id="line2d_85">
      <g>
       <use xlink:href="#m38ece00b16" x="341.42236" y="71.82685" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_56">
      <!-- 1.0 -->
      <g transform="translate(348.42236 75.246147) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_57">
     <!-- Neural Activation -->
     <g transform="translate(369.376891 125.728109) rotate(-270) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_18">
    <path d="M 330.63132 287.64765 
L 336.02684 287.64765 
L 341.42236 287.64765 
L 341.42236 71.82685 
L 336.02684 71.82685 
L 330.63132 71.82685 
L 330.63132 287.64765 
z
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p60638cf039">
   <rect x="42.113906" y="44.84925" width="269.776" height="269.776"/>
  </clipPath>
  <clipPath id="pc3c6d525f6">
   <rect x="441.389015" y="44.84925" width="374.828273" height="269.776"/>
  </clipPath>
 </defs>
</svg>
