<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="824.000413pt" height="352.267438pt" viewBox="0 0 824.000413 352.267438" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:56:33.161132</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 352.267438 
L 824.000413 352.267438 
L 824.000413 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
L 311.889906 44.84925 
L 42.113906 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g clip-path="url(#pc6639645e1)">
    <image xlink:href="data:image/png;base64,
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" id="imageabb7d42f55" transform="scale(1 -1) translate(0 -270.24)" x="42.113906" y="-44.38525" width="270.24" height="270.24"/>
   </g>
   <g id="patch_3">
    <path d="M 177.001906 230.32025 
C 190.416675 230.32025 203.283815 224.990506 212.769489 215.504832 
C 222.255162 206.019159 227.584906 193.152018 227.584906 179.73725 
C 227.584906 166.322482 222.255162 153.455341 212.769489 143.969668 
C 203.283815 134.483994 190.416675 129.15425 177.001906 129.15425 
C 163.587138 129.15425 150.719998 134.483994 141.234324 143.969668 
C 131.74865 153.455341 126.418906 166.322482 126.418906 179.73725 
C 126.418906 193.152018 131.74865 206.019159 141.234324 215.504832 
C 150.719998 224.990506 163.587138 230.32025 177.001906 230.32025 
L 177.001906 230.32025 
z
" clip-path="url(#pc6639645e1)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 177.001906 264.04225 
C 199.359854 264.04225 220.805087 255.159343 236.614543 239.349887 
C 252.424 223.540431 261.306906 202.095197 261.306906 179.73725 
C 261.306906 157.379303 252.424 135.934069 236.614543 120.124613 
C 220.805087 104.315157 199.359854 95.43225 177.001906 95.43225 
C 154.643959 95.43225 133.198725 104.315157 117.389269 120.124613 
C 101.579813 135.934069 92.696906 157.379303 92.696906 179.73725 
C 92.696906 202.095197 101.579813 223.540431 117.389269 239.349887 
C 133.198725 255.159343 154.643959 264.04225 177.001906 264.04225 
L 177.001906 264.04225 
z
" clip-path="url(#pc6639645e1)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 177.001906 314.62525 
C 212.774622 314.62525 247.086996 300.412599 272.382126 275.11747 
C 297.677256 249.82234 311.889906 215.509966 311.889906 179.73725 
C 311.889906 143.964534 297.677256 109.65216 272.382126 84.35703 
C 247.086996 59.061901 212.774622 44.84925 177.001906 44.84925 
C 141.22919 44.84925 106.916817 59.061901 81.621687 84.35703 
C 56.326557 109.65216 42.113906 143.964534 42.113906 179.73725 
C 42.113906 215.509966 56.326557 249.82234 81.621687 275.11747 
C 106.916817 300.412599 141.22919 314.62525 177.001906 314.62525 
L 177.001906 314.62525 
z
" clip-path="url(#pc6639645e1)" style="fill: none; opacity: 0.7; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" clip-path="url(#pc6639645e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="m25ca827a53" d="M 0 0 
L 0 3.5 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m25ca827a53" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −8 -->
      <g transform="translate(35.479922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 75.835906 314.62525 
L 75.835906 44.84925 
" clip-path="url(#pc6639645e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#m25ca827a53" x="75.835906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −6 -->
      <g transform="translate(69.201922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 109.557906 314.62525 
L 109.557906 44.84925 
" clip-path="url(#pc6639645e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#m25ca827a53" x="109.557906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_3">
      <!-- −4 -->
      <g transform="translate(102.923922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 143.279906 314.62525 
L 143.279906 44.84925 
" clip-path="url(#pc6639645e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#m25ca827a53" x="143.279906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_4">
      <!-- −2 -->
      <g transform="translate(136.645922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 177.001906 314.62525 
L 177.001906 44.84925 
" clip-path="url(#pc6639645e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#m25ca827a53" x="177.001906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(174.138781 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 210.723906 314.62525 
L 210.723906 44.84925 
" clip-path="url(#pc6639645e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#m25ca827a53" x="210.723906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 2 -->
      <g transform="translate(207.860781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 244.445906 314.62525 
L 244.445906 44.84925 
" clip-path="url(#pc6639645e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#m25ca827a53" x="244.445906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 4 -->
      <g transform="translate(241.582781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <path d="M 278.167906 314.62525 
L 278.167906 44.84925 
" clip-path="url(#pc6639645e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#m25ca827a53" x="278.167906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 6 -->
      <g transform="translate(275.304781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_17">
      <path d="M 311.889906 314.62525 
L 311.889906 44.84925 
" clip-path="url(#pc6639645e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#m25ca827a53" x="311.889906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 8 -->
      <g transform="translate(309.026781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- Relative X Position (m) -->
     <g transform="translate(105.68925 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-52" d="M 2297 2597 
Q 2675 2597 2839 2737 
Q 3003 2878 3003 3200 
Q 3003 3519 2839 3656 
Q 2675 3794 2297 3794 
L 1791 3794 
L 1791 2597 
L 2297 2597 
z
M 1791 1766 
L 1791 0 
L 588 0 
L 588 4666 
L 2425 4666 
Q 3347 4666 3776 4356 
Q 4206 4047 4206 3378 
Q 4206 2916 3982 2619 
Q 3759 2322 3309 2181 
Q 3556 2125 3751 1926 
Q 3947 1728 4147 1325 
L 4800 0 
L 3519 0 
L 2950 1159 
Q 2778 1509 2601 1637 
Q 2425 1766 2131 1766 
L 1791 1766 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6c" d="M 538 4863 
L 1656 4863 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-76" d="M 97 3500 
L 1216 3500 
L 2088 1081 
L 2956 3500 
L 4078 3500 
L 2700 0 
L 1472 0 
L 97 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-58" d="M 3188 2381 
L 4806 0 
L 3553 0 
L 2463 1594 
L 1381 0 
L 122 0 
L 1741 2381 
L 184 4666 
L 1441 4666 
L 2463 3163 
L 3481 4666 
L 4744 4666 
L 3188 2381 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-50" d="M 588 4666 
L 2584 4666 
Q 3475 4666 3951 4270 
Q 4428 3875 4428 3144 
Q 4428 2409 3951 2014 
Q 3475 1619 2584 1619 
L 1791 1619 
L 1791 0 
L 588 0 
L 588 4666 
z
M 1791 3794 
L 1791 2491 
L 2456 2491 
Q 2806 2491 2997 2661 
Q 3188 2831 3188 3144 
Q 3188 3456 2997 3625 
Q 2806 3794 2456 3794 
L 1791 3794 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6f" d="M 2203 2784 
Q 1831 2784 1636 2517 
Q 1441 2250 1441 1747 
Q 1441 1244 1636 976 
Q 1831 709 2203 709 
Q 2569 709 2762 976 
Q 2956 1244 2956 1747 
Q 2956 2250 2762 2517 
Q 2569 2784 2203 2784 
z
M 2203 3584 
Q 3106 3584 3614 3096 
Q 4122 2609 4122 1747 
Q 4122 884 3614 396 
Q 3106 -91 2203 -91 
Q 1297 -91 786 396 
Q 275 884 275 1747 
Q 275 2609 786 3096 
Q 1297 3584 2203 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-28" d="M 2413 -844 
L 1484 -844 
Q 1006 -72 778 623 
Q 550 1319 550 2003 
Q 550 2688 779 3389 
Q 1009 4091 1484 4856 
L 2413 4856 
Q 2013 4116 1813 3408 
Q 1613 2700 1613 2009 
Q 1613 1319 1811 609 
Q 2009 -100 2413 -844 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6d" d="M 3781 2919 
Q 3994 3244 4286 3414 
Q 4578 3584 4928 3584 
Q 5531 3584 5847 3212 
Q 6163 2841 6163 2131 
L 6163 0 
L 5038 0 
L 5038 1825 
Q 5041 1866 5042 1909 
Q 5044 1953 5044 2034 
Q 5044 2406 4934 2573 
Q 4825 2741 4581 2741 
Q 4263 2741 4089 2478 
Q 3916 2216 3909 1719 
L 3909 0 
L 2784 0 
L 2784 1825 
Q 2784 2406 2684 2573 
Q 2584 2741 2328 2741 
Q 2006 2741 1831 2477 
Q 1656 2213 1656 1722 
L 1656 0 
L 531 0 
L 531 3500 
L 1656 3500 
L 1656 2988 
Q 1863 3284 2130 3434 
Q 2397 3584 2719 3584 
Q 3081 3584 3359 3409 
Q 3638 3234 3781 2919 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-29" d="M 513 -844 
Q 913 -100 1113 609 
Q 1313 1319 1313 2009 
Q 1313 2700 1113 3408 
Q 913 4116 513 4856 
L 1441 4856 
Q 1916 4091 2145 3389 
Q 2375 2688 2375 2003 
Q 2375 1319 2147 623 
Q 1919 -72 1441 -844 
L 513 -844 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-58" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="573.583984"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="608.398438"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="681.689453"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="750.390625"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="809.912109"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="844.189453"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="891.992188"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="926.269531"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="994.970703"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1066.162109"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1100.976562"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1146.679688"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1250.878906"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_19">
      <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" clip-path="url(#pc6639645e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <defs>
       <path id="m58d4b5b10e" d="M 0 0 
L -3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m58d4b5b10e" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_11">
      <!-- −8 -->
      <g transform="translate(21.845937 318.044547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_21">
      <path d="M 42.113906 280.90325 
L 311.889906 280.90325 
" clip-path="url(#pc6639645e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#m58d4b5b10e" x="42.113906" y="280.90325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_12">
      <!-- −6 -->
      <g transform="translate(21.845937 284.322547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_23">
      <path d="M 42.113906 247.18125 
L 311.889906 247.18125 
" clip-path="url(#pc6639645e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#m58d4b5b10e" x="42.113906" y="247.18125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_13">
      <!-- −4 -->
      <g transform="translate(21.845937 250.600547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_25">
      <path d="M 42.113906 213.45925 
L 311.889906 213.45925 
" clip-path="url(#pc6639645e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#m58d4b5b10e" x="42.113906" y="213.45925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_14">
      <!-- −2 -->
      <g transform="translate(21.845937 216.878547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_27">
      <path d="M 42.113906 179.73725 
L 311.889906 179.73725 
" clip-path="url(#pc6639645e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#m58d4b5b10e" x="42.113906" y="179.73725" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 0 -->
      <g transform="translate(29.387656 183.156547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_29">
      <path d="M 42.113906 146.01525 
L 311.889906 146.01525 
" clip-path="url(#pc6639645e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#m58d4b5b10e" x="42.113906" y="146.01525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 2 -->
      <g transform="translate(29.387656 149.434547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_31">
      <path d="M 42.113906 112.29325 
L 311.889906 112.29325 
" clip-path="url(#pc6639645e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#m58d4b5b10e" x="42.113906" y="112.29325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 4 -->
      <g transform="translate(29.387656 115.712547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_33">
      <path d="M 42.113906 78.57125 
L 311.889906 78.57125 
" clip-path="url(#pc6639645e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#m58d4b5b10e" x="42.113906" y="78.57125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 6 -->
      <g transform="translate(29.387656 81.990547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_35">
      <path d="M 42.113906 44.84925 
L 311.889906 44.84925 
" clip-path="url(#pc6639645e1)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#m58d4b5b10e" x="42.113906" y="44.84925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 8 -->
      <g transform="translate(29.387656 48.268547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_20">
     <!-- Relative Y Position (m) -->
     <g transform="translate(15.558281 250.792094) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-59" d="M -63 4666 
L 1253 4666 
L 2316 3003 
L 3378 4666 
L 4697 4666 
L 2919 1966 
L 2919 0 
L 1716 0 
L 1716 1966 
L -63 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-59" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="568.896484"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="603.710938"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="677.001953"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="745.703125"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="805.224609"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="839.501953"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="887.304688"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="921.582031"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="990.283203"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1061.474609"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1096.289062"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1141.992188"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1246.191406"/>
     </g>
    </g>
   </g>
   <g id="patch_6">
    <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_21">
    <g id="patch_8">
     <path d="M 200.10902 150.124824 
L 225.429957 150.124824 
Q 227.229957 150.124824 227.229957 148.324824 
L 227.229957 139.614511 
Q 227.229957 137.814511 225.429957 137.814511 
L 200.10902 137.814511 
Q 198.30902 137.814511 198.30902 139.614511 
L 198.30902 148.324824 
Q 198.30902 150.124824 200.10902 150.124824 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 3.0m -->
    <g style="fill: #ffffff" transform="translate(200.10902 146.453105) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-33" d="M 2981 2516 
Q 3453 2394 3698 2092 
Q 3944 1791 3944 1325 
Q 3944 631 3412 270 
Q 2881 -91 1863 -91 
Q 1503 -91 1142 -33 
Q 781 25 428 141 
L 428 1069 
Q 766 900 1098 814 
Q 1431 728 1753 728 
Q 2231 728 2486 893 
Q 2741 1059 2741 1369 
Q 2741 1688 2480 1852 
Q 2219 2016 1709 2016 
L 1228 2016 
L 1228 2791 
L 1734 2791 
Q 2188 2791 2409 2933 
Q 2631 3075 2631 3366 
Q 2631 3634 2415 3781 
Q 2200 3928 1806 3928 
Q 1516 3928 1219 3862 
Q 922 3797 628 3669 
L 628 4550 
Q 984 4650 1334 4700 
Q 1684 4750 2022 4750 
Q 2931 4750 3382 4451 
Q 3834 4153 3834 3553 
Q 3834 3144 3618 2883 
Q 3403 2622 2981 2516 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2e" d="M 653 1209 
L 1778 1209 
L 1778 0 
L 653 0 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-30" d="M 2944 2338 
Q 2944 3213 2780 3570 
Q 2616 3928 2228 3928 
Q 1841 3928 1675 3570 
Q 1509 3213 1509 2338 
Q 1509 1453 1675 1090 
Q 1841 728 2228 728 
Q 2613 728 2778 1090 
Q 2944 1453 2944 2338 
z
M 4147 2328 
Q 4147 1169 3647 539 
Q 3147 -91 2228 -91 
Q 1306 -91 806 539 
Q 306 1169 306 2328 
Q 306 3491 806 4120 
Q 1306 4750 2228 4750 
Q 3147 4750 3647 4120 
Q 4147 3491 4147 2328 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-33"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_22">
    <g id="patch_9">
     <path d="M 223.954075 126.279769 
L 249.275012 126.279769 
Q 251.075012 126.279769 251.075012 124.479769 
L 251.075012 115.769457 
Q 251.075012 113.969457 249.275012 113.969457 
L 223.954075 113.969457 
Q 222.154075 113.969457 222.154075 115.769457 
L 222.154075 124.479769 
Q 222.154075 126.279769 223.954075 126.279769 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 5.0m -->
    <g style="fill: #ffffff" transform="translate(223.954075 122.60805) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-35" d="M 678 4666 
L 3669 4666 
L 3669 3781 
L 1638 3781 
L 1638 3059 
Q 1775 3097 1914 3117 
Q 2053 3138 2203 3138 
Q 3056 3138 3531 2711 
Q 4006 2284 4006 1522 
Q 4006 766 3489 337 
Q 2972 -91 2053 -91 
Q 1656 -91 1267 -14 
Q 878 63 494 219 
L 494 1166 
Q 875 947 1217 837 
Q 1559 728 1863 728 
Q 2300 728 2551 942 
Q 2803 1156 2803 1522 
Q 2803 1891 2551 2103 
Q 2300 2316 1863 2316 
Q 1603 2316 1309 2248 
Q 1016 2181 678 2041 
L 678 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-35"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_23">
    <g id="patch_10">
     <path d="M 259.721657 90.512187 
L 285.042595 90.512187 
Q 286.842595 90.512187 286.842595 88.712187 
L 286.842595 80.001874 
Q 286.842595 78.201874 285.042595 78.201874 
L 259.721657 78.201874 
Q 257.921657 78.201874 257.921657 80.001874 
L 257.921657 88.712187 
Q 257.921657 90.512187 259.721657 90.512187 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 8.0m -->
    <g style="fill: #ffffff" transform="translate(259.721657 86.840468) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-38" d="M 2228 2088 
Q 1891 2088 1709 1903 
Q 1528 1719 1528 1375 
Q 1528 1031 1709 848 
Q 1891 666 2228 666 
Q 2563 666 2741 848 
Q 2919 1031 2919 1375 
Q 2919 1722 2741 1905 
Q 2563 2088 2228 2088 
z
M 1350 2484 
Q 925 2613 709 2878 
Q 494 3144 494 3541 
Q 494 4131 934 4440 
Q 1375 4750 2228 4750 
Q 3075 4750 3515 4442 
Q 3956 4134 3956 3541 
Q 3956 3144 3739 2878 
Q 3522 2613 3097 2484 
Q 3572 2353 3814 2058 
Q 4056 1763 4056 1313 
Q 4056 619 3595 264 
Q 3134 -91 2228 -91 
Q 1319 -91 855 264 
Q 391 619 391 1313 
Q 391 1763 633 2058 
Q 875 2353 1350 2484 
z
M 1631 3419 
Q 1631 3141 1786 2991 
Q 1941 2841 2228 2841 
Q 2509 2841 2662 2991 
Q 2816 3141 2816 3419 
Q 2816 3697 2662 3845 
Q 2509 3994 2228 3994 
Q 1941 3994 1786 3844 
Q 1631 3694 1631 3419 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-38"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_24">
    <!-- Far-Distance Cell (Neuron 60) -->
    <g transform="translate(76.933156 16.411875) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-46" d="M 588 4666 
L 3834 4666 
L 3834 3756 
L 1791 3756 
L 1791 2888 
L 3713 2888 
L 3713 1978 
L 1791 1978 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2d" d="M 347 2297 
L 2309 2297 
L 2309 1388 
L 347 1388 
L 347 2297 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-44" d="M 1791 3756 
L 1791 909 
L 2222 909 
Q 2959 909 3348 1275 
Q 3738 1641 3738 2338 
Q 3738 3031 3350 3393 
Q 2963 3756 2222 3756 
L 1791 3756 
z
M 588 4666 
L 1856 4666 
Q 2919 4666 3439 4514 
Q 3959 4363 4331 4000 
Q 4659 3684 4818 3271 
Q 4978 2859 4978 2338 
Q 4978 1809 4818 1395 
Q 4659 981 4331 666 
Q 3956 303 3431 151 
Q 2906 0 1856 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4e" d="M 588 4666 
L 1931 4666 
L 3628 1466 
L 3628 4666 
L 4769 4666 
L 4769 0 
L 3425 0 
L 1728 3200 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-75" d="M 500 1363 
L 500 3500 
L 1625 3500 
L 1625 3150 
Q 1625 2866 1622 2436 
Q 1619 2006 1619 1863 
Q 1619 1441 1641 1255 
Q 1663 1069 1716 984 
Q 1784 875 1895 815 
Q 2006 756 2150 756 
Q 2500 756 2700 1025 
Q 2900 1294 2900 1772 
L 2900 3500 
L 4019 3500 
L 4019 0 
L 2900 0 
L 2900 506 
Q 2647 200 2364 54 
Q 2081 -91 1741 -91 
Q 1134 -91 817 281 
Q 500 653 500 1363 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-36" d="M 2316 2303 
Q 2000 2303 1842 2098 
Q 1684 1894 1684 1484 
Q 1684 1075 1842 870 
Q 2000 666 2316 666 
Q 2634 666 2792 870 
Q 2950 1075 2950 1484 
Q 2950 1894 2792 2098 
Q 2634 2303 2316 2303 
z
M 3803 4544 
L 3803 3681 
Q 3506 3822 3243 3889 
Q 2981 3956 2731 3956 
Q 2194 3956 1894 3657 
Q 1594 3359 1544 2772 
Q 1750 2925 1990 3001 
Q 2231 3078 2516 3078 
Q 3231 3078 3670 2659 
Q 4109 2241 4109 1563 
Q 4109 813 3618 361 
Q 3128 -91 2303 -91 
Q 1394 -91 895 523 
Q 397 1138 397 2266 
Q 397 3422 980 4083 
Q 1563 4744 2578 4744 
Q 2900 4744 3203 4694 
Q 3506 4644 3803 4544 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-46"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="62.435547"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="129.916016"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="179.232422"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="220.736328"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="303.744141"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="338.021484"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="397.542969"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="445.345703"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="512.826172"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="584.017578"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="643.294922"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="711.117188"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="745.931641"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="819.320312"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="887.142578"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="921.419922"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="955.697266"/>
     <use xlink:href="#DejaVuSans-Bold-28" x="990.511719"/>
     <use xlink:href="#DejaVuSans-Bold-4e" x="1036.214844"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1119.90625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1187.728516"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1258.919922"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="1308.236328"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1376.9375"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1448.128906"/>
     <use xlink:href="#DejaVuSans-Bold-36" x="1482.943359"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1552.523438"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="1622.103516"/>
    </g>
    <!-- 2D Activation Map -->
    <g transform="translate(114.950656 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-32" d="M 1844 884 
L 3897 884 
L 3897 0 
L 506 0 
L 506 884 
L 2209 2388 
Q 2438 2594 2547 2791 
Q 2656 2988 2656 3200 
Q 2656 3528 2436 3728 
Q 2216 3928 1850 3928 
Q 1569 3928 1234 3808 
Q 900 3688 519 3450 
L 519 4475 
Q 925 4609 1322 4679 
Q 1719 4750 2100 4750 
Q 2938 4750 3402 4381 
Q 3866 4013 3866 3353 
Q 3866 2972 3669 2642 
Q 3472 2313 2841 1759 
L 1844 884 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-41" d="M 3419 850 
L 1538 850 
L 1241 0 
L 31 0 
L 1759 4666 
L 3194 4666 
L 4922 0 
L 3713 0 
L 3419 850 
z
M 1838 1716 
L 3116 1716 
L 2478 3572 
L 1838 1716 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4d" d="M 588 4666 
L 2119 4666 
L 3181 2169 
L 4250 4666 
L 5778 4666 
L 5778 0 
L 4641 0 
L 4641 3413 
L 3566 897 
L 2803 897 
L 1728 3413 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-32"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="152.587891"/>
     <use xlink:href="#DejaVuSans-Bold-41" x="187.402344"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="264.794922"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="324.072266"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="371.875"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="406.152344"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.337891"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="538.818359"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="586.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="620.898438"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="689.599609"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="760.791016"/>
     <use xlink:href="#DejaVuSans-Bold-4d" x="795.605469"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="895.117188"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="962.597656"/>
    </g>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="m95cd932f46" d="M 0 -5.477226 
L -1.229714 -1.692556 
L -5.209151 -1.692556 
L -1.989719 0.646499 
L -3.219432 4.431169 
L -0 2.092114 
L 3.219432 4.431169 
L 1.989719 0.646499 
L 5.209151 -1.692556 
L 1.229714 -1.692556 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#pc6639645e1)">
     <use xlink:href="#m95cd932f46" x="177.001906" y="179.73725" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_11">
     <path d="M 242.388656 62.99175 
L 307.889906 62.99175 
L 307.889906 48.84925 
L 242.388656 48.84925 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="PathCollection_2">
     <g>
      <use xlink:href="#m95cd932f46" x="253.588656" y="56.028" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
     </g>
    </g>
    <g id="text_25">
     <!-- Observer -->
     <g transform="translate(267.988656 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4f"/>
      <use xlink:href="#DejaVuSans-62" x="78.710938"/>
      <use xlink:href="#DejaVuSans-73" x="142.1875"/>
      <use xlink:href="#DejaVuSans-65" x="194.287109"/>
      <use xlink:href="#DejaVuSans-72" x="255.810547"/>
      <use xlink:href="#DejaVuSans-76" x="296.923828"/>
      <use xlink:href="#DejaVuSans-65" x="356.103516"/>
      <use xlink:href="#DejaVuSans-72" x="417.626953"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_12">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
L 813.937288 44.84925 
L 436.259015 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="m87a4891e59" d="M 440.975868 -37.642188 
L 440.975868 -37.642188 
L 450.409572 -48.009498 
L 459.843277 -47.232141 
L 469.276982 -47.327059 
L 478.710686 -48.674114 
L 488.144391 -46.817735 
L 497.578096 -47.843269 
L 507.011801 -46.664621 
L 516.445505 -55.139703 
L 525.87921 -51.619278 
L 535.312915 -62.453781 
L 544.746619 -54.624814 
L 554.180324 -54.561945 
L 563.614029 -61.041088 
L 573.047733 -76.275991 
L 582.481438 -92.858305 
L 591.915143 -117.12003 
L 601.348847 -93.92973 
L 610.782552 -161.999775 
L 620.216257 -117.707776 
L 629.649962 -178.226837 
L 639.083666 -151.991441 
L 648.517371 -175.710718 
L 657.951076 -229.263485 
L 667.38478 -240.06636 
L 676.818485 -266.303021 
L 686.25219 -264.525097 
L 695.685894 -262.991109 
L 705.119599 -208.233777 
L 714.553304 -294.571711 
L 714.553304 -37.642188 
L 714.553304 -37.642188 
L 705.119599 -37.642188 
L 695.685894 -37.642188 
L 686.25219 -37.642188 
L 676.818485 -37.642188 
L 667.38478 -37.642188 
L 657.951076 -37.642188 
L 648.517371 -37.642188 
L 639.083666 -37.642188 
L 629.649962 -37.642188 
L 620.216257 -37.642188 
L 610.782552 -37.642188 
L 601.348847 -37.642188 
L 591.915143 -37.642188 
L 582.481438 -37.642188 
L 573.047733 -37.642188 
L 563.614029 -37.642188 
L 554.180324 -37.642188 
L 544.746619 -37.642188 
L 535.312915 -37.642188 
L 525.87921 -37.642188 
L 516.445505 -37.642188 
L 507.011801 -37.642188 
L 497.578096 -37.642188 
L 488.144391 -37.642188 
L 478.710686 -37.642188 
L 469.276982 -37.642188 
L 459.843277 -37.642188 
L 450.409572 -37.642188 
L 440.975868 -37.642188 
z
" style="stroke: #33a02c; stroke-opacity: 0.3"/>
    </defs>
    <g clip-path="url(#pedbabf7579)">
     <use xlink:href="#m87a4891e59" x="0" y="352.267438" style="fill: #33a02c; fill-opacity: 0.3; stroke: #33a02c; stroke-opacity: 0.3"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10">
     <g id="line2d_37">
      <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" clip-path="url(#pedbabf7579)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#m25ca827a53" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 0 -->
      <g transform="translate(433.39589 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_39">
      <path d="M 483.468799 314.62525 
L 483.468799 44.84925 
" clip-path="url(#pedbabf7579)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#m25ca827a53" x="483.468799" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 1 -->
      <g transform="translate(480.605674 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_41">
      <path d="M 530.678584 314.62525 
L 530.678584 44.84925 
" clip-path="url(#pedbabf7579)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#m25ca827a53" x="530.678584" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 2 -->
      <g transform="translate(527.815459 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_43">
      <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#pedbabf7579)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#m25ca827a53" x="577.888368" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 3 -->
      <g transform="translate(575.025243 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_45">
      <path d="M 625.098152 314.62525 
L 625.098152 44.84925 
" clip-path="url(#pedbabf7579)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#m25ca827a53" x="625.098152" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 4 -->
      <g transform="translate(622.235027 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_47">
      <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#pedbabf7579)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#m25ca827a53" x="672.307936" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 5 -->
      <g transform="translate(669.444811 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_49">
      <path d="M 719.51772 314.62525 
L 719.51772 44.84925 
" clip-path="url(#pedbabf7579)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#m25ca827a53" x="719.51772" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 6 -->
      <g transform="translate(716.654595 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_51">
      <path d="M 766.727504 314.62525 
L 766.727504 44.84925 
" clip-path="url(#pedbabf7579)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#m25ca827a53" x="766.727504" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 7 -->
      <g transform="translate(763.864379 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-37"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_53">
      <path d="M 813.937288 314.62525 
L 813.937288 44.84925 
" clip-path="url(#pedbabf7579)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#m25ca827a53" x="813.937288" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 8 -->
      <g transform="translate(811.074163 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_35">
     <!-- Inter-Agent Distance (m) -->
     <g transform="translate(547.755261 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-49" d="M 588 4666 
L 1791 4666 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-49"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="37.207031"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="108.398438"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="156.201172"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="224.023438"/>
      <use xlink:href="#DejaVuSans-Bold-2d" x="273.339844"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="314.84375"/>
      <use xlink:href="#DejaVuSans-Bold-67" x="392.236328"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="463.818359"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="531.640625"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="602.832031"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="650.634766"/>
      <use xlink:href="#DejaVuSans-Bold-44" x="685.449219"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="768.457031"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="802.734375"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="862.255859"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="910.058594"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="977.539062"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="1048.730469"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="1108.007812"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1175.830078"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1210.644531"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1256.347656"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1360.546875"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_55">
      <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" clip-path="url(#pedbabf7579)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#m58d4b5b10e" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 0.0 -->
      <g transform="translate(414.946203 318.044547) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_57">
      <path d="M 436.259015 268.956725 
L 813.937288 268.956725 
" clip-path="url(#pedbabf7579)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#m58d4b5b10e" x="436.259015" y="268.956725" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 0.1 -->
      <g transform="translate(414.946203 272.376022) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_59">
      <path d="M 436.259015 223.2882 
L 813.937288 223.2882 
" clip-path="url(#pedbabf7579)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#m58d4b5b10e" x="436.259015" y="223.2882" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 0.2 -->
      <g transform="translate(414.946203 226.707497) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_61">
      <path d="M 436.259015 177.619675 
L 813.937288 177.619675 
" clip-path="url(#pedbabf7579)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#m58d4b5b10e" x="436.259015" y="177.619675" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 0.3 -->
      <g transform="translate(414.946203 181.038972) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-33" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_63">
      <path d="M 436.259015 131.95115 
L 813.937288 131.95115 
" clip-path="url(#pedbabf7579)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#m58d4b5b10e" x="436.259015" y="131.95115" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 0.4 -->
      <g transform="translate(414.946203 135.370446) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_65">
      <path d="M 436.259015 86.282624 
L 813.937288 86.282624 
" clip-path="url(#pedbabf7579)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#m58d4b5b10e" x="436.259015" y="86.282624" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 0.5 -->
      <g transform="translate(414.946203 89.701921) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_42">
     <!-- Neural Activation -->
     <g transform="translate(408.658547 233.746391) rotate(-90) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="line2d_67">
    <path d="M 440.975868 314.62525 
L 450.409572 304.25794 
L 459.843277 305.035296 
L 469.276982 304.940379 
L 478.710686 303.593324 
L 488.144391 305.449703 
L 497.578096 304.424168 
L 507.011801 305.602816 
L 516.445505 297.127735 
L 525.87921 300.64816 
L 535.312915 289.813657 
L 544.746619 297.642624 
L 554.180324 297.705493 
L 563.614029 291.226349 
L 573.047733 275.991446 
L 582.481438 259.409132 
L 591.915143 235.147408 
L 601.348847 258.337707 
L 610.782552 190.267663 
L 620.216257 234.559662 
L 629.649962 174.040601 
L 639.083666 200.275996 
L 648.517371 176.55672 
L 657.951076 123.003952 
L 667.38478 112.201077 
L 676.818485 85.964416 
L 686.25219 87.742341 
L 695.685894 89.276328 
L 705.119599 144.03366 
L 714.553304 57.695726 
" clip-path="url(#pedbabf7579)" style="fill: none; stroke: #33a02c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_68">
    <path d="M 714.553304 314.62525 
L 714.553304 44.84925 
" clip-path="url(#pedbabf7579)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #33a02c; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_69">
    <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#pedbabf7579)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_70">
    <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#pedbabf7579)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="patch_13">
    <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_14">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_43">
    <g id="patch_15">
     <path d="M 700.612124 114.0718 
L 795.053374 114.0718 
Q 798.253374 114.0718 798.253374 110.8718 
L 798.253374 58.33805 
Q 798.253374 55.13805 795.053374 55.13805 
L 700.612124 55.13805 
Q 697.412124 55.13805 697.412124 58.33805 
L 697.412124 110.8718 
Q 697.412124 114.0718 700.612124 114.0718 
z
" style="fill: #ffffff; opacity: 0.9; stroke: #808080; stroke-linejoin: miter"/>
    </g>
    <!-- Peak Distance: 5.9m -->
    <g transform="translate(712.787124 64.4168) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-3a" d="M 750 794 
L 1409 794 
L 1409 0 
L 750 0 
L 750 794 
z
M 750 3309 
L 1409 3309 
L 1409 2516 
L 750 2516 
L 750 3309 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-44" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="346.179688"/>
     <use xlink:href="#DejaVuSans-73" x="373.962891"/>
     <use xlink:href="#DejaVuSans-74" x="426.0625"/>
     <use xlink:href="#DejaVuSans-61" x="465.271484"/>
     <use xlink:href="#DejaVuSans-6e" x="526.550781"/>
     <use xlink:href="#DejaVuSans-63" x="589.929688"/>
     <use xlink:href="#DejaVuSans-65" x="644.910156"/>
     <use xlink:href="#DejaVuSans-3a" x="706.433594"/>
     <use xlink:href="#DejaVuSans-20" x="740.125"/>
     <use xlink:href="#DejaVuSans-35" x="771.912109"/>
     <use xlink:href="#DejaVuSans-2e" x="835.535156"/>
     <use xlink:href="#DejaVuSans-39" x="867.322266"/>
     <use xlink:href="#DejaVuSans-6d" x="930.945312"/>
    </g>
    <!-- Max Activation: 0.563 -->
    <g transform="translate(707.779624 73.37505) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-4d"/>
     <use xlink:href="#DejaVuSans-61" x="86.279297"/>
     <use xlink:href="#DejaVuSans-78" x="147.558594"/>
     <use xlink:href="#DejaVuSans-20" x="206.738281"/>
     <use xlink:href="#DejaVuSans-41" x="238.525391"/>
     <use xlink:href="#DejaVuSans-63" x="305.183594"/>
     <use xlink:href="#DejaVuSans-74" x="360.164062"/>
     <use xlink:href="#DejaVuSans-69" x="399.373047"/>
     <use xlink:href="#DejaVuSans-76" x="427.15625"/>
     <use xlink:href="#DejaVuSans-61" x="486.335938"/>
     <use xlink:href="#DejaVuSans-74" x="547.615234"/>
     <use xlink:href="#DejaVuSans-69" x="586.824219"/>
     <use xlink:href="#DejaVuSans-6f" x="614.607422"/>
     <use xlink:href="#DejaVuSans-6e" x="675.789062"/>
     <use xlink:href="#DejaVuSans-3a" x="739.167969"/>
     <use xlink:href="#DejaVuSans-20" x="772.859375"/>
     <use xlink:href="#DejaVuSans-30" x="804.646484"/>
     <use xlink:href="#DejaVuSans-2e" x="868.269531"/>
     <use xlink:href="#DejaVuSans-35" x="900.056641"/>
     <use xlink:href="#DejaVuSans-36" x="963.679688"/>
     <use xlink:href="#DejaVuSans-33" x="1027.302734"/>
    </g>
    <!-- Sparsity: 0.514 -->
    <g transform="translate(734.883374 82.3333) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-70" x="63.476562"/>
     <use xlink:href="#DejaVuSans-61" x="126.953125"/>
     <use xlink:href="#DejaVuSans-72" x="188.232422"/>
     <use xlink:href="#DejaVuSans-73" x="229.345703"/>
     <use xlink:href="#DejaVuSans-69" x="281.445312"/>
     <use xlink:href="#DejaVuSans-74" x="309.228516"/>
     <use xlink:href="#DejaVuSans-79" x="348.4375"/>
     <use xlink:href="#DejaVuSans-3a" x="400.367188"/>
     <use xlink:href="#DejaVuSans-20" x="434.058594"/>
     <use xlink:href="#DejaVuSans-30" x="465.845703"/>
     <use xlink:href="#DejaVuSans-2e" x="529.46875"/>
     <use xlink:href="#DejaVuSans-35" x="561.255859"/>
     <use xlink:href="#DejaVuSans-31" x="624.878906"/>
     <use xlink:href="#DejaVuSans-34" x="688.501953"/>
    </g>
    <!-- Peak Significance: 2.95 -->
    <g transform="translate(702.065874 91.29155) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-53" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="332.654297"/>
     <use xlink:href="#DejaVuSans-67" x="360.4375"/>
     <use xlink:href="#DejaVuSans-6e" x="423.914062"/>
     <use xlink:href="#DejaVuSans-69" x="487.292969"/>
     <use xlink:href="#DejaVuSans-66" x="515.076172"/>
     <use xlink:href="#DejaVuSans-69" x="550.28125"/>
     <use xlink:href="#DejaVuSans-63" x="578.064453"/>
     <use xlink:href="#DejaVuSans-61" x="633.044922"/>
     <use xlink:href="#DejaVuSans-6e" x="694.324219"/>
     <use xlink:href="#DejaVuSans-63" x="757.703125"/>
     <use xlink:href="#DejaVuSans-65" x="812.683594"/>
     <use xlink:href="#DejaVuSans-3a" x="874.207031"/>
     <use xlink:href="#DejaVuSans-20" x="907.898438"/>
     <use xlink:href="#DejaVuSans-32" x="939.685547"/>
     <use xlink:href="#DejaVuSans-2e" x="1003.308594"/>
     <use xlink:href="#DejaVuSans-39" x="1035.095703"/>
     <use xlink:href="#DejaVuSans-35" x="1098.71875"/>
    </g>
    <!-- Selectivity Index: 0.677 -->
    <g transform="translate(700.612124 100.2498) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-65" x="63.476562"/>
     <use xlink:href="#DejaVuSans-6c" x="125"/>
     <use xlink:href="#DejaVuSans-65" x="152.783203"/>
     <use xlink:href="#DejaVuSans-63" x="214.306641"/>
     <use xlink:href="#DejaVuSans-74" x="269.287109"/>
     <use xlink:href="#DejaVuSans-69" x="308.496094"/>
     <use xlink:href="#DejaVuSans-76" x="336.279297"/>
     <use xlink:href="#DejaVuSans-69" x="395.458984"/>
     <use xlink:href="#DejaVuSans-74" x="423.242188"/>
     <use xlink:href="#DejaVuSans-79" x="462.451172"/>
     <use xlink:href="#DejaVuSans-20" x="521.630859"/>
     <use xlink:href="#DejaVuSans-49" x="553.417969"/>
     <use xlink:href="#DejaVuSans-6e" x="582.910156"/>
     <use xlink:href="#DejaVuSans-64" x="646.289062"/>
     <use xlink:href="#DejaVuSans-65" x="709.765625"/>
     <use xlink:href="#DejaVuSans-78" x="769.539062"/>
     <use xlink:href="#DejaVuSans-3a" x="828.71875"/>
     <use xlink:href="#DejaVuSans-20" x="862.410156"/>
     <use xlink:href="#DejaVuSans-30" x="894.197266"/>
     <use xlink:href="#DejaVuSans-2e" x="957.820312"/>
     <use xlink:href="#DejaVuSans-36" x="989.607422"/>
     <use xlink:href="#DejaVuSans-37" x="1053.230469"/>
     <use xlink:href="#DejaVuSans-37" x="1116.853516"/>
    </g>
    <!-- Peak Width: 1.8m -->
    <g transform="translate(724.529624 109.20805) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-57" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="365.804688"/>
     <use xlink:href="#DejaVuSans-64" x="393.587891"/>
     <use xlink:href="#DejaVuSans-74" x="457.064453"/>
     <use xlink:href="#DejaVuSans-68" x="496.273438"/>
     <use xlink:href="#DejaVuSans-3a" x="559.652344"/>
     <use xlink:href="#DejaVuSans-20" x="593.34375"/>
     <use xlink:href="#DejaVuSans-31" x="625.130859"/>
     <use xlink:href="#DejaVuSans-2e" x="688.753906"/>
     <use xlink:href="#DejaVuSans-38" x="720.541016"/>
     <use xlink:href="#DejaVuSans-6d" x="784.164062"/>
    </g>
   </g>
   <g id="text_44">
    <!-- Distance Tuning Curve -->
    <g transform="translate(549.284402 16.318125) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-54" d="M 31 4666 
L 4331 4666 
L 4331 3756 
L 2784 3756 
L 2784 0 
L 1581 0 
L 1581 3756 
L 31 3756 
L 31 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-44"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
     <use xlink:href="#DejaVuSans-Bold-54" x="525.195312"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="582.408203"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="653.599609"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="724.791016"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="759.068359"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="830.259766"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="901.841797"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="936.65625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1010.044922"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1081.236328"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="1130.552734"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1195.738281"/>
    </g>
    <!-- Peak: 5.9m, Sparsity: 0.514 -->
    <g transform="translate(531.359402 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-6b" d="M 538 4863 
L 1656 4863 
L 1656 2216 
L 2944 3500 
L 4244 3500 
L 2534 1894 
L 4378 0 
L 3022 0 
L 1656 1459 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-3a" d="M 716 3500 
L 1844 3500 
L 1844 2291 
L 716 2291 
L 716 3500 
z
M 716 1209 
L 1844 1209 
L 1844 0 
L 716 0 
L 716 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-39" d="M 641 103 
L 641 966 
Q 928 831 1190 764 
Q 1453 697 1709 697 
Q 2247 697 2547 995 
Q 2847 1294 2900 1881 
Q 2688 1725 2447 1647 
Q 2206 1569 1925 1569 
Q 1209 1569 770 1986 
Q 331 2403 331 3084 
Q 331 3838 820 4291 
Q 1309 4744 2131 4744 
Q 3044 4744 3544 4128 
Q 4044 3513 4044 2388 
Q 4044 1231 3459 570 
Q 2875 -91 1856 -91 
Q 1528 -91 1228 -42 
Q 928 6 641 103 
z
M 2125 2350 
Q 2441 2350 2600 2554 
Q 2759 2759 2759 3169 
Q 2759 3575 2600 3781 
Q 2441 3988 2125 3988 
Q 1809 3988 1650 3781 
Q 1491 3575 1491 3169 
Q 1491 2759 1650 2554 
Q 1809 2350 2125 2350 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2c" d="M 653 1209 
L 1778 1209 
L 1778 256 
L 1006 -909 
L 341 -909 
L 653 256 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-53" d="M 3834 4519 
L 3834 3531 
Q 3450 3703 3084 3790 
Q 2719 3878 2394 3878 
Q 1963 3878 1756 3759 
Q 1550 3641 1550 3391 
Q 1550 3203 1689 3098 
Q 1828 2994 2194 2919 
L 2706 2816 
Q 3484 2659 3812 2340 
Q 4141 2022 4141 1434 
Q 4141 663 3683 286 
Q 3225 -91 2284 -91 
Q 1841 -91 1394 -6 
Q 947 78 500 244 
L 500 1259 
Q 947 1022 1364 901 
Q 1781 781 2169 781 
Q 2563 781 2772 912 
Q 2981 1044 2981 1288 
Q 2981 1506 2839 1625 
Q 2697 1744 2272 1838 
L 1806 1941 
Q 1106 2091 782 2419 
Q 459 2747 459 3303 
Q 459 4000 909 4375 
Q 1359 4750 2203 4750 
Q 2588 4750 2994 4692 
Q 3400 4634 3834 4519 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-79" d="M 78 3500 
L 1197 3500 
L 2138 1125 
L 2938 3500 
L 4056 3500 
L 2584 -331 
Q 2363 -916 2067 -1148 
Q 1772 -1381 1288 -1381 
L 641 -1381 
L 641 -647 
L 991 -647 
Q 1275 -647 1404 -556 
Q 1534 -466 1606 -231 
L 1638 -134 
L 78 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-31" d="M 750 831 
L 1813 831 
L 1813 3847 
L 722 3622 
L 722 4441 
L 1806 4666 
L 2950 4666 
L 2950 831 
L 4013 831 
L 4013 0 
L 750 0 
L 750 831 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-34" d="M 2356 3675 
L 1038 1722 
L 2356 1722 
L 2356 3675 
z
M 2156 4666 
L 3494 4666 
L 3494 1722 
L 4159 1722 
L 4159 850 
L 3494 850 
L 3494 0 
L 2356 0 
L 2356 850 
L 288 850 
L 288 1881 
L 2156 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-50"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="73.291016"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="141.113281"/>
     <use xlink:href="#DejaVuSans-Bold-6b" x="208.59375"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="275.097656"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="315.087891"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="349.902344"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="419.482422"/>
     <use xlink:href="#DejaVuSans-Bold-39" x="457.470703"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="527.050781"/>
     <use xlink:href="#DejaVuSans-Bold-2c" x="631.25"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="669.238281"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="704.052734"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="776.074219"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="847.65625"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="915.136719"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="964.453125"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1023.974609"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="1058.251953"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="1106.054688"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="1171.240234"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1211.230469"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1246.044922"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="1315.625"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="1353.613281"/>
     <use xlink:href="#DejaVuSans-Bold-31" x="1423.193359"/>
     <use xlink:href="#DejaVuSans-Bold-34" x="1492.773438"/>
    </g>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="m77f11d09ef" d="M 0 5 
C 1.326016 5 2.597899 4.473168 3.535534 3.535534 
C 4.473168 2.597899 5 1.326016 5 0 
C 5 -1.326016 4.473168 -2.597899 3.535534 -3.535534 
C 2.597899 -4.473168 1.326016 -5 0 -5 
C -1.326016 -5 -2.597899 -4.473168 -3.535534 -3.535534 
C -4.473168 -2.597899 -5 -1.326016 -5 0 
C -5 1.326016 -4.473168 2.597899 -3.535534 3.535534 
C -2.597899 4.473168 -1.326016 5 0 5 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#pedbabf7579)">
     <use xlink:href="#m77f11d09ef" x="714.553304" y="57.695726" style="fill: #33a02c; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="patch_16">
     <path d="M 441.859015 86.47675 
L 558.766515 86.47675 
Q 560.366515 86.47675 560.366515 84.87675 
L 560.366515 50.44925 
Q 560.366515 48.84925 558.766515 48.84925 
L 441.859015 48.84925 
Q 440.259015 48.84925 440.259015 50.44925 
L 440.259015 84.87675 
Q 440.259015 86.47675 441.859015 86.47675 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_71">
     <path d="M 443.459015 55.328 
L 451.459015 55.328 
L 459.459015 55.328 
" style="fill: none; stroke: #33a02c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
    </g>
    <g id="text_45">
     <!-- Tuning Curve -->
     <g transform="translate(465.859015 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-54"/>
      <use xlink:href="#DejaVuSans-75" x="45.958984"/>
      <use xlink:href="#DejaVuSans-6e" x="109.337891"/>
      <use xlink:href="#DejaVuSans-69" x="172.716797"/>
      <use xlink:href="#DejaVuSans-6e" x="200.5"/>
      <use xlink:href="#DejaVuSans-67" x="263.878906"/>
      <use xlink:href="#DejaVuSans-20" x="327.355469"/>
      <use xlink:href="#DejaVuSans-43" x="359.142578"/>
      <use xlink:href="#DejaVuSans-75" x="428.966797"/>
      <use xlink:href="#DejaVuSans-72" x="492.345703"/>
      <use xlink:href="#DejaVuSans-76" x="533.458984"/>
      <use xlink:href="#DejaVuSans-65" x="592.638672"/>
     </g>
    </g>
    <g id="line2d_72">
     <path d="M 443.459015 67.0705 
L 451.459015 67.0705 
L 459.459015 67.0705 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_46">
     <!-- Close threshold (3.0m) -->
     <g transform="translate(465.859015 69.8705) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6c" x="69.824219"/>
      <use xlink:href="#DejaVuSans-6f" x="97.607422"/>
      <use xlink:href="#DejaVuSans-73" x="158.789062"/>
      <use xlink:href="#DejaVuSans-65" x="210.888672"/>
      <use xlink:href="#DejaVuSans-20" x="272.412109"/>
      <use xlink:href="#DejaVuSans-74" x="304.199219"/>
      <use xlink:href="#DejaVuSans-68" x="343.408203"/>
      <use xlink:href="#DejaVuSans-72" x="406.787109"/>
      <use xlink:href="#DejaVuSans-65" x="445.650391"/>
      <use xlink:href="#DejaVuSans-73" x="507.173828"/>
      <use xlink:href="#DejaVuSans-68" x="559.273438"/>
      <use xlink:href="#DejaVuSans-6f" x="622.652344"/>
      <use xlink:href="#DejaVuSans-6c" x="683.833984"/>
      <use xlink:href="#DejaVuSans-64" x="711.617188"/>
      <use xlink:href="#DejaVuSans-20" x="775.09375"/>
      <use xlink:href="#DejaVuSans-28" x="806.880859"/>
      <use xlink:href="#DejaVuSans-33" x="845.894531"/>
      <use xlink:href="#DejaVuSans-2e" x="909.517578"/>
      <use xlink:href="#DejaVuSans-30" x="941.304688"/>
      <use xlink:href="#DejaVuSans-6d" x="1004.927734"/>
      <use xlink:href="#DejaVuSans-29" x="1102.339844"/>
     </g>
    </g>
    <g id="line2d_73">
     <path d="M 443.459015 78.813 
L 451.459015 78.813 
L 459.459015 78.813 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_47">
     <!-- Far threshold (5.0m) -->
     <g transform="translate(465.859015 81.613) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" x="48.394531"/>
      <use xlink:href="#DejaVuSans-72" x="109.673828"/>
      <use xlink:href="#DejaVuSans-20" x="150.787109"/>
      <use xlink:href="#DejaVuSans-74" x="182.574219"/>
      <use xlink:href="#DejaVuSans-68" x="221.783203"/>
      <use xlink:href="#DejaVuSans-72" x="285.162109"/>
      <use xlink:href="#DejaVuSans-65" x="324.025391"/>
      <use xlink:href="#DejaVuSans-73" x="385.548828"/>
      <use xlink:href="#DejaVuSans-68" x="437.648438"/>
      <use xlink:href="#DejaVuSans-6f" x="501.027344"/>
      <use xlink:href="#DejaVuSans-6c" x="562.208984"/>
      <use xlink:href="#DejaVuSans-64" x="589.992188"/>
      <use xlink:href="#DejaVuSans-20" x="653.46875"/>
      <use xlink:href="#DejaVuSans-28" x="685.255859"/>
      <use xlink:href="#DejaVuSans-35" x="724.269531"/>
      <use xlink:href="#DejaVuSans-2e" x="787.892578"/>
      <use xlink:href="#DejaVuSans-30" x="819.679688"/>
      <use xlink:href="#DejaVuSans-6d" x="883.302734"/>
      <use xlink:href="#DejaVuSans-29" x="980.714844"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_17">
    <path d="M 330.77382 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 330.77382 71.82685 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAABcAAAHBCAYAAACCOiAmAAACGUlEQVR4nO2b0W0bURDEnqRz3+k7VpwCjvkcAQzIAhbEzuzpLMiPc379nBHXOddq9nnOJp9zrnO+ZsPX5u0ch7eWO1URqYqIuope811bWgtSFZGqiFRFpJ0jvVog6ip6zXue3/Ee0eOcn9mXOVPzhv9nw6/z2A0fm7+Ww3cPxfVadg/FzJmqiJgD9ZpXRcAcqNe8KgLmQL3mVREwB+o1r4qAOVCveVUEzIF6zYdVHJsPe24ONHPAe0Q9z5HMkal5a0EyR3oqIuZAM//0cO9aMkd6KiLmQDPn4bNfWs3X8p4Nzxy5nq/v3fDry7qWlzZQs/myit61PLXmJ/M7a/PlEYnXkvmdqogUKJL5v4bver5dS0dEeM2rIuIN1GteFRFvoF5z9Vo6ojtVEamKSFX8/HDvWjoiZHpE6kC95lURhpsDzfxOR4R0RIjavCrCcHOgXvOqCMPNgWZ+x3tEfSWCZI5URUQdaOY4vPeWO95Ax+bvZRW/tVV8/5kNHwe6W7nY/Hrsar7+rxxtoJnz8KoIZI5M22JeS+aAt4renRco4g3Ua16gSOZIVUQKFPF+EpnX4jXv/D893LuWqohURaRAEa+5eS1VEfCaFyiSOdKfLUjmiLeK3p0XKOIN1GteoEjmSFVErt/aQIe/hVzvfPmWK16L1nwovt65dXiBIu2ch7cWoioiVRGpiojXvECRjgipikhVRLw7967lLyPgue9DbEF4AAAAAElFTkSuQmCC" id="imagec4dcf4f4f7" transform="scale(1 -1) translate(0 -215.52)" x="330.72" y="-71.52" width="11.04" height="215.52"/>
   <g id="matplotlib.axis_5"/>
   <g id="matplotlib.axis_6">
    <g id="ytick_16">
     <g id="line2d_74">
      <defs>
       <path id="m3db65e0591" d="M 0 0 
L 3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m3db65e0591" x="341.56486" y="287.64765" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_48">
      <!-- 0.0 -->
      <g transform="translate(348.56486 291.066947) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_75">
      <g>
       <use xlink:href="#m3db65e0591" x="341.56486" y="244.48349" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_49">
      <!-- 0.2 -->
      <g transform="translate(348.56486 247.902787) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_76">
      <g>
       <use xlink:href="#m3db65e0591" x="341.56486" y="201.31933" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_50">
      <!-- 0.4 -->
      <g transform="translate(348.56486 204.738627) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_77">
      <g>
       <use xlink:href="#m3db65e0591" x="341.56486" y="158.15517" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_51">
      <!-- 0.6 -->
      <g transform="translate(348.56486 161.574467) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_78">
      <g>
       <use xlink:href="#m3db65e0591" x="341.56486" y="114.99101" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_52">
      <!-- 0.8 -->
      <g transform="translate(348.56486 118.410307) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-38" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_79">
      <g>
       <use xlink:href="#m3db65e0591" x="341.56486" y="71.82685" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_53">
      <!-- 1.0 -->
      <g transform="translate(348.56486 75.246147) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_54">
     <!-- Neural Activation -->
     <g transform="translate(369.519391 125.728109) rotate(-270) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_18">
    <path d="M 330.77382 287.64765 
L 336.16934 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 336.16934 71.82685 
L 330.77382 71.82685 
L 330.77382 287.64765 
z
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="pc6639645e1">
   <rect x="42.113906" y="44.84925" width="269.776" height="269.776"/>
  </clipPath>
  <clipPath id="pedbabf7579">
   <rect x="436.259015" y="44.84925" width="377.678273" height="269.776"/>
  </clipPath>
 </defs>
</svg>
