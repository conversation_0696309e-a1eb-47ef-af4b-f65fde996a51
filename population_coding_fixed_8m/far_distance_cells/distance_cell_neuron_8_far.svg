<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="824.000413pt" height="352.267438pt" viewBox="0 0 824.000413 352.267438" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:56:30.792326</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 352.267438 
L 824.000413 352.267438 
L 824.000413 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
L 311.889906 44.84925 
L 42.113906 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g clip-path="url(#p347f4f76ea)">
    <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAjMAAAIzCAYAAAD1UpjCAADHx0lEQVR4nO3963riWrJujb5OCYSxXZlVNZ651t3uG676Zo5lJ+Yg2fuHFFIoFP0gwDaYt+VD6iwEBqkREb3rDvj/vYMQQggh5Er58dUHQAghhBByCpQZQgghhFw1lBlCCCGEXDWUGUIIIYRcNZQZQgghhFw1lBlCCCGEXDWUGUIIIYRcNZQZQgghhFw1lBlCCCGEXDWUGUIIIYRcNZQZQgghhFw1lBlCCCGEXDWUGUIIIYRcNZQZQgghhFw1lBlCCCGEXDWUGUIIIYRcNZQZQgghhFw1lBlCCCGEXDWUGUIIIYRcNZQZQgghhFw1lBlCCCGEXDWUGUIIIYRcNZQZQgghhFw1lBlCCCGEXDWUGUIIIYRcNZQZQgghhFw1lBlCCCGEXDWUGUIIIYRcNZQZQgghhFw1lBlCCCGEXDWUGUIIIYRcNZQZQgghhFw1lBlCCCGEXDWUGUIIIYRcNZQZQgghhFw1lBlCCCGEXDWUGUIIIYRcNZQZQgghhFw1lBlCCCGEXDWUGUIIIYRcNZQZQgghhFw15VcfACGEzGORWH74lKMghFwOlBlCyIWRkpVzbU/pIeS7QJkhhHwxx8qLd/qqj3xeig0h1wxlhhDyReRKzJzTVGzdmOjYY6HcEHJNUGYIIZ/EueXF219MQux+c+WGYkPIpUOZIYR8IHNSSKnTUc6+Qut4QiLPl0pNUWwIuXQoM4SQMzO3BiZ2GkrtS297bKQlV2r0fig1hFwSlBlCyJk4ppA3dAoK7St2ysotCA4JCaWGkGuFMkMIOYFTmlF7px9vf7mRm1gqSdCiskhsQ6kh5FqgzBBCjuDcEuPtc27UJqdFUomp0MTWBfKlhkJDyFdBmSGERDhn6ihn33bbXMFJHUdORMYi68xNX4X2Qwj5KCgzhMwm5wJ/jRewc7Y8yt3vXHlZRJYB4SiKFRgbocklJjih52drKEI+GsoMISNO7Ur/HPv5jAveOVsczd3nHIEJyUtOqskr8M0VmAP8eptU3zap1lWssSHkI6DMEHI2gTkXl3I8pzSZTm1v9zFXYEozL7fm5djalpDc2OPyxCYkNRQaQs4FZYbcKB8RmbAck8a4BOYW3sa2SW3ryUquwOjxWEQmJDwhobDiYiMyXqrJik2O1FBoCDkXlBlyY3zE/YCO3celyU5uU+nQurH1Q9umojAxgQkdQ0oeTpGIlNikpIZCQ8hHQJkhN8Q5UiPn5NibIn7GMcyRmGM6uNPbecJi54XWjdWonKM+RctLLGITkxoKDSEfDWWG3AhzusU/ZvsQx16kcr6ac4Xn2CbT5+7czls/JCspifEiNrGefc8lDlpsQlJDoSHks6DMkBvgWJE5RyHu3Ds7z+Ejvr5zmkaHjmHO+32MxBybZrLHKEISkpxQVCY1P0doYsdECJkLZYZ8Q44pVJ0baTiFY/s48Tjm4jdX7mJ9uxz7vp0qMTGh8VoZ6XVr+CJjp62wzBET75hCAqSh0BByDJQZcuUcE3XJ7dcktH3sOFIXotCFVjPnYjan87bY+ppceckRm9RznyIxdpvUe6vXu0f7PtfOMnusOS2UUlEeqPVt9EbvJ/R8hJAYlBlyhZwqMMf2aZJzPCm5yEk7zenZNnVMBzOdwzGd1s0Vm9C+PanxJCZWMxMiJCy1s0xkJ7S+bRl1TLSNdTSEnAvKDLkCTmmFNLfvklS/J6lj0xdBjXehC3WNn3Pfn9yL3Nyv+CnS503HSBX1ekKj5+npHGItj1LyEpq2YpNTg2NbRcWiNISQHCgz5ELJvSgeE4WZcxFNHUtOGutg5sU6XbPbh6Ql1iT5FOYIzJzolcecv4cWGi9ak4sWl5jc5E7b+hs57pRs6uWx9RmdISQHygy5MM4lMXqduRfN0Lzc49BykdubbEhgcqTlnDUWsahVzvLUKSVWkxKTGJlvozRzhcYTk2PkRgtMianghPA+D8C0jobRGULmQJkhF8JHSIxe5l34QhITqtfIORZgfCGKpZBiFzZZrklFbM6RpogJTCoFN7dOJvR83vvvSUwJ4C7jOb3nj4mKyM09BnGx6+h0kic1+vns3ypUSBwSmpxIHSG3DWWGXADH1sTkFqF6BaR2fE6haeyYgLDMpJrnhiIDdp/eunbZsRe9WAopR2rmXnhzomZnEJmJZ8i2OXJjj01HZWLpoZwIS+79ovRybztCbhvKDPliUiIT+ojmRGNkvhdtkfFQJCYkPTnH5slMbgdssk2uvIQumMcKTUouctex094x5uzT/k3uMZGY2NPq6TowBBCXG0905P21UZmU5ITkxfs7xmToGKlh/Q35vlBmyBfxGRIj8zwxSUmMJzU5x6D7HQHirWT0vNh8vb9YWkqvl9qfxxxhiUVkPLwLaSz640XIAiJTqnG7Oz0ekpmk3CwAvGP89xNxmSMIthBcntQKzZx95qZnY+tTcMj1Q5khn0xuhCO1XuxCaOel6i7scO3M944zlOZ474ZWbFLT3jI7H2da7q2Xeh/1tDfP/s1yokjetp4sOp+HEmmZsfO8QIs+JD1vIjdatGxURua9mvmvah9rxMUh1ivxRxITIooOuQ4oM+QTCJ0sQzUu3vJU4ag3nSsw92pajytZCf3qt9M12u3q7lhkOCFXePQ8OMty18uVG2CeKNpt9MU3NxUSEplQes85lNgwFKCKjSejN/LZslIDTAUk9d7bQnDZZhFYZokVnKdIidLcGihCvgbKDPlAciRmbtpIz7fRF70sJC2hCIwsuxt2XQJYYXxBzEll2F/33q/9+s6Ijn0f3tW4bQEVmx9a125378wXUu+7HveWH1vPofGM0cilJyze3yr0N7PX8dw0VA1gq4/byoxOE0l0Rt5voP3cbcyT26gMMBaaGKecxkPbhuqwAEoNuUQoM+SDmCsyuemLUARG5nnpJREVibrYeY7ArMy4PUTvQinUCItMluiglZ2eWAoGmEZ5gHRqS+bZHotz64Nif7u5LXS8/ceOwaxuRSY0bfGu1zkio+t8t8CQgpLPm6SaoIZWaGIRJ1ubM6fOZW4NTapTxnMWlxPycVBmyAeQEpm5EhOb9qRGR19EYPTQkZgVfInxZCb2yz8lMqllcMZhxifzbEscPR5LZ9n+VHJEIieqZus+PPQFO0ZguZWU0jxC8/TTpyQnJTTbbh9bqL9DqL4KGEtOjWmHifY9idXMzJUW2a/dV06dkz42vR2FhlwOlBlyZk4p3D1FYmIys1bjEYmJPVIiY9NMxzzs9nCG3rzo0IrOAmPBSZ0CUn9P/Rx2u3eEL8ZznzNSv5R6eIecSj2l3v+ts3yUerLoP6pIjU0nCbpO5hhhiP1NPavThFJJMRGi1JCvhzJDPpjQRyxHXE6RGF0XIxKzGFazIvOIqcDoefbXPpwhcJrMhORm7jA0r78Wec2OY8ztZddu511A50QWnM9QLAITk5qMXSdFRhxQojIy1Msn0Rb7BDWmOw4V257jNJ2zb308nqjEokQUGvK1UGbIGUkVcc4Vl5TE6PW84QLAE0bRmFDk5dEZtzKTIzQ6BeEJSmj+KTKTO56UmxMJZST69EtKmjIjON7fQU/nCk3s7JeKhIm8aJHRy0ZCIwsk4hLLfXkHNlcUUqKYEhsrNRQacvlQZsgnkSsyscLeWATGSoyTVopFYKzAPJplnrzY8ZS4xETGWwZn3A7niExMajI9wj1jeJLg7vPOrByqzxCcCE7o7xCK0sxtjSZPq4d2no3M6H3ItCs0egexNz3WueAxxMJPnthYqaHQkMuHMkPORKqoV+bnSkxu9MWub+chLwJjx2MyE4vQaDHRgpISm5DowBm3w9woTOphSQlOztkjuo/QxU9vZPpZEZGwwxxiQmPn6f3acfl7rTBGUk4rNV3LDuWhm8R/xun3WBE65pYbcNYj5HOgzJATiBWHhgp9Y4ISmp8rMKa7+xLx9JEnMPYhy73sQAmg7FInZXdi3y6mghIapoQnJTOxCEtMWmLihMh4LtFtJDoTWilkVYvxpBUaOOMpvChPaD3vOay8QM1fQb2vdxhajNUYtyD7CEIvRMujl1JKYaXGC2d5z0XIx0OZIUcQ+rXnpZD0Nlo87LxcifFuNWCORzevDomLnfcLEak5AGWDH2UDACjKBkVZoxxNt+P77RK7bYWmLvC2XQJ1CWzvBonw5CU0hBnGojKxefY5Vxg/T0xoThWbWdvZiIwg9TZ301WtZNhoSihyE4uueXhRGsGTGh256fuikabwi25Hut+Zc5D6XupxeeOs4OT8sURqcuptBIoN+VgoM2Qmc0UmVecSk5f7wHqmt17Zva6PCKWScqIxvdwcsHh8xfpxg2W1BwAUqFGiQYFOZND082oUaB5K7B+W2KFC0xRTuZHIjRaXlMzMicp4y/R+V2bcCo19Hr2f2A9xPT/3mjjZUEh09+8JTEpeoLYJjXupJm97eY5UmknW6/flpZnOdQr29hNLMeVGTmL3i5rb4smuS8j5oMyQGZwiMjIMiYy91cC9WSdwzyQdhbFCM0deRtGZdyx+PWP9uMF9tcETnnuBGSSmnswDgD06kUGBXbHE/qHC7mGJBiX2uyV22yX22wpvdQFsKz9qowUDzriel4rMaKHR+7cCENpP6Pm9a1ZMKLLlxt52YeGMB55vrtgAfvpQL/OQ1xJLM1mpqYGhVVethnqHx+B9J2PpX/18IidzozO2Mz+vnkY/j3dslBpyXigzJJPUCdITGSs1slwkJSQwOp3kSIyOvsSGOQJjRObHrz9YP27w+PCMNV7xhGesscESexTdyVlLjEiNsEPVC80ey1ZqZF5VYV8tsftZYb9boqkL7LYVDttlKzb13TQVhYzxnIeVGXl40ZmYIIXqRzxOuUb3G+teclWqyR5LSqS8KIwnMjlnxJw0k37uUf2MiIO9jYR8F+a8aanvZGgdLyqT02LJduYXS1mF9mGPi1JDzgNlhhxJrBDBq4uRcR1t8aIykdsOANNbDpSJeb+QiMKgTymtfj3j6ecL1mijMU94xj2mkRkrMTo6o0VmhyX2qPrhaJmIzUPVR2yauhyLzbmEZmseWgZl2ktthR45QqMFInl9tuKip72Lp1lNP483nkss62MDG7kRmZUavsiKEpXxbjgJ5NXS5KSVvB8bgJ86ihH6O3jRnDlC461PyHFQZkgGsZOkXW7HrdTYKIy9CaQddqQEJjT9C77AmGjM069nPFWDvDzhpRcaHZmxAmPFZoO1EZqx2GywRoPCFxuMxaapizYdVRfte1CX6G9AeUxUxnvoyERqH1DrxSQGZnnweha7iFmx0U/sRGe8cZhxSyxCY9fzpq0YwszXIiPDLTCuPQmlbmNCkxsljb2AXJERPImxLZsoNOTroMyQE/FaLtmTdKjw1xMZXSuD8cUiJCwhqXEjMHrYFvg+/XrGU/Hcy4s8Hvvxl0mNjDfdoMQTnntJsWKzwRprbIKis8MS+6rCrlpi31QAgKYu0HQyU9dFLzkABtERyRHpSMnMC/yLuF7fbq9TJd61yUqDJxRZGRS9ktRzyHznIh6SGO+YNN713ZMZ+7GW/aaiMno9PezfB4nOxN6U3IO3adzUMvuctrA3RKg5trd9Tmsn7zgpNeQ4KDMkQm7YOhR9sS2ZrLSIyMy4EaQ3L7QsKDEYpZWsxHhCU2EfLP7VMiNy8oo1ltiPpGaJPTa47+cPwwqv3X5kv1Wxb/daFECFdknTSowIjpabXmxWSmxERLzIg3fh9tJXtgWUJSQt2fIiHCLjoYgAphJj02HeOpZUNKYMrGffV52205EZTxpHAhCjxrSZeujAQ8vs91i+nzo6kxVKixyjfQ49L+deUPZYKTVkHpQZYphb6CvztMTYeV5qSWRmbaadeyjNERj7ePQe0uT6FWtsJo/7/vGKdfcICUz7TkjNzL5/R5rufWhQYIkdAKBGgQp7NCj77QsU3XA87VKYybJBUzeo66JtITX6e6n3UdC1Hna8T4EgHFHxflzb616dGE7wNgamrZr0ATh9znipJk/OYk/riZidlwoweARTWLpFU85O9HbeslyZkeX2RS4C8+V7fICfPjomTeSJnH4vbI0PIXEoM6TjmNZKdtpLJel5VmYi9THHyotNMz3CSM1733fMuogLjBacWOGvlo+mMw4Z1v10iRINGjSoOrlptx6iMbK87P7XTb5HFEBRNH2kpn3PdipK04zTT+2B+BJjIwoIDGdFWXKIRWIE+4s/dLFDXGiAsMx4snJMtMk6gBcBm0RnXMNxyJEUT2i8Hx7AWEB0VEaPn/pHj21v/662SbesIzBaQ9JQZgjyCnxjEiPTtm+YHJk5Ia2Um2ZSIvPjcdP3H5MSGC06oX5m2nHRkmIUkdGRmAq7UexlGo1pkIrOyHNIB302UqMZRWnKu3EEBpjKjb3ohiIUarf9fizJqIy3IBSV8S50wKSZtgxDAhOSmVTk6JQzZChV1b+X0u+M3cimfmx6zROalNh4olBiLAoxgwtFaWLpqhxSYkOpIXlQZm6enLoYT1zseCoaE5KZiMicGqExMqNFZh0RmHsnUhNqiq3lRmIsAHpVAdBrSoGml5sSDZbYKw1qJaZEgz0A3XdNiBIN6qJAU5RoygJFXfRpp1GUBl1Tb09e7Dyo8doZF84Wqcm9MHlPGInOIDLuyZqXOgtd22OHZNcLRWlGG8hrSYV+ZD099JbFvsP6wG1ERoQkdSx633PFQsvLsX3VsPUTmUKZuWlyC3xl2p549bhtoaTn6/oYkRolMsdGYnJkppeaLZarHe4fUgLz6oqOvX1B+26NhabptKQdH8p5CzRYdpGZBkUfpdHRmbLfetyjcIhCPVc3YxhVtTRNXeJQF8BqMY7GhIpTYaat0MSw8qCHPe+hBfDrZGIEOtGz46lji8lLaHkONjITjM6kTsNWYFJRUj1P1RaN9veO9AsHxp352aGOGNmehFPEpMZGYCg0JA1lhnSEREaLCTCWFD3PioyVmjXGLZgckTnnQ6ToEW3LpcfNpOB3bpoJgBuR8agxThyJ1OiHrpVZYj9KQYX/Sl2KSVF0ItRHaZrx8ma1b9NOdUBotNgAU8HJuXjPitQczDCVYopRIphuknFPbOwwJS85IuNFfFKiCAzHPzkd6whJSmBi8uIh69mWTV46qQTwGtiPJxWnSo0XpaHQkDiUmZslVVTo/dqbU+Srx3VqKbPQ91yRma7gt1rtTcFvWmBsmml4t8ZCI+OiJrXSFX98iM4sVZRGFwBbdCTG1s406u/npZ4AYGubbYdSTlszz4vc5ERovGGUuVGZwPN64rLF8Hrtcdn1BSskev0cvPW893P0vDpKoy/UJwhMaYaA83qldsdGa6zA2Pn2GENSkxvW89ZPCQuFhrRQZm4Sr+WSnm8/FlZgUiITuj2BEZlHjCMoOsX0iLPITLjgN10n46WZgGk0ZkgzjeMuOywxFA57TbvH0Rkdx5mLrdkBgKKoW6mpCyxW+zbdVC7GIqOjM1pkYuRGa1zshrlRmdgFKxCdkaEnOKF5elsrAHZaP72N6sh4TApdtFhoThAYO6wxfe2jfdoWZN4Be2kmO+5tY4UlNe29sKM/fOQbQ5m5OUJ1MqHiwWNFxt4o8oMjMp78PG6xftxgWe1HdTAVdll1Mm33d+2jcCImVjp0BGaHZV/sKxGZPaquCFjXzIzTT3q/En3xEHnRCazx/GHecrVHXRc4lM0QndEi411kQxdh76Ktry1ZURl9IQTiUZlQaya77mKYZdM93jHFxMa7Zs45U+bInn2/J+vfYfymK2EL7c/uN0dmvL8dgOF7/WrmefeSOoU5ghOC0RlCmbkhvGiMFhlPajyJuXemrcw4kRgddfFqW1K1Lzk1MqPHO36UDYpSoh01huqVIRoSazVkl9laFTst91x6xbq/PcE0DtROSy/BO1R4xT02o3nLSZRFP4c9Br2uvNp2vK2f2W+X2G+r9gaW27v4vZrkAhdbbtfzxr2LJd4RFpc5FyOdgogsjg1D43of9uxoozeh1+zNO4o7DKmfgMikJMZLM2l5s+/D6L24Q/uj5L2b8ao2lqbZskFq/N6Z7+XvasRTU14LKIBCQygzN0GOyHgSI8tjN4WUKIxT3CsiooUkNJ67Xix0P3rUKMqmO4yxuEwjIYPsaKysAFOZ0LQCU/X3XBKp0YksERt9m4ONM2+Hyn0O+/yevADjezptXtZ42y5bkXlBXGastMRkJ3Tx1tv3yAURmF7UzkGgZZONIHnRGq+mxksx6Xm5EuPJgr1GR2UnEY0JiUtMaELvgx3v/36S+tIi84r2uy7TWlZC4/YAQk3BQyIjURogr1k3uSUoM9+eVPNrG3mBM8/21utNq1+PuaJy7DApNe9AJzJtZGYqLoKXPvLQEtGYr40sa28YuexlZIcKG9y7YrPvl7XzXnHfr/+KtStS3vNreZFh3Y93fc1sl8DL4jRxsfNiEuNGI+Si50VjvFZNIfTNJ2UbR9atyMRSTjLcwk//eOmgXJEJSczR0Rr1ejxpSU3rY7eiZ8cnERwtNVoctNCk5OZgdq5/NIUiMvZvXGP8/CH5IbcEZeZbM1dk7FDXw+iHvp9SohbGk5oyYzxneVBq2hNbUdYoinEtSig647VQsujefYd5Qyd5Q2RG0kv3SmDG0ZlYmmnTrHs5CR6LWi79yej5b7K8LoBtRGRyozRzJGYUkQHGfcvoixrg/5LO/XWt+zZxTmWePKSGdnsbTLBCkCsx3vMcc/31ZGWO0MgwJC56XvB1iNToQmX9N5Ud6b+1DZV5obMYWmBKM61hdOZWoczcDFZkvOVWZHTq6B7APzCWmi4S84iptITExspJbF5sfnB5G5X5UTYoR/UytgXREKGxQmMZRUJUYe0ovdMV/YrA7PvIzJBC0uXFQ3rpfhyxaSpsXu57OYnhigsAyLZyX6YXtBddOzxFbGKSM0LSS7rGwt6wEN6GDjYqY7dX0UG9O3uhjg3t7rxxGYYe3nsTe665eN+D3Pn69YWG3nFHxQYYF/nL39yLyOjPga7B0cOQkMSERv+RKTS3CGXm25LqR0bGbRTGFvl6IrNuN0/JS0xozvkY7bceCn9LHYmpgxEaPbTjGisyukE1gFFkZm8etiH4XqWU+vTSbo3ddonty3oQkxRWXLyL0ylRmblRmhFaZEJXcDt/TmdrmacveYocqbG7jo3nSEwqQjNHbFLfg9Q6sjwUILHHlfs6Jq9F33NKojev6om8FNEpERoPCs2tQZm5KWLNr2Mio4Wm28cK47tS64c3LyQymDFfz3P3p2tl6r5exiv+bXeVn1rSWJHR0xKBmXbD56eZ+vTSbo3NyxqH7RJ4WeVf5OxF2BvmRGNSkYatGo9d3AD4xb7eL3PLMRcfex8fhRdtseMxqZgjMrkX/NBzpcgV+9S6wPh12OOJyYw3374+9zXrVlFWajZqWoqKNakIDRCWGwrNLUGZ+fbIn9gTGb1MP6zISFSm28cjxiKj5SUmN94JNzUemzdKXb2PojJl2aAobH3MOLWk77M0zPOvNJ646CbUMi2RGXkMAnMfSDOt8frnHrtthcPL/VCoO+eCl7ooaWl5CYzPTS3p8RE2GmOLRD2RmXvBCaWb5KJ4N54VG9fCt1K78HZ7rNCEpC/3b3zOKKZ9Td5xxOTFmw9nvrdtLzVWPPTf3wrNwqwj07JtKqJDobkVKDM3i5Yb+ZUk47q4V4p9u9TSY+ARkhk9HZISYHqyDa2n1x09aqBPL9Wqf5lxJEb3wivzUr3u2pZMVmT2WEIKgNu0UlszIy2YxkXA61F66fXP/dB0+mUxCMbcX+8xoZkrL3MiOCPkl7c8uW2Cfe4m2Z55JFYHxte/mOxYgbH7miMvMamxxF7SOYTGviZPZuwxe6/BWze0/Uh+vabYGi003k71Njk9D5NbgDLzLbFRGG9aBCan1ZISmV8YBEaPh4TmUQ09WYmNx6b7yMw7sNrhR9lgudqhLBssV3sURYMKe6yxmVSxVNihQI0l9qYgODw+7s9F3xl7WFtHZGx0ZtfXztz3tTUSkZmIzAvSMmN/kMbGQ5GZ0Dy7jSczI1LRGOB0kYmJi91fJDpjp3MjJVpuZPrUR+q5LDln6zlC4+3XSpwnM954aCiPEuPP1qiZt77Xk/QwbCM03u0tPCPzIjU2ukO+I5SZb4/tO0bGZWgFxqaWOpH5hUFevHFPXmIyI0R/hdr70+hlNX6s9ijKBtVq1xf8Losd5C7Uw20LxrcouO96dpHlyz6mskPhRG88dDPtBiX2faSm7KMx9rFRfc3sd8shteQ1nc6NzMQiDDYyc0p0pr8IaTyJscLiiU2OqYXQzbEXyI7OhK51enyL9IX9XEKTe7xyzPY1eMTmhyKfoX3MlZbYUN7XlRofSc0a7d/S3thSWjuV3Tr2Vgpek/xQD8EAozffG8rMtyNWGyPjso53V+t/YFLs+yvj4clLP77FYrVHUeZeoafYpspFWaPqZEYEpkCDJYZxKzKDxOx7qRHpWWLfSdBuVDsj0Rm5T5Jtpl2r6IzEfiT1JNEYPdxhif1uORT7bhd+Hcvci15oCBwvMbUZ78mpi9EH4UmMrYHwXtTc05M1AKf3XLuK9x57fnSqyAD5f9MUmf6WLTChbfXzxD5fMZGuMUhMVGp0mtvjFf5nJSYnntRQaL4rlJlvhf2yh5phQ4170RklMjr68gvAX4HxkbwAeDxg8fiKarXD/UMrFCG8WwToHnC9Hm/bNNKuj6Do8QJ1X70yrlJ5nUiNiMxYaPaT6IykmuTYQve9bm8y2aaR2tTT0OfMHlXf/LpvteRFTFJpJrss9Sv6VInpn8+mjHJrYjyJ8abt51eupjYK40Vn7PaybiDdFBIZi72g69TLHIFxn0sij5FbFujj9qJGcJbZae0IuVEdu/+QvMSW2ciMFZlSjb8A7ftgWzPZnelUVAzbyo1C892hzHwbQp2JaWmxImNvVbDGpNXSLzX8Sw3/MvNEZh63WD1usH58xbrYjIRCCwEQERZTm6I7p0PRzvcERkREC42OzgziIvIyjp3Ich1vsdjj11GZXZ9SGqIz+rHDcuhHZlvFWxfFLh52/jllxo3G6BsNAn4vvjZCA7XMO2hBn4JineJZrNDY/esO3IzQ5IiMJzF693OjMhN083XZeYbU5EZk7LiXavLWD81LfQZD8+SYPZHRQoNuWIvQ2DdaS46XcgKmYpwSGrsNuWYoM9+C3AsAMJYbG515gttq6RfG0ZjJ4x0/HjdYP276KIyOgMi4YG8LYG+WKPOCd4JGMUonWaGRdFOF/ag+xks5yXq65VNphoVJL9ljb5wjGQqAh56AX3fr4e7VL3e+xKTSTN6v4pDM6F/Gc0WmR1ooSf8goYLeVERGo1NM9oIeEppUjYyVGN0pW2DVFJ7EzI3MTPC6/xcypCYlNCGR8YZ23JuOkSOEwPgzZkXGStZIaIDx500fXExg9XRMaOx+KDbXDGXmW2KjMaGhl2bCuOYlJjP/F8BfBzz+9XskMU94NuIwTjN5cmLFxUqNbl8kTIWmHk0vsetlapxy2vR1M2u8jtJSlaqZ0dGZdrjsj1mGEpWpR/3M6MLfcVTm7WUNbO/iUZmJUCAeyo/JjDxyZcaNxhzUMCYw3gU6hhYXfZUOzYeZ50VnrMTIupHWTTFC6ZbZIhO7hxEwFjUTTfKOI0VMZEIyc4rYhNCRmZTICC/A+O9p04nA+A8RMk7ZT+5nkumna4Yyc/XMicoAvshIuumunWVFRoY6tdSJzM//+1/8qn7jCc+TgttHPI8iMyUa2N5zc+QlJDSewNiUk5YXPS5DkZflaHyQpPYdG6fH9HEOfc1Uo+PVBb87VNg3VReVuUtLxQum6SI7LtOhi6ue50VePJHpkQuIRGOkl9aUwMyxBGAQEmAadfEiNKl53oVN7/cEodGHPFtivPdMT1sSQmOHerPUcG60JjctFUM+0zkyoz+z/blqjembqz8nsqGeFxIaLzqjYfrpWqHM3AS2ZsZGZ1RU5hHjZtbByEwrMv+u/ou/8J9eYtqoTJvCGQSnnQ+MIzGx3nT1col82ChOKCKjZUaiL1XXekmLjERsdP3NONoT70yvfT1DDEdHYYZO9LrozHbZ9icTi8jocXtxDP0onSMzIaHp0UW8XkTm1ayrD8YTmtjpJSQvsUhMbJ6NztjC4EyhSUVAYpGZETkS412g9UXXEZq5WJHJER49zB0PPa+OzMSkBmr9/iHppoOzsve3jd3eYG7UhVGaa4My8+3Qf9IFpmcfKzQSnbmbSoyN0Mjjr3c8/vUbv6rf+IX/xb/x3z4qIwIjUvPURWcGmRlLSkxqvF52ZTt7O4JQdMamlLTciHpUqmjY7ifU54wITPua2nRTPXpt1UiJdtsKbn8yWSkfjC+idhgTmVhkpt+/vmeOJzA5MqPn6WkvcqhTRLUzL7a9F4nR6abQ/gMRzJDQyG5C2yQlJiUwsahMpN4nl5z00ZyITUx2YtNaZjyx0uvVaM81Mt2nm+4xfrNfzYb2va0RFxprTyEYpbkmKDNXTSzFlJIYGV8Py0r491UyUvPjcYPHh2c84Rm/0KaYBnl5GQmNng9gpAo7c8H3bg8wtBaqRtsMTaZbAWnHx4JTKVmRNNIgN+PWS+GE1rh5tk49hZimpJyvWU7UJXeYemSJjJaZg5nWUuMd9CEwbn/dehKi59vtY9IDTH+Vh4QGZn4g2mGjMva9tutGRSYWhdFFrbZoVY5R79NpjZVCryfj9insfDuvRPr5YpGcUEYnRxIfMXxme5mxUiFSI62b5lzOYsJoD9oTanJpUGa+Fd6f0/Y1I0Pn59EKY6F5xDTd9PiOp19jkZHhvRKYJ7yMhEYiMzmpJSsunty0r3Z812svUqOlxUZjZKi3laMb5o0jNhovBWXFRTc/D0ZbQuMhcdHzdBQnV2aiIqMjMV50RggJjL76WVkJtWKyAmNJFQpboYGZp/cr8510k3fxDf1wT4pMSmLsvFDngYFTtLcoNs/KDcx8+3q8bWJRmtwIjn0OedioTI3hxp/1HYYIsn5hgnSoF4uyAONITawDvlTkhhGbS4Qy8y3xOs8rMY3KyOPOj8g4kZnFr2c8VYOg/MJv/MLvkbg8KqGxMqNrTMZ3nK4CQrPsU0tabgpHZNqhlZnh9gY6pWT7lPHESPZXNo26A/dw5+0UNkLTE5OYlMDkRmSs5CRFxhb5eiITE5jQC/QiMLHISuinfEyEQnUTdp4+Bifi4ZH1pw7d1sFLu9kdW/HTmO/x3KiMNwTS8mIPzcNLGXnTuUgkRkuMzHsB4p3pyeNYuZAX7EUIY2LDuppLgjLzbfD+lKFIjROVkdFYiunxHevHjSsq9vGIZ/wTvwex+butNG1KoCl/oCkL1MVYaHIkRtYBxpERLRhaTERYPImReXpfo/00ev/NaFnouT2apkBTG7EJSQoC8+vItCcvdl6WyHhRGT2MtVoKXYyteMh82SYUWdHoq3isZiYmNPa4E6m/LLz6GO/9Cj2BJ1kZhCIm3jp2GFqGwHpQyyzOacSd52HX0/Liic021juwJqd3YE2ohsaLvngWyCjNpUCZuVpyT346EuNFZbr92MiME6n58bjBUzVNJ+m6GVtH89Q84+nvA+7+2z1zCaB8A4o3oDzgvQTqYiw5u6IaiYseF8nRWJnw0kS6yFfGl9iNhKXfX+3UwxTDvkP1Mn4vxupYrbTATMfGYzLjRWG8cQC+yNj6mJDIyE68E7f3Uz+VVtJ9xFgRyd1eP29MaGRcH79JN80iFo2ZI30ZEZk5eHITkxa7TDP36iBp6pjMyHzbj5IVmZUZboHhTts6EmOfMPXeaXmp4X8mZBkylkOtR6H5Sigz34JQBEYvX6hxIzheVMaJzjz9Ghf5tuLyv279zBOe8bR7xsN/34D/B+C/6umL4TDuSmBRjCXnfXXArmrlZlctlcQMQtPuKt5s2peZZkgd1Q2K+i3rHS6qdDNtIZheErxITCoakyMyofEskQlFZeBMh0IYufJhoyxWaBBYTz93TqTHzoOz7TFCkxIZ/V55pEIedttISyzv7UpFY2CWxyI1MaxH2MfKWd9KjBeZWcGXmi0wTQWFviQWG4EJSY23TJ43J0pDofkqKDPfDlvwG5qWs4iql/HSTL3QbHFfbdwIjNTNDHLzgl9/v2AhEvM3gP+ve1olMlZsZPxuBawKAKs3PFRbHKotmhLYrxZ91CYHT14AoKjfUNRA2QB36nz0br4NOjNUNg3Kwjb/nhYFj7ZXUvMmOwtFX7xlKZnx5MUOe+RCKzIj4zGRsfNkPyly6lvsPLsslaKywhJ6Dr1vu67eT0ho3s20fh9y3q8YmdKi63u8yItHSl5i2Ou4fb5YWknOIZ7MWIcTuZFj8kTGRmhqYPib6rSTjtLMISUuZWB+CArNV0GZuQlsfzMmxeRFZkYig/bmkd2dp63QaJH5hd94+nuLxf+HVmREaDyZkXGoaTkRVt1jBSwqYLECVsWhj9oITfkj+spFXAAlL3JuajC6ONyZk+2ingpOCi8q01iRCUVlvGmLF3U5WmRCMhOqAcm5QHvkFOh60pGKxnjzY0Jj17XHIUKjBUa/ZlvAGxOZYy5oR9bQ6EMqA9NWJFJRGZhtQykjGXoiUwIoOxHTURlvXEuMFhkv3VQD475n9N87VyLtC4qln0JCEzJDCs1XQJn5loT+rN7PKIxFxks3Pb5j/ThIjG1yrUXmn//ftq2PkWjMf9UQ46cNRmk6iRkNu/G7CliNfvG9DfuyL1VLC9S0nb/DVKyadp5c3oq6bdEUisaEmmRHWzTlSE3qERWZ0P2VPJHx7ojtRSFyibVUsvOs0Oh1ZF9Q6wJT2QkJjV43JDRQ82V/ghUYmafHPZE5RvxsGgSYnWLy1rO7jomMN0wdcjAyc2h/QaBC27wavsjYyIx92MhMDbTfTH0zSttTsPf+h1JNOfIyN0JDPhvKzFVi62FC8+UR+pW3mK5qT0orAGWNZbHrmzEv+4779/1tASrsUe32uNuilYM/zqMThIm8aLmRk5ZETfRD5tmXLvIh+9DLa0xFx1KaoRIbicw05Ti5pHE7xTsWLTW5QmMfo53Zi+2rWlEvA3xpOfbCHJIVOPNCQgM1HwhHY0L1Np7ceEJj5cYTGD1fv0chkUnISBZHfK6874edb9f3BEaiJXZo160xfuv7537vRAZA2a1QKqHR+wxJzMpZT15HXz+jRUbkRuSkNOM6+mdFxkZbYsKil3sCrI+NfBaUmasjR2RC2zmtmCYnIfXo57UteErIPYzqkdj0grN98yVGHrXZf2Ges0ArQg+YigvMOlpAarXMSAhKlT6ycqPlBdNtJTvUlEBd6P5xwmIzm1Bk/NgoTc87hgurF2mBmbbS4klN7guyHypPaPR+YymnmLzAbFOb5acIjX2v7LzcVJz3vp0iODOIOWjszyRyoSVClut5GVePH2XTxk7rcvqjoS/qxfQHlCc0slwf50hg9AvRsqLH5clDYpMiJkH2OCg0nwll5qrIjcjMxEZkzPwf5fjyXWHfi00l94Vudljs0IrGVg1DMmNFxsiIKzIiMd4yhTT37pFm1c14XZEcXRejBUZoyh99fziicbnktoA6OgojF5jRe/KuFkoaSQuLF5Wx0RmY+aHpGDnRGDjzrNAgsH5ov3CeIxT9sRKjj122s/NC0azQe2i/kzlRG73MKU7O+Zrn/qm8t0lLiyc13iOGRGekfkawkRmRFk9s7Ef1BRiaa0t0JhSVSY2nxMamqORAclo5kc+AMnMV5P6S07869MPaivqJZCb7oXoUZTOKwtjxCnsst4exyIjA7NCedKzMAPGUk31ZehtHVATbb41ueq0FpajH21p5GcaH2heJTw3TZ4jMxNAXiZjETC4mcyRGRxlCkRjvwp46cE88YOblCg0wFhUvSmMjP6loTepXc0hi9LKYyFjhkuPWnCMVZfAcKvRn8yRGxj2RCX0WvedG9yOoSzX10Rl9XBpPXrZmvjyfFqA+3QSM/05eVCZnPAf99/SERsPozGdBmbl4vBOdF5WZ+6c0+w2ITCszwz2hl10kRo8vsUNlRcZGZbTMANPiX5lnm3OK6MijwVAfU3bjRsZ0B3wiI8C4M7zGvF16PQCoC1sXM76Xto7OnE1ovItEbnSmxwqMHoaiB7LcGz/lxXiiAviRFC9qoy/2OSkmvU0qWuOls/QFLSZzXooJGL/Xer2U1EAtT4jNnK+5veaG1rHSI+tbkbHT9jOq9+Eee/dkJYD6brwfndZKFQADznOXGFJO+u86J0IDM25FxP599DKv7oZ8JpSZiyXn15q3zpxfeXd+0MY8SlUzM9TO7Pt0U9k0Q+GvlhqJyojghGRGT++cw7THJE2z9f5kVp8mKiZCYqc1oSJeERXptE/Pm5NuAqbHOhwY/HNf6MQdFJlQnYw3TwuOfUI9forYhCI0XiTFzgtFYWIRmRyhSZErMTKdEkF7/LIs99Q74xTtRVrsNdp7ajtPtkmJjPsZnDKKzvSUGKXPQtGYUAGwHm7RylHfXNtLIcWiMsBUakJ4QurJD4Xms6HMXCQxIQnFaPV2+srvFf4ukPzTjyIzfmdxQ+GvSjHFWjPp84DXx4yN1Mi4zqNXGISn6Pap94U2KiP3fernZYpHaD2JxujojF0/6zm8C4mdznnoFBOAcZ3MwTxqZxxm2otIeAeZEzLXn98coZFtYvPs9qG0lHdFT61rX1OOxMh0bDx2/Pb5zngq1iKTe03V62jxkQLdHJEZ7aOdKMoaTV2GhUaiNDYq402L5NTOsAbGzbWtyenPjReVsZ+BVHooVCfDtNJXQZm5KvSf69j0krNL76GWW5nR6aU+xSQPqZOxYvOCcfNpKy3iASs1rtNLWmRkHdV5HtDWy7QppvENLC2zIyoYamY8oZHlIeREnsS7MKSkZrSBFRZ7zyUbrbFPFrpoIzAdwl60U0Kj59uIRk6UJrT+HIHR83IkRqZjgpOTSvOwP05mkCMyqcCQ3iYlMZHojHzurdAAndSUDVAXnaQs4gLjFQDbYV8/865evBeR0dEaTUxuYqRqbig4nwFl5uIIncBSf6qQ3HimssDkeUJSU753ncW1NTMVdrAppzv5BWcLfu3Dyow8r27ZpK/7WnpElioMJ2Npvm2kpo3KFP09nICpbMztG8YTGAC93BxFTF5C4+7FQ9JL9i7YVl5yojRQ0/rA5p6MU0IDhC/0ev2cdefWyXhyZI8dmL72VGRGL5PnCkVjjiAlIDYgpV+mvc7GojU6MpMt04FdlQ3qukBRBlYum/ZWH6tFOM1kIzCh6Ez/OiV9laqTkb/PHIHxXofdj50mnwFl5mJIFfra9bxfb3qeIyweXlpn9GglRupkJikm7FvJkBONrpdREZr3P0DdAGXR3lxy0sxai0yFcaGvd8wVhoLhFfD+AOwqYPOwwivW2GA9qXEBxlGZ3LtvA+gboe/bWFQ/7knRRJzqI2THio4nOADG6SVgfDGVDayoQK3jncTP9SsyJjR2+Rx58da182PpqND+9XEC86RGpu08ISVVGntBNC9Pj9t5wHmuobkyM0NsNEXZ9N+Lvi+a4A8q9QiJjK2p6ZGm2zZaMxfvb8VoyyVBmfkyciMwnqzo+VZq5BfHfeCxVuPIOIHohM041STRGtQY5EX33KvEpm6A1y1QlsBCpEZeilzrdZTFppcqtJ3pOY/DA/D6uMCmWGOD+15mJDITSwc1KPq+YIbhcEYUodm1DdCxQfsc+k7eOzXeGFlyoza537rsX9P2AhqLpuiozFdghSa0LLe+BoH5Xgoh9zXHolKpaIye770eT7hkXR3Jsb/0za71uKRnQsTeckssKpNitH6Jpi5Qd8LS1OMD0IKfdRNWe4x2qB/u9ndmZXlTXu2KDvYNlJ3rz2cqlcRU00dDmfl0ciTmGIGx07HC3+6R8UvIdphXdJ3l9WmmpglHZkRytq3IHMwJpoTpDsz+orS/yCq0N6N7APCzHW4fgOeHx15invGE9paY99h1ZhQr0q1RjGQNQB+Jasfb+RKREZGRmzkMEjPuVC8rjeX9INdDOy7TtZ0BhC+oOvqSezH3TrznPBmHpCW2LCQGQFh07How68SOT8iNznjzbbrLExovNWHnm7tmA9Pvih33Plupj6TepycwVmwyZUeLjCsxsi89DB2fHffOW/aYR2ipCa5klnmhr9B29u9JPgvKzKdiRSYnChObV8IXGhuZKdW68rib9ucQODEMIjNcqvsIzfbQfncb+FLTDV933eW0AcoaqEvgfuUIjURm5PkrBCMzf37+wKZa4xmPeMZTLzMiNPtAzYwtAJ7KTNMLjZWZfVf2vO9TTYPQ1L3UhIuPjyJ4sXhXKwDhC+tXEqoZOZfQhOaHhMZGbUJiM1diQstsussen73w2V/9znkgJBp2OnRhn3PWD8mK95aN5OYOb3XRi0xQYgDAi8xYbDRGxlMyEzzeO7PyvV3BPKEXCbTiSb4SysynkSsyOVEXO71w5umHTi+pb3xGZKZtyTTUzegL/BK7tiddkReJxEi6qZObw24oST0AWOv7z3VPded9EiXNJFGZFfqojBWZZzzhxdzLe1wAbMLcSjREXoaaoLHIFGjQoMAeS9QoHJGRtacS06A4rmbGw5WaVIrp1JOsPlHbgttT8S4QWlDk+WPpJbu+/UXsbe8t845NH5ed9pbZeXr/Wqy00NjamIzojH1Kb1ofQs58/WeWcft5S0RgPLIkRvbtjXuEBEbOFdoTLaN5Vmjsm2LFU3bgRdzsuN5n6LNJzgll5lOIiUwq8iLTKYmx697DTy/doz8pZsnMVGKGmpmmvd+RTimJ2Ci5OdRDO5t7tGKz7mpoJNe/qBG/X5OITJda0iLzG//sx0Vo/he/sDfNnLx6GQD9rRmq7jV54yIxUiMjraUalF0h8Pj2Bh9+qwMA+RGZUKHvscw9KYeiM8D0ypoTpbGiY4XGO8bciIw+Dr3PXInR8+1F0BMa+8teC4yN2qg4pj10aQVkCUVjUmknL51k92nTTJNHAblz9jilpMfPdAkKRWXk7bTD/vVooQkZovc3tJ8n729IPhPKzIdzjMjExCUlMTJuU00qvSS7yXiUbod5g+Dc6YiMDEVwusfrdlxmJ7eEQwPUNbAogfe6i87oFkxyHJJi6tJNm4fVRGTGwyc84xGvWI/eeZtealD2r6UVmrqvhtngvo/USERGK50ITWg62Etw7IJj54UeANIpJjsem5fCOznPjdLMFRr9HLFoTChy40VBhFhERh+TPhY9npIYfZz6V7k9NrudFhktMJpAMXCKuSkmT2TmRE86ghIDjEXGiwRZcs5ZOZQwN2cVobGppgPCactQPRb5KigzH0qOyNh5ITmxohJbT0SmxFhs1HOlTgjdcNxh3tDPjDTNHtXKbKfD9y3w2oxlZhTYrbuHDWJokZHHCm3LJaxVfcxjP/yNf+I3fnWS86uXEI1XmCtJI4nCSAJp31XISO2NFZpxncxQUVR7InOuVNOIUIpJj3uFvzkn4Nzoy5wojY2M2GMKhfnt8rn1MnMiMt6xxqI0er63na2ZsQIGjF+LTVtYC4mkm3KEJRWRsevaj1ZIOFzxLtF3jDdaV0uMc0fwHHJkJhSVAYY+bSZCo6MwoSjg3FSThqmmj4Iy82GEfoXqZSmRsdEYOx1bv8S0TmZGVAYAyvdJzcz4jtn1cOKSfmFMvUzdjPuh1ZQAFl10pi67VJOgm2aX6NNMr4+LSY3Mb/zqRUY/vNZM3vS0nHePNTbd9LKVNmAkMO300PR6fIuD8Y0o6zkik/1DT954L8WUiiB408Dx4fFjTtBepMa7Gocu7LH5njTkRJL064gV9up59jj1cv383rHZbUMXRft3Ud9l71ByozCe2MhThUTGbh+LptR3CKaUciXG+5Gl347QD7HQMdltodeVPmn0Qhud8f6uoSejuHwmlJkPwROZ0iyz03rbElNBsfNT89YYvuGmTmZlhvbRt26qURTDjSVtq6ZRs2x9ryCVenrdtl9niczYH28HhNsR9MfSRWbeH9D1JdM+pkIzPP6Df/c1M6lbGqzxOpGYfScxMi3Yjvjs3bP3fbpp6CM5+NrmEA3B56aTTjmxHis6MaxoCPbCLevOTTvpfaReu1x45qaSQsdtj9NLMdnoTEhqvPc9ITTePHsoIfSfOjXUz2UfACY1MXUkqhR7eMeoIy5eZCb0mkoMffPIvadqvaKeYSXGE1TvSchnQ5k5K3MlxlumBcVLGwF5wiPrRyRmhbbflke4TbR/rPaTptjSoZzuHTf4kku0neTtxj+apIrnHwDuq7aJ9kJqYh66BV0/Mn2fMj+B559DVEbSSUPdzFhqpKm2JlQz08ZfdljjdSIxeyxxbzrWCkV65G5Vo9qZpmueWpf+r93Qe+fNn5wr9cUxFAEQ7AU0Jwpgt5n8lDX7P1aWYvU0ofVSQqPnh67qdt9CKhLjvU4vUpNKMXnRmdDx6fVkP/J9z4xypK6z9vOVIzT2M61/0JQAVnf+Pu1+ts7D7i8oTInXZUmu7wltzjT5SigzZyO3PiaVHlqb9Wz9iyc6dn/q5KajL1ZmtMhYoVm19TL2Fgajlk11JzShX04FsCgHgQFMf8SeyDxg3DFeJzbvP9uozLhOpm21NBQD/+rn/cY/sd8tJ82ivZRPtdrjvtpgj2oiMV7dTYi2M71qdBvOpu6aZtd34YuC99554fPJSrEcvr5w2quU/Zkq24dC51ZUQr9APyKsbsMIxwqN3WeKHJHx9hNLN4VqZjSh91a2j/1gmlF/Evs46OU5wmAFQ0RGxmP7k+ELpiLjSc25hMYefxCmia4FysxZ0CcY+5aGoi5WYkRaZJk3fh/Y1pzEbAopNG5FZiQ0B1Sr3SS1BAzd/gNI/ugti/EpWLTsvgiIjH08AvgJbB5+uJGYFxOp6SM2fz9i+6IiM5EixP3jBrvVEuvHV+yKZR+hqbu+ZXaoUJgX6kWmZH2RmgYldttl26Ij56SbCpNPojOetHgRmlDdiFccm1MLEFt+rNDEojNzhEaOQV+dc4/HHvspEqOPc47Q5IqW7ENHaSzeB8qcK6zTpg4lJ0pjpSa1Hbp1rdB4UqPlJnScHxowSVkd+SooMycTEhk73xMZG2XR0mKntcg48mIfXj2MnReJzPxY7bFctXEGffuCUcqpfvPfki4qg7Jtbm1jR2t0IhO659JP9fhHO++50imkR1dg+uHfv7D9zz+Bl7xOxt62D9g+LrHfVlg/brBfVVgXG+z6SM0O+vYGQmGExt6nqY0Mla1IpU7A+r2TYTAqA4y7ZdfSEqux0JEbfRB2WU7KSR+sfb6cYluPUA2NPEdMaPTxxJblPP+pEiPrhgTmmAJlKy+yrp4vWLnT+5flzndDv0VWUuw8bx0rMsD0BpCh/XlpJk9qQpGZs3MIjJNLhTJzNjyRsekfO7QpJS0tdlydjDx5yZEZ+3iEXzPTpZiKwutjZlw7E307ukdZoL25JFR6KXSbgp/T6T8/f/S3KLA1M5Phn1/Y/n4C/nNnml52eIe9BbBd4G21wMt2icXjK/arJe4fXrt002YiM/amlCWaUWRmh0qlmMrhuXNOwPZbaf9utV7gpZq8+aloTI7QeNvHOCVKI9trYkJjl9t9zL3qxSQm9ppCwhMTmtA+PYGRdT2pgdq/EJK6gNCECEUWQ9EYPR2TGRmPRWU8gQk95nC0COX8KgEoQZ8LZeYkvF+QnsjIUMTFpot0JMbeekC1REq1Qpr7CIiMpJik+kP3+DuKTMgJxGZcdE++Xd3MfbeOpJfupEdf/fyPmEZnuqjMUCsz1MtMxGb3Cy+/n4D/LIDfaE+QQFpoHtGeNB8B1AsctgscHrfYdZGauhpuRtm+vHEhdNuxXou8a/vdErtthbftEqN6mVyiURlNKNWk53sRmNT0OWpoTiVU72Iv7ED4on3KBSVXYkKv3QpHTs2Mt62dp6M4VpBS74fGCI13CN5QxkOPrVqvNNtb8dDbxIqAPamZQ/b6oRXPJSYUnI+CMnM0XnopJDJyZbKRGVsXo0VGRWNEPEK1L6GIDALr2H2aCI2kmJbYo8Rwl+xxt3CB1kzldFiWQLnrEmqlSi9Js+tQvcxPYPsTk1qZSX0MnvC7+YXn30/Af1bAf9A+XtRxxYRGRGarx1c4PFZ4rgvsVktUqz2Krmv2NmJl799U97c22DcVdtvlEJVJ/boMvYcy7v39AKRTTfqimVs7c26hObUoOEdovPViF/EUKYnJuTKGoi859Typ16ylxo7b/eq/tayjO0NQQuM5r/dSQ/Nin+/Uci8yE2rZ5O3TO7aT/fqUqCL5bCgzRxEqVAR8kfGKgL36GBGZJ4yiMTZ6EpIXPQ1ML4B22m2a/Y7laodlsUOBWnWQ17ZsktoZAO1tV2IX465uZlEOlUH3IjA6xRRJNT0/PGKD+0lURgvNb/wTz7+f8Pafh1ZifqMd2jRTSGhGEqPG67uhnqaTmaKs21s8yEOJDdBGZvbbtlZmFJVJ/Zp0ZSWy7mRfsVSTfrGhaMxHCs2phC7u8px6PeHY9JK3L/ucuevH5OXYKI3exo6HnkPP16X4UOuZp9PD2Dx5lGZaojN2fiyak1Mzkzq+sxDacaiOhgJzCVBmZhMSmYUzXpp5OsWUITI6ciLDmMCEZAaBeRORAbDaoVpJRGYfqJcZhGaEfrnSe2+BvkXTfdVFZUr1fKHIzM+ukzzc9wLzijVenLtjP//9iMN//tFKzG8MQiORmdRJT59MHzE+wT4CqNt6mrfyHYeybvvfUWKzXO3RFO378frnHrtthcN2OY3KhJ7fw4vIuLKTczEUYtEZuzxXaOY+77HE6mgAP1Kjnz+13xDea53zWqxQ6iiNEPsbetvbqE8oEudFhfRz6nEVnbGRmZA8lIF1thg6pDtFZuxyexwf4c3kaqHMzMI7KYYiL1DjuqDX3vxRT3dNia3AzI3OhCTGDh/NY/WO1eMGy2o/umWBrZUZdRrXCQtKDGKyxehGk3c74F9NJzK6lZKqi+nH/z2M/++/Vr28aIHZYI3XTnI2zbptgr1FKy8SrpbhKSFovc4WbQdg5QJv2wXeVgccygY/ygZ1XaDsUlC9yGwrYHs3PVF7J3KY8RQTn0jVzbgbxXY4g3OIyhy8KA0QlhrZZi6h9+PY1IMnNDDztDB64yG8SJz33BqvcNqkm+xnMyQhK0wFRg7dkxnve6CjL3Y89oPglO93lJzoDLkkKDNZhCRGL9Mi4w21tPwDU7G5GwuGU8uSVTejIzPA9C+sp3+Nn+PH4waVao6ta2UsOyyxq4CVHJe+a7Y9+aBNNaHAuNm1Jzb/AvA/wOFfUJ3iDS2Z9HCPJTYv9504IC0LeqjHPfmz68pJuT9BL1qxKd+xrQv86GTmbbsEtot0uDxWB4DAvKP5rHRQiHNfAEIXaCAuNbmcQ2SstIRSfqEapzkRN4/YeyTLgXFrNnV4KYEpERcYqOGcyIwVG71c9mWx82LT0XXfMQ+KzSVBmTkKzxByROYfznh3wrHyEnrEIjG2+Dd16CNZOrS1MqOoTNMXAYvQjO4UXf4AqrchIrPrht4Jp+iOT4uLjsz8qxv+n3b8989H/MavSURmg/vuscamWWMvEZA5OXY9LNV6MfTJeyQ2d0C9wlvZnQi3d1NpsSfl2EldH5891ln+4bVmCq0XSjV9BfZYQx/mUNrJ7ifnFJd6Y+dKzJx1Y0LzkXjGYaIzKfnQIqPH5SO3QnhfqehMSGpSpMQmNT9YC5N68s/8cUA8KDNJQnlmWeYJjK6LkWkrMV1tjERjRGZ+IS4zOSITii5Y+n2+Y/H4ivXjax+RWXY3XvRaLkn3ebtqiYdqO7RMEpHxrkeShgpFZFRk5s+/fwyFvbY+pqud2eAe++2yi4QgPyoTEhs5Tou+JmqJqe08dREIRWE82bLHNPucGGrRFGKOrIRE6JyRnZyLROw0lYpAnHqcp4iMPXZPwGJCE5pOHcMcgQu0wPREo1RD+1nVIuN9V1ISHxJ+u90ccsVmQupvfkydFfloKDPZ6LfKnjxDIqMLfHVUphMZHY1ZYSwyelxPh2TGPoB0SPYRwOM7fjxusH7cYFm0AiMd8uvmxxYpA35fdX3G2JZNNuwsrZd0jcy/u7flEW1k5mebXvpdDT39jm8ced+JzBr7psLmZQ28LMLNOnOFJoQ+idqIjBelkW1yfmHG0kzHnryTWAmZIzbnjtjMeXE5QgPEpWYuua/1mD9SSMBOidCkpM4i76c8h7b2u+nnMCY2OeehmLyk5tvaN+9lWHJEJutPx1TStUCZiZIqnCsx/mWzgC8yOirjiIwVFjucJTPvQFlD33sIdaCnz5USmWq4TaI0yZbWTO2ra7rNh7LgvdTNVN1+H8xbI0OJHIUKgLvH4R9DeikUmdl0MrN5ucebFP7qgt/cItuYLIRO4m5ERg31tjGBif1K9Y4lNh1lzgXxnNGWjyAlNEB+C6bcfcTIfa9Cx51qPm3nnRu9fz2skYzOyLhOI+XITOxzn4rceG+3/ciGfsRlf4f0gtym+ZSdS4Eyk4X3NumTgE4pWZGxxb934ZTSL4SFRpbpAl8Rl65VjW4uLHeHbjqpkbtHv8kNF+sCP1Z7rB83uH+QeEdbjVKp9JJ36wKJytQo2rqZh8A9mgqM+5SxaSYlNtufbZ8yv/Gru+v1L4x7+33sj/D1z33XgukufLfdlNDo6RjHykzoJJ1z0rYn7+R1014EvXTEnCLgr66bCWEvwDFSYnPs6ztG+GLHHWp6DaT7qDkFKy8ylGLgRHRGy43elSczc6Iwqe9t6E8f+jgfLTaauX0Ohda7xO/U94EyM4tAfrn/5i7UuBaZ9TBdwo/GaGH5hanEPAL4dZj0cSKdtwHjnmmlSBcAmqboZaapi150qtUey2rf30xRojHDfZ93/U0WbdPsBkW7RrXEerXFnc5E6dSSRGUkMtOlk+Tx/hN4/rnAc/GkOsGTDvH+qVJMw32Z2vTSKp1e0gXAoZMj1Hpw1i+d8ZDMSOg95yIQE66zc+lRl7nMkRrgPBeRc7x/oeOOtXRKMTe95B3LzOiMXdXuQk/bfcyV+dB3Q3+k7Q8K+xJD0y6pXxGxnVBWvhLKTDYhkVlEHrb5NeZLTPf48esPnn49Y1ntJ/cFkuiJvhniqNVRUQBF1xKp6uY1BZbFDmu8yt2EOqkZJKbAcKdsoRekkdBsUQG4k/5m/mCIykgrhwdMIjOHfwDPP1ejHn1f8IT/4q+R1IyKf3frNr30gvEjJjXeiVHPtyfelVpuZcarlbEn0tyTdewYgfF5c/a1VEdr7En2HEXAp3Cu/en9fNSp7CMk0DtuG0my68wRm1gIQ+9Tf7jlefWyu2nBr/7sWqm33wUtPLFamNjn334X7EuLRUg1s8TG++JRVC4dykwQKy963H7pdXop9Ljza2SSj3csfj3j6dcznornPlICALozO1usO5KZUfd33XRRKHGRWpnpVpYGheqFZokN7rF82KEp91gWb21/MhKVadCesKTvmRUmaSWdSnpR/cq09TH3/VD6ldltl+Pmz7nRjdS69henHmqZEdFZYXoStSd973nluWIpMThDkuCcYvOZb7onHragOSY2qZqb2PNamz+YZd3z68il9R9PJLzvTkpkUgITwwqN91KTzO1jhlwalBkXG7b1evWV8VATbNOXjK6TkcdfSIjMAatfz3j62fa2IukgLTPeEGilo+hiNEPKaTl6VV5neELdbS+VM0M7p1ZgpFC4v2t0tUdRNaiaHZbbA6odcCcRGRGaLjLz5+cPPFdPo1sVvPQ9+66VvFTq5gndsdSBj6wNb+d+sr1fc1o6RGC2mJ6kV/CfLyY0st86Yxh7TF4E4PeLcXA2mNP09NQL+1f8orUX6WO2/UxCkRQvhWTX9W5fkGrWbTvKs++X/jKZ2hmduvW+b6nvQ+zzrdf3hrLfOjJunzfGp/yp9UFeai3a94AyMyLU5Fovt1ITkxhVJ/Mr8nClZovHX894fHjGGq99omWJnTq6ceREy8wOy14CChTYoVUZGdfbhO6CLSKxQ4Ul9thg3XekJ+NyDK9osMQO+2KJ4qHB8mGHsmkGsamBQwW8Prb1MUNPvve9yAw9+7aVO22yqx1veiFTf5rQeEhoYlGb2LQtdLTjNjIj+wj94sxtqp2K2gAYflFqebHNbYHpSTT1oi3nFJzPIiY1l/YaYkIDTPumgVk/t58aWdeagN7vAsBrN95FlXV0Rou89z20w1w594TGYueH5CbEh/7Zc2SFQvNRUGZ6YkV0IjVWZFJNsU3B7y81/EsNjcxIfcx9tVHVIm10JhZN0RRosMcSdVfXUmHfS4yMp/qQKbvam0ZFZ5ZdZMZu10rOspOaPQrcoyr2I7HZFVXbGqlrOyV9x2ixkajMritBFpGpMRQwT/40dtxGaULn7BCh/jNWmNYK2KbysZC5lRlPWI6KyuTIi/fCvXXmSss1nJgvTVyOITdKkxIaISZHG7SNFuQD10VoYqlYPbTjqc/zMX+eWLQmtu5s7PtGLhXKzGxybltgROYX/DoZLTJ/YVQfsy4GkXnEEJ0JyYedbvrUUycB3Z9ahEZX0ti0lSCpJpGYVpD2eHXumt3ua61qb5rR+LLYYY+xzHjjr11tjDxqXcXTGJHReCdWG3rO/bXnLbci4wmNXt8LlevxczTZHkVlQhKSEpjcwtIcvoM0fCWxKzIQFhq9TY7Q6Au0jhRINAYY/83lh9rdNN0KM+7NO6fEaKzQ2HlnZU5z+FSIiNGZj4AyA8CPyoSEJSYwqgk2kF/s+xeAv4b6mDU2ozY899iMIjM63dL0stIOJYKxRzWa3qEVG4nKSEVNiGG/DRqU2KNCiQYbubN3t29RIrn1wWsXtWlTUfe9NC0hMtPe8XpnxKatxVl389cmOtPVztiojP5T2aEVDg/9wxOB9WR5rLfTGtPnSslMSmBCLT0m2HvJePeWyU0x5URl5lwtTumf45axERdNqLdjvU1KaDwL0PvxUEIjn0+9eSgqo3eZJTGpQty76azYywlx9MfQCiHF5FKgzLiE3hYrMiGpgX/n61Bk5q8DHv/6jceH567Qd6iRWWPTt/dZ43XUWkmLTJ+G6fRBKDAWAJESabytm3drtBwNaaaqj+bYdXUTcek52O6/QTESl0Fglnjt5OW1SzXp6Ix+rW85QlM607Uz1MRERstK7GH3lRKbuZGZEe9qpi3y9Yp+bfTmHCfgjz6JH9uHyncgdYX2UkYpobGEog33gWMq0dfQeIKdSjO5vMcWKuT1dEKl9+1997zjuCgoQefmYv/Un4dX9GuXl2Y9T2RkGLhVQSg604nMr4ffKhIzlhk9TyIzXnNrQJpOL/u5G6xRYd8vw6j8d5pakqiPpJHaqMzSXW+4rcFYXl5HlTaD0Ohu+SQyoxuHD5IjTb8rSJqp9iIz9k/liYwXNbHUZn2ZZ3PxOTLjZXhyZCbn0SMXgByJiUVlQnITWucroirnuEXBtaM/hB5zbo3gbefhCY1s70iFPkw7PiEm4jk4QmMPMes4LKfIhXeCobB8JpSZKPanRijdJL9YujoZ6fHWiowe/wvAX+9Y/XrGr4e2izibVhpEZqMiM77MSH1Jg7Iv/gUAKQAuu9QPsOzLegUv3SSRnmEf42bd+vm1zLTvVFxm7FDkRkvOEJ2RNFPZ9lycEho935OYHKmx07HoTOi5UlLjpZCyozIauRh4tTDewQBxQQmJjPe8oe3OQao4WbglwcmJ1ni3RvD6q8lBP4e370VgXWAsOl6LOz1+DIEITcr7LgrKzjm5ij/556HfDhuJkeW651+RGKmVwfRO2JHIzOLXM379HG6rOJaZ577XFR2xAQaRGDe/HiRnp8SjMVIy9B4zloKY0LTNs8fz9c0mRWba/YwLirXkaOnSEjP0YzO9mUKvRU1mHzOpR056afpG+M9j54X2acUmJjDe/BGhqAwi84F44W/OCXXuhfCzTtJe78a3QEhscu/1lMtrt42O1Fhz8I5Dnk+OSbazLe9Cn5Ocv6NZR6ecUt5n1/lQYr+gyLm4cZkJ/bKwwqJrZGwzbBWl0VGZUKqpfxywftw4wiLzhu7j7rvbDKyxATD0IQO0BbqDdDTYjWpihi7nGjX00NIzHretotpi30Gimq6WZlwfM24pNaSpbFpsePfHx9wWDC/7YuJdUaFa7dDUBd7qEqjv4kW5OZ/s7LC4s51NLaXC257QpAQmKTJ6GIpkzHmR5zzhftWvzluss7EfQE9o7Hy9bi5yUfZ6GbZ1OXZ5SGJCMpqTXnxHVrrJc64PcwvdIWFOSNh7veQYblxmhFBERk/btJIWGxWVsSITkJrF4yueqiHiMvR7O76DdXvvpOFmkIJIRisFS6BrLVSgxhJ+FEaEokTTVdFA7cOn6bete5GpUUD6oPEKfYci4OEuUYAvMkPUpuiPfYdlnxaru6hNhR2aVXuTzG1dANvV+B5KVmyAsNTEhME739hffHY8JjKhdJMnMXbZCE9kgEFkPMmBWscbj53Rv/rkeooMUWjiy7yr/jFXd9sRH+BLlBeF8T6HnhDp50lFgpxdequ6L3VO2ks+m3JMIYHR6+qDoNR8BDcsM97JzkZqdMGbbcWk+pOxMhORGInKPP2SKMxm1JfMehSJGU9LIW/ZRUMA9L3zSi2M7hBPojF7iDSM616AcESmNvPbaEzTi0xhxsd9y0jtzlhqZF+ePMn27V0P2siPpLH66aJAs2rTTYdV1UZnRGS8fmD0nzJGLHhhl8WkJmefOUIzwvYl44XqcwjJjbf8I5nzs/hUodH7uTVCQmfny99iTgpKPvBecbHXfw0w/Tvaz2POvkLN0QPRGb2raJDEaw5+ilRYubHvRY7UnHoMt8cNy4zgvQX2SqglxuYyVAsmKzKBptmrrlO8dd//rb0rUTuuS2RlGTD0ISMMQoM+zaTv1zQeb6VGYibAtI8afWfsduiLi9ckW+aJ1LTvYNMfn33O9riGL7XtzE+iM/2zViWausBhtQTq1VhkZKj/bBYv3ePNt+OevOjIjJ4X24/3vMFriNfqwxZPhqIyXmFwblGt5VjROUeq6Rwn91sXGyAcnVkExjXyWdIfdL0PW5djl4X26U2HpCb2WTJCo5kVhDrmc26FJSQw3vGnDogFwnOgzPTodJJM64eWF9tJHqYppmBkZov141hixk2wN0pehpSTjsx46D5gChRGXOpRailU7KslRvddM1THjFNKQwqqlRcdjWnn193+wp3zCbKupMik2scdXxVYrPY41AWwWuTVzoQiJyFxscv0Ot45vQxsF4r0JCVGVrLyEhOZEKEXEuKYE+iciMuxRQvnOLl/Z7GxwuJFYUo1P0doQnU2+rlSIuIdp0eoWFkf8wlEP3I5qdhjiqn1Zz2USop9H5h+yoUyM0KLjMXKjRIcW/gbSTGtHjejqIxuvTTUyOwmHf1X3W0XQ4jCeB3iAVBK0ESUaOinZtx3zbSY1zbFFvmRlJCkpgC5F1Q1isDIdva2CDJ/EKShzqaXmqJAtVrisF22MiPvub0h5LDD0JsWH8bWD4mMXdebzpaY2LgnMDlRma9MMV0a31lshJTQ6HVSnwPZNqdOxi6DmW+JyYs+5hCR6EwU22nfR0Rn7AnCExRGaU7lRmXGi8JY9E/7RWDeAqMUk/cYycxhFJWRiMyQbnrtozDjiMyuX27rTaRwVneMZwtyYwJjO97TIiPLJDLT1t74TbB1BKVypEtHlbybZWrp0lEYOQqbbtqv9m10ZlsB5V06MqOZG43xkMhKKPLuPVeQkMQA02iMNy8WnYnNOzbtNIfQSfgrozMe30VsbHQGiAvNqc/hSYjMB+b/jWV/XhTkoy9Xc9+POe9hqjg4th7J4UZlxiMkNrHm2ZFWTPJQ83StjPQjI1GZJzyjwn4SnRGRkV5ZbMHuUItS9jUmtpbFRlI8xuqjpKYp0BSDwLTv1Li5taSctAT577COFNUI1+HUowiPrrMR0VkWyzY6s1oCq1WexGh0miea8gEG2YjcF2b2+cfrSMxOexIj63jjervcqMwxnGs/l3rivnaxCQkN4EdKPIEI7VPXr8RExqabQnjRl1SK6YjamSihHwJzWjbl1M54v3jmCA2jMzEoM8GozEIt8+pmIimmjKjMEJ15GRUAS1RGR2REcmzNjK5xkWhIbURGCwQwLgi2+xHtEZHZ75Zo6gJFKesvUZSq9qZo1D6GGhsPr05nLFqyh3oU4dE6JqLWoO11uI/OlKvhztW2TmaO1Ezw7hsjOzw2pC1PCvgC403H1rPjuSFzu089T3OJsvGZXKvYhCIZMQlBYJ4nGqFUlX3ukESFnlsTkqxQKy3ZJ5D3/YzdF+ocn/tQMXBu2unWv3vzuGGZKZ3xhTM/FJlRKaaY0HSPH6s9loV02j/0dSsplCV2fZ2IDHVn/23UZddd5PejdI5tRi1DkYIdqlH9ifTboouGJ+mrpujvhaTviSTjRdmM5heVjtq08iL7Fxnp13XERmNbXelWTdKhXoH1IFNl13mW9ycNUSNTZFJ5esHrvl0/meYcEmOnQwWMoX2dwtzUUaolyjGpiK/4hRq7MF8iKaGx64RqZ3IiNzFCMpIbvQmhj9PuIxJNjSKF9SFC74+NxiAyTx9vTGpy1iHCDcrMInO8NEMZv1fzMIiMHZrHcrWDLmwd95DbotM27bDupws0KJuuF9+iiBYDS92JYFNTuhWSR4M2tWSFZbSOmd9UbRmwbktlU1fyfHpcI+/FHjYl5b1H3XtT1u3X2n6SvfSPFRj3Ghrq1yV28g9hTzheFCQlMDnzQuOh47g0rklogOuSmpxak9BnOhSRQWJeaD+fgX2ukNSEetPOwTvZ2M+CNy+FPL8nNZ5A6XXIjclMKNSpxxdmXS0xul4GU3EJiU35jrK0IjMusx3uQW1LctV6dXshXxa+yOj6EyswQiwVJHU3OjJiqUNy0xRtp3YYS02NAksMEhWq29Hzx/IiEZqheXlphCaIl3GpzWOEjcbok1zqhB96YlnfW+5FULxlORLkvdiYTFk++qSYko9rDKtfSwoqVEPjFQXbZTm1LKH6lpwfAaFLUE4jDYt3PMKxkZocdARFR2n0PJj5sWVaamJCY9e5bW5IZmJfBi+9pOfbqEz3hbASExSbHZarIZVkC3ND0ZnCCk791h5d2aApmkkNjV7XExgdlbGpKam70VETG5mpR+mm8Udnv12ieGigVQyqiHd4nhLDrRCmX04/CjOd5zXpdvEiMUmR0TKg5SanLiAlEKcITGh+TI68E91HnvxOkZK5217SifzSozXnFJo581LRSyElNyliPyo8qUmlkFPofVrhyBWvlPDYehsKTYwbkRn74Yr9GvDqZHSqybRgykgzLVZ7FIUXiRlfpEt1ofZSTEX3OS7qNsKzK6pRB3V71fuvl2YaC8xQPOw1hbbNuT2RsaLTtnyatmoSwZHXq8c1IWmJpZ3KsgFKcwLxvu9RoQnd90jGQycK72Kgl9l1vQOaKzChZamamNibcgzHnEBztrnGCI0m9+L9FZxLaIBwIbA3L/b8c8jZNvS8oX3Y733O51OeR8iJvNjnnCMvOUJDbkRmPKy4eMu9mpkuKiOuk0wzvaMo676gd3ypH+IYQmEu2n36qW7QNyravqEp34AVUBetGMgdpkVIvDSTbrW0VvPGxcLLsTiY6IwWmTczv6mL7ngmMaU+/RRLNQ3vwZBa0k3Ax8v8Dvd6QhEZPQQQvxM11DTMxl5URk/b9eEsP0ZecrbLqb/5SGIn3HMLzSX+Kr3kKM05hEbPzxGaUwUvta3dfyz9F0rB2nW85V6rpNgxpMTGk5rYvFQNzaV9Dz6XG5CZ3C+RJzdelAbhlJL72KFa7fvLuq6ZaZ9hmlaS+Vp0ivoNd/VwVACw3B5a2SgbFEUNXWiroyJyTycrNVapdt1NHeXeT0WREBlZVpdoOulpqja95N0h28MWA0+LoP17TGUTisjUegVPZGx0xhKLysjy0MF4y3OXeet4297Sie1ST+SXKjWnCg3gFwbnRmhSHJtyCr0ub9+yvv2xkov9zFmRCb3mlNTYeXOE5ra5AZmZg43EwEyXw6xgse94+KNssKyG5tW2TxV9kRYmbYEkxVQDaIC7GliUQNkAdd1GaURqJFKjZUbQvbq0y7oO6LrG4SIy3jEJnsigvsNbXQRTTSq+hKKfbqALjnU0SKeW9B2/dfrraKGZRGdCIuNFaUK/8uwvMMucaE3uPuy6Xr2M3Vcs+hPi2CjLZ0ZnLp1LTD3FLvwhcbHLUkJj963X88i5HJ36PobSsakfH/rzaMft+jnHmCswMew6lyr1nwNlJphq0hKjUkw6ChONyLTLl6tdfxGWNJNcvMdRmdpc9tVySTE1GH12tdRg94a6eENTHtCUPyA31h4X/76OojL7TmL23b/xzSrrXiKCdCLTSkIxSjVJCyYrUClsaikVoRk69POOz4x7UpMlMtaAUr/+vAMIrZezjrdeaJsckYltf05uPd2kucQoTUgsQuJil6WEJjc6k/OehC5V3nPm7sNGZXKiNPpzFks72WNMRWpCEZjYMqL55jLjfYDKxHIrMLLNYhi19TJeVKYEsDqgLBsjMiIJfm+8FmnFdGfTIzJsgLsdgKI76hJ4L98AbNBUcYHQsaE2xdTKzSvue9kSWZg0yR6JTDvd1EWfatp3kZSpng0tnkST9LSWLW+YjSsudtp2bicrhCIzQurknCMt3no5F+7QiezUtNVXcGsRmkskJOhAOL2kl+UKzbGEvmfHRLtC3wnv+53C1rdYPMnRMhKSFLvflMDw+yF8c5mJYV96qBC4HA/NpLsrh3HKZWg9JI892pZJNj7jYi/OhrsSeKjfgJ/PKCqvNmcQqmE3Q4un0aMo0BRSK9Ngt21DPm9ld5Kqu4JolWra75YoKt26angu2yh9jyXusemnC9R4xbrv/3iH5Wh6g3UfUdphid122YkVxo/Z6DqZ3PVD5KaJQgd6ynEck8KKwRPleTlHyin2+Th236dKRw76tee+D15hbaiVVGyfc2vYYuSkgo79O9j9euMxubndyM0Ny4zGSzEBwwdy4S8uEZeaOvz2ym0Gdt2FWaIjcv8hvZ7ZUO0/MN5NP+ANxcMLiodBH3SLKns8+rFHpcaXQAU0ZXtcu22Ft7KZvr4+1dRgX1ZAMaSHhojLpCtA1GjvtSR1Rbv+Jg7tUARmmN+NN1VblDyKEMGXmqMlx3tzU8xJFaVOPLknpmMjP3OlLHfbnO1vlblCM1duzyk0uYXBp5Czn5xjk3k56Fozr45GL/fQQnMKNkoDZ1w/n31+gNGZlhuTGSspHraSXqZNk2wtMlZqHBoTidGRmrYlUTW6keKoULf8AeDNT53UGNfSmOGqBop6i/KxQVkM+lCp2yHURi7sQ2Ipm2KNomxQlDWasuiiM+P3sumiM9gu25RbMY7GLLHDXknbuOvATpY6aWm698WKjNyxqqkLHLZLYAtfYOBMu4Qu8qEU0zH7i217yi+pU6JAp4jMJXJNv0pjhbLn2DcC+09xTIQmJ7V0rLB4eBGeWLos5/uYkhfZt96XThnNxROhkMTo4yMhbkxmPBaBcSCYYnIWhajrcbrIk4U2CvI6ilTowt3xDtWjcca35gAaYNEAZXNA8fC7b1klne0NIlOaY1p2d882/dVUrbA0ZYM3ef29TJR4qxvstxWw2qGo29cmu/CiMsuuAFkkR/rMEXmpu/dH5kkEa4eqTTFtq+F1hyIzs88DqVRNaL3UNh8dyThHKiv3GK5FGi6Zj34P7S/5XKxUxKIzqef3nvfcKS2vtidnGxlakYmJjSc03jqx552E8DGWM71f77lqNc3ojECZ6QkVBjspJisyGe+ivnO0beUj8jBONZXTFNOws1ZetmpYA9hh/HlWLaDu6raORtJOcksBe6uDkSx0qaZhd20UqW9BVDbj96e+A7YV3la7VmiA9tYLZVsULLdZGHemN8iNNAuXY9greZF0lwjNfrdsn6O+G4sMMH4PAhGrMLHUzJzoxjlkIbemYM7zXGM0hifq07nE1lQhcoqHc2pp9LLc+bkRGr1+6BhzIlG20NceBzCVGH4fPG5cZmIFv47QeKklbzroILp3mSG9dI/NJNUUxBOZnRpK9qhR87fD8tUOqHZbVA97oBoiM1ocnlD10RCRLC1f+7Lq7la9HB8XAKATmrLGHmg79CtrNHUrQe3dtccCo1NP7aEXE4mRCE1/LHWBNy/FdHQ0xsPLpev5oenYAZyryNjj2BqXc9UEnbJP8jnkSnJudCZHPI5dZp87dUy6/iS1P/38dt4cUq1jbUumGKF1Yt8l7/VeU7r1fNyQzMReauhDFpg/u15mHN2wxbD7rrs6nWqydTMjtNCIsPxR45JyqjBOQ0mUZgs8bN/Q/J/n/k7XOjK0R4VX3HcRmmV3rO16O1RYFjvsyyV+SKrJHhvuAJR42wJvAA5lgx9l098aoVm1raR07YxITYlmKi5KaBqU2O+WbasqrxWTJzTZghMrAIwJzJyIzVdc5M8Zjbn0k+Rtnsjnca7C3di+Q5Jy6nPnCI33nN5+7PJjxCZUmhBaLvv3in3tccVgdMbyjWXmmC+MLf6V8XIy6q4WQdeltNOhguCpxLStiDpt0BdmKzJ/uodc0CsMEiPyswXw0K7/j/KA+n+eVU3KEmusscfz0GKoSzUtse/6oulqW2xndZPC2zuglIhWiTe0zbmlQHhfLrFc7SdS0+5q2tqpQdn2LlwX2G2XXeHv3fCazlYvA4QjMnqZXX5sE+jUifPY4sIcvpvIkHyOKcg9RkQ+Qpy84wLSUiPbeuvMEZm5/ZeFpC4kNClCx3jbgvONZSaFJy6aSMjFq5uJ7cpgpUVSTDsscT+6oBd2Q3QLh4eklv6oh1zcVxgXBj+obR4AVMBTtcX+57Mqum3FZo01XrHpIzZDb8FdL8FdpOWtfEff0ksLxOh7JS3BFnirS2zrAj/KZiQ1u6Ia3SzTCoxIkPQyjG01lhhbN2NJlb5E0c039Q6Ojc7Mfe5zc8wJb85x3O4J9bo4po7GFtum+nfJ3dfcfXiRnznR0NzvcyrSEpIYb34s1XTK95zRSOCmZUaIfcDq6TqhtIa+oPbDO+y3FfarPfbVsheCtsh3qfrcbRVCd6g3HIEjNCIoIjISnfgD4AXjuhk5ZonSiMhsAZRtj8G/yr/RPOhm0ENrIr9gt8CuWKJaLXEo74cIjPfWWbZ3QL3C2+qAbV1gsdqjrgtUqz2asmhvbqkEBsBYYoD23lBb07dMiGhN052ZIdLi9WQqy3M4R4rp3F/NjxaYY56HJ+DLICYSOTUtdl0v1SSfCytCufvwxMt+1uxzaXJTTXb+OaJTQipyEjv+Y/Z3W9ywzMTa+QfyFFkCo4Zb4G3b9lK7rAZxkbsh6RoZaQZtewl2EZGx0RlbBCzLJSLTqHG5FUMFrCrgafWMXTEcg0RodDNxW7i8X1VYPL7isFWiJ0PvbRRvWAHYLoCyxKEu0Kz2aOoSRVmj7CI2AKYCAwB1gUmtjH5fhFhxtjxqYNxHhe2vwvZhYXv+/MhfRPbFnLqPORz7migyt8HcC3qs5ZFuWhz6IaGFwit2FeznL6cwPxVd1fP1MaSk7BS0vHkwEuPxjWUmVH0fWterOj+ox2IqMSs1rgRm/FiMojNeXy5LVTMSLfoNydQO43STTrlIFEennKpu3ap7rICn1QG7f730txDYmQLcca/AnewUS+xXSxxWh1YwJJ0kx2uPXeiiQijv+tTTW1mjWe279kxGXgD0vQ3XKqVlHx6exKzUe4QSwH3ioO1JTAuN5SMKf3PE5tRfaKeeHD9SZPjr8+M5NjoTi6TMic7k7Efw6mVi5KaZQvv8jObsFJRT+cYyk4MWFysx+mJVDwMrElZkHKnR0RkvxSQRGhEIHaFxD1kkRUdkXtRz/sEgLrpjPRGZVbedusDfFcBT9YL9g+ll1ylYlvjSBmssV3v8WO3xVjeteGzNF9++Z26UZJCa8bYJOQpJTCgSY4Wmluew+aeDmj62+efohcxY9zP3dY6T59zj4Qn7cpkTcTklOqNTRjYqGhMaIfSljy2fG6Xxjt1GiOzrn/OeMEV0bm5cZiyhD+gB7R2W76ZRmYTITKMzVZdk2k/kwHZiN0E++1ZQRGx0EbBdR8RHojJyra6Gx2oF/Cp/o67GN7vUTaWlufYGa6yxwb5YYrO6R1OXbQujshzeJ+2CEo3xhAZQUoPwdzwUOPF+tOUITf9cd/BPrJ4xWZG5tl9U5zzWzxAZnvA/l9AFOSedkhud8YQgJyKjscd4rMB468UioR+RVvK4tvPK10OZmdTO6Ga5B4wvaCrVJEJjW9QEHhKd2VVtXGbd9/bbtmDSveHa2x+M8CTFppp2Zj2RGDlmkRklMlJD81C9YWeaa0uKqe1QT/qh2WCD+3Zqtcdui+F+TbU60XiRGS00cMZziPX6O0doeuwvv0NgRftEqYvtV1+Mr7E1FPlaciMMnqzE9hUaj0VkEHiOUyOjuaITig5565Cv5Mb+AvqD6V2gYm9HpG4mIjE6OtPUZXun5+JV9fb72t+byXbvPxKZUDRGC42kmP5g6DBPR2P0UMuMidL8s9xi/6+hufZwK4GxyLTNt9dYVlLpAmzrYojO6LdXS4cWGjhDBKY1OakmPR5LN229Fb3ojBe1uzQ++pg+U2QoTZfFMdEZb9uYrNiITCpKE6vlsceVWsdbd076KwYjLR/NjcmMxrs46TSDRGUOalylmrTQhMbN47BdYr9dYv8wpJXGQ0diYofvRWZEaOT7VmAsXSIt9qKuxOauBH5VbXNtEaxBXjaj8Wc8tXfg7m5A+UPfgFIfp5YOKxqhebHpmMjo9ZI1M2p/o3RTDeDVedIYOScrb51TCgw/8gR5jqjSZ4oM+To80fEu+rH6kxzJ8Z43xLECQ66RG5YZwG+ered7QhNINcXG+0eF3XaP3UN7PybpJE+Gtk+XCbaoV/c3o6Iy79v2xpK9zJTdOjKuhcZJNdnm2ntU2OC+H66x7voI3mPdCY3cgLLtSE+Jon6vhFhKOkd0rMhYqfFSSZ7ITGpnENlIk3uhzfl1GJv/mZxbHk55TRSZrye3dmZOIWxMbuZIzlxyv4exY50bjTlH6ilWM0Qs31xmvA+eF/4UQhXzcrVUqSZd0BqKxmihqQFs79rO4JoC+6LqWjGV0Lc6GN/HKdLPjH0owXndAYsGKOs2ytIv0+N1d3x/APyNkch4zbU3WPcy84o1nvCMV6w7udm0x9VFZ9qbQC7GaZxQYMPOi50DdHpKi0xo3ZyHFptRdMZLNemda/QvyYNZz36+ck6kn9V/zUdBkfnehL6kKREJFQN7QgNnO03q0jVHYEKfuTnHnpKdWJGxXa5/QHtcwo+fy+Oby8xcvFzpAW0/JJFUk47SeKJhroVNXUB7SqEWFseczHUEBm2vvhP0rZREfkS0TGRH5OauGpprtzKz7KVGbn0gdTQST8IDUJQNngG8lWu4fc/Ezh3yevTQjtvXXjtDu44d98RmtJ3++9udhPqhkIhUSmhyOGd9zmcJwlcfI0/y5yW36DUVrfDkJyQBuSmm3M9LSiJS5KTKciJItdrWG5d1jils5uceoMxEEInxhMakmnJEpnv0ncF9FN3uS/00OiqjsZ3tPWAoHv4bo3TTplirImA7VDIDoKwa4BdaoakfhmOwAQ4rNzFhyUELjUzH5MVbVtsN9QHIgdp+Zyy2P42cepsUdp+53IrIkMsgJjm5EuClnXIjmvI83nGFiO3bO4YcgfGiSiGR0eUMc4VGc9vfI8pMlJDQmHqQOY8ZFBP76PAuyHqxEpn3uks12ee29TYiNJJqWqGP0LTppt9966ZXrLHDsh+KzIyoMAgNHvz3ITSM4X1i9euLRWa8ofs+3qkVdKrJi87EfjHqX5rnEBq7zxSXLjLnPD7+Ov1atLjMSTfFamW8FFPskpUThZn7OZkjVbG0mD6eWHQGZv65ROf7Q5np8T4kIi8iNPIIpJpyJKarKWmqcYRGSn+PIiE37+r57/QCnV7SEiMpqwKjdNPm4b6Tmfs+zSQd6nnUVYHmscBLXQD1Kvy+HCF6EzfwhCa3bsZNNdlWTaHojPeL7JKE5iO5BIkBLuO9+K7kpprmLIsJDcw8+6PhmM9O7POR+uyEokQ5HfzZefJcqehMDrcdhfGgzLg1EYL+oInYyAVp4V+cUxds1WV/qMD3aKnJ4LAbxhe6SfcLxjKzwnCB79JN/1z9xr4YbkA5TTM5x91lmV76G0QqCezvjxQg9X3NFRoEhqF0Uw1ngf2cWKnxfj3qdWKfM825TlIffbKjyNwOuS147Ho50Re7jt0OyJP3VOo3Nj+GFyVKpcg8qdEiE5MaPR2K2tjjy5n3/blxmQn9ejhgfEGq1byFGp8Znek+l/XMupmizpAbiaSUXVoJ44iM7OKg5i0kMiMtmKReRhUTo0ArJE66SRcAC1bQGpRoHkrUdYHtyxrQUlNi3DePlT/7p8m5Duo/2xyBmURnvFSTPhArLF4KSttVbjRF7+dSf30dc7L8iNdymyfty8J+UUPikys0gB+lkfkh5l7Ucz+PNkqUE1EC/HOGPqZQdCb0Orx9Ec0NyEzqy2XnAcNFSCIygB9mmRGd6Te96/YQuQdTCG/VMjBfoUWmrofx+wq400XAknKy++9kxqab9O0OBHtjSrmnU/PYzZd7OInU6PtdBV+AOhb7PbZOAbOulRY97j1GyAlJ0ky2ANCO6+1OERr7Ijxi+/rIkx1F5jbJjc7YdWPykprnbX8sp34GY0ID5ElNKjoDs9wes339dpvb5gZkJpdUlEYeZrqeXzvT2LtDK8o5KSZPYiSqIlEgIzIHNf91B6yl8NdKkX7Jncx46aZQZGZyt+2iAB7bZum7ssZ+W7V32kYFbO/GtxWwgQ8vfWTxpCYmMqHxSaoJGJ/EEBnXB3CuupaY0HwmTCuRkNB4585jhQZIp22g1gsR+9zlfoZidTw5x2t/6MSiMzJMCcwlnAsuE8oMgPAFqYQfvdHFoZnRGZhxg5UYPd2UBdDeJECvMKZw5skRK5GpmyGYWW676EyBIb3k7dv0EizppqHH4nYDfSsGGd/pu4EXwK6oUJQNyrLBblt1X9VOaLy0UmmGKWyWpjTLYtEZPb9PNckMr48JYHqy9SIxdp4ldXL9KqH5yl/CFkrMZfCRQgPEJcF7ztxjPoZQHY/XHw4wPt7UscREJietxO+D5QZkJhQaXZhxfcGS8QXaNNPCPO7VfAy958YepYy/oyjrXlbktpKCvp1BfxftogCqQ7v9A4YbTG7VuL7xJNrpuy2waIBF3dbPvO5aqSnrbn7or2/3V2Nouv0C3D0A64cNNtVwjya5x5S9JYOk02oUKNFgiT32xRLFQ3vrg11ZY182eCuXQLkYR2fkoa/lMbHJEUlvOiic72qmHh7DsSmjz+Ycx3FOkbmU94UMzBGaFHabY1JMoXKBc5BbmOz9qPGOw5MYb35sPLTP2+aby4z3hbM/1UMiU6IVFvvzXc/DuNVPLyzwRaYEUNYoyz75Euzxt0HZ16XsUeFQbbF4wLjH3keM75wtF2LpQ6ZA39PvXdk1MC/RC0NZdsXCZf+kw0NLjO5Ur3vu9Z83rKvXkciMRazsh/LoZQZLLLHHprofojRlg0PZAGXlp+40ntDY8VMfox0B+SeWr+aYE9s5X8+5TqyX/B6T/BqaVHTGzveWAfFLVe5nZe5nM6eGx3bREGrdqKdTIlM7y7z1PG73e/ONZcZ+0UqzzEshWbmx0Zi1Gu9au3ji4slNLzRtRMKKjG7WrFVn17UYen1cYPFHRWe2GEuHHgda6agw3GBy1911SJZvu8iM9wnQIqPlSd3y4G7VRmf2VVs3UzsS076W4QkK1K3EqNtqvhZNH6XZoPsq6hZPXmGwJzd2fK64eNLUEyu0O9fJY2505tTnPfdJj9EYAhyXbpL5MMvgLNccWzOTixcZiqWVvO+wVwDsiUquvLCGJsQ3lhlN7GVagdEio61ER2sCIhNNL7XjPzqZaZ95uL2kMMjA8Nijwq6ocHg4tM2pRTREYKzYAIOA2JoQtGmmssT4lgeCRHQ8oZHoTDdc/3nDrtrgqWvR5N0wU7+OAjVeu6lX9boLNJAa4lFx8LYCVkZojkkzzRWcHqku0tNfwaVHO85xfBSY6+PYdFNIaLxtYxGgz7iQe5KVEjDByk2sGNjO86I1JMY3lZlY+FMvS4mMrZuRFBNM6ghjaYlEa4qyQVGE00tCjaGAdi+P1Q8sHt5asXjAWDiszJSYtnbqlt11r+RO1rGfAqmXAYbojhaaRwzRmWqL/cOrkphpEbBMF6O1hh6P+/qhqj3AUXGwFhorMraWRr/OY9JKI5l5x5iQPaUINdNOrXsMoX1/pCicepKlxHxPUlKSEhpgKgm5TcMtx37GYserl9vvQKzOJyYxoW1CfFSU+Dr5pjKjseklmed9MazI6KiMCI2KyliBSYrNAdVqhwK1ufSPWzJJhGOHCvd47VNNu2qJpdTOhKIJMi7NrfVboC7ad/pY9ba6aXeJaarJ9Ba8qoD1aoOmKMwrGkuNMFTWrLvpBpu+L592v/tyaOrdC015l3aB0PuRG6kJ7swL7cZOHKkD/UwuVWRu+8R7G8wVGkSW63U+i1zJEmIpMb1Puz89PzYvlu4m31xmPJGBmafFBWZaS4xT9JtKL9mamS7FVGE/ERkZTjudW/ZFwK9Yo3xsUDYH3NkLsReZ+aNeql4mRb5emqlR623R19u4N6LsIjtP5QH4+Yy68AVGjy+xHEVnXr1+deR2Ch2HugDKxbQeKVds5kZoXCT0y4twy7EnU75/34tUIfAcoZHlwFQMvupSFZIs4RjZCkViUqknb/u5z/19+YYykwpFWnnR87zIjB7ehQt7M2pmfoyKf6ciI9joRt2lm5ZYoijWKB6e8VC/jUXGyswfTNESIxGYSDoKwLh34ArjG1OW7by7FXBfHFD/fHabZI8jM2vnwByKtn+doqzRrPZ4q8s2OhPiWHlxZeYdQ72M7fPhVM6RaoqFrz8aRmOIJbdlU2h9KzSyDpAX7Tg33mXRFvoKseOyYe/Q/o6RG2L5hjLjodNLGp1u0oITqpmBLzKp9FIJYHXAcrXr62X0wzKWmKprzrzvi2aX1RLFwxYrLTIh9IXaNt/21rXTBYb0ko7O/OmWVe3rWwBYl1s0Dy9mF/bu4P4NKXW/Ov37UHVFxHWJt7IeojM5x33WqMypWEH5rDTUpbRYosR8f1KFup6sxFI4ej1E9v0R2M956JiE3ALlWEukOXJj4fcL+NYyo8XETttC33U31IW+9wD+AeCpW4628HXVDeXxyzzc+e9YPL5i/fiKNTbdLRr3qu3P0LBZGPpoGe5OPVzuG5SrBkV9wEJ7QSj66S235w4dodHzdPTGFgub5yhqTORMt2/SsaYK+/5u2yUaNV2jQKGari+7HTVwTxr2NdVm/KiojJ6pozN2mMuc5pTHnJg+UoxO3TdPtATIExpZD4F1PT5DclKprrmf8dwoTQ78fgnfVGZiIiNDuUqvMbRSulePNSYi8wtjkfFkZvJ4x+LXM55+PWNdbLBG+1iOqmH28O7JpCM0e1RKfNYoigZ43LT1M8Bp1xx5ezyZqTDcm2mlph/UdPfYr350N5YsRxGWXERs9nZ+WeMgUhMiJDHevBrTu3WPsCkmERhvPW9Z7I8x99dV6tddiFNPcucQJJ5ob4u56abYNp78hLY/hmMkKCU1udsLqfMG00tz+EYyE/pw2oiMzBNhEXkp0UZiRHCUyPzCWGa8ofs4YPXrGevHVzwVz1hjg3tscA+J0OywxG4UsdBYkRleUdsCqOg6nFvjrS0IBo77zOfIjDweMblXE1bAoWprXMYxmFLFnKbVxl66abJOGVnHvmZveJaojH7CkMCE+oPIOdmeIjLeH/zYE/w5T5gUGWIJCcqpQnMMoRqYHGwI/JjvzRyRyd3HbfNNZCbWUskb9zrF+wemkRmM00ep8dFji9XjBk8/X/CE5z4iIyIj95seoi22VVOJBk0/3HW9ymlV2PSdzW3agmBhzl/VNt9un2SYZ2VGCYyWmqZsbyKpxcXWwHiE+tuR5us2ShPk2PTS1u7EK/wNpZes1Mz95eWtk7uv0P7m8hG/+HiSvV3mtm5KbXdqNCSHc4nN3OcKbX/KeeN2+SYyo7EvyZMa3ezaPv6B/gP9C/npJLXOj19/sH7c4PHhuROZ1z61JCKj50lTbS/VtDfplSHxpFoFVQB+brAu3xBp7xMnFJ2R2yeIyDhSc6iA/WoB6a1YWl8NUpMSGnuLynTEpse6xRypGYmMtGB6xbRWxkqLlZpQCip0oLHtQuvmrpNzkvvIkDVPsuSYdFNqO+8z+xGXr48qOD42qkqRyeUbyEyq/xhvnu1HxhGZmMT85cx7RF8fs37c4KlqReYJz7jHa5dS2vfDpZq2PeICQ4pJxiUyE6SL0Kzx1naINwevqFekRmRGhhKNUeMSlRGBsbcy+HBs3YuVGj3P1ssAGKeXgGlURqeQrNToJ/Xm22Whdease6zIfHTenSdZIsxt3aS3Q2Rbu58Qp17avM9y6phyP/+55wOKzByuXGZyPvChPmPu1bjUznT7C7VY+isw/gvAY1sf8/TzBWts+tTSYxeZEYkp0EzEJhSVGSIa7V2mh1RT7W+jhSb0dtiLfKh1kpYZLTSPGDU711EZWy/TvgYbc/lAuZmbYhptaFstHcyKWmT0eKh2JsZnisxHC0zsucltc6zQyLaIbJ/Cfu7Pcan7iKL6r/zefi+uXGZyiHWCZ1NM8EXGRmj+wkhmvLSSyIxEZqQGpJWXetQLcOUUAU8v+kt3vu3HpakKNOUGTzhMhSb3h4zepU4z6VSTasEkUZlWz4YIjT22FLpn4H68aFCWDX6UDd7K96HjvNxMTFJmJL0kMzdq3AoO4ItMbabtQWjm1L58VsulU6DIkGPJqYc5V9pHf8c+67IXO0kdIzH8rsW4YpkJfbi9WxTY5Z7Q3E1Fxks1/aUeptl1Ky4bPOFFicwGayUztr+V0kwDraC0Zb+6kHaIwth0lB5vsGll5KcRmjkRWT2t5UULTQVsH4BdNTQw11EZ3ZopJxoz6lMml7mRGFdmpEbmFXFJscLyFSIzN+zM2hjy1eTUz6SiNLIfy6lRm3Nf/nK+b8dGYvh9S3HFMuPhvRwbjdHzJLWk0ktaaH7FHu94/L//wf2DjcJsJtGZe7wCGFrotOP6dgbDfEvTdSAnQrDrOpqTfejhSBoKoHh4HjfbTr1NatseG5l5APCzFZnNw6orY15jjyU2WJuKoKUrNrobveF1+h/FpilQ1wXe6gKo7+Lf+ZCwRKMyOgJjC35tqimUWvIO5BQuWWR4UiVzyRUaYN4l6VTBOVe05hSJydme37kcvpHMeOEFWwAcSjVhLDKxXn7/AvBXKzK/Hn4HozFaaJbYdUc0vRfTnJY7rdjUo2ktC9KEu4/0VGtMioJ1j776bbNDWwDcPazESGPzHapeaFqZmU5vcI/9aLgerSPDUbqqLtDUJVAX54nETGpl7MxQxCUmGKFCYM0pgjJXZD4KnlTJR5MTpYlxbFrqmOfN/cHwVdHT2+JKZSb1QfVaMQFjkZH0Une/H30vpVDNTFcns/rrf/Hr4Tf+jf/2AiO1MV6UpsrvLQXAOMIytGgqg+IjUQ5dINzTFQUvizcs5F5K8tDiYmVH5q+A9wdg8/ADu2o5kRgZ36uyZi0zcyRH5vf7bCrstksctktgu2hbIclDS4o3z0Zl4CwbtVTyUkkxifCWfWTKaA4fcYKkyJBTmNNc+xxpoGOah+cKzZzvF783n8WVyowl9DJsR3kyT0dpMMiMlphAumnx1//Dr5+/8QvtQ0RGF/taobEy49WQaGmRdWyKqT3U8f2bir7Qtm3xJD0Gj56vKrAuN2jKA1byWh/U22Gk5r0LhDRl21JpV1SuvOh5Tde3jJaamNzYdcZCtMS+qbDfLrHfVsC2CgtMSGKAsNRMiAmNXR7aNrVvzZyT4SVEZXhCJl/BV0RpYs8590cCvzefyTeRGY0WGD0d6mMG07tch1JMv7b49e/f+CdEZv4XT3hxhGacdlpiPyrm1UMtMe28vWrKXLpRGo1uBl04ItOvUxRYPuyxX+2w3B5Q7bp9dtLSlD/QlAXqYnheaZUkwrLDclIjY6Myu65OZt9tK/IikpUjNju0IrPbVnjbLoHt3SAwqTQSEssn9TJeqskb91JJqfTSnJPZqdLzUfCETM7FsdES4HOjNFpoPvI7eAnf7+/DN5CZWDXrwgx1778l+hZMtlZGhr/U8Nc7fv7VRmIkKvMX/osnPPd9yXhC84Rn1dx6XPiqhWXcH0vZx2SSneVhEKMd0LeC0rU0rSS02rAvligeGiwfdqPnFtmwx2GlxatzkcjMIEDlSGDsfC0x03kV9rtWZPr0khaZlNDEUk0j6dFC4jW71uM5NTGWU/qSiT3PRxcfz3kuQuZyjNAA48/1MZetY4TmFPjd+WyuUGZiH0i9zBMaLTX3w2iq+PcRbRPs6hm/8L+9zOg0k5di0mkmryXPLiAPDZpeBCrsJkJjU0+aVoREntr9tvc4qrDEEq+Qvm7uTQRmEBAbWUkV7uroi+zTTqciNv283VLVyVTjVNKxQuMFXwCkJQYIi0xMcE4VmUvgUo+LXD/HCo1wrNicq8+a3Ochn8kVyowmdfi2qlWnmkxzbC/F1AvNAU+/RFpeuqjMf/DvLjLj1cz085pnFHXTp3Dkwl2j6NNPWmwK1NijQtHJihYaT2B0k2ZdYyNSI/MKNNhhOYoD6TqXUFTFFulKqmkYVr2g2EiTFSM9bSWmaQo0dYHdqE5GpZeOERoE5o06yotJTCj1dC6RCfHVURmejMlHc6rQCMeIzbme+xSYYjo3Vy4zGptS0vO9AuBy8BsrNKb4d/XrGU/Fs6qTeY7WzPRppz8vWP1Bm/+p3vBeHrCrttivFp3Y7Pu6FJENoZWYfT/eqoDtm0XkpezlRepqyn6p1NNM+7jRUmGlxivktfUxetqm0GJyMxI4JTFNXaKpC7y9rFuRsRLjFQEfHZXRC2ISYzc+p8ics06GIkOujXNLxZz6mo8SGn5/vopvJDMaKzB2vqmXsammUYrpgKefL31KSaeYvJqZJzxj3Wzw9PcBd38A/I32orsC7ipgtQJW1QGH6oD9ao9ltexkZZyEAlqJaWtgmn5osXIzjOu+bIYLnW0NFS7K9Ytzx53jDetaiWnHvfszlb3ANHXbIV4vMXUxRGR0a6VT0krJehmocRuJsU23YZZrziEyMT76JMmTMPkKPiL1U+NrhCb3O8SozEdwxTKjDz32gSydR2TR5CGX4RpL7NHeX2mHZXc/Jbmkr7Hp59+/dCIjjxpAMx4uaqBs3lDUW5Sr7h5E6pLfHt6QEhL8Fk3TP2NIcnQjcS+F5KWWciTHa50FtD34AkBTD0MRmMN2ibYzvBJ97746CpPzeEksD6aYbK+/UOP2pHRukQnxlSe5BSg05Os4t9R8ldDkUGLed53fzRyuWGY8cl+O+vBqv3Fk5kcpSZymF5rKSI2ITYU9qt0eix2GC63IjK7h7S6udw2wqoGiPqBYNSiqeiQvw72m635aRztkWHYFw4Kto9FDLRs2yuLVxcQkRxftiqwAQN2LizqObl57WwJHYGz6KCYnKYmxtTUyrt/80cNLLSEw/xwiwxMTIT7nlIvvJjQAzx1hrkxmvA+cnRcq9tUP1Vme3cRML1fDHa115ERLzUhstm9tjYw8tt1QR2fK8fSiBha7NxQPQ5QGwCTtNBT3Tpt2y7Iawy0PQrUqttm1bmJtm2IHU1Bdq6O99AWjqZ2PVa3udp1KCcUExYvKpMQmGpXR0Rg7HL2A6Ws6W0TmHMw9OXrwFyC5BM4ZpfksoTnme3NMXzb8joa4MpkRYn3LzNxN4lGWTS8vVmhkvojNKCojEiORGf2onHllW7IjURpUgJd2GgtMqzAyTxiiL+MO+Wxrpen9laa3K/AEpk8R6fqWGLYUJSQxMp0jMqk0UzIqE4rG5HJJInNOeLIkl8K5pOaSIzTAfKlhlMbjSmVGSPU54xlKF53RURgEVi2BwtTM9OLSRWIqlWrqozJSK/OCITqzwjg6Y6e745BaGmDjpp1sfzAiNTvz6kVuZA+hQt4N1njFvTv+jKdxBMYW6GphyCUmMV50xoqMnrYiY6ejURn9xF5Uxs7TnCIyPAERMo9zSM0coZn7XOf6Th8jNTyfCFcuM4LuIM9rxWTXDaSZJvPeUZRNn07SERmduJlEZXSa6U83r0EbkRF5ke+WkRmppVljXBwMoG9OLX3RjDvX2/dCo+tkhh6Ap33E6AjMM5766Wc84XW3xuZljcPLfZs28gRGT9s/RYjaGcZSTTGxyXkAaEXmVT1SaabQCSJ24riEiMw5Uk0AT5LkMjlVanKFRp7LPs9nfSfmfI/5XRW+iczEsPUyHV4Dp0lkpkZRWHmRKI3cErFr1aSjMnIhlXGpmbEpptKMA3205q4GVg9+2qlBiRLtrQ5053pDPzLjlkwiNPZGka3A3OOlE5lnPOG5ecLm5R7b30/Ay6KNdnhC4aZxMP1E2WlPZuww9jye1IRSTQDG92ASWXl15tmTh51/jrTSKSedzz5p8SRJLpVTpGau0HwVx9TT3DZXLDP2RpKhPmW8eXfJ9FK7bJAXLTQ2UlM1u2lU5o8Z6qiMSIxONYl/mPSLl3bSnevtVF80hWnVpONI+o7UryqN9IzHPjLzvHvC8++nttO633fAb8RbCemh/lPoce8TZgXGzks9V3ZERnaoIzJaYGwUxqag9PzQi8iBUkDI+TlWauYIzVeTE6XhDw/gev6iitQhl5heVe08Z9XA+LhZ9q4vBO7TS9hjuT20nzdb/KtrZnRURoYiMXr4oJYB0bSTh9yoAP3mw92v5b5KWmT6mzT8ecLL7yfg96o97v9gLDOeVIRkJjTUxIQmR2ZiUtMjUZiDM27zW57YeCeRub+UrrkjLZ4kyTVwjNRQaL4b1/LXhP9BtVEZT2Jk+QLGUpKPH+U4xSSMi3IbFCIfMtTjIjayuS0AlrqZQi17UONVu/3dtk07VbsDltUBxUPd3w5B90Uj7PsozPjYGxOt2aPCvqmwk/shvaCVmBf18OTFDmMiE0o1edOp50nV0fRoefEkxtbLyImgxlRqvINO8VUnlnPVzRBybcxtjfTdhOa2uZa/pGHhjKuWSm6/MjLe3S17heFCGxgvyrHAlI40NCjQlJlfIb3pFtMCYF0cLKmplRr/A9w9tLdEqHZb7KotNg8rVNhjg/tuuO6Pcehyr+3czkaWbO/Ck75gctJLOTKjh/Z9yInM5MpMjycynsR4IpM6yBRf/euIJzxyy1BobpUr+SuGIi/euF1fRKZEKzJ3vsQ4j6Kspxf8FNO7DbR40QgtMrpIWOpuOoHppabsph+6SI2SmuXDDvtOWKquXZM0394NCTFU3fIN7idi1h+XfeQU5ObKTMoXUs8bEq0eXQujRcaTGmAqMjlFvx4fLTFfLUmEfFe+UmjmtpiKCc1tp5quQGa8Il49Xppx/ZBIjAy7db1IjCM2ZTkU+Vp0T7wAWokJvZvyXZHPoKSVbK2JjshI5KZyhkp2hvTTFpuHPYpqKASWNNIr7idRmcnrqYupSOQU/9qamWNkJifNFJObnlBERo/b9JI+gLnppXOdOC7119btnhjJrfFZQvMVnfLdBhcuM6E/vBeJsfUxOr0kw7th8xWmAjOSnLaPmaG5sx+dadNMPwC8xV9KrE5kp4YiKyJHZTcuxybLX9De2bsTm7sKeNi+Yfnwgupxh6YY7oi96XryfcW6b0peBtJm/bF5ApEjM7Gh9/pjaaaU1PTbSvNrKzLSBNv2JZOSGg9e2Am5Do7tyfcjhSb3eHKiK0w3eVy4zMTQAqPnhWpoVFQmJjH9dN3XzAw3ehwLjXcH6yTbwHyJ1khERiRGxv906z1gLD0iFN38xQ4omwN2/3rpWy+tsemiM63e2HqZvnWUFoRUfcopMhOLzngiFY3GSId4IiUpkfE6y7MHFYvYnBuelAi5HD5CaOaK1SnpottNNV2wzMwxWR2BgZlWtTLAVGCCdTNN32EeEI7MZKNTTHraXsv0SyjUuMyXWyWI1OhH16z7DsBT9YLNw30vMs94QtXf3XsXfj1evYzXaugcMhOrmQlFYiZpJSsuNqXkSU2sXsYe0G2eGAi5bo6NzgDTE9pXkJISRmcsFywzFissOes5d8kOiYyRmh+qXqaMiMys6IxureRN65egh8AgNtK6Sd+VWz+6QuJVBTw9PPcd5K2xwTOe+jRT9LXFxCU07R239zpyamaiKSVgGo2pAWwQFxgrMvbJv4PInPPkdm2vnRDLKUIDfL3U3G6U5RguVGZSH0CvHsbKix7vojJeiikgNVIvM/T+Oy0Elps5NmUBlG/xAmAtLnpcT8eQfUtxsIiLfkgfNwDwCKx/bnH/0HaTJ3dnkjRTUGK8R0xkPJnR46EC4JDIeGmmHlsbowXFCsxrt41X+Gvn6wOAWg6z7Nxfl1z54AmNkK/jFKk5teCXQpPLhcrMMXhFvzKOqbwkojPSLNsiEtAcUy+jxWWLoVM9LTOp65sXmbECAAAPwOofwPphuCOTtGaq+uiM07IpJDSe2MQiM6FoTUhmbJopmFLSMuNFXryoDJzpUL0M4IuMt55+YYSQy+PU6IzmqyM1FqaaNJfyV0ngHaZXK6PXDxT+aokJjh+6ZtlNPJIBR2p0rYuHjsaEhAaRcd0fjb7g6+kSbf3MT+Dp5wueHx5x3wnNE57xjKd++hmP7XD1iO3qoW0hFYpghR4536dYeikpMu9qBRtJsULjEZITO55a1+PYiM25T0Ln2h9/BRISZ853/pwyRWJcoMzkppjsPJteUimmWFTGGy8bLFf7SYppVhFwTGqs0EiEBQgXCMv8P5he9O2tFERm/m6jM08PL3jCM146kWkjNa/98Bk7rB9fsX08AKvFWPxW6jg8kdGvN/RavZoZK2LeEMCQWrKRGNsnjCc2tkbG60cGiXk5zBWaOeLxmXJBkSHfkY8Qio9IO3vEvpOMymguUGYssSumFRuvRRPyJaYbX6z2WBbj+pJQ3QwA1EWB9/IglTnTwy8x1LTYVJO+27YsQzv+3n1W6wY4dOOLsntVWl7sfZ8K9DKDfwHr3Qbr6hX3eO0jMmtscK+Fptjhx2qPNyszWgK10NjXF8N+57JFRrC1LnOjMbFITIxj1/3sr9U5TmoUGULmkSs0jM58BlcgMyFCEuOkn7zITDAq845qtUsKDOCkmHLKaORCruVG6l86gRF5qetu2AyXmgWAfwFY6Bta2mhHCeAn2ijO38DD3294+p/nXlzusemlRoYV9liudm2qyb5HoajMsdmVUHpJzwMwRGWsuISiMHa9VCSmTiw/ls9M+VBkCEnzUULxkULD7+UcrkhmYjeXlHm66FelmFxpCU3vUJRN3/pHS40eD5Jzodcpok5kDn98gbGX6BLA4W/gXzWwDtXNlAD+gTYy00nNutngqXjGE56xxq8+zVR199deY4Nqtcd29Q6snNRcrYbyOvXQvr7Ya9fHaudNVrZpolDhbyy9ZJ/cniRSovMVfNaJjCdMQj4HRmg+kguXGe/wdAQmNE/1BByKyAQei9Uey2qol1maVJMg403sLbSLbGqoaw30vgVet63EvDbhtjmvaLv/q9HKz79r4KkG7mwNDdBKzN9ob3vwN/D09wH3/xqKgCXNdK9aOt1XG/y92rV3sJwTmfFep0dIZNz13xFukZRLLEITEptr4quFi5Br4hJkIvcYrvm89DVcmMwc80EzrZZGERqEb1/gRmjeUZT1qD+WVmqGVJMXlWlQoi4CR+9d6OXRANgBr91Dun2zHfLr6V5m0G57qIF/NF3aCRjqZ/5GG535f+343R/g6V/P+K1qZirssMYrltj1UZrV4wZbLTM6MmNrZo5JNdlASVBktGzkpJVi/cnYJ55zkF8B00uEfAyXUAx8jmP46nPU5XFhMpNCp5f0tIzrNFOgFZNNOY2kZodqte8iMjssuzTTkF5KtGYKvZsy30Yjtm2NzOu2FZlX9RCJ0YIjMjNKojRA/XcboVmLIAFDiqkrAsbfwPpfW6wfpBBYFwO/Yok9lth1qSZMReaUeplQjUzw++jVvth0kx16hKIwsajMrZwkKDKEfB0xoeF38xguWGZSh+aFBwK3LwhFZQIpppTExG5v0N8cUoYWEY5u+LprU0vPGITlgLHY6IfIjPaBA9q0U123wRgArcToxx9g9ae9xcHaSMxoutoDK9NEext4L/UjlVrS01FfsOklT1o8gbFRmZTI5BzsV/AZURmeLMktcwnRmY86jtvlx1cfQB5e8a8ddz5IsRqPSKRBS8z4MVxE9NwoqQ708mePXqENjiwAlDKjUEMdFapldjNJoelplI1fOJ3TkZ53oNm8O/NixbqxqMw18hmv5Tu9X4Qcy6V8D7w+s1Jcwo+uy+OCIzPH4KWf1KJYmqSrl4m2VOqwN5dsUKAp2z5goocmD3037MAqQPuxLtFGY4T77rFW4/cA7qv2gQqtdFTquSKpoVLVAkk0arHa47BajdNMEp0JRWpCeG9nLJJDEpzyxl3KCZyQS+DckZFjojP6WMgpXElkJob9MJppe7ENRWf62eNozGxyWvsENgOmfRnfYxAa/dDz1gDuV8CdRE9EaHQkpQDeS4yiSYPIDMMCDYqyHkdmvPFYlMa+5qMjNR9Byji/io8+mfFkSQj5vnz5peU4EgIjhPIy3vyyRllO5SUmNHVOmsl7h5Vg9D36oh1qt9e/waVeRuTlH2r66QFYSFTmAYPQiNSE6nfU69MSV5bNtAB4i3iUxuK9kGRE5g5ff9G95LDRscf11e8pIZfKJUVncvcf43a/61ckM/ZQY0JTjkdjtTKKovSjMqnUU5tm+gEUb/4KBcbpHgcrMsA4vYRuuY3Q3BcqvaQfIiPFMF4X0xSZvD7d9LwoG6DsOs8TYUkVAntoidHTxPCRefLbPbkRkse1FOLyBBrjimQmB3OvplB6Sc/rH15UJvzh8aSg32eqRZNevWgfaPxVJaW0QPuV02mmSXpJp5ms1JRAkziWvnPAsgHKGigX4wiMJzYyDoR/lFzdd/AzozOUDUK+nnMKzUdHZ4jHldfMRD4wNhoTmqcYIjNDIbAdWoJS4x2PjdBEojQlprUz+vEPmKJfSTFVAB4xSE2hHvq19q9reiPNZXfTyUkzdm+YWwj85Xgnqmv4NQYwKkPIrXN1vwg/nYu+/LSkLjjeS7ibLoqlmkrghxOZifYng+FWBjUKNGUBlG+jephjCoC9+dIAeVQIXKiojG7BJA8rTSXQlD9GBcC28LfsOguU6MybLvgNRWZsdGYuF/sd/YzozEfKBkWGkHlcanTmYk+SF8WVRWZil3xhMZ4VKvw1FGWDohhuKJkSmWxCgZvuuO7KcZNuewtNic7cq+l7dIXDtim215qpgpKZcBRJojJ9lKas45GZWHTm4qM01wRPZIR8Huf8EXCO7y6//7l8o0tOqjhFDWdeeK3Y6JtL6vH3so8JxQuPK7SRjC56UpbAejd8jaTw1+vj9h7Avx6A9U+0aaUHtLct+Afa9NI/1PRDN+8BeH8AdkXV3Qu8HY7LnYcoE4C2RVP5DpR38TSdJ4rZrZcsttM8r/vgc5B7YB8ZnWHRLyGXyaVEaCgyc/hGMgN8xMvJidA0KFAXBYDDOL0jLYl2GGpa7H2KAKyLIToTCZ4AAO60wMjwpzPvf7rH/wHe/wf4z78e8V/8G//Bv/Ebv/Abv/C/+IVnPOEZT/2dmlrZWWK3rYDtXStd9l5KsVsS1JFx7xEk1vNv7EnnIqXVIT6zGFhDkSHke8CC4M+A73CAVKd5kpDR1CIlTr0KVhjuyQSMr1U61SRD20uwDLXMeBIjkZl/A/gf4PAv4L8/f+K/+Df+F7/wX/zVy8xv/BPPeMIG67HQNBUO22VYQGLCkhpPom8wedQODDkykhKac0PhIOSy+er+ZxiVmcuFysyZDyuzEFfqZeb2/Nt0VTb9c2ihkboWLQKyey/dZSM7+tgLDKkkGXpi8xPAv4A///6B/1T/7sVFJEbE5hmPfWRGbjW5xxL77RLYVuOoDBAXGQTmx6IzI94DO/Qu/MfKgJYWKzkxoTlndIbpJUJuE0ZoPpIrfGdnHPInvrr+/kyejIjU6GuUjrzIcj2082VfsahMN374B/D756OKwLTpJJ1eGiIzRmiaNTYv6zbFFIvM5ARPTo7OeBdnL7Ql25xKSmi8550DRYaQ6+Gj7q4NXOWl98LhOwpAbjJ5DDrV1JQ/AKjm2SsAf9AKyeQ5u4fU0+wwlRq5FYFulRQq+H0Atj+B54fHkaBokWmHrcCMReYRG9y3j5d7vEmKyauXSeG5RlJ8JCrjpZj0RnMu2Memjj6qhobNsAkhwkdFaT47ZX453LbMnKkJse67pY+iSCRFCoC959WiIlJj76tk5zmRmT8/f+C5esIG973EvHTD8KMVmlfc4xXr9rFbY7+tgO1iKjK5ERqLTVHN9oC5EjOHzyruzT3+S5MkQm6dj7zVQUhoWC9zDLcnMx/4ipuywHv5hjstKwWmkRldSyMSU2OQFrlZ5INa7xEjmXn/CTz/XGBTrFWa6H4kMVLYK8W9z5Nlj92yNZ53T9i8rPH2sm5FRmQmJDUaG40JDYPoqIwepi7UuRdyLS05v1y+ooUTRYaQ24N1NOfidt/FM7/yumueXReHtmWSbckENS1FwNJzrrRyelCPlZnu5h3+Abw+LvBcPBk5ue8jMiIxG6x7iWkLfO97eRnWucfrrq2TObzcD82xtdCkWjHF35hhGCz8/UpCYnKucO1HCQdFhpDP4aNvRKmFhlGZY7kdmSkD4w6pZtmaUYoJmBYBrzBEXWoMUZhGTaObtvKiOsbbPgCbh9VEWGKRl02XQtph2QuMzJPWS69/7tuIzHYJvCyAF7SPVJoJ8L932dGYOXiRm1OwonJMpCVnm49MLxFCvg+M0JwK3z1DSmL0bRmHW1IW2KPCKxqUqwZFfcBC76boN24JpWF0s+uHIQojqSQRES9V1KeLuvE9lqPm1mO5qcYSs10MApMaeo9YOsouG2ELf22KKVYvI/NrZ54dP5bPKKZjeomQy+ejozMAf9ScBmUmE92XjL0NwB5Vpzg1imINPG4AHNoIDTDu+TdGF43ZPrStkrS8iLhMZeZpNF/micDIcIclXndr7LZL7LdVVxtzFxaVnHm1M+4NTxKZ0BBqp/riHgsXCZQBQshcPkNoyLFQZhShWxfUOo3UjUtERqRmh2pITxUYhKbdaIy9ZUH3V/jz8wc2VVhadCpJ17y8mOlWZtrbEuybCpuX+1ZgJAqTEpM5Dxt9ySkYPlpkxn+VloMZXgof1afMpb1OQgj5ei5UZmL5wxpfZcc2ItNGZZZ9aqrQF6cCqH/usC63KOq2lgaQvmjalk/t/ZyGm1WGUkbhAl8buRkKevsIjBUYT1q8CEtIcELSE0otbfU7KMW+c0UGznbAVGROCdN+9j2YGFIm5PpgdOZSuVCZuRwkKqMLffW8tl5mCWBcb9NLz0OJAvWo1qYeCdEwL1TcK6kkLTm2pdJz84TNyz22L2vgZRUXECssNqISSxuFtg9FZ8y7ORaPXJHxojOxCEUq7fTRsAUTId+XSxaa2z1HUGYyaNTbVBsJacebXmi0pOxRYYkdCjTYoxqlpLxxLS+/1R2tQzIjUZjn309tDczLHfAb4eLcWCoop4DX2z67TiYkLAjMD4mM3Q5qvuUcIvMRRcBMLxFy3Vyy0NwmlJlMJHpip4tOSCrssMdyFK1ZYo9dLzltSkrkRY/vsMQeVUBmHic9+rb3T7rH9vfTuDn1C8YyExIVTzi8GpfQNrF9uyLzjnineLEambkic+kXfaaXCPkeUGguiW8mMx9/obBRmbYD331XAFx3cZoCBZp+HgDsUfUCMx4u+2Jdfb8kkRmRmP/FL7z8ecLL76c2jaQFxpOZWITFCktqOCd644rMK8JpJeBjROa7SMOlyxkhhHw930xmPhav7gWQe0S2CrMH+jV066gdKmxwj70aSmd2IjbTm0J2wz+/Won5T0JiQh3eAb6AaGJZmpDAZEVkZGasD5lYignwRcYe8Dku+ucoAo4dx9x9U2QIuWwYnbkUvpHM1Ih+qE64Rtleftt5ZT+vXT5u0VT2022kZtx53bgju1Zw1qO7WbfDJ/z+7y8cfj8B/7kbC0vsEZMZO55iTirKFRkrL3NExkpNqqM8OMst1yII13KchNw6lyI0t33OuAKZ0R+UhLBYQte0GWLTFv/uu/EhKrMERoIzjPstmyY98OJ+Ija66Pd38wvPv59akfndiYw8pJm1FRhPZoC42OSQs5+gyOjITKypdapDvFhHefZAPc7d78t3SWMRQk7nUoTmdrkCmclBS45z0bLXTztvJhKV0QXBNWxPeEMrKCsz3vge1SAzzVMrMv/5x1hi5GHFxUpNSGbseC4xeZnsy+sQD/CjLucSmdALmvNL5Rxycs4UEyGE5HLbURngW8hMIFoTqwE56lnaaIyOygyyMr7Ngawvy0Rm7B2srdg84wnPu1Zk3n4/jAXmPxjSTJ7M2Hnyej35yHkfvJu4JkUw1rOvkCMysULfnA7yzvnF/oqTBE9MhFwfjM58JVcuMzPTTpnYm03q2hgZitTsu9teD0XB4yJhdOvoHntFYF56semmpbXS75UfkfmN/BtCAhnpoHNyyr2WhFihb05LpWMl4NhQFSGEaCg0X8UVyUyOuERSTInrT1mOBaYIbCCd5LXjhSsxuiM8mZZ7K9l7KL1ijU2zHupjXu58gdHj9t5K3njyNb+r8bv4m5PcPlbQm4rEwFlf7xcIR2NOjWCkpIQREkLIpcPzFHBVMmOJGfA7gLv0tWpmIXDTtUzS0yIyeyxRd73+Skd4uqM8787Wz3jC65/7oe+Y35iKix0PSYwVmcn7IS9Yi4H98+v30/toeG+YJzEyPzelZEXGa62UasEU4rOiKKyXIYQAnxudocgIVywzlgOy+gk5pgB2sou2518tMtJXjERibMd4ugM8uZfS8+8nHF7ugd+LsLjYYUhgpOi3fy/0MFRca+XltRtfqHnDq44TkpJjReYczbBP4atOEjw5EXL9MN302XwjmYkw43pn62XGuym6vn0lSlOqNFMx6s1XevzdYI0dlqPbEfRFvi/rcbPrkLzoed4dq/tojPS2K1KSahUk68nHYKHme1/E2MfFa36dKzJ2H3oagXkfCaMshJBLhz98NFciM9ZyJQoTmka6NZOppanrcZGvoO/BZOtopCZm30dlyl5kdGd40oopKjLyCPXq60VlJtEYEZmcSId+T+W90xIj69v3PUZMWuDMs822U9GYaxSJazxmQsjpMDrzmVywzHg1HcBYXGRc0kv6QrkYZiUfd2jqso+w6Lthjzu/G+pm6i5Go/FaPYVfnlN0WyYeK2c/vdDYL413t+dQKk6/1/IFPOYi7NXNeFEae0yeZQJhkfGk6hwnjZSsnbsV1UfthxDyveG5wnLBMpNCt26yLZ26C7IWlq0aOjdMbOpWYLTQ2LSSl4KS5te6X5nxsN2P16le0hdW0+PsvUM/+v3IjHtMWwtpWfGwQgPMl5pckfFaLlmpmSMysfmamPDw5EAIOTeMznwWFyYzqT+8k07qRUaWHcaLYg8lNW91gaYp0BTlJDqjxcYKDhCOyFiBcYVGSEVlYtEZdK8Fd2hF5tVZwV6svfdZS4N5L7OIiYxeJ5Y6ss95zjTNKcJyTFRmrggSQkgKnis8LkxmctECo0VGmwrSMjOSmqKNzhRDJMXKi9yjSeNFYuywFZ9ueR0RGiAuMaFrowRVaqAVmpxfAqkLbe6FWHcVnGo9ZSMsoWWhY/iKL3HsfeBJhRCSgtGZz+DCZSZUN6PRIqOLV1VfM04kZppqKluZqQb5GFos6U7yxv3NyPz2aP1bGei+aYA2CjSidIaeyEhUxqaaZP6ofkY3tdbvVWx6LjoNZeVEDtSTGpte8sZjqSVZ9tEf31OiQozKEEKEcwkNzxUhLlxmNFZadMpJz1MX1Yz00vC4Q10Pzaz1LQsATKbb3U+FZhyR8e6qnSAmNPKavFTTKDoDTCM08h7NuciG1vXqb1JSI9Mx0fGeM5Z28o7vHB/pnPeI6SVCyGfCc0WMK5IZTSgaYzt5c1o0WZFR001djqIpuh+Zwrkvk0yjHx9STjpKo9epQ2mm0ox7EmNrZurAeqOCYL2iU1c0wrsIe78oQuvpZSmRsfvyjumY+plTBCdXQnhSIYTM5ZToDM85Ka5AZlKpJv0BkSv5YZifWzdTA4ftEk1ToC50E+2hXsYr+m2f1Y/A2PGmObJexiv8tREaLTt9uulO7UBWCjW9jqV1cr5IMYkJzU9tEzq+uaQE55yRFEZlCCEhjhEanidyuECZif2xdSRG103opsQzUk2TdJMUAZd99GUoALa1L/s+EtPOM+LiRGmySQmNJzI2gjNJN8mMXJGZKw+xDu9isvLRIhPimH1SZAghpzJHaHieyOUCZcZDR2e8dBIwiIyOzrwD9d1YXkKPFwCPC+y2S2yqe6yxGR2B9DEzDNvnFXGRdJInLn3iqmhQlg0Wqz0OdQE8LvRK4+NZYdrTr4zrdV+6aRnq8WCHel4Td3kvbeswj5w0VUpkci/8sZTWZ7YQoMgQQs5FjtDwPDGHK5EZi9eaxeuArbs4e3KgZeAFwGM73L6s8bra47naYx3pybfo001lfysDf70htlNhh2bV7bMu8Aa0x2dFxkaO7Dx5LVpaSvhy09+7KdSvjFeX4vUeLBwjMh/FZzV5pMgQQsglc6Eyk3uRCv1ql4t0d68hG9FQ8tIPX9BKwssKm9Uay2rap4wgMZjhKOxtEIYeaqQhd4Vdu25R9CmhXdngUDbAtgK2d768hGRGjlfGtdRYoQEcoclpjWOjM8eKTKo2JpRimtOq6NxSkysdFBlCyDHErnM8V8zlQmXGQ9fLpNY7mMdimp4ZpZfGw8PqHq+rHfAw3rNu36TZowqmmMY3RqixxivKokHx0KAoG+zKGs1qj8N22UqNTovZ1ldaZrTE6PSS4P1le6E5oO0pWK8cS+fEmCsyp5ITMTpFbOYc50fU8hBCbgdPaCgyx3DBMnPsH1kiM68A1hjJjE41ibzI+CjascBmtW53Z4RGM6SaxlEZS4kG6CIzjaq9Kaoay2qP/W6JZrVDXW+w31Ztp3p1AWwXY5GRYazVkyc2mi3QioytNQLmf4lyRcbb5pypG4u373Pnp48tkCaEEI2+1vE8cSwXLDNzsP2WiNBs0L7EewB3fvHsClOpWQFvK1UC/ACjK8N4+4y+yExvTLlDgxJF14OwDKtqh1p6Hn58RVMX2G2Xbb83ddHKjURt5DWs1ENLTOptqtHuZxSZAabymLpYzxGZWNHvuSM3Ic65f4oMIeScfFb93/flwmUmN6eorVbXzMjVu4vSxFJNK/u4wxvW2JVtOmhTDRf/IXU0yIru+Venm/Q6RVc/I/KjIzu9EBXt/aHuRW6aAvvtEnW9QVOXbTqqXI0lBoj/JW2z9BfZQG9kRUZ3spfTlHuOyBx7cf9qKfiI5tyEEALwXHEaFy4zwFRo5A+uO4KDmmeFRhUC62LgpMwAwB0O5X0foSkr0Y71KEoTulVBgbqXFh2NaXsTbguM9X2ddNxnJDcPw/R+t8Rz+YS38iFPZmq0kScZ71NW9g7btRrK+xhr1QTki4y3jV3XHvQlcezx8ORECCGfwRXIDDBcWHV/M3q+jAumNRMW3XjZXsQ9efHkBgDKBQ4AdmWNoly3fcWgwR7LSasmD708tK53x+2R0Kjp12qN4q8Gz2WDQ/mEtlM8TP+S2k3kIa9T6oOghUb346Pf7xQ5IuOlklLrXgKUGEIIuQauRGY8dAQBatymmA4YamdUuikUmbG97ZYAsMC2bAuCi5+287xm0qFeOz5cCEuMU02aWKpqXKHTLquwR1E0wC/gGcABSmhsOkkE5lGNi9D02SPdQ3BtxodXcHwT5LlCMPe5PgJKDCGEXBNXKjNeLY2Wm3uM6z2kbmYxbGtFRoalGo5YtQ2BAODnMFdiJqUjNeOozHRZ6UhQKCLTLhsiOE1RoHks8Pd2CaxWfprMyosWNWkdBZiF8v5603rcaw0FZ15o3dg+vkpozt2CihBCyGdwRTKTEhidFtHTkkKRyAzQy4wWGS0x+l0ZXd+mQrPErtcOGddYodHiUyhdKVVhsI3G2NZSNQrsUGFZ7bF63GArzbhjQhOSm+zojCc0+r2OSc4xcuJtk6rhOZZTxYkiQwghX8kVyYzgCYydlvSSlRjhHqiV0HhNm4PvzFhopKh3iR0aFFhijwI1qq7AV2RGS0u7jp2uRyLTvlJfbhoUfUd9zWPR9k3zuBiKe0PNtr2HG505OOPAuLddO54SGjjzUut6nFNozhH9ocgQQshXc4Uyk0JfiLXcaMlRtzmwMpPVZ8sgNM1jgWUx7hBv2Y3b1JIWmQq7XoKqbl5MZHRz71ZmlmhQYFcssX7c4GW7BFaLqcjYqExQaGx0xsqKF64KRVy8+ZeUbjrXPikyhBByCVypzMSiM7rJtq39EBbtvHo9rp3xVg1e95TQrAo0VdkJSnvrArmBgSCVMCIyS+z7ocxrn24ajbE1M5Jm2qHCGq/Yryr8WO3xpmXGikxp5iWjM7Y1k5Ybb16oKfepkZRzp5sYjSGEkO/GlcqMxgqNrvWQi/KrmpZhCeC+vcHji5mtV42Or7CtCzSP3f6r4f5NrXiMb1apG1wXqLHEvo/MrLGJFv7qqM0eVfdo97AuNtg/LofojCctVmBW6i2LRmdCURlgEBs7L9TZ3jHRGbvcPs8cWBtDCCHfkSuTGV0ErPuc0UJjL6ZyAXo124nkrMcyI6voYYzHRd/4GwCKaojAyE0PgH2vI6VKLS2xwxqvqLDDPTbJmpm6lxnRIHlUWK720+hMKOWUXTsj76kXlbFvUExC5orHR9TPsKUSIYR8V65MZiye0MTuA/Sqhgu0PbUAfd8zL84msXeoj+IscOjugL2s9iMhAaa3NJDamapLNd1jgzVes2tmRGB23R5escay2GG52mG7WgOru2kz8+xiYInO2GgWEBYCKy0xyciJzqS2mwv7jSGEkO/MFcqMbaJthQaIX0xV82wdgagXg8yEalq9cZkuK+zLBvvVHlU11L8IQ2pJ18sMIiNpJmCcVtL9zsiyfS8x9/0+dqjwulpju9qN+53xhEYPJ6kmeUG5PQAD/nttI2Rzm2p/ResmgRJDCCHXxBXKTA62RYydZ3sJVrc6kKpeKQi2BcIyrce3ALZ3eFsVaOoCdRXuwbdJvOWx5fZ2B96y0UvUeAImQ6/wuZ/ppY5y8Jpre0Kjb0dh83y5rZs+qv8ZQggh18CPrz6A47AXrtivfC0uB/XQtzqQ5toI34gyNt0/Kuy2FfZN5UqJvQdTiNhyb1lKkEakIkwAhlSTfki0JrTThRqHmvbm23l6PW9+aBnMeueAUkQIIdfGN4rM6ObZKeSXvNTOOLc6iEVhJlEZ9Heibuo2OtMUto/fsYTY5taW0M0m9fY6/TQsiIsSgKnQJAuBU9gWT3ZZrEO93BqaU3sUJoQQ8l250sgM4EtLzgXOSzEd1PC9XW1uVKbrffdNZMakl4RUVCaFt0/3JXrEojJBdFRGj9uhjMs2wDhio+d568HMSx2kt+zU6AyjMoQQco18o8iMR6ww1btnk7wd90P9TCwq84KhiLYXmgp1vekjJ4COwoyH56IXp1OjMm6rplx0/Yvg3cNJz/ea0gPhKMyxPQTnrEeRIYSQa+XKZca7+aTMB6YFpXYdSS/pKI2wPqIIGF2qqexSQ6VKE03rZebUujRGjmSfSTHynkIHR4IyE9o4hpUTYJqCihUGn0NoWAxMCCG3xpXLDBBvqq0vgF7zHt2fiqVLncRqZfS0uv1TUxdoGqmbGd7iY0TGCoutvTmJUMBiFJ3JJRWVseJyitCcG8oPIYRcM99AZoDwfYMWZlxfDO29m2yxa1cDsr3zozJ2Wj3etss25dN5R1adi8NR4lI7f9JQOim0rLYbzyEUlTmH0CAwjwXBhBByy3wTmRG8G0+GCLXUqc06TropmGKSR4nddommKkw0phjV0sSw8mM7ztMFxv1+pWYm97quHSAoNHeY36LJjgNhcZkjNCnJOQZGZQgh5Nq54tZMx6Jvd1Cbca8vmvfxYisu7nhbN7NDhQZFfx8lXesi91eShyzzpndY9gKz7/Z5dIsmIRSQ8ub1QhNrxeS1YIJaDrOOnrYpKq+VEyLzLOfqc4YQQsg18M0iMx42WgNMe57Vv/zl17++wSLGQuOJjJEdqZvZFRXuseluQSD/lt2tDapeZ+TGlLZfmZ2Sl9pEY1rJUdvUZdsKK0RKXuSlu+mmYyI09r0ORV70wYTmpdJNTDURQsit8o1lRstLaJ5cVG1X+vri2017MhOK1tRt3cx+u8T+ob0TU4kGOyyxxBIF1ijQYI89Nrjvk08iMToSYwVHbjIp8/plzYxWTSF58WppJkKT8yS2hknnsqyYpNJNhBBCSJxvKDNec+3QPH3h1GknfVXvto3JjJWaLYDtArtthfqh6CRm10dnRGy01LyimURlvPs77fvUlURshtZOwX5mrMjol+gJjZ4GjNDEPjJeVEZvYyMyofoZ7wWkojqEEEJulW8oMylSF1KbZqqHzbwSm8ijqQvsmwr7Ym8kpsGym95jiQ3W/dGFJEbm7brElC0AbnKKf1MtmLzozGR/qXRTqGWZzAu1cLIHmhudYQSHEEJunW8uMzqtFOtgT6eabD0N0N7i4C4vMqMiNJJq2j1IemmQmDa9NNaVUZTFic6080sM92UqhsLiusBbTGisyMBMe+mlYHTmzlngpfTs/FCKSY+fkm5ixIYQQm6Rby4zMbzCVC+VcRjm54pMP69NNd0/vPZ1M/tOaiS91GqJH5mRad37b1suXGGPqisOHnoXTuJJjC349eRGv2XBnXoRGT1f1vUiY0BaXE5NNVF0CCHku/JNZUZHYbzojL0o6mW66bYMI3UzTvGvntfUBfa7JYqq7iMzg560ElOqyAwwFRgdmdn1TbXHN7Ks6wLuHbPtX9hLMSEwHqqhBhBON2lhsMtjKSY9/lm1MUxPEULId+CbyozFExrvYmpTHgeMLni5BcAm1bTbLlGUDcqiUa2Xml5idGTGExgtLfsuGjMSmqZom2XLMVo8gQnVy3jRGf36R4jQ2MJfIdaUOmfc286DdTOEEHLL3IjMhPDSHICfZjJ1M1sMd8x26mV0q6b9tkJZNigeWv14RRM8IntnbS03AFQBsKqd6WtljvhzpoRG0D44kZqQTNiUkxeFSUVn7MHayA9TR4QQcut8Y5mJ3YDSrmfrPmT9gxkuhlsZrDC+T9MKw20PVuPhGx7wUre3G2ge2xtQ2tiMf1ft6Q0m21qZZV8703aWZ1ozpVo05URlaoQ9I4vPiJbMOSBKDyGEfFe+sczMwWt5o0XmFcA9RnUz9v5MWmRkfBTZWGFbt+mg5rFAXdnm11OZsei+ZnYiNbsldtuqbcmU0/uvnfYkBvDlJVtoYisyJUQIIeS83JjMyFU7VOPhFa9qoenuRSQio2XGSo2tO6kB1Asc6hLPEqF5GN+2oMgwBR2ZGUVl6mJ8+KFdhaSmjkwDEcmxAjVHVnizSEIIIadzYzKj8a72Ni11QBuRqdX0YhyZ8eQl5Ek1gPoOb/WQdtqvlthVS6zxCgCu0JSqxqZGgVessUNlojKJAmA7DBX8xqQmmXJiDQshhJDP55vLjNdRXqh2Rta36+pO9ZTQeNEZm17S448wrZ/atNN+te+jNMBYZkRiCiUzEsVpRWapojJ3eR4REprYvKy6mZwoy6myc+y2FCxCCPnOfHOZORbdD42OyrxiuNrfjetmRGRkaHdXYyo02wXeHhd4qQvsthWKskZZdgKjhkUhUtP2RSMis9dRGdtM3GJTSzL0BAZm3qcEXM6RcqK0EELILUKZCTYptv3RSM2Mqp3RTbFFZICwzNQYN+fux1c4rCocyhroJOZH2bQiYwSnkfSUiMy2GqIyqWu5jRp58uKJjV7fFZsc27Gy8tUpKdbZEELId+EGZCZ2TybANw87LQ8tNECfbnpBmpjMbAE83gHlonsAb+U73soah7LpxUZo6gJv22UXkbkb+rjxDt/7C3s1PqFozpc4x6lRGooKIYTcEjcgMx5e3YzurE2QqIx+bMx2pn5G79aLyHgyI8PRUw1y87ZqxUaiNpNoTCy9BOcl5D5srczRaaevjMIw9UQIId+dG5GZUHTGW25TIXIvJ/3QTbu76EgsOqPrZXTdTExmVmp8ewesuqgNEJYYb37ocQyh6E//nsQ29IqrQ+REVigphBBCWm5EZoCw0OgojR3X4hJ6q14BrId0kd6tDK3IyDAkM9K7sJYae2j6EEPjMXmZIz1Rb3g3O5WiaY9TBcZbjyklQgi5dW5IZjxsREaiMMC4+FfXyYTesnVYZkIiY6VGi4yVGJsZs6LhDUOyM4fQ/kd4QnGIzI9tN3c9Qgght86NyYyWFy8iY4VGLOKg1hOp8VgPxbhWYmIio8e3iaGldsZjInNKdia6j1CqKScF5Y3PhWknQgi5VW5MZoBpukkLTImp8ADjt+kecaG5H2podG1MSF5WZrxU4/rO3HOKb1PpornX/ahv2BkxqQnu5IOg4BBCyC1wgzLj4QmNxl4URWi8C3mN/qaUNkrjiYyd1hKjm25rqcnhGJGJ1dq4272rhV4ti90olDoKjYcO4FRJYdqKEEK+EzcqM1ZeNJ7QhDpr0RdVuY+THneiNCmR0RJjRUYfrj2kUEvzUIopJi7efux4j5WPVIHuR0dLKCqEEHJr3KjMaKy8eJLzqubJBVvX1tgbUurxsp3edp3b6RqakMiExEZ2Z4XG9gkDxGXlrGkm4HiBOTXaQnEhhBBCmenQ8mJFJYbuRVgkRlrx6FsiqNTTC8ZpJO9hhceTGS0yXrTGRmRSERhLlgDZJ4lFaea0Tjp2O0IIIbfIDcuMLQSOCY0nN7LOBsBazdehEYnYHBBNPQHT3YfEw+ulVwuNDiDlpI9C0mTXmxyj7V9GiEVlvHHKCiGEkNO4YZnx8ITGGwfGqanYBVzdx6k3BdOTr25+LbvWy6Dm20P15ul9x2QlFqXxIjsTYoW7dpknL3r92LxUOOmj63AIIYRcMjcuM16vwLZextbU2OJg76aIXjWuvc/TnS8roXky37sHlD3sWIop94HAvkYLgHHneLYFUyjVFIvKMDpDCCFkHjcuMyG0wNiCYLvMu/jG+qER7tELTSgKE4vM2EP15OZUoQmKjG6SHRKT3PRSLCoTi+JQegghhLRQZpL3bJpTEKyRFlDSXFuMw0k3xdJLdp70AhySGH34xwqNt68JoYjLnOJdKya5tzcghBBCBigzANJ31RaOFZxYr8GqfiZHanSqKXWoOTKT2jY7xRRKL9lxIE9MYpEbQgghZIAy0xOrn/H6oJkbKdB9zwTqZ3LSTd79mfSh6lZOKZkJzc9OMcGMH5NeslGZz5CXUHqQEELINUKZGaGlRdD1Mt6NKHMviqHKXaCvn4lJjBeZsQ9NSlq8eV60Jppi0lGXObcqyBWZlAAFD5AQQsgNQZlxsVJjhUaWpaI0crFfY5pm0vUzr5gUBIu02GhNKDLjPXVIXOy05xDB5Z5QhFJKsVRTjGOjJoy2EELILUKZiRK7g7a9d1PsQiod61lD2HTLJQW18NNNOZ3gaayw6PFcuXGfx6aYdFoplV6y46GoDIWEEELIPH589QFcH7lpjdR6R160rRt44hJbLzadWg9AuOBXluWM54hMKMUEZx0Ppp8IIeRWoMwkyZEOe+Gcc++hjN2F0kDe8pCk5EyHtpvs/Jg6mdB4jshoGLkhhBAyhjJzFF66JAcdlfCWnYmYoNjpnBoaAH6KSQ9lWapmxmvOnRKZnKgMJYcQQm4VyszRhNIYoYvszIttKjrjrZ8SlmPmjbAS4qWR7MGGxO8YaaGwEEIImUKZySL3ImrX84pjT9h/bjoolTqKpZlmp5hCshGqk6kRF5lQR3wx6aHkEELILUOZOYlzFJnqC/H7eNEx9a05hb12mTc9IpViCqWUgLCEzInSzO1XhsW/hBByS1Bmskn9+p8bLQiaQ3y3sXlWTELrpFJKs1NM3oHZOpljojR2vgejMoQQcutQZs5CKL0U4oTIQU5KyS7zHna5Nw2gjcrIzFiT6lD9jF3mvZBckWF6iRBCyBTKzCyOuXgmQx7zdhGbH00VJdaNbmdTSDatZNeT8VBTbC9KE9uXXeYtD61HCCHku0OZOTuhvlFiImRMIpUKykkLxaIy3vbBfXlRFZh5ofSSt8xu6z0pIzKEEELy4e0MZqNvcQC0F9rQ22jX1fP1HSLlLs71sL6X+glFT0pnfk6U5qigUaiPGC+9hIxlKVGZIzKMyhBCyC3CyMxZCUUhBC9CY2tR3seLcupdUqmi3KjMZDqnFZOetuM2SuMtDy3zlsegyBBCyK3CyMynoaIu/bTMkwhOINVkhQXwozGlWicVmcmOyqRaMdn5sswzpmPrY0LrpdYnhBByCzAycxSpewbFUiyh+hCn/mRONCYmMLH5selkRMYSKvq143NFJlZzRJEhhJBbhzLzocTSKKF7FKlU07HSYtf31gmlppJC470em04K7TAkcsEnDzxvbH1CCCG3BtNMRxMq7q3hv60HNV/GQ6kmk5LKKeYFpmmm1PZRiXm3M9Sx6w1yIjE2OmWXU2IIIYQcD2Xmw9CyExKZhVnmkFP/YglJjTcvKjS59TI5vQDbfYaedG5Py4QQQm4dysxJ6GhK6K3Uy2q1vhYZOy2Pu+NkRp4qlnKKSowm1YIpFKFJFQB7T/xZ0Rj2U0MIId8JysxZsXKjozM2naRFBhj3OWNSTcfKjN4WznSsdmY0I6dJtsyfWwDsLbf7JIQQQsJQZj4FG3kJ9ZxbYxydQbtdDWDbTcqiVeLp7DA0bucBGNfLhFJMmlMjKhQZQgghx0OZOTuh6IyNxMg8QQsMzPy7QWhEYrYYZ6TswxMWO4xGfHRrK082QukmOx7aTj9PznqEEEKID2XmbHh1M3ZerFnyqxqXdJPMu8dIaGISs0JaZOy87BRTTGDsa/LGcwXlo0XGiiQhhJBrhjJzMl4TbTvPRmDsPEGbxQbAuht/xUhoxJG8lJPITkhcvKca8Y6pfKSKeY+qLEY84kMIIYTkQZk5K3OjMxadarpHKzQyroTG7saL1nhPF5KZSVQG8FNMXoQltp633znrfCS62TwhhJBrhjLzYdiWTDlIFEbwhGaBSSsnL0Jjn9KbdqMyeuVYismOe0/irROa91V4HR96XNIxE0II0VBmzo6OxIR6CZb1LLJdSGhKs66J0ugITexpZL4blclJMdnxOfUxLPglhBByXigzZ8FKiyc09mLtzdPoZSI0a7SiI/t3ojQrzAsE9eioTCp15NXLxKIysWbdoW0vDRYNE0LIpUKZORs5QiPjQkpobCRGb3NvlnVRmi2O/KvaqEwoxaTHQ7cqmCMnx4jMXKnITSURQgi5RigzH4pX/KtvbQBMhcZO63GdfgKGZkslgsXBWYiY1GpcpmuEhcabtqSiMsccKyGEEDJAmTkrXo2MyIaNzmhpsfdrsugojKSZ7HNJcbCK0iR5x1hWDt1+9DQwFRoEpoF58vJZ6aVY7dKcfRBCCLlEKDOfTkxuYhdMicLcO8tqjKM03oXb+1NrSRGJOWAqON48zWeJDIWCEELIFMrM2cmJzujpY9DioqM0ep5eV47LkiMtNuXlrRciJDdfITLHRGcoT4QQcg1QZj6E2IUzdO+mc6ZcbPFxat2YyISiMd5+cp9vDl8hFJQYQgi5JigzH0asdZOdlyM0B4wjL6FhKmVlb7PgRVliQhOL3nhNtkO3PUjxEUKRis5QYggh5BqhzHwocnHUPQGXZn5KaLTEhOZ7Q0soUhMTk5jIxDglyvTRQhESGooMIYRcK5SZT0FfQFMd6sUiNDry4kV9QjLj1edI9CbWaskTGXssVm68Fk52fojPEgormRQZQgi5Zigzn4YVGmBeEbDto8bW3uh1bAGwJzKhTvK8cb19LDpzyRGZS3lOQggh54Yy86Wk6mhysakorwA4FMkJSYxsG+trxttGHxMCywghhJDzQZn5VGLNtvXyVFPuHNlJFSB74mKLdr2Ukx0H4iKTAyMkhBBCjocycxGcKjR6PFSfA0yjNJ7QhOZ7rZM8CZnTpwwlhhBCyOlQZj6dUGua3Kbbx6SgQtGgHIkJTXvpJYoMIYSQz4cy8yWkhMaLrpxy8feez4uwhCTGzgutrwmJDCWGEELIeaHMfBm5Hbjpptq5UZpQNEb24c3XzxmbFyr4De3T7ocQQgg5L5SZLyXU0giY1tDIsliUJlYv4z3n3P5hTk0tEUIIIeeHMnMRxJpP2+Vz+qjx9qv37607Z1lu82tGZQghhHwclJmLwmtOLZxSNxOK0njPo48ltM6cPmQoMoQQQj4WyszFEYqmeOknWS/3VgGxO3lb5gpOzj4JIYSQ80OZuVhym3CHojmxepkYua2QKDKEEEIuA8rMVZLqDE/W0aT+1HOaUlNkCCGEXA6UmYsmVcBr/3xzCn5znzt3P5QYQgghXwNl5iqIpZyAsNQIsf5sQtuEnuuYbQkhhJCPgzJzNeREXUJ/zlNkg62VCCGEXDaUmasj1irJNuU+htx0FCWGEELIZUCZuUq0SOSIzUc9NyGEEPL1UGaunhyxOdf+CSGEkMuDMvOtoHgQQgi5PX589QEQQgghhJwCZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXDWWGEEIIIVcNZYYQQgghVw1lhhBCCCFXzf8f/a1YZN6OOhwAAAAASUVORK5CYII=" id="image2143aec860" transform="scale(1 -1) translate(0 -270.24)" x="42.113906" y="-44.38525" width="270.24" height="270.24"/>
   </g>
   <g id="patch_3">
    <path d="M 177.001906 230.32025 
C 190.416675 230.32025 203.283815 224.990506 212.769489 215.504832 
C 222.255162 206.019159 227.584906 193.152018 227.584906 179.73725 
C 227.584906 166.322482 222.255162 153.455341 212.769489 143.969668 
C 203.283815 134.483994 190.416675 129.15425 177.001906 129.15425 
C 163.587138 129.15425 150.719998 134.483994 141.234324 143.969668 
C 131.74865 153.455341 126.418906 166.322482 126.418906 179.73725 
C 126.418906 193.152018 131.74865 206.019159 141.234324 215.504832 
C 150.719998 224.990506 163.587138 230.32025 177.001906 230.32025 
L 177.001906 230.32025 
z
" clip-path="url(#p347f4f76ea)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 177.001906 264.04225 
C 199.359854 264.04225 220.805087 255.159343 236.614543 239.349887 
C 252.424 223.540431 261.306906 202.095197 261.306906 179.73725 
C 261.306906 157.379303 252.424 135.934069 236.614543 120.124613 
C 220.805087 104.315157 199.359854 95.43225 177.001906 95.43225 
C 154.643959 95.43225 133.198725 104.315157 117.389269 120.124613 
C 101.579813 135.934069 92.696906 157.379303 92.696906 179.73725 
C 92.696906 202.095197 101.579813 223.540431 117.389269 239.349887 
C 133.198725 255.159343 154.643959 264.04225 177.001906 264.04225 
L 177.001906 264.04225 
z
" clip-path="url(#p347f4f76ea)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 177.001906 314.62525 
C 212.774622 314.62525 247.086996 300.412599 272.382126 275.11747 
C 297.677256 249.82234 311.889906 215.509966 311.889906 179.73725 
C 311.889906 143.964534 297.677256 109.65216 272.382126 84.35703 
C 247.086996 59.061901 212.774622 44.84925 177.001906 44.84925 
C 141.22919 44.84925 106.916817 59.061901 81.621687 84.35703 
C 56.326557 109.65216 42.113906 143.964534 42.113906 179.73725 
C 42.113906 215.509966 56.326557 249.82234 81.621687 275.11747 
C 106.916817 300.412599 141.22919 314.62525 177.001906 314.62525 
L 177.001906 314.62525 
z
" clip-path="url(#p347f4f76ea)" style="fill: none; opacity: 0.7; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" clip-path="url(#p347f4f76ea)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="md603bd9214" d="M 0 0 
L 0 3.5 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#md603bd9214" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −8 -->
      <g transform="translate(35.479922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 75.835906 314.62525 
L 75.835906 44.84925 
" clip-path="url(#p347f4f76ea)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#md603bd9214" x="75.835906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −6 -->
      <g transform="translate(69.201922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 109.557906 314.62525 
L 109.557906 44.84925 
" clip-path="url(#p347f4f76ea)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#md603bd9214" x="109.557906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_3">
      <!-- −4 -->
      <g transform="translate(102.923922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 143.279906 314.62525 
L 143.279906 44.84925 
" clip-path="url(#p347f4f76ea)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#md603bd9214" x="143.279906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_4">
      <!-- −2 -->
      <g transform="translate(136.645922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 177.001906 314.62525 
L 177.001906 44.84925 
" clip-path="url(#p347f4f76ea)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#md603bd9214" x="177.001906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(174.138781 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 210.723906 314.62525 
L 210.723906 44.84925 
" clip-path="url(#p347f4f76ea)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#md603bd9214" x="210.723906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 2 -->
      <g transform="translate(207.860781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 244.445906 314.62525 
L 244.445906 44.84925 
" clip-path="url(#p347f4f76ea)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#md603bd9214" x="244.445906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 4 -->
      <g transform="translate(241.582781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <path d="M 278.167906 314.62525 
L 278.167906 44.84925 
" clip-path="url(#p347f4f76ea)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#md603bd9214" x="278.167906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 6 -->
      <g transform="translate(275.304781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_17">
      <path d="M 311.889906 314.62525 
L 311.889906 44.84925 
" clip-path="url(#p347f4f76ea)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#md603bd9214" x="311.889906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 8 -->
      <g transform="translate(309.026781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- Relative X Position (m) -->
     <g transform="translate(105.68925 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-52" d="M 2297 2597 
Q 2675 2597 2839 2737 
Q 3003 2878 3003 3200 
Q 3003 3519 2839 3656 
Q 2675 3794 2297 3794 
L 1791 3794 
L 1791 2597 
L 2297 2597 
z
M 1791 1766 
L 1791 0 
L 588 0 
L 588 4666 
L 2425 4666 
Q 3347 4666 3776 4356 
Q 4206 4047 4206 3378 
Q 4206 2916 3982 2619 
Q 3759 2322 3309 2181 
Q 3556 2125 3751 1926 
Q 3947 1728 4147 1325 
L 4800 0 
L 3519 0 
L 2950 1159 
Q 2778 1509 2601 1637 
Q 2425 1766 2131 1766 
L 1791 1766 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6c" d="M 538 4863 
L 1656 4863 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-76" d="M 97 3500 
L 1216 3500 
L 2088 1081 
L 2956 3500 
L 4078 3500 
L 2700 0 
L 1472 0 
L 97 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-58" d="M 3188 2381 
L 4806 0 
L 3553 0 
L 2463 1594 
L 1381 0 
L 122 0 
L 1741 2381 
L 184 4666 
L 1441 4666 
L 2463 3163 
L 3481 4666 
L 4744 4666 
L 3188 2381 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-50" d="M 588 4666 
L 2584 4666 
Q 3475 4666 3951 4270 
Q 4428 3875 4428 3144 
Q 4428 2409 3951 2014 
Q 3475 1619 2584 1619 
L 1791 1619 
L 1791 0 
L 588 0 
L 588 4666 
z
M 1791 3794 
L 1791 2491 
L 2456 2491 
Q 2806 2491 2997 2661 
Q 3188 2831 3188 3144 
Q 3188 3456 2997 3625 
Q 2806 3794 2456 3794 
L 1791 3794 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6f" d="M 2203 2784 
Q 1831 2784 1636 2517 
Q 1441 2250 1441 1747 
Q 1441 1244 1636 976 
Q 1831 709 2203 709 
Q 2569 709 2762 976 
Q 2956 1244 2956 1747 
Q 2956 2250 2762 2517 
Q 2569 2784 2203 2784 
z
M 2203 3584 
Q 3106 3584 3614 3096 
Q 4122 2609 4122 1747 
Q 4122 884 3614 396 
Q 3106 -91 2203 -91 
Q 1297 -91 786 396 
Q 275 884 275 1747 
Q 275 2609 786 3096 
Q 1297 3584 2203 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-28" d="M 2413 -844 
L 1484 -844 
Q 1006 -72 778 623 
Q 550 1319 550 2003 
Q 550 2688 779 3389 
Q 1009 4091 1484 4856 
L 2413 4856 
Q 2013 4116 1813 3408 
Q 1613 2700 1613 2009 
Q 1613 1319 1811 609 
Q 2009 -100 2413 -844 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6d" d="M 3781 2919 
Q 3994 3244 4286 3414 
Q 4578 3584 4928 3584 
Q 5531 3584 5847 3212 
Q 6163 2841 6163 2131 
L 6163 0 
L 5038 0 
L 5038 1825 
Q 5041 1866 5042 1909 
Q 5044 1953 5044 2034 
Q 5044 2406 4934 2573 
Q 4825 2741 4581 2741 
Q 4263 2741 4089 2478 
Q 3916 2216 3909 1719 
L 3909 0 
L 2784 0 
L 2784 1825 
Q 2784 2406 2684 2573 
Q 2584 2741 2328 2741 
Q 2006 2741 1831 2477 
Q 1656 2213 1656 1722 
L 1656 0 
L 531 0 
L 531 3500 
L 1656 3500 
L 1656 2988 
Q 1863 3284 2130 3434 
Q 2397 3584 2719 3584 
Q 3081 3584 3359 3409 
Q 3638 3234 3781 2919 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-29" d="M 513 -844 
Q 913 -100 1113 609 
Q 1313 1319 1313 2009 
Q 1313 2700 1113 3408 
Q 913 4116 513 4856 
L 1441 4856 
Q 1916 4091 2145 3389 
Q 2375 2688 2375 2003 
Q 2375 1319 2147 623 
Q 1919 -72 1441 -844 
L 513 -844 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-58" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="573.583984"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="608.398438"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="681.689453"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="750.390625"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="809.912109"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="844.189453"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="891.992188"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="926.269531"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="994.970703"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1066.162109"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1100.976562"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1146.679688"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1250.878906"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_19">
      <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" clip-path="url(#p347f4f76ea)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <defs>
       <path id="me5fd12a7f9" d="M 0 0 
L -3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#me5fd12a7f9" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_11">
      <!-- −8 -->
      <g transform="translate(21.845937 318.044547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_21">
      <path d="M 42.113906 280.90325 
L 311.889906 280.90325 
" clip-path="url(#p347f4f76ea)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#me5fd12a7f9" x="42.113906" y="280.90325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_12">
      <!-- −6 -->
      <g transform="translate(21.845937 284.322547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_23">
      <path d="M 42.113906 247.18125 
L 311.889906 247.18125 
" clip-path="url(#p347f4f76ea)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#me5fd12a7f9" x="42.113906" y="247.18125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_13">
      <!-- −4 -->
      <g transform="translate(21.845937 250.600547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_25">
      <path d="M 42.113906 213.45925 
L 311.889906 213.45925 
" clip-path="url(#p347f4f76ea)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#me5fd12a7f9" x="42.113906" y="213.45925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_14">
      <!-- −2 -->
      <g transform="translate(21.845937 216.878547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_27">
      <path d="M 42.113906 179.73725 
L 311.889906 179.73725 
" clip-path="url(#p347f4f76ea)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#me5fd12a7f9" x="42.113906" y="179.73725" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 0 -->
      <g transform="translate(29.387656 183.156547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_29">
      <path d="M 42.113906 146.01525 
L 311.889906 146.01525 
" clip-path="url(#p347f4f76ea)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#me5fd12a7f9" x="42.113906" y="146.01525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 2 -->
      <g transform="translate(29.387656 149.434547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_31">
      <path d="M 42.113906 112.29325 
L 311.889906 112.29325 
" clip-path="url(#p347f4f76ea)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#me5fd12a7f9" x="42.113906" y="112.29325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 4 -->
      <g transform="translate(29.387656 115.712547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_33">
      <path d="M 42.113906 78.57125 
L 311.889906 78.57125 
" clip-path="url(#p347f4f76ea)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#me5fd12a7f9" x="42.113906" y="78.57125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 6 -->
      <g transform="translate(29.387656 81.990547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_35">
      <path d="M 42.113906 44.84925 
L 311.889906 44.84925 
" clip-path="url(#p347f4f76ea)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#me5fd12a7f9" x="42.113906" y="44.84925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 8 -->
      <g transform="translate(29.387656 48.268547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_20">
     <!-- Relative Y Position (m) -->
     <g transform="translate(15.558281 250.792094) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-59" d="M -63 4666 
L 1253 4666 
L 2316 3003 
L 3378 4666 
L 4697 4666 
L 2919 1966 
L 2919 0 
L 1716 0 
L 1716 1966 
L -63 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-59" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="568.896484"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="603.710938"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="677.001953"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="745.703125"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="805.224609"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="839.501953"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="887.304688"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="921.582031"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="990.283203"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1061.474609"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1096.289062"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1141.992188"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1246.191406"/>
     </g>
    </g>
   </g>
   <g id="patch_6">
    <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_21">
    <g id="patch_8">
     <path d="M 200.10902 150.124824 
L 225.429957 150.124824 
Q 227.229957 150.124824 227.229957 148.324824 
L 227.229957 139.614511 
Q 227.229957 137.814511 225.429957 137.814511 
L 200.10902 137.814511 
Q 198.30902 137.814511 198.30902 139.614511 
L 198.30902 148.324824 
Q 198.30902 150.124824 200.10902 150.124824 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 3.0m -->
    <g style="fill: #ffffff" transform="translate(200.10902 146.453105) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-33" d="M 2981 2516 
Q 3453 2394 3698 2092 
Q 3944 1791 3944 1325 
Q 3944 631 3412 270 
Q 2881 -91 1863 -91 
Q 1503 -91 1142 -33 
Q 781 25 428 141 
L 428 1069 
Q 766 900 1098 814 
Q 1431 728 1753 728 
Q 2231 728 2486 893 
Q 2741 1059 2741 1369 
Q 2741 1688 2480 1852 
Q 2219 2016 1709 2016 
L 1228 2016 
L 1228 2791 
L 1734 2791 
Q 2188 2791 2409 2933 
Q 2631 3075 2631 3366 
Q 2631 3634 2415 3781 
Q 2200 3928 1806 3928 
Q 1516 3928 1219 3862 
Q 922 3797 628 3669 
L 628 4550 
Q 984 4650 1334 4700 
Q 1684 4750 2022 4750 
Q 2931 4750 3382 4451 
Q 3834 4153 3834 3553 
Q 3834 3144 3618 2883 
Q 3403 2622 2981 2516 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2e" d="M 653 1209 
L 1778 1209 
L 1778 0 
L 653 0 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-30" d="M 2944 2338 
Q 2944 3213 2780 3570 
Q 2616 3928 2228 3928 
Q 1841 3928 1675 3570 
Q 1509 3213 1509 2338 
Q 1509 1453 1675 1090 
Q 1841 728 2228 728 
Q 2613 728 2778 1090 
Q 2944 1453 2944 2338 
z
M 4147 2328 
Q 4147 1169 3647 539 
Q 3147 -91 2228 -91 
Q 1306 -91 806 539 
Q 306 1169 306 2328 
Q 306 3491 806 4120 
Q 1306 4750 2228 4750 
Q 3147 4750 3647 4120 
Q 4147 3491 4147 2328 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-33"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_22">
    <g id="patch_9">
     <path d="M 223.954075 126.279769 
L 249.275012 126.279769 
Q 251.075012 126.279769 251.075012 124.479769 
L 251.075012 115.769457 
Q 251.075012 113.969457 249.275012 113.969457 
L 223.954075 113.969457 
Q 222.154075 113.969457 222.154075 115.769457 
L 222.154075 124.479769 
Q 222.154075 126.279769 223.954075 126.279769 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 5.0m -->
    <g style="fill: #ffffff" transform="translate(223.954075 122.60805) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-35" d="M 678 4666 
L 3669 4666 
L 3669 3781 
L 1638 3781 
L 1638 3059 
Q 1775 3097 1914 3117 
Q 2053 3138 2203 3138 
Q 3056 3138 3531 2711 
Q 4006 2284 4006 1522 
Q 4006 766 3489 337 
Q 2972 -91 2053 -91 
Q 1656 -91 1267 -14 
Q 878 63 494 219 
L 494 1166 
Q 875 947 1217 837 
Q 1559 728 1863 728 
Q 2300 728 2551 942 
Q 2803 1156 2803 1522 
Q 2803 1891 2551 2103 
Q 2300 2316 1863 2316 
Q 1603 2316 1309 2248 
Q 1016 2181 678 2041 
L 678 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-35"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_23">
    <g id="patch_10">
     <path d="M 259.721657 90.512187 
L 285.042595 90.512187 
Q 286.842595 90.512187 286.842595 88.712187 
L 286.842595 80.001874 
Q 286.842595 78.201874 285.042595 78.201874 
L 259.721657 78.201874 
Q 257.921657 78.201874 257.921657 80.001874 
L 257.921657 88.712187 
Q 257.921657 90.512187 259.721657 90.512187 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 8.0m -->
    <g style="fill: #ffffff" transform="translate(259.721657 86.840468) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-38" d="M 2228 2088 
Q 1891 2088 1709 1903 
Q 1528 1719 1528 1375 
Q 1528 1031 1709 848 
Q 1891 666 2228 666 
Q 2563 666 2741 848 
Q 2919 1031 2919 1375 
Q 2919 1722 2741 1905 
Q 2563 2088 2228 2088 
z
M 1350 2484 
Q 925 2613 709 2878 
Q 494 3144 494 3541 
Q 494 4131 934 4440 
Q 1375 4750 2228 4750 
Q 3075 4750 3515 4442 
Q 3956 4134 3956 3541 
Q 3956 3144 3739 2878 
Q 3522 2613 3097 2484 
Q 3572 2353 3814 2058 
Q 4056 1763 4056 1313 
Q 4056 619 3595 264 
Q 3134 -91 2228 -91 
Q 1319 -91 855 264 
Q 391 619 391 1313 
Q 391 1763 633 2058 
Q 875 2353 1350 2484 
z
M 1631 3419 
Q 1631 3141 1786 2991 
Q 1941 2841 2228 2841 
Q 2509 2841 2662 2991 
Q 2816 3141 2816 3419 
Q 2816 3697 2662 3845 
Q 2509 3994 2228 3994 
Q 1941 3994 1786 3844 
Q 1631 3694 1631 3419 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-38"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_24">
    <!-- Far-Distance Cell (Neuron 8) -->
    <g transform="translate(81.107844 16.411875) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-46" d="M 588 4666 
L 3834 4666 
L 3834 3756 
L 1791 3756 
L 1791 2888 
L 3713 2888 
L 3713 1978 
L 1791 1978 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2d" d="M 347 2297 
L 2309 2297 
L 2309 1388 
L 347 1388 
L 347 2297 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-44" d="M 1791 3756 
L 1791 909 
L 2222 909 
Q 2959 909 3348 1275 
Q 3738 1641 3738 2338 
Q 3738 3031 3350 3393 
Q 2963 3756 2222 3756 
L 1791 3756 
z
M 588 4666 
L 1856 4666 
Q 2919 4666 3439 4514 
Q 3959 4363 4331 4000 
Q 4659 3684 4818 3271 
Q 4978 2859 4978 2338 
Q 4978 1809 4818 1395 
Q 4659 981 4331 666 
Q 3956 303 3431 151 
Q 2906 0 1856 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4e" d="M 588 4666 
L 1931 4666 
L 3628 1466 
L 3628 4666 
L 4769 4666 
L 4769 0 
L 3425 0 
L 1728 3200 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-75" d="M 500 1363 
L 500 3500 
L 1625 3500 
L 1625 3150 
Q 1625 2866 1622 2436 
Q 1619 2006 1619 1863 
Q 1619 1441 1641 1255 
Q 1663 1069 1716 984 
Q 1784 875 1895 815 
Q 2006 756 2150 756 
Q 2500 756 2700 1025 
Q 2900 1294 2900 1772 
L 2900 3500 
L 4019 3500 
L 4019 0 
L 2900 0 
L 2900 506 
Q 2647 200 2364 54 
Q 2081 -91 1741 -91 
Q 1134 -91 817 281 
Q 500 653 500 1363 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-46"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="62.435547"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="129.916016"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="179.232422"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="220.736328"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="303.744141"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="338.021484"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="397.542969"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="445.345703"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="512.826172"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="584.017578"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="643.294922"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="711.117188"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="745.931641"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="819.320312"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="887.142578"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="921.419922"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="955.697266"/>
     <use xlink:href="#DejaVuSans-Bold-28" x="990.511719"/>
     <use xlink:href="#DejaVuSans-Bold-4e" x="1036.214844"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1119.90625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1187.728516"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1258.919922"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="1308.236328"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1376.9375"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1448.128906"/>
     <use xlink:href="#DejaVuSans-Bold-38" x="1482.943359"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="1552.523438"/>
    </g>
    <!-- 2D Activation Map -->
    <g transform="translate(114.950656 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-32" d="M 1844 884 
L 3897 884 
L 3897 0 
L 506 0 
L 506 884 
L 2209 2388 
Q 2438 2594 2547 2791 
Q 2656 2988 2656 3200 
Q 2656 3528 2436 3728 
Q 2216 3928 1850 3928 
Q 1569 3928 1234 3808 
Q 900 3688 519 3450 
L 519 4475 
Q 925 4609 1322 4679 
Q 1719 4750 2100 4750 
Q 2938 4750 3402 4381 
Q 3866 4013 3866 3353 
Q 3866 2972 3669 2642 
Q 3472 2313 2841 1759 
L 1844 884 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-41" d="M 3419 850 
L 1538 850 
L 1241 0 
L 31 0 
L 1759 4666 
L 3194 4666 
L 4922 0 
L 3713 0 
L 3419 850 
z
M 1838 1716 
L 3116 1716 
L 2478 3572 
L 1838 1716 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4d" d="M 588 4666 
L 2119 4666 
L 3181 2169 
L 4250 4666 
L 5778 4666 
L 5778 0 
L 4641 0 
L 4641 3413 
L 3566 897 
L 2803 897 
L 1728 3413 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-32"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="152.587891"/>
     <use xlink:href="#DejaVuSans-Bold-41" x="187.402344"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="264.794922"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="324.072266"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="371.875"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="406.152344"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.337891"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="538.818359"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="586.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="620.898438"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="689.599609"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="760.791016"/>
     <use xlink:href="#DejaVuSans-Bold-4d" x="795.605469"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="895.117188"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="962.597656"/>
    </g>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="mcec608fad5" d="M 0 -5.477226 
L -1.229714 -1.692556 
L -5.209151 -1.692556 
L -1.989719 0.646499 
L -3.219432 4.431169 
L -0 2.092114 
L 3.219432 4.431169 
L 1.989719 0.646499 
L 5.209151 -1.692556 
L 1.229714 -1.692556 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#p347f4f76ea)">
     <use xlink:href="#mcec608fad5" x="177.001906" y="179.73725" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_11">
     <path d="M 242.388656 62.99175 
L 307.889906 62.99175 
L 307.889906 48.84925 
L 242.388656 48.84925 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="PathCollection_2">
     <g>
      <use xlink:href="#mcec608fad5" x="253.588656" y="56.028" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
     </g>
    </g>
    <g id="text_25">
     <!-- Observer -->
     <g transform="translate(267.988656 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4f"/>
      <use xlink:href="#DejaVuSans-62" x="78.710938"/>
      <use xlink:href="#DejaVuSans-73" x="142.1875"/>
      <use xlink:href="#DejaVuSans-65" x="194.287109"/>
      <use xlink:href="#DejaVuSans-72" x="255.810547"/>
      <use xlink:href="#DejaVuSans-76" x="296.923828"/>
      <use xlink:href="#DejaVuSans-65" x="356.103516"/>
      <use xlink:href="#DejaVuSans-72" x="417.626953"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_12">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
L 813.937288 44.84925 
L 436.259015 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="me4a06df3b8" d="M 440.975868 -37.642188 
L 440.975868 -37.642188 
L 450.409572 -39.800243 
L 459.843277 -39.461081 
L 469.276982 -39.350798 
L 478.710686 -39.529667 
L 488.144391 -39.288008 
L 497.578096 -39.324123 
L 507.011801 -39.150468 
L 516.445505 -41.445505 
L 525.87921 -41.634682 
L 535.312915 -45.269783 
L 544.746619 -45.052302 
L 554.180324 -41.75102 
L 563.614029 -42.216792 
L 573.047733 -46.47395 
L 582.481438 -57.506601 
L 591.915143 -85.299063 
L 601.348847 -66.301231 
L 610.782552 -120.158222 
L 620.216257 -74.84586 
L 629.649962 -137.901896 
L 639.083666 -112.773628 
L 648.517371 -123.061306 
L 657.951076 -181.353285 
L 667.38478 -204.553812 
L 676.818485 -231.273458 
L 686.25219 -216.076642 
L 695.685894 -263.548361 
L 705.119599 -235.403442 
L 714.553304 -294.571711 
L 714.553304 -37.642188 
L 714.553304 -37.642188 
L 705.119599 -37.642188 
L 695.685894 -37.642188 
L 686.25219 -37.642188 
L 676.818485 -37.642188 
L 667.38478 -37.642188 
L 657.951076 -37.642188 
L 648.517371 -37.642188 
L 639.083666 -37.642188 
L 629.649962 -37.642188 
L 620.216257 -37.642188 
L 610.782552 -37.642188 
L 601.348847 -37.642188 
L 591.915143 -37.642188 
L 582.481438 -37.642188 
L 573.047733 -37.642188 
L 563.614029 -37.642188 
L 554.180324 -37.642188 
L 544.746619 -37.642188 
L 535.312915 -37.642188 
L 525.87921 -37.642188 
L 516.445505 -37.642188 
L 507.011801 -37.642188 
L 497.578096 -37.642188 
L 488.144391 -37.642188 
L 478.710686 -37.642188 
L 469.276982 -37.642188 
L 459.843277 -37.642188 
L 450.409572 -37.642188 
L 440.975868 -37.642188 
z
" style="stroke: #33a02c; stroke-opacity: 0.3"/>
    </defs>
    <g clip-path="url(#p51cab06ae2)">
     <use xlink:href="#me4a06df3b8" x="0" y="352.267438" style="fill: #33a02c; fill-opacity: 0.3; stroke: #33a02c; stroke-opacity: 0.3"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10">
     <g id="line2d_37">
      <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" clip-path="url(#p51cab06ae2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#md603bd9214" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 0 -->
      <g transform="translate(433.39589 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_39">
      <path d="M 483.468799 314.62525 
L 483.468799 44.84925 
" clip-path="url(#p51cab06ae2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#md603bd9214" x="483.468799" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 1 -->
      <g transform="translate(480.605674 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_41">
      <path d="M 530.678584 314.62525 
L 530.678584 44.84925 
" clip-path="url(#p51cab06ae2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#md603bd9214" x="530.678584" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 2 -->
      <g transform="translate(527.815459 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_43">
      <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#p51cab06ae2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#md603bd9214" x="577.888368" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 3 -->
      <g transform="translate(575.025243 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_45">
      <path d="M 625.098152 314.62525 
L 625.098152 44.84925 
" clip-path="url(#p51cab06ae2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#md603bd9214" x="625.098152" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 4 -->
      <g transform="translate(622.235027 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_47">
      <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#p51cab06ae2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#md603bd9214" x="672.307936" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 5 -->
      <g transform="translate(669.444811 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_49">
      <path d="M 719.51772 314.62525 
L 719.51772 44.84925 
" clip-path="url(#p51cab06ae2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#md603bd9214" x="719.51772" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 6 -->
      <g transform="translate(716.654595 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_51">
      <path d="M 766.727504 314.62525 
L 766.727504 44.84925 
" clip-path="url(#p51cab06ae2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#md603bd9214" x="766.727504" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 7 -->
      <g transform="translate(763.864379 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-37"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_53">
      <path d="M 813.937288 314.62525 
L 813.937288 44.84925 
" clip-path="url(#p51cab06ae2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#md603bd9214" x="813.937288" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 8 -->
      <g transform="translate(811.074163 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_35">
     <!-- Inter-Agent Distance (m) -->
     <g transform="translate(547.755261 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-49" d="M 588 4666 
L 1791 4666 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-49"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="37.207031"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="108.398438"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="156.201172"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="224.023438"/>
      <use xlink:href="#DejaVuSans-Bold-2d" x="273.339844"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="314.84375"/>
      <use xlink:href="#DejaVuSans-Bold-67" x="392.236328"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="463.818359"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="531.640625"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="602.832031"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="650.634766"/>
      <use xlink:href="#DejaVuSans-Bold-44" x="685.449219"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="768.457031"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="802.734375"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="862.255859"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="910.058594"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="977.539062"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="1048.730469"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="1108.007812"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1175.830078"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1210.644531"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1256.347656"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1360.546875"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_55">
      <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" clip-path="url(#p51cab06ae2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#me5fd12a7f9" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 0.0 -->
      <g transform="translate(414.946203 318.044547) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_57">
      <path d="M 436.259015 270.327938 
L 813.937288 270.327938 
" clip-path="url(#p51cab06ae2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#me5fd12a7f9" x="436.259015" y="270.327938" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 0.1 -->
      <g transform="translate(414.946203 273.747235) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_59">
      <path d="M 436.259015 226.030626 
L 813.937288 226.030626 
" clip-path="url(#p51cab06ae2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#me5fd12a7f9" x="436.259015" y="226.030626" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 0.2 -->
      <g transform="translate(414.946203 229.449923) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_61">
      <path d="M 436.259015 181.733314 
L 813.937288 181.733314 
" clip-path="url(#p51cab06ae2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#me5fd12a7f9" x="436.259015" y="181.733314" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 0.3 -->
      <g transform="translate(414.946203 185.152611) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-33" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_63">
      <path d="M 436.259015 137.436002 
L 813.937288 137.436002 
" clip-path="url(#p51cab06ae2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#me5fd12a7f9" x="436.259015" y="137.436002" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 0.4 -->
      <g transform="translate(414.946203 140.855299) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_65">
      <path d="M 436.259015 93.138691 
L 813.937288 93.138691 
" clip-path="url(#p51cab06ae2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#me5fd12a7f9" x="436.259015" y="93.138691" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 0.5 -->
      <g transform="translate(414.946203 96.557987) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_67">
      <path d="M 436.259015 48.841379 
L 813.937288 48.841379 
" clip-path="url(#p51cab06ae2)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_68">
      <g>
       <use xlink:href="#me5fd12a7f9" x="436.259015" y="48.841379" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_42">
      <!-- 0.6 -->
      <g transform="translate(414.946203 52.260676) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_43">
     <!-- Neural Activation -->
     <g transform="translate(408.658547 233.746391) rotate(-90) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="line2d_69">
    <path d="M 440.975868 314.62525 
L 450.409572 312.467195 
L 459.843277 312.806357 
L 469.276982 312.916639 
L 478.710686 312.73777 
L 488.144391 312.979429 
L 497.578096 312.943314 
L 507.011801 313.116969 
L 516.445505 310.821932 
L 525.87921 310.632756 
L 535.312915 306.997655 
L 544.746619 307.215135 
L 554.180324 310.516418 
L 563.614029 310.050645 
L 573.047733 305.793487 
L 582.481438 294.760837 
L 591.915143 266.968374 
L 601.348847 285.966206 
L 610.782552 232.109215 
L 620.216257 277.421578 
L 629.649962 214.365541 
L 639.083666 239.49381 
L 648.517371 229.206131 
L 657.951076 170.914153 
L 667.38478 147.713625 
L 676.818485 120.99398 
L 686.25219 136.190796 
L 695.685894 88.719076 
L 705.119599 116.863996 
L 714.553304 57.695726 
" clip-path="url(#p51cab06ae2)" style="fill: none; stroke: #33a02c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_70">
    <path d="M 714.553304 314.62525 
L 714.553304 44.84925 
" clip-path="url(#p51cab06ae2)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #33a02c; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_71">
    <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#p51cab06ae2)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_72">
    <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#p51cab06ae2)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="patch_13">
    <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_14">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_44">
    <g id="patch_15">
     <path d="M 700.612124 114.0718 
L 795.053374 114.0718 
Q 798.253374 114.0718 798.253374 110.8718 
L 798.253374 58.33805 
Q 798.253374 55.13805 795.053374 55.13805 
L 700.612124 55.13805 
Q 697.412124 55.13805 697.412124 58.33805 
L 697.412124 110.8718 
Q 697.412124 114.0718 700.612124 114.0718 
z
" style="fill: #ffffff; opacity: 0.9; stroke: #808080; stroke-linejoin: miter"/>
    </g>
    <!-- Peak Distance: 5.9m -->
    <g transform="translate(712.787124 64.4168) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-3a" d="M 750 794 
L 1409 794 
L 1409 0 
L 750 0 
L 750 794 
z
M 750 3309 
L 1409 3309 
L 1409 2516 
L 750 2516 
L 750 3309 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-44" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="346.179688"/>
     <use xlink:href="#DejaVuSans-73" x="373.962891"/>
     <use xlink:href="#DejaVuSans-74" x="426.0625"/>
     <use xlink:href="#DejaVuSans-61" x="465.271484"/>
     <use xlink:href="#DejaVuSans-6e" x="526.550781"/>
     <use xlink:href="#DejaVuSans-63" x="589.929688"/>
     <use xlink:href="#DejaVuSans-65" x="644.910156"/>
     <use xlink:href="#DejaVuSans-3a" x="706.433594"/>
     <use xlink:href="#DejaVuSans-20" x="740.125"/>
     <use xlink:href="#DejaVuSans-35" x="771.912109"/>
     <use xlink:href="#DejaVuSans-2e" x="835.535156"/>
     <use xlink:href="#DejaVuSans-39" x="867.322266"/>
     <use xlink:href="#DejaVuSans-6d" x="930.945312"/>
    </g>
    <!-- Max Activation: 0.580 -->
    <g transform="translate(707.779624 73.37505) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-4d"/>
     <use xlink:href="#DejaVuSans-61" x="86.279297"/>
     <use xlink:href="#DejaVuSans-78" x="147.558594"/>
     <use xlink:href="#DejaVuSans-20" x="206.738281"/>
     <use xlink:href="#DejaVuSans-41" x="238.525391"/>
     <use xlink:href="#DejaVuSans-63" x="305.183594"/>
     <use xlink:href="#DejaVuSans-74" x="360.164062"/>
     <use xlink:href="#DejaVuSans-69" x="399.373047"/>
     <use xlink:href="#DejaVuSans-76" x="427.15625"/>
     <use xlink:href="#DejaVuSans-61" x="486.335938"/>
     <use xlink:href="#DejaVuSans-74" x="547.615234"/>
     <use xlink:href="#DejaVuSans-69" x="586.824219"/>
     <use xlink:href="#DejaVuSans-6f" x="614.607422"/>
     <use xlink:href="#DejaVuSans-6e" x="675.789062"/>
     <use xlink:href="#DejaVuSans-3a" x="739.167969"/>
     <use xlink:href="#DejaVuSans-20" x="772.859375"/>
     <use xlink:href="#DejaVuSans-30" x="804.646484"/>
     <use xlink:href="#DejaVuSans-2e" x="868.269531"/>
     <use xlink:href="#DejaVuSans-35" x="900.056641"/>
     <use xlink:href="#DejaVuSans-38" x="963.679688"/>
     <use xlink:href="#DejaVuSans-30" x="1027.302734"/>
    </g>
    <!-- Sparsity: 0.635 -->
    <g transform="translate(734.883374 82.3333) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-70" x="63.476562"/>
     <use xlink:href="#DejaVuSans-61" x="126.953125"/>
     <use xlink:href="#DejaVuSans-72" x="188.232422"/>
     <use xlink:href="#DejaVuSans-73" x="229.345703"/>
     <use xlink:href="#DejaVuSans-69" x="281.445312"/>
     <use xlink:href="#DejaVuSans-74" x="309.228516"/>
     <use xlink:href="#DejaVuSans-79" x="348.4375"/>
     <use xlink:href="#DejaVuSans-3a" x="400.367188"/>
     <use xlink:href="#DejaVuSans-20" x="434.058594"/>
     <use xlink:href="#DejaVuSans-30" x="465.845703"/>
     <use xlink:href="#DejaVuSans-2e" x="529.46875"/>
     <use xlink:href="#DejaVuSans-36" x="561.255859"/>
     <use xlink:href="#DejaVuSans-33" x="624.878906"/>
     <use xlink:href="#DejaVuSans-35" x="688.501953"/>
    </g>
    <!-- Peak Significance: 3.20 -->
    <g transform="translate(702.065874 91.29155) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-53" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="332.654297"/>
     <use xlink:href="#DejaVuSans-67" x="360.4375"/>
     <use xlink:href="#DejaVuSans-6e" x="423.914062"/>
     <use xlink:href="#DejaVuSans-69" x="487.292969"/>
     <use xlink:href="#DejaVuSans-66" x="515.076172"/>
     <use xlink:href="#DejaVuSans-69" x="550.28125"/>
     <use xlink:href="#DejaVuSans-63" x="578.064453"/>
     <use xlink:href="#DejaVuSans-61" x="633.044922"/>
     <use xlink:href="#DejaVuSans-6e" x="694.324219"/>
     <use xlink:href="#DejaVuSans-63" x="757.703125"/>
     <use xlink:href="#DejaVuSans-65" x="812.683594"/>
     <use xlink:href="#DejaVuSans-3a" x="874.207031"/>
     <use xlink:href="#DejaVuSans-20" x="907.898438"/>
     <use xlink:href="#DejaVuSans-33" x="939.685547"/>
     <use xlink:href="#DejaVuSans-2e" x="1003.308594"/>
     <use xlink:href="#DejaVuSans-32" x="1035.095703"/>
     <use xlink:href="#DejaVuSans-30" x="1098.71875"/>
    </g>
    <!-- Selectivity Index: 0.838 -->
    <g transform="translate(700.612124 100.2498) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-65" x="63.476562"/>
     <use xlink:href="#DejaVuSans-6c" x="125"/>
     <use xlink:href="#DejaVuSans-65" x="152.783203"/>
     <use xlink:href="#DejaVuSans-63" x="214.306641"/>
     <use xlink:href="#DejaVuSans-74" x="269.287109"/>
     <use xlink:href="#DejaVuSans-69" x="308.496094"/>
     <use xlink:href="#DejaVuSans-76" x="336.279297"/>
     <use xlink:href="#DejaVuSans-69" x="395.458984"/>
     <use xlink:href="#DejaVuSans-74" x="423.242188"/>
     <use xlink:href="#DejaVuSans-79" x="462.451172"/>
     <use xlink:href="#DejaVuSans-20" x="521.630859"/>
     <use xlink:href="#DejaVuSans-49" x="553.417969"/>
     <use xlink:href="#DejaVuSans-6e" x="582.910156"/>
     <use xlink:href="#DejaVuSans-64" x="646.289062"/>
     <use xlink:href="#DejaVuSans-65" x="709.765625"/>
     <use xlink:href="#DejaVuSans-78" x="769.539062"/>
     <use xlink:href="#DejaVuSans-3a" x="828.71875"/>
     <use xlink:href="#DejaVuSans-20" x="862.410156"/>
     <use xlink:href="#DejaVuSans-30" x="894.197266"/>
     <use xlink:href="#DejaVuSans-2e" x="957.820312"/>
     <use xlink:href="#DejaVuSans-38" x="989.607422"/>
     <use xlink:href="#DejaVuSans-33" x="1053.230469"/>
     <use xlink:href="#DejaVuSans-38" x="1116.853516"/>
    </g>
    <!-- Peak Width: 1.2m -->
    <g transform="translate(724.529624 109.20805) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-57" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="365.804688"/>
     <use xlink:href="#DejaVuSans-64" x="393.587891"/>
     <use xlink:href="#DejaVuSans-74" x="457.064453"/>
     <use xlink:href="#DejaVuSans-68" x="496.273438"/>
     <use xlink:href="#DejaVuSans-3a" x="559.652344"/>
     <use xlink:href="#DejaVuSans-20" x="593.34375"/>
     <use xlink:href="#DejaVuSans-31" x="625.130859"/>
     <use xlink:href="#DejaVuSans-2e" x="688.753906"/>
     <use xlink:href="#DejaVuSans-32" x="720.541016"/>
     <use xlink:href="#DejaVuSans-6d" x="784.164062"/>
    </g>
   </g>
   <g id="text_45">
    <!-- Distance Tuning Curve -->
    <g transform="translate(549.284402 16.318125) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-54" d="M 31 4666 
L 4331 4666 
L 4331 3756 
L 2784 3756 
L 2784 0 
L 1581 0 
L 1581 3756 
L 31 3756 
L 31 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-44"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
     <use xlink:href="#DejaVuSans-Bold-54" x="525.195312"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="582.408203"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="653.599609"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="724.791016"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="759.068359"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="830.259766"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="901.841797"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="936.65625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1010.044922"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1081.236328"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="1130.552734"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1195.738281"/>
    </g>
    <!-- Peak: 5.9m, Sparsity: 0.635 -->
    <g transform="translate(531.359402 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-6b" d="M 538 4863 
L 1656 4863 
L 1656 2216 
L 2944 3500 
L 4244 3500 
L 2534 1894 
L 4378 0 
L 3022 0 
L 1656 1459 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-3a" d="M 716 3500 
L 1844 3500 
L 1844 2291 
L 716 2291 
L 716 3500 
z
M 716 1209 
L 1844 1209 
L 1844 0 
L 716 0 
L 716 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-39" d="M 641 103 
L 641 966 
Q 928 831 1190 764 
Q 1453 697 1709 697 
Q 2247 697 2547 995 
Q 2847 1294 2900 1881 
Q 2688 1725 2447 1647 
Q 2206 1569 1925 1569 
Q 1209 1569 770 1986 
Q 331 2403 331 3084 
Q 331 3838 820 4291 
Q 1309 4744 2131 4744 
Q 3044 4744 3544 4128 
Q 4044 3513 4044 2388 
Q 4044 1231 3459 570 
Q 2875 -91 1856 -91 
Q 1528 -91 1228 -42 
Q 928 6 641 103 
z
M 2125 2350 
Q 2441 2350 2600 2554 
Q 2759 2759 2759 3169 
Q 2759 3575 2600 3781 
Q 2441 3988 2125 3988 
Q 1809 3988 1650 3781 
Q 1491 3575 1491 3169 
Q 1491 2759 1650 2554 
Q 1809 2350 2125 2350 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2c" d="M 653 1209 
L 1778 1209 
L 1778 256 
L 1006 -909 
L 341 -909 
L 653 256 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-53" d="M 3834 4519 
L 3834 3531 
Q 3450 3703 3084 3790 
Q 2719 3878 2394 3878 
Q 1963 3878 1756 3759 
Q 1550 3641 1550 3391 
Q 1550 3203 1689 3098 
Q 1828 2994 2194 2919 
L 2706 2816 
Q 3484 2659 3812 2340 
Q 4141 2022 4141 1434 
Q 4141 663 3683 286 
Q 3225 -91 2284 -91 
Q 1841 -91 1394 -6 
Q 947 78 500 244 
L 500 1259 
Q 947 1022 1364 901 
Q 1781 781 2169 781 
Q 2563 781 2772 912 
Q 2981 1044 2981 1288 
Q 2981 1506 2839 1625 
Q 2697 1744 2272 1838 
L 1806 1941 
Q 1106 2091 782 2419 
Q 459 2747 459 3303 
Q 459 4000 909 4375 
Q 1359 4750 2203 4750 
Q 2588 4750 2994 4692 
Q 3400 4634 3834 4519 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-79" d="M 78 3500 
L 1197 3500 
L 2138 1125 
L 2938 3500 
L 4056 3500 
L 2584 -331 
Q 2363 -916 2067 -1148 
Q 1772 -1381 1288 -1381 
L 641 -1381 
L 641 -647 
L 991 -647 
Q 1275 -647 1404 -556 
Q 1534 -466 1606 -231 
L 1638 -134 
L 78 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-36" d="M 2316 2303 
Q 2000 2303 1842 2098 
Q 1684 1894 1684 1484 
Q 1684 1075 1842 870 
Q 2000 666 2316 666 
Q 2634 666 2792 870 
Q 2950 1075 2950 1484 
Q 2950 1894 2792 2098 
Q 2634 2303 2316 2303 
z
M 3803 4544 
L 3803 3681 
Q 3506 3822 3243 3889 
Q 2981 3956 2731 3956 
Q 2194 3956 1894 3657 
Q 1594 3359 1544 2772 
Q 1750 2925 1990 3001 
Q 2231 3078 2516 3078 
Q 3231 3078 3670 2659 
Q 4109 2241 4109 1563 
Q 4109 813 3618 361 
Q 3128 -91 2303 -91 
Q 1394 -91 895 523 
Q 397 1138 397 2266 
Q 397 3422 980 4083 
Q 1563 4744 2578 4744 
Q 2900 4744 3203 4694 
Q 3506 4644 3803 4544 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-50"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="73.291016"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="141.113281"/>
     <use xlink:href="#DejaVuSans-Bold-6b" x="208.59375"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="275.097656"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="315.087891"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="349.902344"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="419.482422"/>
     <use xlink:href="#DejaVuSans-Bold-39" x="457.470703"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="527.050781"/>
     <use xlink:href="#DejaVuSans-Bold-2c" x="631.25"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="669.238281"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="704.052734"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="776.074219"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="847.65625"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="915.136719"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="964.453125"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1023.974609"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="1058.251953"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="1106.054688"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="1171.240234"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1211.230469"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1246.044922"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="1315.625"/>
     <use xlink:href="#DejaVuSans-Bold-36" x="1353.613281"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="1423.193359"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="1492.773438"/>
    </g>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="m0a7ac705be" d="M 0 5 
C 1.326016 5 2.597899 4.473168 3.535534 3.535534 
C 4.473168 2.597899 5 1.326016 5 0 
C 5 -1.326016 4.473168 -2.597899 3.535534 -3.535534 
C 2.597899 -4.473168 1.326016 -5 0 -5 
C -1.326016 -5 -2.597899 -4.473168 -3.535534 -3.535534 
C -4.473168 -2.597899 -5 -1.326016 -5 0 
C -5 1.326016 -4.473168 2.597899 -3.535534 3.535534 
C -2.597899 4.473168 -1.326016 5 0 5 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#p51cab06ae2)">
     <use xlink:href="#m0a7ac705be" x="714.553304" y="57.695726" style="fill: #33a02c; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="patch_16">
     <path d="M 441.859015 86.47675 
L 558.766515 86.47675 
Q 560.366515 86.47675 560.366515 84.87675 
L 560.366515 50.44925 
Q 560.366515 48.84925 558.766515 48.84925 
L 441.859015 48.84925 
Q 440.259015 48.84925 440.259015 50.44925 
L 440.259015 84.87675 
Q 440.259015 86.47675 441.859015 86.47675 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_73">
     <path d="M 443.459015 55.328 
L 451.459015 55.328 
L 459.459015 55.328 
" style="fill: none; stroke: #33a02c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
    </g>
    <g id="text_46">
     <!-- Tuning Curve -->
     <g transform="translate(465.859015 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-54"/>
      <use xlink:href="#DejaVuSans-75" x="45.958984"/>
      <use xlink:href="#DejaVuSans-6e" x="109.337891"/>
      <use xlink:href="#DejaVuSans-69" x="172.716797"/>
      <use xlink:href="#DejaVuSans-6e" x="200.5"/>
      <use xlink:href="#DejaVuSans-67" x="263.878906"/>
      <use xlink:href="#DejaVuSans-20" x="327.355469"/>
      <use xlink:href="#DejaVuSans-43" x="359.142578"/>
      <use xlink:href="#DejaVuSans-75" x="428.966797"/>
      <use xlink:href="#DejaVuSans-72" x="492.345703"/>
      <use xlink:href="#DejaVuSans-76" x="533.458984"/>
      <use xlink:href="#DejaVuSans-65" x="592.638672"/>
     </g>
    </g>
    <g id="line2d_74">
     <path d="M 443.459015 67.0705 
L 451.459015 67.0705 
L 459.459015 67.0705 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_47">
     <!-- Close threshold (3.0m) -->
     <g transform="translate(465.859015 69.8705) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6c" x="69.824219"/>
      <use xlink:href="#DejaVuSans-6f" x="97.607422"/>
      <use xlink:href="#DejaVuSans-73" x="158.789062"/>
      <use xlink:href="#DejaVuSans-65" x="210.888672"/>
      <use xlink:href="#DejaVuSans-20" x="272.412109"/>
      <use xlink:href="#DejaVuSans-74" x="304.199219"/>
      <use xlink:href="#DejaVuSans-68" x="343.408203"/>
      <use xlink:href="#DejaVuSans-72" x="406.787109"/>
      <use xlink:href="#DejaVuSans-65" x="445.650391"/>
      <use xlink:href="#DejaVuSans-73" x="507.173828"/>
      <use xlink:href="#DejaVuSans-68" x="559.273438"/>
      <use xlink:href="#DejaVuSans-6f" x="622.652344"/>
      <use xlink:href="#DejaVuSans-6c" x="683.833984"/>
      <use xlink:href="#DejaVuSans-64" x="711.617188"/>
      <use xlink:href="#DejaVuSans-20" x="775.09375"/>
      <use xlink:href="#DejaVuSans-28" x="806.880859"/>
      <use xlink:href="#DejaVuSans-33" x="845.894531"/>
      <use xlink:href="#DejaVuSans-2e" x="909.517578"/>
      <use xlink:href="#DejaVuSans-30" x="941.304688"/>
      <use xlink:href="#DejaVuSans-6d" x="1004.927734"/>
      <use xlink:href="#DejaVuSans-29" x="1102.339844"/>
     </g>
    </g>
    <g id="line2d_75">
     <path d="M 443.459015 78.813 
L 451.459015 78.813 
L 459.459015 78.813 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_48">
     <!-- Far threshold (5.0m) -->
     <g transform="translate(465.859015 81.613) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" x="48.394531"/>
      <use xlink:href="#DejaVuSans-72" x="109.673828"/>
      <use xlink:href="#DejaVuSans-20" x="150.787109"/>
      <use xlink:href="#DejaVuSans-74" x="182.574219"/>
      <use xlink:href="#DejaVuSans-68" x="221.783203"/>
      <use xlink:href="#DejaVuSans-72" x="285.162109"/>
      <use xlink:href="#DejaVuSans-65" x="324.025391"/>
      <use xlink:href="#DejaVuSans-73" x="385.548828"/>
      <use xlink:href="#DejaVuSans-68" x="437.648438"/>
      <use xlink:href="#DejaVuSans-6f" x="501.027344"/>
      <use xlink:href="#DejaVuSans-6c" x="562.208984"/>
      <use xlink:href="#DejaVuSans-64" x="589.992188"/>
      <use xlink:href="#DejaVuSans-20" x="653.46875"/>
      <use xlink:href="#DejaVuSans-28" x="685.255859"/>
      <use xlink:href="#DejaVuSans-35" x="724.269531"/>
      <use xlink:href="#DejaVuSans-2e" x="787.892578"/>
      <use xlink:href="#DejaVuSans-30" x="819.679688"/>
      <use xlink:href="#DejaVuSans-6d" x="883.302734"/>
      <use xlink:href="#DejaVuSans-29" x="980.714844"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_17">
    <path d="M 330.77382 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 330.77382 71.82685 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAABcAAAHBCAYAAACCOiAmAAACGUlEQVR4nO2b0W0bURDEnqRz3+k7VpwCjvkcAQzIAhbEzuzpLMiPc379nBHXOddq9nnOJp9zrnO+ZsPX5u0ch7eWO1URqYqIuope811bWgtSFZGqiFRFpJ0jvVog6ip6zXue3/Ee0eOcn9mXOVPzhv9nw6/z2A0fm7+Ww3cPxfVadg/FzJmqiJgD9ZpXRcAcqNe8KgLmQL3mVREwB+o1r4qAOVCveVUEzIF6zYdVHJsPe24ONHPAe0Q9z5HMkal5a0EyR3oqIuZAM//0cO9aMkd6KiLmQDPn4bNfWs3X8p4Nzxy5nq/v3fDry7qWlzZQs/myit61PLXmJ/M7a/PlEYnXkvmdqogUKJL5v4bver5dS0dEeM2rIuIN1GteFRFvoF5z9Vo6ojtVEamKSFX8/HDvWjoiZHpE6kC95lURhpsDzfxOR4R0RIjavCrCcHOgXvOqCMPNgWZ+x3tEfSWCZI5URUQdaOY4vPeWO95Ax+bvZRW/tVV8/5kNHwe6W7nY/Hrsar7+rxxtoJnz8KoIZI5M22JeS+aAt4renRco4g3Ua16gSOZIVUQKFPF+EpnX4jXv/D893LuWqohURaRAEa+5eS1VEfCaFyiSOdKfLUjmiLeK3p0XKOIN1GteoEjmSFVErt/aQIe/hVzvfPmWK16L1nwovt65dXiBIu2ch7cWoioiVRGpiojXvECRjgipikhVRLw7967lLyPgue9DbEF4AAAAAElFTkSuQmCC" id="imagede3904cdbb" transform="scale(1 -1) translate(0 -215.52)" x="330.72" y="-71.52" width="11.04" height="215.52"/>
   <g id="matplotlib.axis_5"/>
   <g id="matplotlib.axis_6">
    <g id="ytick_17">
     <g id="line2d_76">
      <defs>
       <path id="m3342ebb9dd" d="M 0 0 
L 3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m3342ebb9dd" x="341.56486" y="287.64765" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_49">
      <!-- 0.0 -->
      <g transform="translate(348.56486 291.066947) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_77">
      <g>
       <use xlink:href="#m3342ebb9dd" x="341.56486" y="244.48349" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_50">
      <!-- 0.2 -->
      <g transform="translate(348.56486 247.902787) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_78">
      <g>
       <use xlink:href="#m3342ebb9dd" x="341.56486" y="201.31933" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_51">
      <!-- 0.4 -->
      <g transform="translate(348.56486 204.738627) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_79">
      <g>
       <use xlink:href="#m3342ebb9dd" x="341.56486" y="158.15517" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_52">
      <!-- 0.6 -->
      <g transform="translate(348.56486 161.574467) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_80">
      <g>
       <use xlink:href="#m3342ebb9dd" x="341.56486" y="114.99101" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_53">
      <!-- 0.8 -->
      <g transform="translate(348.56486 118.410307) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-38" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_81">
      <g>
       <use xlink:href="#m3342ebb9dd" x="341.56486" y="71.82685" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_54">
      <!-- 1.0 -->
      <g transform="translate(348.56486 75.246147) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_55">
     <!-- Neural Activation -->
     <g transform="translate(369.519391 125.728109) rotate(-270) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_18">
    <path d="M 330.77382 287.64765 
L 336.16934 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 336.16934 71.82685 
L 330.77382 71.82685 
L 330.77382 287.64765 
z
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p347f4f76ea">
   <rect x="42.113906" y="44.84925" width="269.776" height="269.776"/>
  </clipPath>
  <clipPath id="p51cab06ae2">
   <rect x="436.259015" y="44.84925" width="377.678273" height="269.776"/>
  </clipPath>
 </defs>
</svg>
