<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="1090.772683pt" height="823.59625pt" viewBox="0 0 1090.772683 823.59625" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:56:34.727451</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 823.59625 
L 1090.772683 823.59625 
L 1090.772683 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.113906 380.010435 
L 354.444341 380.010435 
L 354.444341 67.68 
L 42.113906 67.68 
z
" style="fill: #ffffff"/>
   </g>
   <g clip-path="url(#p2aa7b21e81)">
    <image xlink:href="data:image/png;base64,
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" id="imagebc87c97dbd" transform="scale(1 -1) translate(0 -312.48)" x="42.113906" y="-67.530435" width="312.48" height="312.48"/>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="m7876762515" d="M 0 -4.472136 
L -1.004057 -1.381966 
L -4.253254 -1.381966 
L -1.624598 0.527864 
L -2.628656 3.618034 
L -0 1.708204 
L 2.628656 3.618034 
L 1.624598 0.527864 
L 4.253254 -1.381966 
L 1.004057 -1.381966 
z
" style="stroke: #000000; stroke-width: 1.5"/>
    </defs>
    <g clip-path="url(#p2aa7b21e81)">
     <use xlink:href="#m7876762515" x="198.279124" y="223.845217" style="fill: #00ffff; stroke: #000000; stroke-width: 1.5"/>
    </g>
   </g>
   <g id="patch_3">
    <path d="M 198.279124 282.407174 
C 213.809936 282.407174 228.706737 276.236717 239.68868 265.254774 
C 250.670623 254.272831 256.84108 239.37603 256.84108 223.845217 
C 256.84108 208.314405 250.670623 193.417604 239.68868 182.435661 
C 228.706737 171.453718 213.809936 165.283261 198.279124 165.283261 
C 182.748311 165.283261 167.85151 171.453718 156.869567 182.435661 
C 145.887624 193.417604 139.717167 208.314405 139.717167 223.845217 
C 139.717167 239.37603 145.887624 254.272831 156.869567 265.254774 
C 167.85151 276.236717 182.748311 282.407174 198.279124 282.407174 
L 198.279124 282.407174 
z
" clip-path="url(#p2aa7b21e81)" style="fill: none; opacity: 0.6; stroke-dasharray: 3.7,1.6; stroke-dashoffset: 0; stroke: #ffffff; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 198.279124 321.448478 
C 224.163811 321.448478 248.991813 311.164383 267.295051 292.861145 
C 285.598289 274.557907 295.882385 249.729905 295.882385 223.845217 
C 295.882385 197.96053 285.598289 173.132528 267.295051 154.82929 
C 248.991813 136.526052 224.163811 126.241957 198.279124 126.241957 
C 172.394436 126.241957 147.566434 136.526052 129.263196 154.82929 
C 110.959958 173.132528 100.675863 197.96053 100.675863 223.845217 
C 100.675863 249.729905 110.959958 274.557907 129.263196 292.861145 
C 147.566434 311.164383 172.394436 321.448478 198.279124 321.448478 
L 198.279124 321.448478 
z
" clip-path="url(#p2aa7b21e81)" style="fill: none; opacity: 0.6; stroke-dasharray: 3.7,1.6; stroke-dashoffset: 0; stroke: #ffffff; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 198.279124 380.010435 
C 239.694623 380.010435 279.419427 363.555882 308.704608 334.270702 
C 337.989789 304.985521 354.444341 265.260717 354.444341 223.845217 
C 354.444341 182.429718 337.989789 142.704914 308.704608 113.419733 
C 279.419427 84.134552 239.694623 67.68 198.279124 67.68 
C 156.863624 67.68 117.13882 84.134552 87.853639 113.419733 
C 58.568459 142.704914 42.113906 182.429718 42.113906 223.845217 
C 42.113906 265.260717 58.568459 304.985521 87.853639 334.270702 
C 117.13882 363.555882 156.863624 380.010435 198.279124 380.010435 
L 198.279124 380.010435 
z
" clip-path="url(#p2aa7b21e81)" style="fill: none; opacity: 0.6; stroke: #ffffff; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <defs>
       <path id="m25a591995f" d="M 0 0 
L 0 3.5 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m25a591995f" x="42.113906" y="380.010435" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −8 -->
      <g transform="translate(35.479922 393.849029) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_2">
      <g>
       <use xlink:href="#m25a591995f" x="81.155211" y="380.010435" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −6 -->
      <g transform="translate(74.521226 393.849029) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_3">
      <g>
       <use xlink:href="#m25a591995f" x="120.196515" y="380.010435" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_3">
      <!-- −4 -->
      <g transform="translate(113.562531 393.849029) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_4">
      <g>
       <use xlink:href="#m25a591995f" x="159.237819" y="380.010435" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_4">
      <!-- −2 -->
      <g transform="translate(152.603835 393.849029) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_5">
      <g>
       <use xlink:href="#m25a591995f" x="198.279124" y="380.010435" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(195.415999 393.849029) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_6">
      <g>
       <use xlink:href="#m25a591995f" x="237.320428" y="380.010435" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 2 -->
      <g transform="translate(234.457303 393.849029) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_7">
      <g>
       <use xlink:href="#m25a591995f" x="276.361732" y="380.010435" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 4 -->
      <g transform="translate(273.498607 393.849029) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_8">
      <g>
       <use xlink:href="#m25a591995f" x="315.403037" y="380.010435" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 6 -->
      <g transform="translate(312.539912 393.849029) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_9">
      <g>
       <use xlink:href="#m25a591995f" x="354.444341" y="380.010435" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 8 -->
      <g transform="translate(351.581216 393.849029) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- Relative X (m) -->
     <g transform="translate(154.058264 408.079029) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-52" d="M 2297 2597 
Q 2675 2597 2839 2737 
Q 3003 2878 3003 3200 
Q 3003 3519 2839 3656 
Q 2675 3794 2297 3794 
L 1791 3794 
L 1791 2597 
L 2297 2597 
z
M 1791 1766 
L 1791 0 
L 588 0 
L 588 4666 
L 2425 4666 
Q 3347 4666 3776 4356 
Q 4206 4047 4206 3378 
Q 4206 2916 3982 2619 
Q 3759 2322 3309 2181 
Q 3556 2125 3751 1926 
Q 3947 1728 4147 1325 
L 4800 0 
L 3519 0 
L 2950 1159 
Q 2778 1509 2601 1637 
Q 2425 1766 2131 1766 
L 1791 1766 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6c" d="M 538 4863 
L 1656 4863 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-76" d="M 97 3500 
L 1216 3500 
L 2088 1081 
L 2956 3500 
L 4078 3500 
L 2700 0 
L 1472 0 
L 97 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-58" d="M 3188 2381 
L 4806 0 
L 3553 0 
L 2463 1594 
L 1381 0 
L 122 0 
L 1741 2381 
L 184 4666 
L 1441 4666 
L 2463 3163 
L 3481 4666 
L 4744 4666 
L 3188 2381 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-28" d="M 2413 -844 
L 1484 -844 
Q 1006 -72 778 623 
Q 550 1319 550 2003 
Q 550 2688 779 3389 
Q 1009 4091 1484 4856 
L 2413 4856 
Q 2013 4116 1813 3408 
Q 1613 2700 1613 2009 
Q 1613 1319 1811 609 
Q 2009 -100 2413 -844 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6d" d="M 3781 2919 
Q 3994 3244 4286 3414 
Q 4578 3584 4928 3584 
Q 5531 3584 5847 3212 
Q 6163 2841 6163 2131 
L 6163 0 
L 5038 0 
L 5038 1825 
Q 5041 1866 5042 1909 
Q 5044 1953 5044 2034 
Q 5044 2406 4934 2573 
Q 4825 2741 4581 2741 
Q 4263 2741 4089 2478 
Q 3916 2216 3909 1719 
L 3909 0 
L 2784 0 
L 2784 1825 
Q 2784 2406 2684 2573 
Q 2584 2741 2328 2741 
Q 2006 2741 1831 2477 
Q 1656 2213 1656 1722 
L 1656 0 
L 531 0 
L 531 3500 
L 1656 3500 
L 1656 2988 
Q 1863 3284 2130 3434 
Q 2397 3584 2719 3584 
Q 3081 3584 3359 3409 
Q 3638 3234 3781 2919 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-29" d="M 513 -844 
Q 913 -100 1113 609 
Q 1313 1319 1313 2009 
Q 1313 2700 1113 3408 
Q 913 4116 513 4856 
L 1441 4856 
Q 1916 4091 2145 3389 
Q 2375 2688 2375 2003 
Q 2375 1319 2147 623 
Q 1919 -72 1441 -844 
L 513 -844 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-58" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="573.583984"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="608.398438"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="654.101562"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="758.300781"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_10">
      <defs>
       <path id="m175ef55289" d="M 0 0 
L -3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m175ef55289" x="42.113906" y="380.010435" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_11">
      <!-- −8 -->
      <g transform="translate(21.845937 383.429732) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_11">
      <g>
       <use xlink:href="#m175ef55289" x="42.113906" y="340.96913" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_12">
      <!-- −6 -->
      <g transform="translate(21.845937 344.388427) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_12">
      <g>
       <use xlink:href="#m175ef55289" x="42.113906" y="301.927826" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_13">
      <!-- −4 -->
      <g transform="translate(21.845937 305.347123) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_13">
      <g>
       <use xlink:href="#m175ef55289" x="42.113906" y="262.886522" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_14">
      <!-- −2 -->
      <g transform="translate(21.845937 266.305819) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_14">
      <g>
       <use xlink:href="#m175ef55289" x="42.113906" y="223.845217" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 0 -->
      <g transform="translate(29.387656 227.264514) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_15">
      <g>
       <use xlink:href="#m175ef55289" x="42.113906" y="184.803913" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 2 -->
      <g transform="translate(29.387656 188.22321) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_16">
      <g>
       <use xlink:href="#m175ef55289" x="42.113906" y="145.762609" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 4 -->
      <g transform="translate(29.387656 149.181906) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_17">
      <g>
       <use xlink:href="#m175ef55289" x="42.113906" y="106.721304" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 6 -->
      <g transform="translate(29.387656 110.140601) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_18">
      <g>
       <use xlink:href="#m175ef55289" x="42.113906" y="67.68" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 8 -->
      <g transform="translate(29.387656 71.099297) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_20">
     <!-- Relative Y (m) -->
     <g transform="translate(15.558281 267.808264) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-59" d="M -63 4666 
L 1253 4666 
L 2316 3003 
L 3378 4666 
L 4697 4666 
L 2919 1966 
L 2919 0 
L 1716 0 
L 1716 1966 
L -63 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-59" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="568.896484"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="603.710938"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="649.414062"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="753.613281"/>
     </g>
    </g>
   </g>
   <g id="patch_6">
    <path d="M 42.113906 380.010435 
L 42.113906 67.68 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 42.113906 380.010435 
L 354.444341 380.010435 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_21">
    <!-- 3.0m -->
    <g style="fill: #ffffff" transform="translate(228.018743 185.059348) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-Bold-33" d="M 2981 2516 
Q 3453 2394 3698 2092 
Q 3944 1791 3944 1325 
Q 3944 631 3412 270 
Q 2881 -91 1863 -91 
Q 1503 -91 1142 -33 
Q 781 25 428 141 
L 428 1069 
Q 766 900 1098 814 
Q 1431 728 1753 728 
Q 2231 728 2486 893 
Q 2741 1059 2741 1369 
Q 2741 1688 2480 1852 
Q 2219 2016 1709 2016 
L 1228 2016 
L 1228 2791 
L 1734 2791 
Q 2188 2791 2409 2933 
Q 2631 3075 2631 3366 
Q 2631 3634 2415 3781 
Q 2200 3928 1806 3928 
Q 1516 3928 1219 3862 
Q 922 3797 628 3669 
L 628 4550 
Q 984 4650 1334 4700 
Q 1684 4750 2022 4750 
Q 2931 4750 3382 4451 
Q 3834 4153 3834 3553 
Q 3834 3144 3618 2883 
Q 3403 2622 2981 2516 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2e" d="M 653 1209 
L 1778 1209 
L 1778 0 
L 653 0 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-30" d="M 2944 2338 
Q 2944 3213 2780 3570 
Q 2616 3928 2228 3928 
Q 1841 3928 1675 3570 
Q 1509 3213 1509 2338 
Q 1509 1453 1675 1090 
Q 1841 728 2228 728 
Q 2613 728 2778 1090 
Q 2944 1453 2944 2338 
z
M 4147 2328 
Q 4147 1169 3647 539 
Q 3147 -91 2228 -91 
Q 1306 -91 806 539 
Q 306 1169 306 2328 
Q 306 3491 806 4120 
Q 1306 4750 2228 4750 
Q 3147 4750 3647 4120 
Q 4147 3491 4147 2328 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-33"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_22">
    <!-- 5.0m -->
    <g style="fill: #ffffff" transform="translate(255.347656 157.730435) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-Bold-35" d="M 678 4666 
L 3669 4666 
L 3669 3781 
L 1638 3781 
L 1638 3059 
Q 1775 3097 1914 3117 
Q 2053 3138 2203 3138 
Q 3056 3138 3531 2711 
Q 4006 2284 4006 1522 
Q 4006 766 3489 337 
Q 2972 -91 2053 -91 
Q 1656 -91 1267 -14 
Q 878 63 494 219 
L 494 1166 
Q 875 947 1217 837 
Q 1559 728 1863 728 
Q 2300 728 2551 942 
Q 2803 1156 2803 1522 
Q 2803 1891 2551 2103 
Q 2300 2316 1863 2316 
Q 1603 2316 1309 2248 
Q 1016 2181 678 2041 
L 678 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-35"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_23">
    <!-- 8.0m -->
    <g style="fill: #ffffff" transform="translate(296.341026 116.737065) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-Bold-38" d="M 2228 2088 
Q 1891 2088 1709 1903 
Q 1528 1719 1528 1375 
Q 1528 1031 1709 848 
Q 1891 666 2228 666 
Q 2563 666 2741 848 
Q 2919 1031 2919 1375 
Q 2919 1722 2741 1905 
Q 2563 2088 2228 2088 
z
M 1350 2484 
Q 925 2613 709 2878 
Q 494 3144 494 3541 
Q 494 4131 934 4440 
Q 1375 4750 2228 4750 
Q 3075 4750 3515 4442 
Q 3956 4134 3956 3541 
Q 3956 3144 3739 2878 
Q 3522 2613 3097 2484 
Q 3572 2353 3814 2058 
Q 4056 1763 4056 1313 
Q 4056 619 3595 264 
Q 3134 -91 2228 -91 
Q 1319 -91 855 264 
Q 391 619 391 1313 
Q 391 1763 633 2058 
Q 875 2353 1350 2484 
z
M 1631 3419 
Q 1631 3141 1786 2991 
Q 1941 2841 2228 2841 
Q 2509 2841 2662 2991 
Q 2816 3141 2816 3419 
Q 2816 3697 2662 3845 
Q 2509 3994 2228 3994 
Q 1941 3994 1786 3844 
Q 1631 3694 1631 3419 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-38"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_24">
    <!-- (i) Close-Distance Cell -->
    <g transform="translate(130.040452 49.362406) scale(0.11 -0.11)">
     <defs>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-6f" d="M 2203 2784 
Q 1831 2784 1636 2517 
Q 1441 2250 1441 1747 
Q 1441 1244 1636 976 
Q 1831 709 2203 709 
Q 2569 709 2762 976 
Q 2956 1244 2956 1747 
Q 2956 2250 2762 2517 
Q 2569 2784 2203 2784 
z
M 2203 3584 
Q 3106 3584 3614 3096 
Q 4122 2609 4122 1747 
Q 4122 884 3614 396 
Q 3106 -91 2203 -91 
Q 1297 -91 786 396 
Q 275 884 275 1747 
Q 275 2609 786 3096 
Q 1297 3584 2203 3584 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2d" d="M 347 2297 
L 2309 2297 
L 2309 1388 
L 347 1388 
L 347 2297 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-44" d="M 1791 3756 
L 1791 909 
L 2222 909 
Q 2959 909 3348 1275 
Q 3738 1641 3738 2338 
Q 3738 3031 3350 3393 
Q 2963 3756 2222 3756 
L 1791 3756 
z
M 588 4666 
L 1856 4666 
Q 2919 4666 3439 4514 
Q 3959 4363 4331 4000 
Q 4659 3684 4818 3271 
Q 4978 2859 4978 2338 
Q 4978 1809 4818 1395 
Q 4659 981 4331 666 
Q 3956 303 3431 151 
Q 2906 0 1856 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-28"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="45.703125"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="79.980469"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="125.683594"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="160.498047"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="233.886719"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="268.164062"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="336.865234"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="396.386719"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="464.208984"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="505.712891"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="588.720703"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="622.998047"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="682.519531"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="730.322266"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="797.802734"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="868.994141"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="928.271484"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="996.09375"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="1030.908203"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1104.296875"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="1172.119141"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="1206.396484"/>
    </g>
    <!-- Neuron 51, Peak: 0.9m -->
    <g transform="translate(127.333421 61.68) scale(0.11 -0.11)">
     <defs>
      <path id="DejaVuSans-Bold-4e" d="M 588 4666 
L 1931 4666 
L 3628 1466 
L 3628 4666 
L 4769 4666 
L 4769 0 
L 3425 0 
L 1728 3200 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-75" d="M 500 1363 
L 500 3500 
L 1625 3500 
L 1625 3150 
Q 1625 2866 1622 2436 
Q 1619 2006 1619 1863 
Q 1619 1441 1641 1255 
Q 1663 1069 1716 984 
Q 1784 875 1895 815 
Q 2006 756 2150 756 
Q 2500 756 2700 1025 
Q 2900 1294 2900 1772 
L 2900 3500 
L 4019 3500 
L 4019 0 
L 2900 0 
L 2900 506 
Q 2647 200 2364 54 
Q 2081 -91 1741 -91 
Q 1134 -91 817 281 
Q 500 653 500 1363 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-31" d="M 750 831 
L 1813 831 
L 1813 3847 
L 722 3622 
L 722 4441 
L 1806 4666 
L 2950 4666 
L 2950 831 
L 4013 831 
L 4013 0 
L 750 0 
L 750 831 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2c" d="M 653 1209 
L 1778 1209 
L 1778 256 
L 1006 -909 
L 341 -909 
L 653 256 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-50" d="M 588 4666 
L 2584 4666 
Q 3475 4666 3951 4270 
Q 4428 3875 4428 3144 
Q 4428 2409 3951 2014 
Q 3475 1619 2584 1619 
L 1791 1619 
L 1791 0 
L 588 0 
L 588 4666 
z
M 1791 3794 
L 1791 2491 
L 2456 2491 
Q 2806 2491 2997 2661 
Q 3188 2831 3188 3144 
Q 3188 3456 2997 3625 
Q 2806 3794 2456 3794 
L 1791 3794 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-6b" d="M 538 4863 
L 1656 4863 
L 1656 2216 
L 2944 3500 
L 4244 3500 
L 2534 1894 
L 4378 0 
L 3022 0 
L 1656 1459 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-3a" d="M 716 3500 
L 1844 3500 
L 1844 2291 
L 716 2291 
L 716 3500 
z
M 716 1209 
L 1844 1209 
L 1844 0 
L 716 0 
L 716 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-39" d="M 641 103 
L 641 966 
Q 928 831 1190 764 
Q 1453 697 1709 697 
Q 2247 697 2547 995 
Q 2847 1294 2900 1881 
Q 2688 1725 2447 1647 
Q 2206 1569 1925 1569 
Q 1209 1569 770 1986 
Q 331 2403 331 3084 
Q 331 3838 820 4291 
Q 1309 4744 2131 4744 
Q 3044 4744 3544 4128 
Q 4044 3513 4044 2388 
Q 4044 1231 3459 570 
Q 2875 -91 1856 -91 
Q 1528 -91 1228 -42 
Q 928 6 641 103 
z
M 2125 2350 
Q 2441 2350 2600 2554 
Q 2759 2759 2759 3169 
Q 2759 3575 2600 3781 
Q 2441 3988 2125 3988 
Q 1809 3988 1650 3781 
Q 1491 3575 1491 3169 
Q 1491 2759 1650 2554 
Q 1809 2350 2125 2350 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-4e"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="272.021484"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="340.722656"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="411.914062"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="446.728516"/>
     <use xlink:href="#DejaVuSans-Bold-31" x="516.308594"/>
     <use xlink:href="#DejaVuSans-Bold-2c" x="585.888672"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="623.876953"/>
     <use xlink:href="#DejaVuSans-Bold-50" x="658.691406"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="731.982422"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="799.804688"/>
     <use xlink:href="#DejaVuSans-Bold-6b" x="867.285156"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="933.789062"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="973.779297"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1008.59375"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="1078.173828"/>
     <use xlink:href="#DejaVuSans-Bold-39" x="1116.162109"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="1185.742188"/>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_8">
    <path d="M 590.470428 380.010435 
L 1080.709558 380.010435 
L 1080.709558 67.68 
L 590.470428 67.68 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="m868f5f44d9" d="M 596.593061 -457.782653 
L 596.593061 -457.782653 
L 608.838328 -695.494487 
L 621.083595 -698.355507 
L 633.328862 -701.221058 
L 645.574128 -741.719412 
L 657.819395 -603.057576 
L 670.064662 -719.135405 
L 682.309928 -599.323278 
L 694.555195 -686.170727 
L 706.800462 -611.574406 
L 719.045729 -632.52133 
L 731.290995 -557.498021 
L 743.536262 -553.099314 
L 755.781529 -562.38466 
L 768.026795 -570.765402 
L 780.272062 -560.389107 
L 792.517329 -518.097764 
L 804.762596 -489.98237 
L 817.007862 -511.79816 
L 829.253129 -482.320323 
L 841.498396 -488.80684 
L 853.743663 -474.790184 
L 865.988929 -473.641736 
L 878.234196 -470.052434 
L 890.479463 -466.021015 
L 902.724729 -461.594663 
L 914.969996 -460.157399 
L 927.215263 -458.974726 
L 939.46053 -458.050576 
L 951.705796 -458.079364 
L 951.705796 -457.782653 
L 951.705796 -457.782653 
L 939.46053 -457.782653 
L 927.215263 -457.782653 
L 914.969996 -457.782653 
L 902.724729 -457.782653 
L 890.479463 -457.782653 
L 878.234196 -457.782653 
L 865.988929 -457.782653 
L 853.743663 -457.782653 
L 841.498396 -457.782653 
L 829.253129 -457.782653 
L 817.007862 -457.782653 
L 804.762596 -457.782653 
L 792.517329 -457.782653 
L 780.272062 -457.782653 
L 768.026795 -457.782653 
L 755.781529 -457.782653 
L 743.536262 -457.782653 
L 731.290995 -457.782653 
L 719.045729 -457.782653 
L 706.800462 -457.782653 
L 694.555195 -457.782653 
L 682.309928 -457.782653 
L 670.064662 -457.782653 
L 657.819395 -457.782653 
L 645.574128 -457.782653 
L 633.328862 -457.782653 
L 621.083595 -457.782653 
L 608.838328 -457.782653 
L 596.593061 -457.782653 
z
" style="stroke: #e31a1c; stroke-opacity: 0.3"/>
    </defs>
    <g clip-path="url(#pb11f8118eb)">
     <use xlink:href="#m868f5f44d9" x="0" y="823.59625" style="fill: #e31a1c; fill-opacity: 0.3; stroke: #e31a1c; stroke-opacity: 0.3"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10">
     <g id="line2d_19">
      <path d="M 590.470428 380.010435 
L 590.470428 67.68 
" clip-path="url(#pb11f8118eb)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_20">
      <g>
       <use xlink:href="#m25a591995f" x="590.470428" y="380.010435" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_25">
      <!-- 0 -->
      <g transform="translate(587.607303 393.849029) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_21">
      <path d="M 651.750319 380.010435 
L 651.750319 67.68 
" clip-path="url(#pb11f8118eb)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#m25a591995f" x="651.750319" y="380.010435" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 1 -->
      <g transform="translate(648.887194 393.849029) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_23">
      <path d="M 713.030211 380.010435 
L 713.030211 67.68 
" clip-path="url(#pb11f8118eb)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#m25a591995f" x="713.030211" y="380.010435" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 2 -->
      <g transform="translate(710.167086 393.849029) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_25">
      <path d="M 774.310102 380.010435 
L 774.310102 67.68 
" clip-path="url(#pb11f8118eb)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#m25a591995f" x="774.310102" y="380.010435" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 3 -->
      <g transform="translate(771.446977 393.849029) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_27">
      <path d="M 835.589993 380.010435 
L 835.589993 67.68 
" clip-path="url(#pb11f8118eb)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#m25a591995f" x="835.589993" y="380.010435" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 4 -->
      <g transform="translate(832.726868 393.849029) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_29">
      <path d="M 896.869885 380.010435 
L 896.869885 67.68 
" clip-path="url(#pb11f8118eb)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#m25a591995f" x="896.869885" y="380.010435" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 5 -->
      <g transform="translate(894.00676 393.849029) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_31">
      <path d="M 958.149776 380.010435 
L 958.149776 67.68 
" clip-path="url(#pb11f8118eb)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#m25a591995f" x="958.149776" y="380.010435" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 6 -->
      <g transform="translate(955.286651 393.849029) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_33">
      <path d="M 1019.429667 380.010435 
L 1019.429667 67.68 
" clip-path="url(#pb11f8118eb)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#m25a591995f" x="1019.429667" y="380.010435" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 7 -->
      <g transform="translate(1016.566542 393.849029) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-37"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_35">
      <path d="M 1080.709558 380.010435 
L 1080.709558 67.68 
" clip-path="url(#pb11f8118eb)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#m25a591995f" x="1080.709558" y="380.010435" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 8 -->
      <g transform="translate(1077.846433 393.849029) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_34">
     <!-- Distance (m) -->
     <g transform="translate(795.945306 408.079029) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-44"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="525.195312"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="570.898438"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="675.097656"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_37">
      <path d="M 590.470428 365.813597 
L 1080.709558 365.813597 
" clip-path="url(#pb11f8118eb)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#m175ef55289" x="590.470428" y="365.813597" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_35">
      <!-- 0.0 -->
      <g transform="translate(569.157615 369.232894) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_39">
      <path d="M 590.470428 315.795938 
L 1080.709558 315.795938 
" clip-path="url(#pb11f8118eb)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#m175ef55289" x="590.470428" y="315.795938" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 0.1 -->
      <g transform="translate(569.157615 319.215235) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_41">
      <path d="M 590.470428 265.778279 
L 1080.709558 265.778279 
" clip-path="url(#pb11f8118eb)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#m175ef55289" x="590.470428" y="265.778279" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 0.2 -->
      <g transform="translate(569.157615 269.197576) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_43">
      <path d="M 590.470428 215.76062 
L 1080.709558 215.76062 
" clip-path="url(#pb11f8118eb)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#m175ef55289" x="590.470428" y="215.76062" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 0.3 -->
      <g transform="translate(569.157615 219.179917) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-33" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_45">
      <path d="M 590.470428 165.742961 
L 1080.709558 165.742961 
" clip-path="url(#pb11f8118eb)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#m175ef55289" x="590.470428" y="165.742961" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 0.4 -->
      <g transform="translate(569.157615 169.162258) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_47">
      <path d="M 590.470428 115.725302 
L 1080.709558 115.725302 
" clip-path="url(#pb11f8118eb)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#m175ef55289" x="590.470428" y="115.725302" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 0.5 -->
      <g transform="translate(569.157615 119.144599) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_41">
     <!-- Activation -->
     <g transform="translate(562.869959 255.381702) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-41" d="M 3419 850 
L 1538 850 
L 1241 0 
L 31 0 
L 1759 4666 
L 3194 4666 
L 4922 0 
L 3713 0 
L 3419 850 
z
M 1838 1716 
L 3116 1716 
L 2478 3572 
L 1838 1716 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-41"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="77.392578"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="136.669922"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="184.472656"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="218.75"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="283.935547"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="351.416016"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="399.21875"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="433.496094"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="502.197266"/>
     </g>
    </g>
   </g>
   <g id="line2d_49">
    <path d="M 596.593061 365.813597 
L 608.838328 128.101763 
L 621.083595 125.240743 
L 633.328862 122.375192 
L 645.574128 81.876838 
L 657.819395 220.538674 
L 670.064662 104.460845 
L 682.309928 224.272972 
L 694.555195 137.425523 
L 706.800462 212.021844 
L 719.045729 191.07492 
L 731.290995 266.098229 
L 743.536262 270.496936 
L 755.781529 261.21159 
L 768.026795 252.830848 
L 780.272062 263.207143 
L 792.517329 305.498486 
L 804.762596 333.61388 
L 817.007862 311.79809 
L 829.253129 341.275927 
L 841.498396 334.78941 
L 853.743663 348.806066 
L 865.988929 349.954514 
L 878.234196 353.543816 
L 890.479463 357.575235 
L 902.724729 362.001587 
L 914.969996 363.438851 
L 927.215263 364.621524 
L 939.46053 365.545674 
L 951.705796 365.516886 
" clip-path="url(#pb11f8118eb)" style="fill: none; stroke: #e31a1c; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_50">
    <path d="M 645.574128 380.010435 
L 645.574128 67.68 
" clip-path="url(#pb11f8118eb)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #e31a1c; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_51">
    <path d="M 774.310102 380.010435 
L 774.310102 67.68 
" clip-path="url(#pb11f8118eb)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_52">
    <path d="M 896.869885 380.010435 
L 896.869885 67.68 
" clip-path="url(#pb11f8118eb)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="patch_9">
    <path d="M 590.470428 380.010435 
L 590.470428 67.68 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_10">
    <path d="M 590.470428 380.010435 
L 1080.709558 380.010435 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_42">
    <!-- (ii) Close-Distance Tuning -->
    <g transform="translate(756.288587 49.276469) scale(0.11 -0.11)">
     <defs>
      <path id="DejaVuSans-Bold-54" d="M 31 4666 
L 4331 4666 
L 4331 3756 
L 2784 3756 
L 2784 0 
L 1581 0 
L 1581 3756 
L 31 3756 
L 31 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-28"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="45.703125"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="79.980469"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="114.257812"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="159.960938"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="194.775391"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="268.164062"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="302.441406"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="371.142578"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="430.664062"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="498.486328"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="539.990234"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="622.998047"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="657.275391"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="716.796875"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="764.599609"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="832.080078"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="903.271484"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="962.548828"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1030.371094"/>
     <use xlink:href="#DejaVuSans-Bold-54" x="1065.185547"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1122.398438"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1193.589844"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1264.78125"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1299.058594"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="1370.25"/>
    </g>
    <!-- Sparsity = 0.495 -->
    <g transform="translate(784.061009 61.68) scale(0.11 -0.11)">
     <defs>
      <path id="DejaVuSans-Bold-53" d="M 3834 4519 
L 3834 3531 
Q 3450 3703 3084 3790 
Q 2719 3878 2394 3878 
Q 1963 3878 1756 3759 
Q 1550 3641 1550 3391 
Q 1550 3203 1689 3098 
Q 1828 2994 2194 2919 
L 2706 2816 
Q 3484 2659 3812 2340 
Q 4141 2022 4141 1434 
Q 4141 663 3683 286 
Q 3225 -91 2284 -91 
Q 1841 -91 1394 -6 
Q 947 78 500 244 
L 500 1259 
Q 947 1022 1364 901 
Q 1781 781 2169 781 
Q 2563 781 2772 912 
Q 2981 1044 2981 1288 
Q 2981 1506 2839 1625 
Q 2697 1744 2272 1838 
L 1806 1941 
Q 1106 2091 782 2419 
Q 459 2747 459 3303 
Q 459 4000 909 4375 
Q 1359 4750 2203 4750 
Q 2588 4750 2994 4692 
Q 3400 4634 3834 4519 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-79" d="M 78 3500 
L 1197 3500 
L 2138 1125 
L 2938 3500 
L 4056 3500 
L 2584 -331 
Q 2363 -916 2067 -1148 
Q 1772 -1381 1288 -1381 
L 641 -1381 
L 641 -647 
L 991 -647 
Q 1275 -647 1404 -556 
Q 1534 -466 1606 -231 
L 1638 -134 
L 78 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-3d" d="M 678 3084 
L 4684 3084 
L 4684 2350 
L 678 2350 
L 678 3084 
z
M 678 1663 
L 4684 1663 
L 4684 922 
L 678 922 
L 678 1663 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-34" d="M 2356 3675 
L 1038 1722 
L 2356 1722 
L 2356 3675 
z
M 2156 4666 
L 3494 4666 
L 3494 1722 
L 4159 1722 
L 4159 850 
L 3494 850 
L 3494 0 
L 2356 0 
L 2356 850 
L 288 850 
L 288 1881 
L 2156 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-53"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="72.021484"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="143.603516"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="211.083984"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="260.400391"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="319.921875"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="354.199219"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="402.001953"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="467.1875"/>
     <use xlink:href="#DejaVuSans-Bold-3d" x="502.001953"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="585.791016"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="620.605469"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="690.185547"/>
     <use xlink:href="#DejaVuSans-Bold-34" x="728.173828"/>
     <use xlink:href="#DejaVuSans-Bold-39" x="797.753906"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="867.333984"/>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_11">
    <path d="M 590.470428 786.04 
L 1080.709558 786.04 
L 1080.709558 473.709565 
L 590.470428 473.709565 
z
" style="fill: #ffffff"/>
   </g>
   <g id="matplotlib.axis_5">
    <g id="xtick_19">
     <g id="line2d_53">
      <path d="M 590.470428 786.04 
L 590.470428 473.709565 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#m25a591995f" x="590.470428" y="786.04" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_43">
      <!-- 0 -->
      <g transform="translate(587.607303 799.878594) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_20">
     <g id="line2d_55">
      <path d="M 651.750319 786.04 
L 651.750319 473.709565 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#m25a591995f" x="651.750319" y="786.04" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_44">
      <!-- 1 -->
      <g transform="translate(648.887194 799.878594) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_21">
     <g id="line2d_57">
      <path d="M 713.030211 786.04 
L 713.030211 473.709565 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#m25a591995f" x="713.030211" y="786.04" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_45">
      <!-- 2 -->
      <g transform="translate(710.167086 799.878594) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_22">
     <g id="line2d_59">
      <path d="M 774.310102 786.04 
L 774.310102 473.709565 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#m25a591995f" x="774.310102" y="786.04" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_46">
      <!-- 3 -->
      <g transform="translate(771.446977 799.878594) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_23">
     <g id="line2d_61">
      <path d="M 835.589993 786.04 
L 835.589993 473.709565 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#m25a591995f" x="835.589993" y="786.04" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_47">
      <!-- 4 -->
      <g transform="translate(832.726868 799.878594) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_24">
     <g id="line2d_63">
      <path d="M 896.869885 786.04 
L 896.869885 473.709565 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#m25a591995f" x="896.869885" y="786.04" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_48">
      <!-- 5 -->
      <g transform="translate(894.00676 799.878594) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="xtick_25">
     <g id="line2d_65">
      <path d="M 958.149776 786.04 
L 958.149776 473.709565 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#m25a591995f" x="958.149776" y="786.04" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_49">
      <!-- 6 -->
      <g transform="translate(955.286651 799.878594) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_26">
     <g id="line2d_67">
      <path d="M 1019.429667 786.04 
L 1019.429667 473.709565 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_68">
      <g>
       <use xlink:href="#m25a591995f" x="1019.429667" y="786.04" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_50">
      <!-- 7 -->
      <g transform="translate(1016.566542 799.878594) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-37"/>
      </g>
     </g>
    </g>
    <g id="xtick_27">
     <g id="line2d_69">
      <path d="M 1080.709558 786.04 
L 1080.709558 473.709565 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_70">
      <g>
       <use xlink:href="#m25a591995f" x="1080.709558" y="786.04" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_51">
      <!-- 8 -->
      <g transform="translate(1077.846433 799.878594) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_52">
     <!-- Distance (m) -->
     <g transform="translate(795.945306 814.108594) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-44"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="525.195312"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="570.898438"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="675.097656"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_6">
    <g id="ytick_16">
     <g id="line2d_71">
      <path d="M 590.470428 786.04 
L 1080.709558 786.04 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_72">
      <g>
       <use xlink:href="#m175ef55289" x="590.470428" y="786.04" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_53">
      <!-- 0.0 -->
      <g transform="translate(569.157615 789.459297) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_73">
      <path d="M 590.470428 729.252648 
L 1080.709558 729.252648 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_74">
      <g>
       <use xlink:href="#m175ef55289" x="590.470428" y="729.252648" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_54">
      <!-- 0.2 -->
      <g transform="translate(569.157615 732.671945) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_75">
      <path d="M 590.470428 672.465296 
L 1080.709558 672.465296 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_76">
      <g>
       <use xlink:href="#m175ef55289" x="590.470428" y="672.465296" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_55">
      <!-- 0.4 -->
      <g transform="translate(569.157615 675.884593) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_77">
      <path d="M 590.470428 615.677945 
L 1080.709558 615.677945 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_78">
      <g>
       <use xlink:href="#m175ef55289" x="590.470428" y="615.677945" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_56">
      <!-- 0.6 -->
      <g transform="translate(569.157615 619.097242) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_79">
      <path d="M 590.470428 558.890593 
L 1080.709558 558.890593 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_80">
      <g>
       <use xlink:href="#m175ef55289" x="590.470428" y="558.890593" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_57">
      <!-- 0.8 -->
      <g transform="translate(569.157615 562.30989) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-38" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_81">
      <path d="M 590.470428 502.103241 
L 1080.709558 502.103241 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #b0b0b0; stroke-opacity: 0.3; stroke-width: 0.8; stroke-linecap: square"/>
     </g>
     <g id="line2d_82">
      <g>
       <use xlink:href="#m175ef55289" x="590.470428" y="502.103241" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_58">
      <!-- 1.0 -->
      <g transform="translate(569.157615 505.522538) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_59">
     <!-- Normalized Activation -->
     <g transform="translate(562.869959 698.501892) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-7a" d="M 366 3500 
L 3419 3500 
L 3419 2719 
L 1575 800 
L 3419 800 
L 3419 0 
L 288 0 
L 288 781 
L 2131 2700 
L 366 2700 
L 366 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-64" d="M 2919 2988 
L 2919 4863 
L 4044 4863 
L 4044 0 
L 2919 0 
L 2919 506 
Q 2688 197 2409 53 
Q 2131 -91 1766 -91 
Q 1119 -91 703 423 
Q 288 938 288 1747 
Q 288 2556 703 3070 
Q 1119 3584 1766 3584 
Q 2128 3584 2408 3439 
Q 2688 3294 2919 2988 
z
M 2181 722 
Q 2541 722 2730 984 
Q 2919 1247 2919 1747 
Q 2919 2247 2730 2509 
Q 2541 2772 2181 2772 
Q 1825 2772 1636 2509 
Q 1447 2247 1447 1747 
Q 1447 1247 1636 984 
Q 1825 722 2181 722 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="152.392578"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="201.708984"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="305.908203"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="373.388672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="407.666016"/>
      <use xlink:href="#DejaVuSans-Bold-7a" x="441.943359"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="500.146484"/>
      <use xlink:href="#DejaVuSans-Bold-64" x="567.96875"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="639.550781"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="674.365234"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="751.757812"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="811.035156"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="858.837891"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="893.115234"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="958.300781"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="1025.78125"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="1073.583984"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="1107.861328"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="1176.5625"/>
     </g>
    </g>
   </g>
   <g id="line2d_83">
    <path d="M 596.593061 786.04 
L 608.838328 502.103241 
L 621.083595 517.21517 
L 633.328862 527.553852 
L 645.574128 540.244542 
L 657.819395 528.403317 
L 670.064662 609.286308 
L 682.309928 554.834775 
L 694.555195 661.295453 
L 706.800462 711.240589 
L 719.045729 698.275255 
L 731.290995 712.4649 
L 743.536262 675.188079 
L 755.781529 729.515132 
L 768.026795 722.663704 
L 780.272062 687.426769 
L 792.517329 736.515295 
L 804.762596 740.414824 
L 817.007862 709.753619 
L 829.253129 723.075563 
L 841.498396 729.862229 
L 853.743663 755.828972 
L 865.988929 761.826591 
L 878.234196 768.945179 
L 890.479463 763.197588 
L 902.724729 762.407921 
L 914.969996 744.770103 
L 927.215263 773.201268 
L 939.46053 754.660801 
L 951.705796 753.97135 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #e31a1c; stroke-opacity: 0.576291; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_84">
    <path d="M 596.593061 786.04 
L 608.838328 548.328166 
L 621.083595 545.467146 
L 633.328862 542.601595 
L 645.574128 502.103241 
L 657.819395 640.765078 
L 670.064662 524.687249 
L 682.309928 644.499375 
L 694.555195 557.651926 
L 706.800462 632.248248 
L 719.045729 611.301323 
L 731.290995 686.324632 
L 743.536262 690.723339 
L 755.781529 681.437993 
L 768.026795 673.057251 
L 780.272062 683.433546 
L 792.517329 725.724889 
L 804.762596 753.840283 
L 817.007862 732.024493 
L 829.253129 761.502331 
L 841.498396 755.015813 
L 853.743663 769.032469 
L 865.988929 770.180918 
L 878.234196 773.77022 
L 890.479463 777.801638 
L 902.724729 782.22799 
L 914.969996 783.665254 
L 927.215263 784.847927 
L 939.46053 785.772077 
L 951.705796 785.743289 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #e31a1c; stroke-opacity: 0.596778; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_85">
    <path d="M 596.593061 786.04 
L 608.838328 628.187983 
L 621.083595 649.832014 
L 633.328862 633.487358 
L 645.574128 626.669975 
L 657.819395 657.712426 
L 670.064662 637.566036 
L 682.309928 713.025713 
L 694.555195 608.093064 
L 706.800462 502.103241 
L 719.045729 602.433949 
L 731.290995 552.05917 
L 743.536262 622.524492 
L 755.781529 674.330782 
L 768.026795 632.889799 
L 780.272062 716.21686 
L 792.517329 609.489579 
L 804.762596 580.250127 
L 817.007862 636.227736 
L 829.253129 686.423644 
L 841.498396 664.219257 
L 853.743663 729.57138 
L 865.988929 712.380477 
L 878.234196 715.621389 
L 890.479463 712.79927 
L 902.724729 666.42873 
L 914.969996 721.146212 
L 927.215263 741.434423 
L 939.46053 750.074623 
L 951.705796 764.183562 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #e31a1c; stroke-opacity: 0.434477; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_86">
    <path d="M 596.593061 786.04 
L 608.838328 767.196144 
L 621.083595 767.498822 
L 633.328862 766.232283 
L 645.574128 760.86125 
L 657.819395 766.437431 
L 670.064662 750.636808 
L 682.309928 761.057401 
L 694.555195 718.770207 
L 706.800462 705.306866 
L 719.045729 696.614544 
L 731.290995 708.897679 
L 743.536262 685.797366 
L 755.781529 655.860139 
L 768.026795 615.199284 
L 780.272062 623.274873 
L 792.517329 581.561059 
L 804.762596 564.41902 
L 817.007862 525.37259 
L 829.253129 609.30618 
L 841.498396 542.954867 
L 853.743663 581.62129 
L 865.988929 566.198485 
L 878.234196 546.567766 
L 890.479463 543.936584 
L 902.724729 502.103241 
L 914.969996 590.316753 
L 927.215263 607.6382 
L 939.46053 654.56383 
L 951.705796 641.08694 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #33a02c; stroke-opacity: 0.485455; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_87">
    <path d="M 596.593061 786.04 
L 608.838328 768.590474 
L 621.083595 766.318516 
L 633.328862 764.668778 
L 645.574128 765.289773 
L 657.819395 760.731697 
L 670.064662 758.891225 
L 682.309928 757.331771 
L 694.555195 718.271457 
L 706.800462 688.478582 
L 719.045729 690.60133 
L 731.290995 678.774292 
L 743.536262 696.21635 
L 755.781529 676.840791 
L 768.026795 647.084372 
L 780.272062 674.996666 
L 792.517329 595.740934 
L 804.762596 565.52646 
L 817.007862 596.967258 
L 829.253129 658.987131 
L 841.498396 585.005126 
L 853.743663 592.677594 
L 865.988929 599.793678 
L 878.234196 518.599011 
L 890.479463 544.137266 
L 902.724729 502.103241 
L 914.969996 565.894768 
L 927.215263 506.672586 
L 939.46053 555.638773 
L 951.705796 528.368648 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #33a02c; stroke-opacity: 0.490996; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_88">
    <path d="M 596.593061 786.04 
L 608.838328 782.110789 
L 621.083595 782.455976 
L 633.328862 781.851562 
L 645.574128 780.892575 
L 657.819395 781.205958 
L 670.064662 780.541047 
L 682.309928 781.653191 
L 694.555195 772.642803 
L 706.800462 764.528318 
L 719.045729 762.530195 
L 731.290995 760.157612 
L 743.536262 759.062136 
L 755.781529 755.428224 
L 768.026795 729.503004 
L 780.272062 727.768273 
L 792.517329 690.117496 
L 804.762596 671.722121 
L 817.007862 641.424779 
L 829.253129 665.647504 
L 841.498396 620.409332 
L 853.743663 619.157538 
L 865.988929 599.153859 
L 878.234196 534.587984 
L 890.479463 529.114236 
L 902.724729 502.103241 
L 914.969996 530.070129 
L 927.215263 504.779823 
L 939.46053 550.650894 
L 951.705796 507.314048 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #33a02c; stroke-opacity: 0.60748; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_89">
    <path d="M 596.593061 786.04 
L 608.838328 786.023462 
L 621.083595 771.97059 
L 633.328862 751.100453 
L 645.574128 719.104193 
L 657.819395 752.377501 
L 670.064662 643.875043 
L 682.309928 724.60653 
L 694.555195 656.278228 
L 706.800462 630.337111 
L 719.045729 650.832931 
L 731.290995 672.503639 
L 743.536262 632.297405 
L 755.781529 563.06105 
L 768.026795 603.980613 
L 780.272062 607.567106 
L 792.517329 657.183805 
L 804.762596 655.475451 
L 817.007862 630.879605 
L 829.253129 508.942295 
L 841.498396 585.418052 
L 853.743663 566.739486 
L 865.988929 515.246284 
L 878.234196 587.954862 
L 890.479463 522.724565 
L 902.724729 585.007398 
L 914.969996 502.103241 
L 927.215263 555.600918 
L 939.46053 618.847927 
L 951.705796 576.941501 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #33a02c; stroke-opacity: 0.438453; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_90">
    <path d="M 596.593061 786.04 
L 608.838328 775.014753 
L 621.083595 775.899899 
L 633.328862 775.465712 
L 645.574128 772.029075 
L 657.819395 776.159001 
L 670.064662 771.038902 
L 682.309928 770.855361 
L 694.555195 740.923271 
L 706.800462 754.255276 
L 719.045729 713.9358 
L 731.290995 760.245841 
L 743.536262 731.367874 
L 755.781529 704.689465 
L 768.026795 652.400648 
L 780.272062 609.127889 
L 792.517329 626.141214 
L 804.762596 697.453265 
L 817.007862 543.008425 
L 829.253129 633.74244 
L 841.498396 550.34508 
L 853.743663 611.646377 
L 865.988929 566.727153 
L 878.234196 514.582018 
L 890.479463 504.34565 
L 902.724729 528.755643 
L 914.969996 502.103241 
L 927.215263 544.087129 
L 939.46053 655.764875 
L 951.705796 529.205016 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #33a02c; stroke-opacity: 0.547146; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_91">
    <path d="M 596.593061 786.04 
L 608.838328 782.085305 
L 621.083595 782.466397 
L 633.328862 778.808909 
L 645.574128 777.167972 
L 657.819395 777.426992 
L 670.064662 774.91442 
L 682.309928 778.010586 
L 694.555195 752.630779 
L 706.800462 722.004215 
L 719.045729 729.393866 
L 731.290995 713.087221 
L 743.536262 720.988641 
L 755.781529 702.830049 
L 768.026795 687.285447 
L 780.272062 711.191728 
L 792.517329 647.801877 
L 804.762596 605.568037 
L 817.007862 647.700564 
L 829.253129 668.959962 
L 841.498396 624.600613 
L 853.743663 601.742871 
L 865.988929 597.980075 
L 878.234196 527.909044 
L 890.479463 545.959387 
L 902.724729 522.016977 
L 914.969996 560.983221 
L 927.215263 502.103241 
L 939.46053 538.782934 
L 951.705796 527.03893 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #33a02c; stroke-opacity: 0.547753; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_92">
    <path d="M 596.593061 786.04 
L 608.838328 686.907578 
L 621.083595 688.635002 
L 633.328862 690.155488 
L 645.574128 692.154701 
L 657.819395 685.465807 
L 670.064662 687.66796 
L 682.309928 683.699386 
L 694.555195 675.033989 
L 706.800462 665.187743 
L 719.045729 663.000364 
L 731.290995 655.796523 
L 743.536262 655.421205 
L 755.781529 650.607921 
L 768.026795 637.066716 
L 780.272062 636.280553 
L 792.517329 610.538561 
L 804.762596 594.742651 
L 817.007862 603.179794 
L 829.253129 604.895335 
L 841.498396 587.452659 
L 853.743663 573.991979 
L 865.988929 567.764098 
L 878.234196 541.675728 
L 890.479463 547.451442 
L 902.724729 532.385598 
L 914.969996 541.815668 
L 927.215263 510.624472 
L 939.46053 502.103241 
L 951.705796 506.716802 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #33a02c; stroke-opacity: 0.392792; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_93">
    <path d="M 596.593061 786.04 
L 608.838328 777.51186 
L 621.083595 777.92754 
L 633.328862 777.261085 
L 645.574128 776.112211 
L 657.819395 775.554209 
L 670.064662 774.578739 
L 682.309928 772.062195 
L 694.555195 760.200775 
L 706.800462 749.531848 
L 719.045729 745.826557 
L 731.290995 736.488621 
L 743.536262 737.095089 
L 755.781529 728.918669 
L 768.026795 708.81003 
L 780.272062 705.994736 
L 792.517329 669.837449 
L 804.762596 647.271116 
L 817.007862 659.439974 
L 829.253129 661.484667 
L 841.498396 634.919561 
L 853.743663 612.271982 
L 865.988929 599.929052 
L 878.234196 557.970311 
L 890.479463 566.696768 
L 902.724729 546.595021 
L 914.969996 559.399475 
L 927.215263 512.039321 
L 939.46053 502.103241 
L 951.705796 506.002625 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #33a02c; stroke-opacity: 0.566927; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_94">
    <path d="M 596.593061 786.04 
L 608.838328 783.261833 
L 621.083595 783.524654 
L 633.328862 783.636512 
L 645.574128 783.590447 
L 657.819395 783.515904 
L 670.064662 783.850401 
L 682.309928 783.574457 
L 694.555195 782.034836 
L 706.800462 781.572064 
L 719.045729 778.777212 
L 731.290995 778.939426 
L 743.536262 780.442806 
L 755.781529 779.468134 
L 768.026795 772.613459 
L 780.272062 763.080257 
L 792.517329 731.076063 
L 804.762596 738.798132 
L 817.007862 701.650134 
L 829.253129 733.959855 
L 841.498396 673.583794 
L 853.743663 675.93627 
L 865.988929 656.319124 
L 878.234196 591.87066 
L 890.479463 589.766031 
L 902.724729 564.770728 
L 914.969996 580.144717 
L 927.215263 523.41373 
L 939.46053 535.636782 
L 951.705796 502.103241 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #33a02c; stroke-opacity: 0.671783; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_95">
    <path d="M 596.593061 786.04 
L 608.838328 786.04 
L 621.083595 786.04 
L 633.328862 786.04 
L 645.574128 786.04 
L 657.819395 786.04 
L 670.064662 786.04 
L 682.309928 786.04 
L 694.555195 786.04 
L 706.800462 786.04 
L 719.045729 786.04 
L 731.290995 786.04 
L 743.536262 786.026344 
L 755.781529 786.009757 
L 768.026795 785.992229 
L 780.272062 785.939082 
L 792.517329 782.215165 
L 804.762596 783.320291 
L 817.007862 753.955843 
L 829.253129 774.696876 
L 841.498396 724.958395 
L 853.743663 733.844845 
L 865.988929 727.338985 
L 878.234196 661.297371 
L 890.479463 629.72095 
L 902.724729 617.437646 
L 914.969996 618.067405 
L 927.215263 584.917099 
L 939.46053 655.627244 
L 951.705796 502.103241 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #33a02c; stroke-opacity: 0.74243; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_96">
    <path d="M 596.593061 786.04 
L 608.838328 785.975188 
L 621.083595 785.261912 
L 633.328862 776.637822 
L 645.574128 768.307519 
L 657.819395 750.07122 
L 670.064662 729.566388 
L 682.309928 743.713767 
L 694.555195 721.543393 
L 706.800462 707.151811 
L 719.045729 703.828611 
L 731.290995 707.172789 
L 743.536262 682.988814 
L 755.781529 675.512257 
L 768.026795 678.19976 
L 780.272062 655.258433 
L 792.517329 668.231645 
L 804.762596 675.473289 
L 817.007862 623.290782 
L 829.253129 587.140952 
L 841.498396 600.432108 
L 853.743663 605.91649 
L 865.988929 576.794918 
L 878.234196 614.355107 
L 890.479463 569.502267 
L 902.724729 584.975025 
L 914.969996 507.207863 
L 927.215263 559.511326 
L 939.46053 534.671898 
L 951.705796 502.103241 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #33a02c; stroke-opacity: 0.497244; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_97">
    <path d="M 596.593061 786.04 
L 608.838328 783.6551 
L 621.083595 784.029913 
L 633.328862 784.151788 
L 645.574128 783.954117 
L 657.819395 784.221178 
L 670.064662 784.181267 
L 682.309928 784.373176 
L 694.555195 781.836895 
L 706.800462 781.627833 
L 719.045729 777.610628 
L 731.290995 777.850968 
L 743.536262 781.499267 
L 755.781529 780.984534 
L 768.026795 776.279884 
L 780.272062 764.087532 
L 792.517329 733.373655 
L 804.762596 754.368451 
L 817.007862 694.850264 
L 829.253129 744.925651 
L 841.498396 675.241458 
L 853.743663 703.011093 
L 865.988929 691.642021 
L 878.234196 627.222661 
L 890.479463 601.583403 
L 902.724729 572.055112 
L 914.969996 588.849347 
L 927.215263 536.387621 
L 939.46053 567.491004 
L 951.705796 502.103241 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #33a02c; stroke-opacity: 0.680781; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_98">
    <path d="M 596.593061 786.04 
L 608.838328 786.04 
L 621.083595 786.04 
L 633.328862 786.04 
L 645.574128 786.04 
L 657.819395 786.04 
L 670.064662 786.04 
L 682.309928 786.04 
L 694.555195 785.917678 
L 706.800462 785.893173 
L 719.045729 785.576662 
L 731.290995 785.90297 
L 743.536262 785.638198 
L 755.781529 785.449778 
L 768.026795 784.938463 
L 780.272062 784.489188 
L 792.517329 782.758793 
L 804.762596 781.714105 
L 817.007862 758.561531 
L 829.253129 770.799599 
L 841.498396 728.832027 
L 853.743663 736.648464 
L 865.988929 714.871303 
L 878.234196 647.027836 
L 890.479463 623.011194 
L 902.724729 615.565183 
L 914.969996 581.284649 
L 927.215263 567.259122 
L 939.46053 665.037012 
L 951.705796 502.103241 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #33a02c; stroke-opacity: 0.741668; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_99">
    <path d="M 596.593061 786.04 
L 608.838328 774.582927 
L 621.083595 775.441995 
L 633.328862 775.3371 
L 645.574128 773.848449 
L 657.819395 775.899962 
L 670.064662 774.766628 
L 682.309928 776.06917 
L 694.555195 766.703228 
L 706.800462 770.593703 
L 719.045729 758.620327 
L 731.290995 767.272239 
L 743.536262 767.341717 
L 755.781529 760.181516 
L 768.026795 743.345191 
L 780.272062 725.019821 
L 792.517329 698.207817 
L 804.762596 723.835773 
L 817.007862 648.610522 
L 829.253129 697.55829 
L 841.498396 630.677747 
L 853.743663 659.670885 
L 865.988929 633.458349 
L 878.234196 574.276364 
L 890.479463 562.337941 
L 902.724729 533.343404 
L 914.969996 535.308216 
L 927.215263 537.003449 
L 939.46053 597.516617 
L 951.705796 502.103241 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke: #33a02c; stroke-opacity: 0.608378; stroke-width: 1.5; stroke-linecap: square"/>
   </g>
   <g id="line2d_100">
    <path d="M 774.310102 786.04 
L 774.310102 473.709565 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_101">
    <path d="M 896.869885 786.04 
L 896.869885 473.709565 
" clip-path="url(#p87ae78dac4)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="patch_12">
    <path d="M 590.470428 786.04 
L 590.470428 473.709565 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_13">
    <path d="M 590.470428 786.04 
L 1080.709558 786.04 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_60">
    <!-- (iv) Population Code Tiling -->
    <g transform="translate(753.137259 455.306034) scale(0.11 -0.11)">
     <use xlink:href="#DejaVuSans-Bold-28"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="45.703125"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="79.980469"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="145.166016"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="190.869141"/>
     <use xlink:href="#DejaVuSans-Bold-50" x="225.683594"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="298.974609"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="367.675781"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="439.257812"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="510.449219"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="544.726562"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="612.207031"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="660.009766"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="694.287109"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="762.988281"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="834.179688"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="868.994141"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="942.382812"/>
     <use xlink:href="#DejaVuSans-Bold-64" x="1011.083984"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1082.666016"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1150.488281"/>
     <use xlink:href="#DejaVuSans-Bold-54" x="1185.302734"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1253.515625"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="1287.792969"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1322.070312"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1356.347656"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="1427.539062"/>
    </g>
    <!-- Distance Space Coverage (Line opacity ∝ Sparsity) -->
    <g transform="translate(678.847728 467.709565) scale(0.11 -0.11)">
     <defs>
      <path id="DejaVuSans-Bold-4c" d="M 588 4666 
L 1791 4666 
L 1791 909 
L 3903 909 
L 3903 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-221d" d="M 3694 572 
Q 3453 572 3276 672 
Q 3100 772 2928 984 
Q 2744 1250 2669 1409 
Q 2438 975 2253 806 
Q 2009 578 1638 578 
Q 1191 578 897 931 
Q 588 1294 588 1906 
Q 588 2491 897 2888 
Q 1169 3234 1638 3234 
Q 1878 3234 2054 3134 
Q 2231 3034 2403 2822 
Q 2591 2556 2663 2397 
Q 2897 2831 3078 3000 
Q 3322 3228 3694 3228 
L 3947 3228 
L 3947 2594 
L 3716 2594 
Q 3288 2594 2947 1963 
Q 3372 1203 3716 1203 
L 3947 1203 
L 3947 572 
L 3694 572 
z
M 1619 1213 
Q 2050 1213 2384 1844 
Q 1959 2603 1619 2603 
Q 1338 2603 1197 2406 
Q 1044 2188 1044 1906 
Q 1044 1600 1198 1406 
Q 1353 1213 1619 1213 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-44"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="525.195312"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="597.216797"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="668.798828"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="736.279297"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="795.556641"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="863.378906"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="898.193359"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="971.582031"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="1040.283203"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1105.46875"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1173.291016"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="1222.607422"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="1290.087891"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1361.669922"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1429.492188"/>
     <use xlink:href="#DejaVuSans-Bold-28" x="1464.306641"/>
     <use xlink:href="#DejaVuSans-Bold-4c" x="1510.009766"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1573.730469"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1608.007812"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1679.199219"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1747.021484"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="1781.835938"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="1850.537109"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="1922.119141"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="1989.599609"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="2048.876953"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="2083.154297"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="2130.957031"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="2196.142578"/>
     <use xlink:href="#DejaVuSans-Bold-221d" x="2230.957031"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="2302.148438"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="2336.962891"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="2408.984375"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="2480.566406"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="2548.046875"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="2597.363281"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="2656.884766"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="2691.162109"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="2738.964844"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="2804.150391"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_14">
     <path d="M 987.502058 538.822065 
L 1075.109558 538.822065 
Q 1076.709558 538.822065 1076.709558 537.222065 
L 1076.709558 479.309565 
Q 1076.709558 477.709565 1075.109558 477.709565 
L 987.502058 477.709565 
Q 985.902058 477.709565 985.902058 479.309565 
L 985.902058 537.222065 
Q 985.902058 538.822065 987.502058 538.822065 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_102">
     <path d="M 989.102058 484.188315 
L 997.102058 484.188315 
L 1005.102058 484.188315 
" style="fill: none; stroke: #e31a1c; stroke-width: 2; stroke-linecap: square"/>
    </g>
    <g id="text_61">
     <!-- Close (n=3) -->
     <g transform="translate(1011.502058 486.988315) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-3d" d="M 678 2906 
L 4684 2906 
L 4684 2381 
L 678 2381 
L 678 2906 
z
M 678 1631 
L 4684 1631 
L 4684 1100 
L 678 1100 
L 678 1631 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6c" x="69.824219"/>
      <use xlink:href="#DejaVuSans-6f" x="97.607422"/>
      <use xlink:href="#DejaVuSans-73" x="158.789062"/>
      <use xlink:href="#DejaVuSans-65" x="210.888672"/>
      <use xlink:href="#DejaVuSans-20" x="272.412109"/>
      <use xlink:href="#DejaVuSans-28" x="304.199219"/>
      <use xlink:href="#DejaVuSans-6e" x="343.212891"/>
      <use xlink:href="#DejaVuSans-3d" x="406.591797"/>
      <use xlink:href="#DejaVuSans-33" x="490.380859"/>
      <use xlink:href="#DejaVuSans-29" x="554.003906"/>
     </g>
    </g>
    <g id="line2d_103">
     <path d="M 989.102058 495.930815 
L 997.102058 495.930815 
L 1005.102058 495.930815 
" style="fill: none; stroke: #1f78b4; stroke-width: 2; stroke-linecap: square"/>
    </g>
    <g id="text_62">
     <!-- Mid (n=0) -->
     <g transform="translate(1011.502058 498.730815) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4d"/>
      <use xlink:href="#DejaVuSans-69" x="86.279297"/>
      <use xlink:href="#DejaVuSans-64" x="114.0625"/>
      <use xlink:href="#DejaVuSans-20" x="177.539062"/>
      <use xlink:href="#DejaVuSans-28" x="209.326172"/>
      <use xlink:href="#DejaVuSans-6e" x="248.339844"/>
      <use xlink:href="#DejaVuSans-3d" x="311.71875"/>
      <use xlink:href="#DejaVuSans-30" x="395.507812"/>
      <use xlink:href="#DejaVuSans-29" x="459.130859"/>
     </g>
    </g>
    <g id="line2d_104">
     <path d="M 989.102058 507.673315 
L 997.102058 507.673315 
L 1005.102058 507.673315 
" style="fill: none; stroke: #33a02c; stroke-width: 2; stroke-linecap: square"/>
    </g>
    <g id="text_63">
     <!-- Far (n=14) -->
     <g transform="translate(1011.502058 510.473315) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" x="48.394531"/>
      <use xlink:href="#DejaVuSans-72" x="109.673828"/>
      <use xlink:href="#DejaVuSans-20" x="150.787109"/>
      <use xlink:href="#DejaVuSans-28" x="182.574219"/>
      <use xlink:href="#DejaVuSans-6e" x="221.587891"/>
      <use xlink:href="#DejaVuSans-3d" x="284.966797"/>
      <use xlink:href="#DejaVuSans-31" x="368.755859"/>
      <use xlink:href="#DejaVuSans-34" x="432.378906"/>
      <use xlink:href="#DejaVuSans-29" x="496.001953"/>
     </g>
    </g>
    <g id="line2d_105">
     <path d="M 989.102058 519.415815 
L 997.102058 519.415815 
L 1005.102058 519.415815 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-width: 1.5"/>
    </g>
    <g id="text_64">
     <!-- Close threshold -->
     <g transform="translate(1011.502058 522.215815) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6c" x="69.824219"/>
      <use xlink:href="#DejaVuSans-6f" x="97.607422"/>
      <use xlink:href="#DejaVuSans-73" x="158.789062"/>
      <use xlink:href="#DejaVuSans-65" x="210.888672"/>
      <use xlink:href="#DejaVuSans-20" x="272.412109"/>
      <use xlink:href="#DejaVuSans-74" x="304.199219"/>
      <use xlink:href="#DejaVuSans-68" x="343.408203"/>
      <use xlink:href="#DejaVuSans-72" x="406.787109"/>
      <use xlink:href="#DejaVuSans-65" x="445.650391"/>
      <use xlink:href="#DejaVuSans-73" x="507.173828"/>
      <use xlink:href="#DejaVuSans-68" x="559.273438"/>
      <use xlink:href="#DejaVuSans-6f" x="622.652344"/>
      <use xlink:href="#DejaVuSans-6c" x="683.833984"/>
      <use xlink:href="#DejaVuSans-64" x="711.617188"/>
     </g>
    </g>
    <g id="line2d_106">
     <path d="M 989.102058 531.158315 
L 997.102058 531.158315 
L 1005.102058 531.158315 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-width: 1.5"/>
    </g>
    <g id="text_65">
     <!-- Far threshold -->
     <g transform="translate(1011.502058 533.958315) scale(0.08 -0.08)">
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" x="48.394531"/>
      <use xlink:href="#DejaVuSans-72" x="109.673828"/>
      <use xlink:href="#DejaVuSans-20" x="150.787109"/>
      <use xlink:href="#DejaVuSans-74" x="182.574219"/>
      <use xlink:href="#DejaVuSans-68" x="221.783203"/>
      <use xlink:href="#DejaVuSans-72" x="285.162109"/>
      <use xlink:href="#DejaVuSans-65" x="324.025391"/>
      <use xlink:href="#DejaVuSans-73" x="385.548828"/>
      <use xlink:href="#DejaVuSans-68" x="437.648438"/>
      <use xlink:href="#DejaVuSans-6f" x="501.027344"/>
      <use xlink:href="#DejaVuSans-6c" x="562.208984"/>
      <use xlink:href="#DejaVuSans-64" x="589.992188"/>
     </g>
    </g>
   </g>
  </g>
  <g id="text_66">
   <!-- Emergence of a Population Code for Inter-Agent Distance -->
   <g transform="translate(226.467058 20.877187) scale(0.18 -0.18)">
    <defs>
     <path id="DejaVuSans-Bold-45" d="M 588 4666 
L 3834 4666 
L 3834 3756 
L 1791 3756 
L 1791 2888 
L 3713 2888 
L 3713 1978 
L 1791 1978 
L 1791 909 
L 3903 909 
L 3903 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-Bold-66" d="M 2841 4863 
L 2841 4128 
L 2222 4128 
Q 1984 4128 1890 4042 
Q 1797 3956 1797 3744 
L 1797 3500 
L 2753 3500 
L 2753 2700 
L 1797 2700 
L 1797 0 
L 678 0 
L 678 2700 
L 122 2700 
L 122 3500 
L 678 3500 
L 678 3744 
Q 678 4316 997 4589 
Q 1316 4863 1984 4863 
L 2841 4863 
z
" transform="scale(0.015625)"/>
     <path id="DejaVuSans-Bold-49" d="M 588 4666 
L 1791 4666 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
    </defs>
    <use xlink:href="#DejaVuSans-Bold-45"/>
    <use xlink:href="#DejaVuSans-Bold-6d" x="68.310547"/>
    <use xlink:href="#DejaVuSans-Bold-65" x="172.509766"/>
    <use xlink:href="#DejaVuSans-Bold-72" x="240.332031"/>
    <use xlink:href="#DejaVuSans-Bold-67" x="289.648438"/>
    <use xlink:href="#DejaVuSans-Bold-65" x="361.230469"/>
    <use xlink:href="#DejaVuSans-Bold-6e" x="429.052734"/>
    <use xlink:href="#DejaVuSans-Bold-63" x="500.244141"/>
    <use xlink:href="#DejaVuSans-Bold-65" x="559.521484"/>
    <use xlink:href="#DejaVuSans-Bold-20" x="627.34375"/>
    <use xlink:href="#DejaVuSans-Bold-6f" x="662.158203"/>
    <use xlink:href="#DejaVuSans-Bold-66" x="730.859375"/>
    <use xlink:href="#DejaVuSans-Bold-20" x="774.365234"/>
    <use xlink:href="#DejaVuSans-Bold-61" x="809.179688"/>
    <use xlink:href="#DejaVuSans-Bold-20" x="876.660156"/>
    <use xlink:href="#DejaVuSans-Bold-50" x="911.474609"/>
    <use xlink:href="#DejaVuSans-Bold-6f" x="984.765625"/>
    <use xlink:href="#DejaVuSans-Bold-70" x="1053.466797"/>
    <use xlink:href="#DejaVuSans-Bold-75" x="1125.048828"/>
    <use xlink:href="#DejaVuSans-Bold-6c" x="1196.240234"/>
    <use xlink:href="#DejaVuSans-Bold-61" x="1230.517578"/>
    <use xlink:href="#DejaVuSans-Bold-74" x="1297.998047"/>
    <use xlink:href="#DejaVuSans-Bold-69" x="1345.800781"/>
    <use xlink:href="#DejaVuSans-Bold-6f" x="1380.078125"/>
    <use xlink:href="#DejaVuSans-Bold-6e" x="1448.779297"/>
    <use xlink:href="#DejaVuSans-Bold-20" x="1519.970703"/>
    <use xlink:href="#DejaVuSans-Bold-43" x="1554.785156"/>
    <use xlink:href="#DejaVuSans-Bold-6f" x="1628.173828"/>
    <use xlink:href="#DejaVuSans-Bold-64" x="1696.875"/>
    <use xlink:href="#DejaVuSans-Bold-65" x="1768.457031"/>
    <use xlink:href="#DejaVuSans-Bold-20" x="1836.279297"/>
    <use xlink:href="#DejaVuSans-Bold-66" x="1871.09375"/>
    <use xlink:href="#DejaVuSans-Bold-6f" x="1914.599609"/>
    <use xlink:href="#DejaVuSans-Bold-72" x="1983.300781"/>
    <use xlink:href="#DejaVuSans-Bold-20" x="2032.617188"/>
    <use xlink:href="#DejaVuSans-Bold-49" x="2067.431641"/>
    <use xlink:href="#DejaVuSans-Bold-6e" x="2104.638672"/>
    <use xlink:href="#DejaVuSans-Bold-74" x="2175.830078"/>
    <use xlink:href="#DejaVuSans-Bold-65" x="2223.632812"/>
    <use xlink:href="#DejaVuSans-Bold-72" x="2291.455078"/>
    <use xlink:href="#DejaVuSans-Bold-2d" x="2340.771484"/>
    <use xlink:href="#DejaVuSans-Bold-41" x="2382.275391"/>
    <use xlink:href="#DejaVuSans-Bold-67" x="2459.667969"/>
    <use xlink:href="#DejaVuSans-Bold-65" x="2531.25"/>
    <use xlink:href="#DejaVuSans-Bold-6e" x="2599.072266"/>
    <use xlink:href="#DejaVuSans-Bold-74" x="2670.263672"/>
    <use xlink:href="#DejaVuSans-Bold-20" x="2718.066406"/>
    <use xlink:href="#DejaVuSans-Bold-44" x="2752.880859"/>
    <use xlink:href="#DejaVuSans-Bold-69" x="2835.888672"/>
    <use xlink:href="#DejaVuSans-Bold-73" x="2870.166016"/>
    <use xlink:href="#DejaVuSans-Bold-74" x="2929.6875"/>
    <use xlink:href="#DejaVuSans-Bold-61" x="2977.490234"/>
    <use xlink:href="#DejaVuSans-Bold-6e" x="3044.970703"/>
    <use xlink:href="#DejaVuSans-Bold-63" x="3116.162109"/>
    <use xlink:href="#DejaVuSans-Bold-65" x="3175.439453"/>
   </g>
   <!-- (Based on Sparsity Analysis) -->
   <g transform="translate(373.065808 41.173875) scale(0.18 -0.18)">
    <defs>
     <path id="DejaVuSans-Bold-42" d="M 2456 2859 
Q 2741 2859 2887 2984 
Q 3034 3109 3034 3353 
Q 3034 3594 2887 3720 
Q 2741 3847 2456 3847 
L 1791 3847 
L 1791 2859 
L 2456 2859 
z
M 2497 819 
Q 2859 819 3042 972 
Q 3225 1125 3225 1434 
Q 3225 1738 3044 1889 
Q 2863 2041 2497 2041 
L 1791 2041 
L 1791 819 
L 2497 819 
z
M 3616 2497 
Q 4003 2384 4215 2081 
Q 4428 1778 4428 1338 
Q 4428 663 3972 331 
Q 3516 0 2584 0 
L 588 0 
L 588 4666 
L 2394 4666 
Q 3366 4666 3802 4372 
Q 4238 4078 4238 3431 
Q 4238 3091 4078 2852 
Q 3919 2613 3616 2497 
z
" transform="scale(0.015625)"/>
    </defs>
    <use xlink:href="#DejaVuSans-Bold-28"/>
    <use xlink:href="#DejaVuSans-Bold-42" x="45.703125"/>
    <use xlink:href="#DejaVuSans-Bold-61" x="121.923828"/>
    <use xlink:href="#DejaVuSans-Bold-73" x="189.404297"/>
    <use xlink:href="#DejaVuSans-Bold-65" x="248.925781"/>
    <use xlink:href="#DejaVuSans-Bold-64" x="316.748047"/>
    <use xlink:href="#DejaVuSans-Bold-20" x="388.330078"/>
    <use xlink:href="#DejaVuSans-Bold-6f" x="423.144531"/>
    <use xlink:href="#DejaVuSans-Bold-6e" x="491.845703"/>
    <use xlink:href="#DejaVuSans-Bold-20" x="563.037109"/>
    <use xlink:href="#DejaVuSans-Bold-53" x="597.851562"/>
    <use xlink:href="#DejaVuSans-Bold-70" x="669.873047"/>
    <use xlink:href="#DejaVuSans-Bold-61" x="741.455078"/>
    <use xlink:href="#DejaVuSans-Bold-72" x="808.935547"/>
    <use xlink:href="#DejaVuSans-Bold-73" x="858.251953"/>
    <use xlink:href="#DejaVuSans-Bold-69" x="917.773438"/>
    <use xlink:href="#DejaVuSans-Bold-74" x="952.050781"/>
    <use xlink:href="#DejaVuSans-Bold-79" x="999.853516"/>
    <use xlink:href="#DejaVuSans-Bold-20" x="1065.039062"/>
    <use xlink:href="#DejaVuSans-Bold-41" x="1099.853516"/>
    <use xlink:href="#DejaVuSans-Bold-6e" x="1177.246094"/>
    <use xlink:href="#DejaVuSans-Bold-61" x="1248.4375"/>
    <use xlink:href="#DejaVuSans-Bold-6c" x="1315.917969"/>
    <use xlink:href="#DejaVuSans-Bold-79" x="1350.195312"/>
    <use xlink:href="#DejaVuSans-Bold-73" x="1415.380859"/>
    <use xlink:href="#DejaVuSans-Bold-69" x="1474.902344"/>
    <use xlink:href="#DejaVuSans-Bold-73" x="1509.179688"/>
    <use xlink:href="#DejaVuSans-Bold-29" x="1568.701172"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p2aa7b21e81">
   <rect x="42.113906" y="67.68" width="312.330435" height="312.330435"/>
  </clipPath>
  <clipPath id="pb11f8118eb">
   <rect x="590.470428" y="67.68" width="490.23913" height="312.330435"/>
  </clipPath>
  <clipPath id="p87ae78dac4">
   <rect x="590.470428" y="473.709565" width="490.23913" height="312.330435"/>
  </clipPath>
 </defs>
</svg>
