<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="828.560413pt" height="352.267438pt" viewBox="0 0 828.560413 352.267438" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:52:20.496922</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 352.267438 
L 828.560413 352.267438 
L 828.560413 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
L 311.889906 44.84925 
L 42.113906 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g clip-path="url(#pda507d858a)">
    <image xlink:href="data:image/png;base64,
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" id="image14098e6b1d" transform="scale(1 -1) translate(0 -270.24)" x="42.113906" y="-44.38525" width="270.24" height="270.24"/>
   </g>
   <g id="patch_3">
    <path d="M 177.001906 230.32025 
C 190.416675 230.32025 203.283815 224.990506 212.769489 215.504832 
C 222.255162 206.019159 227.584906 193.152018 227.584906 179.73725 
C 227.584906 166.322482 222.255162 153.455341 212.769489 143.969668 
C 203.283815 134.483994 190.416675 129.15425 177.001906 129.15425 
C 163.587138 129.15425 150.719998 134.483994 141.234324 143.969668 
C 131.74865 153.455341 126.418906 166.322482 126.418906 179.73725 
C 126.418906 193.152018 131.74865 206.019159 141.234324 215.504832 
C 150.719998 224.990506 163.587138 230.32025 177.001906 230.32025 
L 177.001906 230.32025 
z
" clip-path="url(#pda507d858a)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 177.001906 264.04225 
C 199.359854 264.04225 220.805087 255.159343 236.614543 239.349887 
C 252.424 223.540431 261.306906 202.095197 261.306906 179.73725 
C 261.306906 157.379303 252.424 135.934069 236.614543 120.124613 
C 220.805087 104.315157 199.359854 95.43225 177.001906 95.43225 
C 154.643959 95.43225 133.198725 104.315157 117.389269 120.124613 
C 101.579813 135.934069 92.696906 157.379303 92.696906 179.73725 
C 92.696906 202.095197 101.579813 223.540431 117.389269 239.349887 
C 133.198725 255.159343 154.643959 264.04225 177.001906 264.04225 
L 177.001906 264.04225 
z
" clip-path="url(#pda507d858a)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 177.001906 314.62525 
C 212.774622 314.62525 247.086996 300.412599 272.382126 275.11747 
C 297.677256 249.82234 311.889906 215.509966 311.889906 179.73725 
C 311.889906 143.964534 297.677256 109.65216 272.382126 84.35703 
C 247.086996 59.061901 212.774622 44.84925 177.001906 44.84925 
C 141.22919 44.84925 106.916817 59.061901 81.621687 84.35703 
C 56.326557 109.65216 42.113906 143.964534 42.113906 179.73725 
C 42.113906 215.509966 56.326557 249.82234 81.621687 275.11747 
C 106.916817 300.412599 141.22919 314.62525 177.001906 314.62525 
L 177.001906 314.62525 
z
" clip-path="url(#pda507d858a)" style="fill: none; opacity: 0.7; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" clip-path="url(#pda507d858a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="md77959c3b0" d="M 0 0 
L 0 3.5 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#md77959c3b0" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −8 -->
      <g transform="translate(35.479922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 75.835906 314.62525 
L 75.835906 44.84925 
" clip-path="url(#pda507d858a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#md77959c3b0" x="75.835906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −6 -->
      <g transform="translate(69.201922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 109.557906 314.62525 
L 109.557906 44.84925 
" clip-path="url(#pda507d858a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#md77959c3b0" x="109.557906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_3">
      <!-- −4 -->
      <g transform="translate(102.923922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 143.279906 314.62525 
L 143.279906 44.84925 
" clip-path="url(#pda507d858a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#md77959c3b0" x="143.279906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_4">
      <!-- −2 -->
      <g transform="translate(136.645922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 177.001906 314.62525 
L 177.001906 44.84925 
" clip-path="url(#pda507d858a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#md77959c3b0" x="177.001906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(174.138781 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 210.723906 314.62525 
L 210.723906 44.84925 
" clip-path="url(#pda507d858a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#md77959c3b0" x="210.723906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 2 -->
      <g transform="translate(207.860781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 244.445906 314.62525 
L 244.445906 44.84925 
" clip-path="url(#pda507d858a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#md77959c3b0" x="244.445906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 4 -->
      <g transform="translate(241.582781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <path d="M 278.167906 314.62525 
L 278.167906 44.84925 
" clip-path="url(#pda507d858a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#md77959c3b0" x="278.167906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 6 -->
      <g transform="translate(275.304781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_17">
      <path d="M 311.889906 314.62525 
L 311.889906 44.84925 
" clip-path="url(#pda507d858a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#md77959c3b0" x="311.889906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 8 -->
      <g transform="translate(309.026781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- Relative X Position (m) -->
     <g transform="translate(105.68925 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-52" d="M 2297 2597 
Q 2675 2597 2839 2737 
Q 3003 2878 3003 3200 
Q 3003 3519 2839 3656 
Q 2675 3794 2297 3794 
L 1791 3794 
L 1791 2597 
L 2297 2597 
z
M 1791 1766 
L 1791 0 
L 588 0 
L 588 4666 
L 2425 4666 
Q 3347 4666 3776 4356 
Q 4206 4047 4206 3378 
Q 4206 2916 3982 2619 
Q 3759 2322 3309 2181 
Q 3556 2125 3751 1926 
Q 3947 1728 4147 1325 
L 4800 0 
L 3519 0 
L 2950 1159 
Q 2778 1509 2601 1637 
Q 2425 1766 2131 1766 
L 1791 1766 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6c" d="M 538 4863 
L 1656 4863 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-76" d="M 97 3500 
L 1216 3500 
L 2088 1081 
L 2956 3500 
L 4078 3500 
L 2700 0 
L 1472 0 
L 97 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-58" d="M 3188 2381 
L 4806 0 
L 3553 0 
L 2463 1594 
L 1381 0 
L 122 0 
L 1741 2381 
L 184 4666 
L 1441 4666 
L 2463 3163 
L 3481 4666 
L 4744 4666 
L 3188 2381 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-50" d="M 588 4666 
L 2584 4666 
Q 3475 4666 3951 4270 
Q 4428 3875 4428 3144 
Q 4428 2409 3951 2014 
Q 3475 1619 2584 1619 
L 1791 1619 
L 1791 0 
L 588 0 
L 588 4666 
z
M 1791 3794 
L 1791 2491 
L 2456 2491 
Q 2806 2491 2997 2661 
Q 3188 2831 3188 3144 
Q 3188 3456 2997 3625 
Q 2806 3794 2456 3794 
L 1791 3794 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6f" d="M 2203 2784 
Q 1831 2784 1636 2517 
Q 1441 2250 1441 1747 
Q 1441 1244 1636 976 
Q 1831 709 2203 709 
Q 2569 709 2762 976 
Q 2956 1244 2956 1747 
Q 2956 2250 2762 2517 
Q 2569 2784 2203 2784 
z
M 2203 3584 
Q 3106 3584 3614 3096 
Q 4122 2609 4122 1747 
Q 4122 884 3614 396 
Q 3106 -91 2203 -91 
Q 1297 -91 786 396 
Q 275 884 275 1747 
Q 275 2609 786 3096 
Q 1297 3584 2203 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-28" d="M 2413 -844 
L 1484 -844 
Q 1006 -72 778 623 
Q 550 1319 550 2003 
Q 550 2688 779 3389 
Q 1009 4091 1484 4856 
L 2413 4856 
Q 2013 4116 1813 3408 
Q 1613 2700 1613 2009 
Q 1613 1319 1811 609 
Q 2009 -100 2413 -844 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6d" d="M 3781 2919 
Q 3994 3244 4286 3414 
Q 4578 3584 4928 3584 
Q 5531 3584 5847 3212 
Q 6163 2841 6163 2131 
L 6163 0 
L 5038 0 
L 5038 1825 
Q 5041 1866 5042 1909 
Q 5044 1953 5044 2034 
Q 5044 2406 4934 2573 
Q 4825 2741 4581 2741 
Q 4263 2741 4089 2478 
Q 3916 2216 3909 1719 
L 3909 0 
L 2784 0 
L 2784 1825 
Q 2784 2406 2684 2573 
Q 2584 2741 2328 2741 
Q 2006 2741 1831 2477 
Q 1656 2213 1656 1722 
L 1656 0 
L 531 0 
L 531 3500 
L 1656 3500 
L 1656 2988 
Q 1863 3284 2130 3434 
Q 2397 3584 2719 3584 
Q 3081 3584 3359 3409 
Q 3638 3234 3781 2919 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-29" d="M 513 -844 
Q 913 -100 1113 609 
Q 1313 1319 1313 2009 
Q 1313 2700 1113 3408 
Q 913 4116 513 4856 
L 1441 4856 
Q 1916 4091 2145 3389 
Q 2375 2688 2375 2003 
Q 2375 1319 2147 623 
Q 1919 -72 1441 -844 
L 513 -844 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-58" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="573.583984"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="608.398438"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="681.689453"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="750.390625"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="809.912109"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="844.189453"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="891.992188"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="926.269531"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="994.970703"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1066.162109"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1100.976562"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1146.679688"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1250.878906"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_19">
      <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" clip-path="url(#pda507d858a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <defs>
       <path id="m4efb2575e4" d="M 0 0 
L -3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m4efb2575e4" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_11">
      <!-- −8 -->
      <g transform="translate(21.845937 318.044547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_21">
      <path d="M 42.113906 280.90325 
L 311.889906 280.90325 
" clip-path="url(#pda507d858a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#m4efb2575e4" x="42.113906" y="280.90325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_12">
      <!-- −6 -->
      <g transform="translate(21.845937 284.322547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_23">
      <path d="M 42.113906 247.18125 
L 311.889906 247.18125 
" clip-path="url(#pda507d858a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#m4efb2575e4" x="42.113906" y="247.18125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_13">
      <!-- −4 -->
      <g transform="translate(21.845937 250.600547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_25">
      <path d="M 42.113906 213.45925 
L 311.889906 213.45925 
" clip-path="url(#pda507d858a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#m4efb2575e4" x="42.113906" y="213.45925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_14">
      <!-- −2 -->
      <g transform="translate(21.845937 216.878547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_27">
      <path d="M 42.113906 179.73725 
L 311.889906 179.73725 
" clip-path="url(#pda507d858a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#m4efb2575e4" x="42.113906" y="179.73725" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 0 -->
      <g transform="translate(29.387656 183.156547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_29">
      <path d="M 42.113906 146.01525 
L 311.889906 146.01525 
" clip-path="url(#pda507d858a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#m4efb2575e4" x="42.113906" y="146.01525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 2 -->
      <g transform="translate(29.387656 149.434547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_31">
      <path d="M 42.113906 112.29325 
L 311.889906 112.29325 
" clip-path="url(#pda507d858a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#m4efb2575e4" x="42.113906" y="112.29325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 4 -->
      <g transform="translate(29.387656 115.712547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_33">
      <path d="M 42.113906 78.57125 
L 311.889906 78.57125 
" clip-path="url(#pda507d858a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#m4efb2575e4" x="42.113906" y="78.57125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 6 -->
      <g transform="translate(29.387656 81.990547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_35">
      <path d="M 42.113906 44.84925 
L 311.889906 44.84925 
" clip-path="url(#pda507d858a)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#m4efb2575e4" x="42.113906" y="44.84925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 8 -->
      <g transform="translate(29.387656 48.268547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_20">
     <!-- Relative Y Position (m) -->
     <g transform="translate(15.558281 250.792094) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-59" d="M -63 4666 
L 1253 4666 
L 2316 3003 
L 3378 4666 
L 4697 4666 
L 2919 1966 
L 2919 0 
L 1716 0 
L 1716 1966 
L -63 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-59" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="568.896484"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="603.710938"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="677.001953"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="745.703125"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="805.224609"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="839.501953"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="887.304688"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="921.582031"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="990.283203"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1061.474609"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1096.289062"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1141.992188"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1246.191406"/>
     </g>
    </g>
   </g>
   <g id="patch_6">
    <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_21">
    <g id="patch_8">
     <path d="M 200.10902 150.124824 
L 225.429957 150.124824 
Q 227.229957 150.124824 227.229957 148.324824 
L 227.229957 139.614511 
Q 227.229957 137.814511 225.429957 137.814511 
L 200.10902 137.814511 
Q 198.30902 137.814511 198.30902 139.614511 
L 198.30902 148.324824 
Q 198.30902 150.124824 200.10902 150.124824 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 3.0m -->
    <g style="fill: #ffffff" transform="translate(200.10902 146.453105) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-33" d="M 2981 2516 
Q 3453 2394 3698 2092 
Q 3944 1791 3944 1325 
Q 3944 631 3412 270 
Q 2881 -91 1863 -91 
Q 1503 -91 1142 -33 
Q 781 25 428 141 
L 428 1069 
Q 766 900 1098 814 
Q 1431 728 1753 728 
Q 2231 728 2486 893 
Q 2741 1059 2741 1369 
Q 2741 1688 2480 1852 
Q 2219 2016 1709 2016 
L 1228 2016 
L 1228 2791 
L 1734 2791 
Q 2188 2791 2409 2933 
Q 2631 3075 2631 3366 
Q 2631 3634 2415 3781 
Q 2200 3928 1806 3928 
Q 1516 3928 1219 3862 
Q 922 3797 628 3669 
L 628 4550 
Q 984 4650 1334 4700 
Q 1684 4750 2022 4750 
Q 2931 4750 3382 4451 
Q 3834 4153 3834 3553 
Q 3834 3144 3618 2883 
Q 3403 2622 2981 2516 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2e" d="M 653 1209 
L 1778 1209 
L 1778 0 
L 653 0 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-30" d="M 2944 2338 
Q 2944 3213 2780 3570 
Q 2616 3928 2228 3928 
Q 1841 3928 1675 3570 
Q 1509 3213 1509 2338 
Q 1509 1453 1675 1090 
Q 1841 728 2228 728 
Q 2613 728 2778 1090 
Q 2944 1453 2944 2338 
z
M 4147 2328 
Q 4147 1169 3647 539 
Q 3147 -91 2228 -91 
Q 1306 -91 806 539 
Q 306 1169 306 2328 
Q 306 3491 806 4120 
Q 1306 4750 2228 4750 
Q 3147 4750 3647 4120 
Q 4147 3491 4147 2328 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-33"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_22">
    <g id="patch_9">
     <path d="M 223.954075 126.279769 
L 249.275012 126.279769 
Q 251.075012 126.279769 251.075012 124.479769 
L 251.075012 115.769457 
Q 251.075012 113.969457 249.275012 113.969457 
L 223.954075 113.969457 
Q 222.154075 113.969457 222.154075 115.769457 
L 222.154075 124.479769 
Q 222.154075 126.279769 223.954075 126.279769 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 5.0m -->
    <g style="fill: #ffffff" transform="translate(223.954075 122.60805) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-35" d="M 678 4666 
L 3669 4666 
L 3669 3781 
L 1638 3781 
L 1638 3059 
Q 1775 3097 1914 3117 
Q 2053 3138 2203 3138 
Q 3056 3138 3531 2711 
Q 4006 2284 4006 1522 
Q 4006 766 3489 337 
Q 2972 -91 2053 -91 
Q 1656 -91 1267 -14 
Q 878 63 494 219 
L 494 1166 
Q 875 947 1217 837 
Q 1559 728 1863 728 
Q 2300 728 2551 942 
Q 2803 1156 2803 1522 
Q 2803 1891 2551 2103 
Q 2300 2316 1863 2316 
Q 1603 2316 1309 2248 
Q 1016 2181 678 2041 
L 678 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-35"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_23">
    <g id="patch_10">
     <path d="M 259.721657 90.512187 
L 285.042595 90.512187 
Q 286.842595 90.512187 286.842595 88.712187 
L 286.842595 80.001874 
Q 286.842595 78.201874 285.042595 78.201874 
L 259.721657 78.201874 
Q 257.921657 78.201874 257.921657 80.001874 
L 257.921657 88.712187 
Q 257.921657 90.512187 259.721657 90.512187 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 8.0m -->
    <g style="fill: #ffffff" transform="translate(259.721657 86.840468) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-38" d="M 2228 2088 
Q 1891 2088 1709 1903 
Q 1528 1719 1528 1375 
Q 1528 1031 1709 848 
Q 1891 666 2228 666 
Q 2563 666 2741 848 
Q 2919 1031 2919 1375 
Q 2919 1722 2741 1905 
Q 2563 2088 2228 2088 
z
M 1350 2484 
Q 925 2613 709 2878 
Q 494 3144 494 3541 
Q 494 4131 934 4440 
Q 1375 4750 2228 4750 
Q 3075 4750 3515 4442 
Q 3956 4134 3956 3541 
Q 3956 3144 3739 2878 
Q 3522 2613 3097 2484 
Q 3572 2353 3814 2058 
Q 4056 1763 4056 1313 
Q 4056 619 3595 264 
Q 3134 -91 2228 -91 
Q 1319 -91 855 264 
Q 391 619 391 1313 
Q 391 1763 633 2058 
Q 875 2353 1350 2484 
z
M 1631 3419 
Q 1631 3141 1786 2991 
Q 1941 2841 2228 2841 
Q 2509 2841 2662 2991 
Q 2816 3141 2816 3419 
Q 2816 3697 2662 3845 
Q 2509 3994 2228 3994 
Q 1941 3994 1786 3844 
Q 1631 3694 1631 3419 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-38"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_24">
    <!-- Mid-Distance Cell (Neuron 22) -->
    <g transform="translate(75.364719 16.411875) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-4d" d="M 588 4666 
L 2119 4666 
L 3181 2169 
L 4250 4666 
L 5778 4666 
L 5778 0 
L 4641 0 
L 4641 3413 
L 3566 897 
L 2803 897 
L 1728 3413 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-64" d="M 2919 2988 
L 2919 4863 
L 4044 4863 
L 4044 0 
L 2919 0 
L 2919 506 
Q 2688 197 2409 53 
Q 2131 -91 1766 -91 
Q 1119 -91 703 423 
Q 288 938 288 1747 
Q 288 2556 703 3070 
Q 1119 3584 1766 3584 
Q 2128 3584 2408 3439 
Q 2688 3294 2919 2988 
z
M 2181 722 
Q 2541 722 2730 984 
Q 2919 1247 2919 1747 
Q 2919 2247 2730 2509 
Q 2541 2772 2181 2772 
Q 1825 2772 1636 2509 
Q 1447 2247 1447 1747 
Q 1447 1247 1636 984 
Q 1825 722 2181 722 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2d" d="M 347 2297 
L 2309 2297 
L 2309 1388 
L 347 1388 
L 347 2297 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-44" d="M 1791 3756 
L 1791 909 
L 2222 909 
Q 2959 909 3348 1275 
Q 3738 1641 3738 2338 
Q 3738 3031 3350 3393 
Q 2963 3756 2222 3756 
L 1791 3756 
z
M 588 4666 
L 1856 4666 
Q 2919 4666 3439 4514 
Q 3959 4363 4331 4000 
Q 4659 3684 4818 3271 
Q 4978 2859 4978 2338 
Q 4978 1809 4818 1395 
Q 4659 981 4331 666 
Q 3956 303 3431 151 
Q 2906 0 1856 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4e" d="M 588 4666 
L 1931 4666 
L 3628 1466 
L 3628 4666 
L 4769 4666 
L 4769 0 
L 3425 0 
L 1728 3200 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-75" d="M 500 1363 
L 500 3500 
L 1625 3500 
L 1625 3150 
Q 1625 2866 1622 2436 
Q 1619 2006 1619 1863 
Q 1619 1441 1641 1255 
Q 1663 1069 1716 984 
Q 1784 875 1895 815 
Q 2006 756 2150 756 
Q 2500 756 2700 1025 
Q 2900 1294 2900 1772 
L 2900 3500 
L 4019 3500 
L 4019 0 
L 2900 0 
L 2900 506 
Q 2647 200 2364 54 
Q 2081 -91 1741 -91 
Q 1134 -91 817 281 
Q 500 653 500 1363 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-32" d="M 1844 884 
L 3897 884 
L 3897 0 
L 506 0 
L 506 884 
L 2209 2388 
Q 2438 2594 2547 2791 
Q 2656 2988 2656 3200 
Q 2656 3528 2436 3728 
Q 2216 3928 1850 3928 
Q 1569 3928 1234 3808 
Q 900 3688 519 3450 
L 519 4475 
Q 925 4609 1322 4679 
Q 1719 4750 2100 4750 
Q 2938 4750 3402 4381 
Q 3866 4013 3866 3353 
Q 3866 2972 3669 2642 
Q 3472 2313 2841 1759 
L 1844 884 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-4d"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="99.511719"/>
     <use xlink:href="#DejaVuSans-Bold-64" x="133.789062"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="205.371094"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="246.875"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="329.882812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="364.160156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="423.681641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.484375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="538.964844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="610.15625"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="669.433594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="737.255859"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="772.070312"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="845.458984"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="913.28125"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="947.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="981.835938"/>
     <use xlink:href="#DejaVuSans-Bold-28" x="1016.650391"/>
     <use xlink:href="#DejaVuSans-Bold-4e" x="1062.353516"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1146.044922"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1213.867188"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1285.058594"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="1334.375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1403.076172"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1474.267578"/>
     <use xlink:href="#DejaVuSans-Bold-32" x="1509.082031"/>
     <use xlink:href="#DejaVuSans-Bold-32" x="1578.662109"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="1648.242188"/>
    </g>
    <!-- 2D Activation Map -->
    <g transform="translate(114.950656 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-41" d="M 3419 850 
L 1538 850 
L 1241 0 
L 31 0 
L 1759 4666 
L 3194 4666 
L 4922 0 
L 3713 0 
L 3419 850 
z
M 1838 1716 
L 3116 1716 
L 2478 3572 
L 1838 1716 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-32"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="152.587891"/>
     <use xlink:href="#DejaVuSans-Bold-41" x="187.402344"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="264.794922"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="324.072266"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="371.875"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="406.152344"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.337891"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="538.818359"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="586.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="620.898438"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="689.599609"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="760.791016"/>
     <use xlink:href="#DejaVuSans-Bold-4d" x="795.605469"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="895.117188"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="962.597656"/>
    </g>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="m79150bd7de" d="M 0 -5.477226 
L -1.229714 -1.692556 
L -5.209151 -1.692556 
L -1.989719 0.646499 
L -3.219432 4.431169 
L -0 2.092114 
L 3.219432 4.431169 
L 1.989719 0.646499 
L 5.209151 -1.692556 
L 1.229714 -1.692556 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#pda507d858a)">
     <use xlink:href="#m79150bd7de" x="177.001906" y="179.73725" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_11">
     <path d="M 242.388656 62.99175 
L 307.889906 62.99175 
L 307.889906 48.84925 
L 242.388656 48.84925 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="PathCollection_2">
     <g>
      <use xlink:href="#m79150bd7de" x="253.588656" y="56.028" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
     </g>
    </g>
    <g id="text_25">
     <!-- Observer -->
     <g transform="translate(267.988656 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4f"/>
      <use xlink:href="#DejaVuSans-62" x="78.710938"/>
      <use xlink:href="#DejaVuSans-73" x="142.1875"/>
      <use xlink:href="#DejaVuSans-65" x="194.287109"/>
      <use xlink:href="#DejaVuSans-72" x="255.810547"/>
      <use xlink:href="#DejaVuSans-76" x="296.923828"/>
      <use xlink:href="#DejaVuSans-65" x="356.103516"/>
      <use xlink:href="#DejaVuSans-72" x="417.626953"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_12">
    <path d="M 446.519015 314.62525 
L 818.497288 314.62525 
L 818.497288 44.84925 
L 446.519015 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="m10f86b086f" d="M 451.168196 -37.642188 
L 451.168196 -37.642188 
L 460.466557 -81.09881 
L 469.764919 -208.518824 
L 479.06328 -255.400702 
L 488.361642 -126.719047 
L 497.660003 -49.255099 
L 506.958364 -232.665147 
L 516.256726 -117.476163 
L 525.555087 -234.574447 
L 534.853448 -154.251399 
L 544.15181 -155.947664 
L 553.450171 -218.703018 
L 562.748533 -235.717627 
L 572.046894 -262.795358 
L 581.345255 -73.975787 
L 590.643617 -217.747795 
L 599.941978 -119.634025 
L 609.240339 -91.390235 
L 618.538701 -294.571711 
L 627.837062 -294.131523 
L 637.135424 -207.762837 
L 646.433785 -115.193179 
L 655.732146 -100.943757 
L 665.030508 -183.340293 
L 674.328869 -119.370388 
L 683.62723 -73.064257 
L 692.925592 -58.366441 
L 702.223953 -91.499605 
L 711.522315 -59.096678 
L 720.820676 -37.715507 
L 720.820676 -37.642188 
L 720.820676 -37.642188 
L 711.522315 -37.642188 
L 702.223953 -37.642188 
L 692.925592 -37.642188 
L 683.62723 -37.642188 
L 674.328869 -37.642188 
L 665.030508 -37.642188 
L 655.732146 -37.642188 
L 646.433785 -37.642188 
L 637.135424 -37.642188 
L 627.837062 -37.642188 
L 618.538701 -37.642188 
L 609.240339 -37.642188 
L 599.941978 -37.642188 
L 590.643617 -37.642188 
L 581.345255 -37.642188 
L 572.046894 -37.642188 
L 562.748533 -37.642188 
L 553.450171 -37.642188 
L 544.15181 -37.642188 
L 534.853448 -37.642188 
L 525.555087 -37.642188 
L 516.256726 -37.642188 
L 506.958364 -37.642188 
L 497.660003 -37.642188 
L 488.361642 -37.642188 
L 479.06328 -37.642188 
L 469.764919 -37.642188 
L 460.466557 -37.642188 
L 451.168196 -37.642188 
z
" style="stroke: #1f78b4; stroke-opacity: 0.3"/>
    </defs>
    <g clip-path="url(#pe033ec8690)">
     <use xlink:href="#m10f86b086f" x="0" y="352.267438" style="fill: #1f78b4; fill-opacity: 0.3; stroke: #1f78b4; stroke-opacity: 0.3"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10">
     <g id="line2d_37">
      <path d="M 446.519015 314.62525 
L 446.519015 44.84925 
" clip-path="url(#pe033ec8690)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#md77959c3b0" x="446.519015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 0 -->
      <g transform="translate(443.65589 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_39">
      <path d="M 493.016299 314.62525 
L 493.016299 44.84925 
" clip-path="url(#pe033ec8690)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#md77959c3b0" x="493.016299" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 1 -->
      <g transform="translate(490.153174 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_41">
      <path d="M 539.513584 314.62525 
L 539.513584 44.84925 
" clip-path="url(#pe033ec8690)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#md77959c3b0" x="539.513584" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 2 -->
      <g transform="translate(536.650459 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_43">
      <path d="M 586.010868 314.62525 
L 586.010868 44.84925 
" clip-path="url(#pe033ec8690)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#md77959c3b0" x="586.010868" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 3 -->
      <g transform="translate(583.147743 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_45">
      <path d="M 632.508152 314.62525 
L 632.508152 44.84925 
" clip-path="url(#pe033ec8690)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#md77959c3b0" x="632.508152" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 4 -->
      <g transform="translate(629.645027 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_47">
      <path d="M 679.005436 314.62525 
L 679.005436 44.84925 
" clip-path="url(#pe033ec8690)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#md77959c3b0" x="679.005436" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 5 -->
      <g transform="translate(676.142311 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_49">
      <path d="M 725.50272 314.62525 
L 725.50272 44.84925 
" clip-path="url(#pe033ec8690)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#md77959c3b0" x="725.50272" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 6 -->
      <g transform="translate(722.639595 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_51">
      <path d="M 772.000004 314.62525 
L 772.000004 44.84925 
" clip-path="url(#pe033ec8690)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#md77959c3b0" x="772.000004" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 7 -->
      <g transform="translate(769.136879 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-37"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_53">
      <path d="M 818.497288 314.62525 
L 818.497288 44.84925 
" clip-path="url(#pe033ec8690)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#md77959c3b0" x="818.497288" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 8 -->
      <g transform="translate(815.634163 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_35">
     <!-- Inter-Agent Distance (m) -->
     <g transform="translate(555.165261 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-49" d="M 588 4666 
L 1791 4666 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-49"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="37.207031"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="108.398438"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="156.201172"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="224.023438"/>
      <use xlink:href="#DejaVuSans-Bold-2d" x="273.339844"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="314.84375"/>
      <use xlink:href="#DejaVuSans-Bold-67" x="392.236328"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="463.818359"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="531.640625"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="602.832031"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="650.634766"/>
      <use xlink:href="#DejaVuSans-Bold-44" x="685.449219"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="768.457031"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="802.734375"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="862.255859"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="910.058594"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="977.539062"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="1048.730469"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="1108.007812"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1175.830078"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1210.644531"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1256.347656"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1360.546875"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_55">
      <path d="M 446.519015 314.62525 
L 818.497288 314.62525 
" clip-path="url(#pe033ec8690)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#m4efb2575e4" x="446.519015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 0.000 -->
      <g transform="translate(413.753703 318.044547) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
       <use xlink:href="#DejaVuSans-30" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_57">
      <path d="M 446.519015 281.809297 
L 818.497288 281.809297 
" clip-path="url(#pe033ec8690)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#m4efb2575e4" x="446.519015" y="281.809297" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 0.002 -->
      <g transform="translate(413.753703 285.228593) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
       <use xlink:href="#DejaVuSans-32" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_59">
      <path d="M 446.519015 248.993343 
L 818.497288 248.993343 
" clip-path="url(#pe033ec8690)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#m4efb2575e4" x="446.519015" y="248.993343" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 0.004 -->
      <g transform="translate(413.753703 252.41264) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
       <use xlink:href="#DejaVuSans-34" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_61">
      <path d="M 446.519015 216.17739 
L 818.497288 216.17739 
" clip-path="url(#pe033ec8690)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#m4efb2575e4" x="446.519015" y="216.17739" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 0.006 -->
      <g transform="translate(413.753703 219.596687) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
       <use xlink:href="#DejaVuSans-36" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_63">
      <path d="M 446.519015 183.361436 
L 818.497288 183.361436 
" clip-path="url(#pe033ec8690)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#m4efb2575e4" x="446.519015" y="183.361436" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 0.008 -->
      <g transform="translate(413.753703 186.780733) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
       <use xlink:href="#DejaVuSans-38" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_65">
      <path d="M 446.519015 150.545483 
L 818.497288 150.545483 
" clip-path="url(#pe033ec8690)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#m4efb2575e4" x="446.519015" y="150.545483" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 0.010 -->
      <g transform="translate(413.753703 153.96478) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-31" x="159.033203"/>
       <use xlink:href="#DejaVuSans-30" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_67">
      <path d="M 446.519015 117.72953 
L 818.497288 117.72953 
" clip-path="url(#pe033ec8690)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_68">
      <g>
       <use xlink:href="#m4efb2575e4" x="446.519015" y="117.72953" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_42">
      <!-- 0.012 -->
      <g transform="translate(413.753703 121.148827) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-31" x="159.033203"/>
       <use xlink:href="#DejaVuSans-32" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_69">
      <path d="M 446.519015 84.913576 
L 818.497288 84.913576 
" clip-path="url(#pe033ec8690)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_70">
      <g>
       <use xlink:href="#m4efb2575e4" x="446.519015" y="84.913576" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_43">
      <!-- 0.014 -->
      <g transform="translate(413.753703 88.332873) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-31" x="159.033203"/>
       <use xlink:href="#DejaVuSans-34" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_71">
      <path d="M 446.519015 52.097623 
L 818.497288 52.097623 
" clip-path="url(#pe033ec8690)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_72">
      <g>
       <use xlink:href="#m4efb2575e4" x="446.519015" y="52.097623" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_44">
      <!-- 0.016 -->
      <g transform="translate(413.753703 55.51692) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-31" x="159.033203"/>
       <use xlink:href="#DejaVuSans-36" x="222.65625"/>
      </g>
     </g>
    </g>
    <g id="text_45">
     <!-- Neural Activation -->
     <g transform="translate(407.466047 233.746391) rotate(-90) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="line2d_73">
    <path d="M 451.168196 314.62525 
L 460.466557 271.168628 
L 469.764919 143.748613 
L 479.06328 96.866736 
L 488.361642 225.54839 
L 497.660003 303.012339 
L 506.958364 119.60229 
L 516.256726 234.791274 
L 525.555087 117.692991 
L 534.853448 198.016039 
L 544.15181 196.319773 
L 553.450171 133.56442 
L 562.748533 116.54981 
L 572.046894 89.47208 
L 581.345255 278.29165 
L 590.643617 134.519642 
L 599.941978 232.633412 
L 609.240339 260.877203 
L 618.538701 57.695726 
L 627.837062 58.135914 
L 637.135424 144.504601 
L 646.433785 237.074258 
L 655.732146 251.32368 
L 665.030508 168.927145 
L 674.328869 232.89705 
L 683.62723 279.20318 
L 692.925592 293.900996 
L 702.223953 260.767832 
L 711.522315 293.170759 
L 720.820676 314.551931 
" clip-path="url(#pe033ec8690)" style="fill: none; stroke: #1f78b4; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_74">
    <path d="M 618.538701 314.62525 
L 618.538701 44.84925 
" clip-path="url(#pe033ec8690)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #1f78b4; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_75">
    <path d="M 586.010868 314.62525 
L 586.010868 44.84925 
" clip-path="url(#pe033ec8690)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_76">
    <path d="M 679.005436 314.62525 
L 679.005436 44.84925 
" clip-path="url(#pe033ec8690)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="patch_13">
    <path d="M 446.519015 314.62525 
L 446.519015 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_14">
    <path d="M 446.519015 314.62525 
L 818.497288 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_46">
    <g id="patch_15">
     <path d="M 705.457124 114.0718 
L 799.898374 114.0718 
Q 803.098374 114.0718 803.098374 110.8718 
L 803.098374 58.33805 
Q 803.098374 55.13805 799.898374 55.13805 
L 705.457124 55.13805 
Q 702.257124 55.13805 702.257124 58.33805 
L 702.257124 110.8718 
Q 702.257124 114.0718 705.457124 114.0718 
z
" style="fill: #ffffff; opacity: 0.9; stroke: #808080; stroke-linejoin: miter"/>
    </g>
    <!-- Peak Distance: 3.7m -->
    <g transform="translate(717.632124 64.4168) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-3a" d="M 750 794 
L 1409 794 
L 1409 0 
L 750 0 
L 750 794 
z
M 750 3309 
L 1409 3309 
L 1409 2516 
L 750 2516 
L 750 3309 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-44" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="346.179688"/>
     <use xlink:href="#DejaVuSans-73" x="373.962891"/>
     <use xlink:href="#DejaVuSans-74" x="426.0625"/>
     <use xlink:href="#DejaVuSans-61" x="465.271484"/>
     <use xlink:href="#DejaVuSans-6e" x="526.550781"/>
     <use xlink:href="#DejaVuSans-63" x="589.929688"/>
     <use xlink:href="#DejaVuSans-65" x="644.910156"/>
     <use xlink:href="#DejaVuSans-3a" x="706.433594"/>
     <use xlink:href="#DejaVuSans-20" x="740.125"/>
     <use xlink:href="#DejaVuSans-33" x="771.912109"/>
     <use xlink:href="#DejaVuSans-2e" x="835.535156"/>
     <use xlink:href="#DejaVuSans-37" x="867.322266"/>
     <use xlink:href="#DejaVuSans-6d" x="930.945312"/>
    </g>
    <!-- Max Activation: 0.016 -->
    <g transform="translate(712.624624 73.37505) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-4d"/>
     <use xlink:href="#DejaVuSans-61" x="86.279297"/>
     <use xlink:href="#DejaVuSans-78" x="147.558594"/>
     <use xlink:href="#DejaVuSans-20" x="206.738281"/>
     <use xlink:href="#DejaVuSans-41" x="238.525391"/>
     <use xlink:href="#DejaVuSans-63" x="305.183594"/>
     <use xlink:href="#DejaVuSans-74" x="360.164062"/>
     <use xlink:href="#DejaVuSans-69" x="399.373047"/>
     <use xlink:href="#DejaVuSans-76" x="427.15625"/>
     <use xlink:href="#DejaVuSans-61" x="486.335938"/>
     <use xlink:href="#DejaVuSans-74" x="547.615234"/>
     <use xlink:href="#DejaVuSans-69" x="586.824219"/>
     <use xlink:href="#DejaVuSans-6f" x="614.607422"/>
     <use xlink:href="#DejaVuSans-6e" x="675.789062"/>
     <use xlink:href="#DejaVuSans-3a" x="739.167969"/>
     <use xlink:href="#DejaVuSans-20" x="772.859375"/>
     <use xlink:href="#DejaVuSans-30" x="804.646484"/>
     <use xlink:href="#DejaVuSans-2e" x="868.269531"/>
     <use xlink:href="#DejaVuSans-30" x="900.056641"/>
     <use xlink:href="#DejaVuSans-31" x="963.679688"/>
     <use xlink:href="#DejaVuSans-36" x="1027.302734"/>
    </g>
    <!-- Sparsity: 0.340 -->
    <g transform="translate(739.728374 82.3333) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-70" x="63.476562"/>
     <use xlink:href="#DejaVuSans-61" x="126.953125"/>
     <use xlink:href="#DejaVuSans-72" x="188.232422"/>
     <use xlink:href="#DejaVuSans-73" x="229.345703"/>
     <use xlink:href="#DejaVuSans-69" x="281.445312"/>
     <use xlink:href="#DejaVuSans-74" x="309.228516"/>
     <use xlink:href="#DejaVuSans-79" x="348.4375"/>
     <use xlink:href="#DejaVuSans-3a" x="400.367188"/>
     <use xlink:href="#DejaVuSans-20" x="434.058594"/>
     <use xlink:href="#DejaVuSans-30" x="465.845703"/>
     <use xlink:href="#DejaVuSans-2e" x="529.46875"/>
     <use xlink:href="#DejaVuSans-33" x="561.255859"/>
     <use xlink:href="#DejaVuSans-34" x="624.878906"/>
     <use xlink:href="#DejaVuSans-30" x="688.501953"/>
    </g>
    <!-- Peak Significance: 2.67 -->
    <g transform="translate(706.910874 91.29155) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-53" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="332.654297"/>
     <use xlink:href="#DejaVuSans-67" x="360.4375"/>
     <use xlink:href="#DejaVuSans-6e" x="423.914062"/>
     <use xlink:href="#DejaVuSans-69" x="487.292969"/>
     <use xlink:href="#DejaVuSans-66" x="515.076172"/>
     <use xlink:href="#DejaVuSans-69" x="550.28125"/>
     <use xlink:href="#DejaVuSans-63" x="578.064453"/>
     <use xlink:href="#DejaVuSans-61" x="633.044922"/>
     <use xlink:href="#DejaVuSans-6e" x="694.324219"/>
     <use xlink:href="#DejaVuSans-63" x="757.703125"/>
     <use xlink:href="#DejaVuSans-65" x="812.683594"/>
     <use xlink:href="#DejaVuSans-3a" x="874.207031"/>
     <use xlink:href="#DejaVuSans-20" x="907.898438"/>
     <use xlink:href="#DejaVuSans-32" x="939.685547"/>
     <use xlink:href="#DejaVuSans-2e" x="1003.308594"/>
     <use xlink:href="#DejaVuSans-36" x="1035.095703"/>
     <use xlink:href="#DejaVuSans-37" x="1098.71875"/>
    </g>
    <!-- Selectivity Index: 0.530 -->
    <g transform="translate(705.457124 100.2498) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-65" x="63.476562"/>
     <use xlink:href="#DejaVuSans-6c" x="125"/>
     <use xlink:href="#DejaVuSans-65" x="152.783203"/>
     <use xlink:href="#DejaVuSans-63" x="214.306641"/>
     <use xlink:href="#DejaVuSans-74" x="269.287109"/>
     <use xlink:href="#DejaVuSans-69" x="308.496094"/>
     <use xlink:href="#DejaVuSans-76" x="336.279297"/>
     <use xlink:href="#DejaVuSans-69" x="395.458984"/>
     <use xlink:href="#DejaVuSans-74" x="423.242188"/>
     <use xlink:href="#DejaVuSans-79" x="462.451172"/>
     <use xlink:href="#DejaVuSans-20" x="521.630859"/>
     <use xlink:href="#DejaVuSans-49" x="553.417969"/>
     <use xlink:href="#DejaVuSans-6e" x="582.910156"/>
     <use xlink:href="#DejaVuSans-64" x="646.289062"/>
     <use xlink:href="#DejaVuSans-65" x="709.765625"/>
     <use xlink:href="#DejaVuSans-78" x="769.539062"/>
     <use xlink:href="#DejaVuSans-3a" x="828.71875"/>
     <use xlink:href="#DejaVuSans-20" x="862.410156"/>
     <use xlink:href="#DejaVuSans-30" x="894.197266"/>
     <use xlink:href="#DejaVuSans-2e" x="957.820312"/>
     <use xlink:href="#DejaVuSans-35" x="989.607422"/>
     <use xlink:href="#DejaVuSans-33" x="1053.230469"/>
     <use xlink:href="#DejaVuSans-30" x="1116.853516"/>
    </g>
    <!-- Peak Width: 4.2m -->
    <g transform="translate(729.374624 109.20805) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-57" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="365.804688"/>
     <use xlink:href="#DejaVuSans-64" x="393.587891"/>
     <use xlink:href="#DejaVuSans-74" x="457.064453"/>
     <use xlink:href="#DejaVuSans-68" x="496.273438"/>
     <use xlink:href="#DejaVuSans-3a" x="559.652344"/>
     <use xlink:href="#DejaVuSans-20" x="593.34375"/>
     <use xlink:href="#DejaVuSans-34" x="625.130859"/>
     <use xlink:href="#DejaVuSans-2e" x="688.753906"/>
     <use xlink:href="#DejaVuSans-32" x="720.541016"/>
     <use xlink:href="#DejaVuSans-6d" x="784.164062"/>
    </g>
   </g>
   <g id="text_47">
    <!-- Distance Tuning Curve -->
    <g transform="translate(556.694402 16.318125) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-54" d="M 31 4666 
L 4331 4666 
L 4331 3756 
L 2784 3756 
L 2784 0 
L 1581 0 
L 1581 3756 
L 31 3756 
L 31 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-44"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
     <use xlink:href="#DejaVuSans-Bold-54" x="525.195312"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="582.408203"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="653.599609"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="724.791016"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="759.068359"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="830.259766"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="901.841797"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="936.65625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1010.044922"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1081.236328"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="1130.552734"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1195.738281"/>
    </g>
    <!-- Peak: 3.7m, Sparsity: 0.340 -->
    <g transform="translate(538.769402 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-6b" d="M 538 4863 
L 1656 4863 
L 1656 2216 
L 2944 3500 
L 4244 3500 
L 2534 1894 
L 4378 0 
L 3022 0 
L 1656 1459 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-3a" d="M 716 3500 
L 1844 3500 
L 1844 2291 
L 716 2291 
L 716 3500 
z
M 716 1209 
L 1844 1209 
L 1844 0 
L 716 0 
L 716 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-37" d="M 428 4666 
L 3944 4666 
L 3944 3988 
L 2125 0 
L 953 0 
L 2675 3781 
L 428 3781 
L 428 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2c" d="M 653 1209 
L 1778 1209 
L 1778 256 
L 1006 -909 
L 341 -909 
L 653 256 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-53" d="M 3834 4519 
L 3834 3531 
Q 3450 3703 3084 3790 
Q 2719 3878 2394 3878 
Q 1963 3878 1756 3759 
Q 1550 3641 1550 3391 
Q 1550 3203 1689 3098 
Q 1828 2994 2194 2919 
L 2706 2816 
Q 3484 2659 3812 2340 
Q 4141 2022 4141 1434 
Q 4141 663 3683 286 
Q 3225 -91 2284 -91 
Q 1841 -91 1394 -6 
Q 947 78 500 244 
L 500 1259 
Q 947 1022 1364 901 
Q 1781 781 2169 781 
Q 2563 781 2772 912 
Q 2981 1044 2981 1288 
Q 2981 1506 2839 1625 
Q 2697 1744 2272 1838 
L 1806 1941 
Q 1106 2091 782 2419 
Q 459 2747 459 3303 
Q 459 4000 909 4375 
Q 1359 4750 2203 4750 
Q 2588 4750 2994 4692 
Q 3400 4634 3834 4519 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-79" d="M 78 3500 
L 1197 3500 
L 2138 1125 
L 2938 3500 
L 4056 3500 
L 2584 -331 
Q 2363 -916 2067 -1148 
Q 1772 -1381 1288 -1381 
L 641 -1381 
L 641 -647 
L 991 -647 
Q 1275 -647 1404 -556 
Q 1534 -466 1606 -231 
L 1638 -134 
L 78 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-34" d="M 2356 3675 
L 1038 1722 
L 2356 1722 
L 2356 3675 
z
M 2156 4666 
L 3494 4666 
L 3494 1722 
L 4159 1722 
L 4159 850 
L 3494 850 
L 3494 0 
L 2356 0 
L 2356 850 
L 288 850 
L 288 1881 
L 2156 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-50"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="73.291016"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="141.113281"/>
     <use xlink:href="#DejaVuSans-Bold-6b" x="208.59375"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="275.097656"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="315.087891"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="349.902344"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="419.482422"/>
     <use xlink:href="#DejaVuSans-Bold-37" x="457.470703"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="527.050781"/>
     <use xlink:href="#DejaVuSans-Bold-2c" x="631.25"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="669.238281"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="704.052734"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="776.074219"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="847.65625"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="915.136719"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="964.453125"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1023.974609"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="1058.251953"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="1106.054688"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="1171.240234"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1211.230469"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1246.044922"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="1315.625"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="1353.613281"/>
     <use xlink:href="#DejaVuSans-Bold-34" x="1423.193359"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1492.773438"/>
    </g>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="me7b6f70d7a" d="M 0 5 
C 1.326016 5 2.597899 4.473168 3.535534 3.535534 
C 4.473168 2.597899 5 1.326016 5 0 
C 5 -1.326016 4.473168 -2.597899 3.535534 -3.535534 
C 2.597899 -4.473168 1.326016 -5 0 -5 
C -1.326016 -5 -2.597899 -4.473168 -3.535534 -3.535534 
C -4.473168 -2.597899 -5 -1.326016 -5 0 
C -5 1.326016 -4.473168 2.597899 -3.535534 3.535534 
C -2.597899 4.473168 -1.326016 5 0 5 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#pe033ec8690)">
     <use xlink:href="#me7b6f70d7a" x="618.538701" y="57.695726" style="fill: #1f78b4; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="patch_16">
     <path d="M 695.989788 86.47675 
L 812.897288 86.47675 
Q 814.497288 86.47675 814.497288 84.87675 
L 814.497288 50.44925 
Q 814.497288 48.84925 812.897288 48.84925 
L 695.989788 48.84925 
Q 694.389788 48.84925 694.389788 50.44925 
L 694.389788 84.87675 
Q 694.389788 86.47675 695.989788 86.47675 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_77">
     <path d="M 697.589788 55.328 
L 705.589788 55.328 
L 713.589788 55.328 
" style="fill: none; stroke: #1f78b4; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
    </g>
    <g id="text_48">
     <!-- Tuning Curve -->
     <g transform="translate(719.989788 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-54"/>
      <use xlink:href="#DejaVuSans-75" x="45.958984"/>
      <use xlink:href="#DejaVuSans-6e" x="109.337891"/>
      <use xlink:href="#DejaVuSans-69" x="172.716797"/>
      <use xlink:href="#DejaVuSans-6e" x="200.5"/>
      <use xlink:href="#DejaVuSans-67" x="263.878906"/>
      <use xlink:href="#DejaVuSans-20" x="327.355469"/>
      <use xlink:href="#DejaVuSans-43" x="359.142578"/>
      <use xlink:href="#DejaVuSans-75" x="428.966797"/>
      <use xlink:href="#DejaVuSans-72" x="492.345703"/>
      <use xlink:href="#DejaVuSans-76" x="533.458984"/>
      <use xlink:href="#DejaVuSans-65" x="592.638672"/>
     </g>
    </g>
    <g id="line2d_78">
     <path d="M 697.589788 67.0705 
L 705.589788 67.0705 
L 713.589788 67.0705 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_49">
     <!-- Close threshold (3.0m) -->
     <g transform="translate(719.989788 69.8705) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6c" x="69.824219"/>
      <use xlink:href="#DejaVuSans-6f" x="97.607422"/>
      <use xlink:href="#DejaVuSans-73" x="158.789062"/>
      <use xlink:href="#DejaVuSans-65" x="210.888672"/>
      <use xlink:href="#DejaVuSans-20" x="272.412109"/>
      <use xlink:href="#DejaVuSans-74" x="304.199219"/>
      <use xlink:href="#DejaVuSans-68" x="343.408203"/>
      <use xlink:href="#DejaVuSans-72" x="406.787109"/>
      <use xlink:href="#DejaVuSans-65" x="445.650391"/>
      <use xlink:href="#DejaVuSans-73" x="507.173828"/>
      <use xlink:href="#DejaVuSans-68" x="559.273438"/>
      <use xlink:href="#DejaVuSans-6f" x="622.652344"/>
      <use xlink:href="#DejaVuSans-6c" x="683.833984"/>
      <use xlink:href="#DejaVuSans-64" x="711.617188"/>
      <use xlink:href="#DejaVuSans-20" x="775.09375"/>
      <use xlink:href="#DejaVuSans-28" x="806.880859"/>
      <use xlink:href="#DejaVuSans-33" x="845.894531"/>
      <use xlink:href="#DejaVuSans-2e" x="909.517578"/>
      <use xlink:href="#DejaVuSans-30" x="941.304688"/>
      <use xlink:href="#DejaVuSans-6d" x="1004.927734"/>
      <use xlink:href="#DejaVuSans-29" x="1102.339844"/>
     </g>
    </g>
    <g id="line2d_79">
     <path d="M 697.589788 78.813 
L 705.589788 78.813 
L 713.589788 78.813 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_50">
     <!-- Far threshold (5.0m) -->
     <g transform="translate(719.989788 81.613) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" x="48.394531"/>
      <use xlink:href="#DejaVuSans-72" x="109.673828"/>
      <use xlink:href="#DejaVuSans-20" x="150.787109"/>
      <use xlink:href="#DejaVuSans-74" x="182.574219"/>
      <use xlink:href="#DejaVuSans-68" x="221.783203"/>
      <use xlink:href="#DejaVuSans-72" x="285.162109"/>
      <use xlink:href="#DejaVuSans-65" x="324.025391"/>
      <use xlink:href="#DejaVuSans-73" x="385.548828"/>
      <use xlink:href="#DejaVuSans-68" x="437.648438"/>
      <use xlink:href="#DejaVuSans-6f" x="501.027344"/>
      <use xlink:href="#DejaVuSans-6c" x="562.208984"/>
      <use xlink:href="#DejaVuSans-64" x="589.992188"/>
      <use xlink:href="#DejaVuSans-20" x="653.46875"/>
      <use xlink:href="#DejaVuSans-28" x="685.255859"/>
      <use xlink:href="#DejaVuSans-35" x="724.269531"/>
      <use xlink:href="#DejaVuSans-2e" x="787.892578"/>
      <use xlink:href="#DejaVuSans-30" x="819.679688"/>
      <use xlink:href="#DejaVuSans-6d" x="883.302734"/>
      <use xlink:href="#DejaVuSans-29" x="980.714844"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_17">
    <path d="M 330.48882 287.64765 
L 341.27986 287.64765 
L 341.27986 71.82685 
L 330.48882 71.82685 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAABYAAAHBCAYAAABt+EsYAAACDUlEQVR4nO2bS25CQQwEDUzunXsHQhYz9MsFelFS1QGskrv9+AhuM9/vKbBmVmPu3CtTZ2bNfFUGN43dcQa7io11C9YtIOvGM+60wlUE6xasW7BuwR0HX/4Dsm48Y5/HG96B3GbelS9CasYOBg9ec+sMLho/WoM7D7fmKjoPN40vrFsghscztm4HYng8Y+t2IIbHM7ZuB2J4PGPrdiCGxzO2bgdieDzjUt2KxqUeE8PT+MA7EJ/HQeNQM3YVQePg0y0Qw9O4PZi3Co2DT7dADE/ja3Dl1zzVVbwqgzUO6/54dgavL9oqHrjwiMatuvFWcccZj8abpnHrQICr0Hhj3YLhBY3/D+70uLcKD+QDz9i6BV54PGPrFnjh8YyRq/BANtYtWLdg3fqDeavwQELtQJDh8Yyt2xlMDE/jjQcSPJCANLZuZzAxPJ6xdTuDieFpvOEdiF8rBI2DdQvI8DTOYN9XbHjhFY1frbo9cXV7/VYGF8PrrBhovG6dGjf/XYELT+NrsHU7aBxqrSCuQuMDr268HRte4IXHMza8oHGwbsHwAu8VhLgKnrEn3R7MW4V1C9YtGF7gGRNXYd0OPGPDCxoHPyoEjQOvbrwdG17ghcczNrygcbBuYf3gwiv9jq6549a7TeAqcMYl4eaOaYMNL7jja7Cr+GDdgnUL1i3wjA0veCDBugXrFng75q3iD9EGue+xdPB4AAAAAElFTkSuQmCC" id="image9883c8dfc3" transform="scale(1 -1) translate(0 -215.52)" x="330.72" y="-71.52" width="10.56" height="215.52"/>
   <g id="matplotlib.axis_5"/>
   <g id="matplotlib.axis_6">
    <g id="ytick_19">
     <g id="line2d_80">
      <defs>
       <path id="mc45e83c36a" d="M 0 0 
L 3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#mc45e83c36a" x="341.27986" y="287.64765" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_51">
      <!-- 0.0 -->
      <g transform="translate(348.27986 291.066947) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_81">
      <g>
       <use xlink:href="#mc45e83c36a" x="341.27986" y="244.48349" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_52">
      <!-- 0.2 -->
      <g transform="translate(348.27986 247.902787) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_82">
      <g>
       <use xlink:href="#mc45e83c36a" x="341.27986" y="201.31933" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_53">
      <!-- 0.4 -->
      <g transform="translate(348.27986 204.738627) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_83">
      <g>
       <use xlink:href="#mc45e83c36a" x="341.27986" y="158.15517" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_54">
      <!-- 0.6 -->
      <g transform="translate(348.27986 161.574467) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_23">
     <g id="line2d_84">
      <g>
       <use xlink:href="#mc45e83c36a" x="341.27986" y="114.99101" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_55">
      <!-- 0.8 -->
      <g transform="translate(348.27986 118.410307) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-38" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_24">
     <g id="line2d_85">
      <g>
       <use xlink:href="#mc45e83c36a" x="341.27986" y="71.82685" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_56">
      <!-- 1.0 -->
      <g transform="translate(348.27986 75.246147) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_57">
     <!-- Neural Activation -->
     <g transform="translate(369.234391 125.728109) rotate(-270) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_18">
    <path d="M 330.48882 287.64765 
L 335.88434 287.64765 
L 341.27986 287.64765 
L 341.27986 71.82685 
L 335.88434 71.82685 
L 330.48882 71.82685 
L 330.48882 287.64765 
z
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="pda507d858a">
   <rect x="42.113906" y="44.84925" width="269.776" height="269.776"/>
  </clipPath>
  <clipPath id="pe033ec8690">
   <rect x="446.519015" y="44.84925" width="371.978273" height="269.776"/>
  </clipPath>
 </defs>
</svg>
