<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="824.000413pt" height="352.267438pt" viewBox="0 0 824.000413 352.267438" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:55:06.816410</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 352.267438 
L 824.000413 352.267438 
L 824.000413 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
L 311.889906 44.84925 
L 42.113906 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g clip-path="url(#p5ab084d5f0)">
    <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAAjMAAAIzCAYAAAD1UpjCAAEAAElEQVR4nOz9a3fbOrKFjU4HEEndVtS9Mnb3eMf5cP7p+b17v6u7vdqORFKkfT6ARRSKBRCSnaw4wcxgeBFF8Wbi4awC8AD8/15RVFRUVFRUVPRB9emv3oGioqKioqKioreowExRUVFRUVHRh1aBmaKioqKioqIPrQIzRUVFRUVFRR9aBWaKioqKioqKPrQKzBQVFRUVFRV9aBWYKSoqKioqKvrQKjBTVFRUVFRU9KFVYKaoqKioqKjoQ6vATFFRUVFRUdGHVoGZoqKioqKiog+tAjNFRUVFRUVFH1oFZoqKioqKioo+tArMFBUVFRUVFX1oFZgpKioqKioq+tAqMFNUVFRUVFT0oVVgpqioqKioqOhDq8BMUVFRUVFR0YdWgZmioqKioqKiD60CM0VFRUVFRUUfWgVmioqKioqKij60CswUFRUVFRUVfWgVmCkqKioqKir60CowU1RUVFRUVPShVWCmqKioqKio6EOrwExRUVFRUVHRh1aBmaKioqKioqIPrQIzRUVFRUVFRR9aBWaKioqKioqKPrQKzBQVFRUVFRV9aBWYKSoqKioqKvrQKjBTVFRUVFRU9KFVYKaoqKioqKjoQ6vATFFRUVFRUdGHlv2rd6CoqOijanPj+tdvshdFRUVFBWaKiooydCu45G6jAE5RUdHbVWCmqKgoovcAmNzfKFBTVFR0vwrMFBUVCd0KManHyHDjbxaoKSoqul0FZoqKiphyQOaWx4Zcdw1uNihAU1RUdKsKzBQVFU1Kgcx7PSr4dmJgU4CmqKjoNhWYKSoqwttB5p7kXtquBjUFaIqKivJVYKao6JfUWjhJezTkhqAITlLrc1CJQU3JoykqKspTgZmiom+q964RdE/B/h7gcgvcxJbnAAxXDIoK3BQVFYUqMFNU9C76HtWY3+t3Yn/2m8g6seXa55oIPrR8mVg46RpZn/9egZqioiKnAjNFRXcrFyze488st4rzLfuQcl82iWXa9lI5M5oLk8qJGbAEllu38R66BxwLYBUV/RUqMFNUdLPuCdu8Ve+1TW3frfKZXKatI/cr9VkMUNbCTGvfeU+g+RatHBe4KSr6HiowU1R0k+6p9fMeheR7FIo5zgqHlhjQyG1pn0sQ4dMSSDSoketxaHlPoPnW4UG+/QI2RUXfSgVmioqyFSv4boGY3D+5WM0e0i0F460QQ/Oxz1JujQwHXcUyAo8cAIl9l35Hy6MB8s7NvRCT005O6jcL0BQVfQsVmCkqytItYPIWiImtvwY3t24zFlKKTaegR85z8NBgJOa48PW4+HdSQCOPLQYOt5y71HW7tXVj/tsFaoqK3lMFZoqKVpULJ2+pzqxJq8oM3JcMnJsXI6dznBoJQINYroGMXJeLA03su7nui1zvvbtrSH0vdZ2KS1NU9J4qMFNUlNR7gcw9TkosdLLWRsuaYxSrtaS5Mdpyvj6Uz1IuTS6McCCKuTo50vJ2Ynrvx+HavhagKSp6LxWYKSoKlJMXc0t7LGtttKTEC8ItlgVfbqGeExqieQkw2hhiWoMd2teBTV8R5tLQZxc2zZfd6sTQ72l6q2uWunb3OGWpVpIL4BQV3aoCM0VFAPIcmFvyRlLzuZK5JBJo3gIzmotCULKFDjExp8YCeFB+95Xtp4QbDi1bhEBzmbZ7xtKhiTWwJ4+VS4a9SO8VFkwlH6dqanG9JZm5qKiowExRUXaIaK3acszpkMtydIUr5IHQzeBAc4sjsJYDw0FlK6YlxAhwsWzMI2DDw7SL/HcBBzkDQpAh94WmL+yYYwW6BgW0XNtBUo6ztva9td+KXRuZ4JxqHVn+ZgGboqKYCswUFQVKhY1S+SWpsIsGOmviAEPzsVBOjmJhIm28Q+jQiELcrgy0u9HhgW2XAO2CJcjw/eT5MhYh7EiAkNBxVZavhQFzXBu+XKttlpMXpF3L1DblukVFRUCBmaJfWrECK1bopWAgBQna99dEoZhtZJrvW0x8vbXcFwkxWwQOTAMPK3z6LpgB0E7DQOeOD1yxJFq+7BZ3Y+0ay2Wp5bn9RWlgo1Vbl9+PHXuBmqIiqQIzRb+o1kBGFmYxIMhxOrT118TDKzzPhBeAa0mpufvGc2UYxHB4adi0hBe5jH6eoCUFMy2AZ7B9IPHYFYWjtM+lUmATu8apUKC2jOBkDUBy3Jk1qIkBTe72i4p+DRWYKfoFlQsyazCQAoRtYr01SRdGFngSbLTvayGy1P5vw+80yiDBJTYN6BDTYgkzz9N3njGFn47iOGIhF9nCsBZi0sBmLcdJm9fgRvtNHs5KQYasph47jlQnm1DWKyr6dVVgpugXU64zsubQcDdDgsIWOixY6LV+uF7ZtKz5w8EmBjNaC7oaxGzF8mm/NIiRQMPhRVsGLMFFThPMEMgACtBwiCN3hp8XPq05MRpkxEAVymcp2FlzUdZCS9q+prpvkNvnKkBTVFRgpugXUgpk1go3izgMpGr/5EIMia9HvyUBRhZ8XFphlwIwkRdzgA4xtFwDGTkGQmiJgQy5MvwwWgDtA7wzxa/ZBeF1gPhyzMmQ113LZeLrxaBHrpMLMZokdEp4scoy+VtFRUWkAjNFv4g0kEmFk4AQSiyWtXy2yrwCCppzsaYABHiuDVVrlg3QyS/L4+Qg8xB+RPumwYwGN1aZ5ss0eNFgpoEHGtoXcmnaDYDfENZs0nRLwZ66zinA0aYpFAik2wOia6WBitYq8hqsrLlQct2iol9DBWaKfgFpOQ/8szWIkaCyjQyRxFlZ2APp8krLM5nB5mEq6GXhJcNTJKr6LABGhodiTswtQJNyZrTxo9gPPjyCHad0xaRywj40L8Nt2nLp/MhlHEy0RgxjDQRqy7Sq9zHQ0Y5J/jaXPFcFbop+XhWYKfrJFQOZtYJMCyn9hijEpOBFDqRUmaQ5GbIWEF8XD2x7/Jg3/jBTOS8SXG6BmXudGblPMuT0OK2HHeIgE0uY1ZZLOE1dd8214cvl72sNGRKsxHKftFwo2kaOY6PlR8n94srJFyvAU/QxVWCm6CeWBjLaMhmG0fJLtnCJqbtpLCBmrfDny2MVc/i8BBdtGCCAJjLOyXVJOTKx41Gh7QoM1uW9pIDmeVr/ETrI0PQzfSfmMsiCnz6TcAPEk7JjTpxcnsp74l030D5oLsyAsN8pGmu11+R0TDFXin+eqxTwFNAp+nFVYKboF5AEGTmvgYvMidnCOzM79zVe2MuCP+VwAGmg4TBDhTkf5gIecaBZgxltWc4xqMMVsCM2TY+66QAAXVvj2lbAYFy4SALNAUuQkUBDx0C5NAMvsKUTwj/TwjO0Tixx28LnREXyiwDdWSNnLNgPua8EJhxkJPBwwNEcmRTUaLkzOVW7b1EJWxX9uCowU/QTKuXIaPMypMTzYqhQ+20aNm7RITJocCOhgCQBhk9r8ELjA5um9WMgI2Em5crcBDSvQNPNAFM1PSrToUaPEQbD3qAfa/RthWEw6NsaL4MB2trl/TxjCS/auSCQARjQ/MZWoCrbsVpAHABkwnYKYBM5RnyTqfEA+PuKErdpvwb4jjSlS6NBSwxqZKiJfy/l1mjr3yq5/aKiv04FZop+MqVscg1kJMRoCb7scwKZkxivDRxsgGXhJ5dxkJEwQyGaFsvCU5tfy3GhY4pBzDwdB5gKfjzCokeFwRj0+xojDLrPNfquwjgY59ocKsA2YaRPOw8Qy5/BrgeBDAEArSRBhl9vreYZhxylCwc5vgVmZgeN+qOiL0uHRoaeeKvH0mGSksfMlwPp76eKgFzQKVBT9NerwEzRT6QYyCgJsUE4gYcdYsMuBJlTZDoFNCcHA7MGM42nP8NhKkipEHzGEmYIZDjMyAFYh5lUvozmJh1aNIcz6qZHVfeo0aHCclzBHV8/oc0Agx4T0NQ1+rrCsDe4dDv8iZMDGqkUzGA6B/N1kSCQKrhjIKO0frwWlos5YXzcwl+nVajZwDs18oC5YsfHoe0t7d9w3Zp3c89vFBW9jwrMFP3EsmI6VoOJQw0NEmYQwstJmefLDnz6FZ8OZxxPT9jWZ4zTfo2jg5lxCMdzWOZ5Bzw/uNwSDjE0PEOHFznEYCbmzATTLTZNj93hjG19XrgwbuxgZouzDzNNENOhmp0aPl3XPfAFeLJHvGDvLxPf7xbh8gNbPlDDehe2gqy5pHUREHHcAB1gtHm+T3LMpwlkLFag5sK+yEGEuzOx8JOUhBp5/Fy3gEdu3k0BmqK/RgVmin4S5bgyQAgyfD7mzLBkXw4oa8PkxHCIOeIJu6nwHWEwmglepvFYW4xw0+fPO1wOZ5yfd3hp9t6VeURYsKYghoYUyESdmSs+EcTsLwGsEMTI+Qo9djg7FwY1RlzmT90yDzMVeqB25+rPwQBDs+4y8elHIADNBbxoYSYOMRGQSQ0cdmhftP2jsXRmJNS0gAtr7dj+XuBdGr7BHMW6Q4iByD01l3LcmgI0Rd9fBWaKfgJpwELLuSOzZZ/zGixU7ZpqK/02zT8sQ0tfVsYCYo71E7bwIHPE0+xcAMAIDzAjzPzZETWe9kds9xc8NR3a5x3w2GCuzsxhJhZu4gVqjuPAk3vtiKrpYCztEe2pH3PRnvv5YV5eo0OHGjV6dACq6Vh3OKOrK3SHCm1bu+rcvKDXykoODo8PAP4O72zIhumAsKXkSCOHuZDHz5sWDZLLWjaWx7WY38AlN+/gkoUvbKBQGh3XwJYP8OEp2fIwsIQbTRp43BpikutqtaqKir6dCswUfXBpNZc0aW2K8PZjqLYStSWDpRvDwUWdTkPMEW7ZHGaaEIDjAi0/Y4cKHXbYof7c4anpcW52uD4eAfsQFqocXOT0Wv6HCjkDNk0Pa0cYu4QXI6DGKmDjjsPO0xxoxmkZze8OFxdWa/fpPCCpAS4MNwMAD9VQwc9DLisNHaYghk+nynk+vwowsYGOicJoHF5oLHNuKNwmXRg5vwYu2oHkOC2pauAlQbjo26vATNEHVgxkUkm+5MbIpF8BMieEMPNFGU58vsXnL49zOEmDGDf9rEKMnN/hjBodzri4xNraDU92QGuPgN348qxVxty1iSWxqu6MCy8ZO8DYEZXpgj0zUbJwMhhnp0muW6MLoK1G59wZU2F3OOO5rZw7wY+FK+U+gXJQgLAqNIGNSPSNgcwBcZDhy3LMijVHZnV4AIbpfgxqb1EoSoamJLzRgWohJ6lYCGqtGwX6bs76BWqKvp0KzBT9BNJAhn/Gk3y1ziJFY3gnLJN5OcT8M5zffPkvTr8/4oT/JCHGfXZOQgzNn7HDFhc84TBlnXRurc8Dnu2IZ3sEmibeVQCHGmI5CTELoHEgUzXdXGtJhpbkHvuxd2hkCIqWjTAw0xQAbHFBhxo7XNA3NTaHC6689eBcyXMAwMONkgDLE55TMCMhhpblPjVzQkx8eE7Mz52NErDIaXJo6Id5qDWnVeBYcrA8fzlVxOn7MQAqOTVF768CM0UfVFr8X2sYjzszHGRk1dyj+8oJeqKvBJl/AvjyiubLf/Dl879wwiNOeJyB5TDBzBHP2MG7NUc8zQmxEmSoKvMIOyXXOoixDCJq9LB7F/45NzvXyi7lm2gwQ85MDGIsphyZIQAZnitD41vE17cY5xwh7tz0qGa4602F8WDwNBi8DPu8xGYaZGWfIDzFHRvkh5TkIAFHk3yaxuBFLiNoaZR5CTgz1PCaXHIHBmWauzOA/vfDAeMWiJHSnCANngrQFL2fCswUfXBxcJHzPLxE8xJkKFfmIV5bSYaXJpA5/PMP/L53IPM7/sDf8IiDcGZiYSYOLrymDwHOE46o0c/IU7FpADC1A5q+rdC1PcbB4KWtXCIphxgqeDm8CIihrgiMHWaQocbwdDdGd2pIBkOQL+OvlJsnsKmmGlDVVFNqqA3Gg8Ez1W6K6RaY4W5NCmBSIKOBDRA+PbXpNUdmgIOVA8KGEDnUWCyhpwWWbSfRwcuuEeRnue6M7N9KSgJLah4I4aio6P1VYKboJ1Qq1KRVw96EBdkJeu2lObzkQeZ3/IEv+Bd+x78CZ4Yghjs0Msw0NybHnBqCGirkOcgACMbGjOj3Fbq9a123a6spkVaBmmDwEPNpSvKtp5pLVd3DYGAg5V0ZCTVSHGpkvoxPCjbz9I65UyP5T3uLYTBoB+OuS8yN4XqGDjOtWBaDmJgrc0IcZujJOY+njibttHN29I5ZCmg4yPBpAhmapnkOOANvYgAIQYLDg7YMSLszEki0dnukNKDh0n6/uDNF76MCM0UfWDFXhqZj8KI0iEeFyYmNOcjMrkwIMv/A/+F3/Atf8McMMxJigjDT+ITO8HZXdKihBGA7w8MQQASBwwU7VOjR1xWqukbf9CHUPG+iAGPsMNdYMtbBUY2OIYYONSTu1IRXhYeVJNQQ7IwTtHUCagzw2a3b4ohlb9nQwYZghUODZfMD1iFGAq067Vpx/mSn6zCP3bmkZd41q+OhQO640CCdIoIXOhYONM+Aa3zvOJ/5dHs7MRCR4SU6obmwQetq35PbKA5N0bdRgZmiDyjtjVKGl2iZZctoSLgymjPDhubLf1SQ+R/835QA/LwIMRHEHP+84uErAHPFawN0NTDaT+jqZSu5PKykVX8mubZbqgXUXOyIzo64TgVsDGAAwBgHK3Z2gQbmwvjPAAShpfDsh8v4/mouzogR/QRzlCNEuTQAMB7cdIsjVIeGRGV0i7C85oV/O62bgphYiOnEx6/YnJ5mF4sA0B1jeM4AoN9XQYebi57ECWQkwCycNHh4gZgOgIaqcWtQQ9JqFMliYA1INEmgoe3eAkVFRferwEzRB1Is6VcDGenKaO6M0p6MhBk2bL78F6fPj0HYKBZKomGLswsZtVc8sLDPA4BmAK71C4Aeox1BZfmAETWqCW1cn0dbnKd2dmmogsKfa4BB1fQAfBcJBDIkvnwcDfymqrnGEbkovJD24bAa9dSpJOUAUbcGy5CY/10NcjhIDTDYwe2P+ew+n4EmVchrYZxBTMfyZNZcmgNmkKFuHTTYk/Nzy8emQr+v0e0naJ1CguNgcX3eOvfsEaE7w92aR8Tdmvn4qW0aYFktnY9j/TpJye9uEebjbNnn3AWS2+BAI5cBBXSK3ksFZop+cMVaLbXiMw1q6KHL3RhRDXsFYHg7MsdTLKnXAww1+0/JrRYj6rGDGQCM02D92I7AOLxgtGYK2/haP3aqvVSjwwVbGAwB4Lgeqn3rwQQDNXqMxp0Lcl8AYBwsjB0wDMwBYVADVKrbQPtjMOKM7dyFQY9qyunxycJhmGoJRARJvOVj+ow6qeS/N4ec7A5zL9uUS8ILdxm60ZJtNYiJDTw/5gB8OpyxO5xxrJ8CaJPHXM2hwXGGPgLPGW7qCn3tps+nHc7PW7SHo4eaA9JwEwOcOVeIanDR34eEG6kY4BD8xMBozQUCW07bzMm9KSq6XQVmir6xYjACxN/IUt+RikGNli+zQ9KRkWGFeXjF4fSEo1mGjxzEXLCdnZjLDCAEHWYYYXmkRYRDzACYYcRgvCcSAoGvln1hgNNPIamBgYxlwGCsEt4ZrDoNYHZurMgH4dOV6dBPPsxlclNqNk2Awx2Kamr11zsYU59U7Dg5yLirypKNp7Z1znbEi927wvsRcZiJwQ0P48iwThJq2tmR2U7Xm/KJKCxHgOdDdePsWGnJ3rT8YnZ4+nzE+XDB0+MR18MWeNyEQEPH+LhyDDIUR87UIOFG6lXMp1ydAWEv5RqUbLFUbq2noqL7VGCm6J10C4C85Tsx8dyY2Fgk/MZqr5wQJAA3X/6D034ZXgrDShdscQnCS/VU0JnhBQ+8gDF+/DCNzfDiIMIArmm5ARxkqIDk42rqoZryTlxXAXaGGh5aCiHGOzMvbPqKCgDm5FYSh5q6qdA3PYzZzY6EdGYq4dLQ/hDU1AJc3NUbp898Q30ECgBg9yOqpseTHXFttkCzCQt6DjIHtox/xiEgNl6El65ops42d6DhPLszfCzPA4eYEGh8NXzXOOIZT+aI3e9nPB2OeGqOrsf05sEDjBzz/aaBJzurUDOdbMkew4NYwMFHujMcZPg0EBYnHGhyHJnSOnDR21RgpugNek8YyRG/XdfARQIMuTKs80gZYlLCTZ9OX3H87KCFaisdmCvj+186T4UcFXBTodb1qDuEiataqMmG7gxAtYX8QG6MKzSrOXzBsSfI3zAjrB0XYaUZYOZx+Bh4EWedz1+bCp+mTigvdouq6VEZ78zIMBQBFw+B0bFRtwZ0vAQAAObv8XBbZXpUv3e4HHZ4ejzipZkKfA4uDcIG5zjsSADQpoUz86npUTf9Ig9qh8sCYnZzeLGbwDKEGFljbYDBBbsZhp9wRFX32P7jjOfDEc/N1MrzI5ZAo8HY3TCjTNN3ZrChzi+ptWHelQJ1syDF7ysJNIAON8WlKbpPBWaK7tBfCTE568o2ZQhqpv1WEzyh5My0OH1x3RTwatfh8BwATcUKtXrsULWTK0P5MvTcNn78AMAYAODuzNzyCmr08z/D5gkQyJnpUC8SUQ2DGe7IYDAOYoaHZQ0hKPPz6W3w0kw5LFMVZd5qMA9D9SyvJwSZWBs14+QNOTkI6gQ0dKjrHtU/elwOZ1dDiJJoeU6JTJYlmOGDVnMoSAT24SUOMkc8oZ6SsjnEhGOfHC2hhjeWeMZ2DlHS8IQj6n3vnKjmiGtzdB1qSpB5ZPtN10zCizbI66tdewLCeTu8Q0/uzGgbSVFTgZWib6MCM0U36tZ8ljWlkgBzvi/dGLB5GsiVQboabgA1ep6Mg5pn4ci4nBkq3DzIXF2ujCxMmCsTc2dCKOHhpg7V9K9m7dRw50a2yEs5MLMrQ1WDYwUdvyxy2ewGPHiwaSq0cyvCW9RNj219xojLnOTLa15RyAlgib7wtZ54jadqAjTueLjCf4fzfoduX+Fy2OH8PPUo3jzoVZ01mIGybL4/XrFpHFDsps4+twhDTeTQuLZyeIjxEnSqKfAyWOaO5DLfN7xV5Mp0qH7v8Nx0eH48hgnQ2iABJgY02nWV0zw0x/OOQFAjgYRCTvTiAIS1p7Rq24AONgV4im5XgZmiG5QDMrfeUvfeghxeAB1ouDMz/U6s1soJQbhpc3rCYf8UhJfiXRQ8zYVchQ52HFG1V9QdXHXsbhpIPG+Sjc0AkDvj2n4Ji0GClZ79c8m/Hmqqqd2ZuT8nO0YcGcQLPCSm1XyTDdBscLUNrs0VfdNjPBiMextADBXjHeoFxNhgPkwedr2H76a+qtzxEUD2qHGuL6jqHhcq9Jtm6bqQQwMEt8Nier43OuwOZ+xM6JqE4cXnReiJT8cAhvccTh2JVlMP4lQjjo61Rr90aZqHcL/5fZUDNEA+zEgInMNVqWcBgc1G+cEUxBQV3a8CM0WZSj28vudtJPdDa1uGP30pZ+Zh3ZGZoeY6V8MmkOEdSfLk3yBEMJ5hhhFV+wI7MpDhBQk5MgN8IjBcIrCFd2eoIbsQYuqFO9OjmsJONSp0M8jMEGTGINQEwIeWqLDScipib/RaroYAm5fDBs+Dcb/5ObxavL0amufLfVKxS/6lYBWNt7jggu3U2/Z5HtfocN7vXKH/eHRJwla0TYPEmB8buTJ1P1Wz74Lr7K77JYAb3icXAQ0BSwxqRpjZ2dPdGe/SGDPC/D7iyQ5ordLuDr9ua0AD6ADDx2sO0JxPw6XdNFrbM1Iyb4a2XYCnKF8FZopWlGrn5dbvpBSzm1OSJRGNNXcGy4RPzaVpgM3hgsp0QQiBFyxab9ZzoTyI9FnarVEs44PIxw2/Hhb2lFjL82i6CWR6CTIiN+VFujI0PEMvh7RCUOabaOMBwNDMDe8S0BiMU97PMIMLHSOvCUXhJJd3chFLqgliKM26wnlq/bhCD2PGKTRzxLMVoRl+PeT1IXFXZs6Jucy11bZTGEn2wUVODV++7JbTV0mnZVtcFkEo6VCRDIawqwcr/j749dIcGbogGryATXPQpcRqzQkKgOaKMLzEXzDWFKvlVICmKF8FZooU5QJMqluB2DZjDyftYZYS749pE1kmSrDU2+ZiVa0Jfp9o280hHVcEn7EFDNDtqZG8EaZ+gRmATQeghk8AJoCxwKt1EDNa17XBaA0GExZ8UstcGtkCbaJ9GenKPCN0aNbyLQhYZO0gCTMAAAc01o4w+2F2kyiJmc4pPy5eM4jCSAQ5zpkxM8RQUu12gk4CzSccYffOkXqyR982TY4Or64qdh2Gl/xwCRyaGMgcx6fpOqb6HHfHTrXS+gnUaMyXURhxh7ML4Q0W18H6/p4on4mu3QyVyoCVMW0jFZojtbSAtwhM4SWL22CkAE3R/SowU8SUAzGbyPLY92Ovwhq43Prgkjkz2j7YYKTmS8Te2pkkPvAgwAW7wEHpTeUcgtrlz/TNCDM4sLGjd2AIYNy0gxj3W3Yu9LhCaPFVthfukPgcVEdoMEtgkYMGM3xagosGNfzS2gZn6pm77qeAWD8Fyrq59hIPwoTVn2ldV9XZtdHi81F4mIlyTOarVI8wX0bXNo09YqFF+yrU0i85Md6FkWEm2scYyGyfrxjtVQDqEmwAzB2O8vEwjbeTC1UxqBmNwXgweBoMXoa933m6TimQuQVmLMJOLYNzx9d/wLJ7A5kzE5P8vABN0X0qMFM0aQ1ENIi5BWwkxLylkSxuY/Nt8/2ih+kGrgYG0hCzAjIkF+YwUwFT48z6fAa4azK4/BUz5a3Uk4MyuvU4uMjf4QUbNYInJatsn6dWjTVXZiEtjKDlz8ixhJk1Z2Za58Xu0DUdzvWO5fz0c+FOx+O7aehnBwQAtkquCaEPhaDmMNMcjut9ztCUawLojQfyhgPDpN/LnB8j2xfS+uMKOhT9E9jUAOwLXu0LBnOd4ZU6FvUwU8+OH42dC3OZx1RRf0bU2gHN82CAoYlDDJRlMYDh0zy0pIEMX/8ZcH9j9DdHYCP/sDSwoc9Lz9pFb1OBmSLkg4yEmBzHRkvYTT24Ym9hch36rhZiUvYjJ7w0DbxDRpIvSO1ceHJ3RsKE1vmgwQhrQtjQQMX9XlgTiKRV15YhJ96n0rw9ni8jXRcCGj4vYYamc52ZYNkD2ucd6sb16t1PGKbVdKJ8JJ53ws+HzDsBEPRbxcd0HQDAfJ76gxrDNndoPAwudFM1PWS7LzxJl9waXkVbAxl8hUv+NsCDdWCzscCrfUHVtugb59jAYA6b8RATDz0tr/KUrbW3GAYzpcI0aWjR5lOfaRCjKcjHke7MBuHGZXGj/U1f2bqpz4uKQhWY+eUVc1Dk5zGQ4bkpcntyeepBlXpISSdmEL8Ty5lh+yDNm0xXhou3tOvacxlQTTWMAAc8Wg4LsKx+TNKcFNc6bhiKkJ/7Lg/CgUPMIu+HN5CnhZhkuEmb19wYuQxsOS1ranRthaquZ3dLHhs5KlvWrguJu1Z8DGB2xywb+Lny59QCBnOoZoDBWLuLP44G42CwM76tIOqtXEKN2ngiBxmCGQuXG9XAg00DbAxgxxcM5gU4nNGZKggtdajnXtK3uMzwPIeZ+LmjhGCCVXmdgdtBRjNV5OcyHAUgdGeUnLV5IxDLY8+CEnIqyleBmSKmHJDRoGYtBCVhg5IEcx5KHFz4Q07uT+p32SprQ3QvfKijg+9M8cIKzrlPJCXk5H4+/MyN/We0PHwD9w4E9V/E+zHyAQhfiPPfWQINlkDzjGXnjDHgibkxfBnY9Dw8oG3Inaln/KJCmZ8rcmZ2OM/nI6WwRpA/j/KcxNp7meHGGBVeqBaTlhBMeTPb5ysevsKBDIeZGg5k6ml+hIOa0UHNaK/Y7S/UoswcWtKSgV3tp3Nw3ka4cBPgQmiL0JkGOfI+iMGNlLYeD23NtZvoj4n/fUp3RkJN7EdjQAMUqCniKjDzSytVbXINZFKAE/u+fOO65S1LQs1aGEzskwwp8WVBZEr2IOzFIYOcGV6AkjMDLMNNtEwm6crPaFnMleHfNdB7147mzWggE8udiQ2pfJkkzAA4OHemm0JNvG8mDzLeETniKTiX4TnwBdwOO+ZOxc+BzLuR/SSNsKIK9hJquBsz58382WLzX3iIkTBD54KDzTRuLDDa8xR+86El6nOdHBoCGjoOf0wuVIXDNK+Ez9y8nZfPrUADWHRpoUWFJMQcsISZlvZINIeghn8H6JAzsO/kPBeKS1PkVWCmaFIsVETzEhRy8lbkvPbASoHNINbjy+S+ruTxaAAjAYciUqLXaO+OuBZ3CWT6qal9v45sCG7pugBwrQQvAITCUM6LoG3KnBq+zXGxjaUrRPJv6lgCjXRmJNTw+VyYaeA7RmzgCsD2AX1bY/wcuiMkAhneRUTSzZqSqc/msnBiNIU9V9eihpSd2u/pIHNkZGN5HnQu2H1lIMNdmf/Ch5g4xJCLxXKLdvYFXX2eQ0yUAO3dmnPgyuwUqIHxYDPAuN8S6/B8oXFq1JDcnBlwCG78SUs7Mvx4yNELXBmLsLo22DJtWgOalHNTgKbIqcBMEeIgo4WdNJDJzau51Y2RkvATCzGJfVSAJQ41lGNBQBG+BVOQp6fSAj6XhgrmMGdDFsTj3EovtXJL0yNzJ6S0/JIwX2b5mwtxiNEcmmcs4YVPy7yZVLjpEb6jxwNmYHo5VOi7Cl1dB8fEG9LjQFOPrmE9M4xB9XbAtZqMAdju/4T5rB/zsvaT78Wb2qqhUJbBMLfsGzoyS5dmhwt23RkNwQsPMRHcSIjh+SiszaGHGth9bdHvfYiJh5kox8gdj3BlFpc4XKYBz2im2lCwc64Qh5trW2GuISUBhg+tMv0MhM8J6mmbuzESbmLgkgs0RUUFZn5BxRJ+Y86GjYzlMrmNGODQw2lITMvQVAqAtFDZEE4Pm3jhTQ/jRxpv8GyPMHYEasyw4FqZ7eaihgpd3mqtlvTrzsAyvETboWnKt6kYyMiwiCbLfsuHWXy4pYLrzfqTHfGihtUQfwpoSaPavMyliSUQDwDaDbq2Ql9XwRmkYx3E8Q7GzA4MF4EMRgc3VIuJqmRTragtzqDOHLmjRueM3LVuWuavZ3h9gvZraC+11p4tnCNDAFOLabnsM3DdA0/7Q5BSfMZunuZdWPbBOaOQVJ2GmO8hq81ozwGwZVu2bA1WZGgZCKGHr1f0K6rAzC+jVPXrlLOigcw2slx+h7alPWyu0IFmmLZP07GHHP/NmKjX3k1YuMoQCwcaC8A2eLJHmC8jzFSVOgYx3E2ItcQrazK5rgd8I/1UkHOQAcKaO7yKckq89s48TP0zBTCjQcwtTwN5SQhoaDrm/gxA39YYPocJzuRASKgxyrXnIIPB9YFlx3Huz4rDCLVfU0/N8xkWCuynZT53Z5jBlHf2KMOBC+fLYG7ReYYUcmQ4wOyX09ffgKfPzZyBQ94Pn/Z9cXOoqYOcn/eAF55A/HbJGk2ShkkcaHIlKwLw7ZXk4F9VBWZ+CeUm+sbCQxrAbMU030Ysx0YDlyvSEMPHQPohJduvoW0hXrhqUGOBF7vHkx1dx5MGAcSQ48EhhmAH0NuY4fPUbaKvUBz6KVz8jZt3XMjFa+zQmA8GA4wdcLWvgH1YAo3m1vgdYGXFK+YGCDW+JGdGc2XY/Mtg0I81elMtCmS1JpdxYSbAh5iCfbNwvZTve4ZCA7tOrmFBfj4pL4lDjYGZ+2Na5iKFVd8XkEWODHdmCGT2CGFmmr/ugcthw9yYLYOabeDQUFtGhNQ0zc9fStHQIzCHmPx5vRNo5vvmAeHfP//7hZgG/N99TihaVgTg2ytQ8yurwMxPr5Qjw+djUMNBRgKMZZ+lnBpKKKRlr1gCDY05vPh2RvTjkr/FRQ+2q/u94SENMTz3Y9rtK37D42CAL0Bl/Fu6BjE0rcELDy257/sm+SkJlUNLrOCJQQyfXv7uBDV2dPlA1JP0eygWFdDAMZiv0bcVhr2ZQyUEcnyajllzZ+ZDn9wZM/h2aup5oJ6zXHvNgHdmCAzpvFMuE7ltqU5FgyRjLWTXTPslQebgp9s90DcbPBlqQ9iPLzPIOLghJ8Y7MrR31P1Bvbg35D1EqeWL0xgDF5kvow2aAiNVJu9zpf62cxSDGrljJUH4V1GBmZ9aayCzwfJpHHNVJLxsoUMOhxe2WXrODJg+JxiRYHPBsvYSlxYnl8cl150efDK8RPvFp4VesMcjgKrpYO2IqnE9M3uHpgNP5uVv9bSOhJndVCuln6reAmlQAfK6KdBqNs2DnZJLqKflmDuTOo0zZCq1Xfj3YyG9eXjAMIQdRobhJQ8acyK2NT5HRebyGJ83w3NnqtnHcM4M5cZINwbwcFNHQky8qwSDMczjkeevnvaL3BkCmT3wugfO+084185Z4XkxfExwQ+0Md6imse89nOfOcBh2KephzbpcvdzjyqiliJZLx3VLMq90beR21hKFC9D8Ciow80spdrljybwWwA7+KS0Bhs+LQk4rJNW3uwf2fe7I0EPrgmWi4NrxDOxzAqVNmKBKYw4yWmE+AC/DHm2zm5yNEZumh7ED6sYl11bGJ//G4KZChwvGwIkZk2+nXeKzPAXBK0oCptOSgpfpuJdv4LzAeFiuT5IQo0z3bY3us+/5WkKN9JbUxv94jaABqLsedd1PntfAXBnnucTcGA43YfLvEmK4S2T4MfPwEoEMr/lThyBDOTBKW8JB0i/NkwMjQYaqmms5MwQ1UadvFEnDQdsz6lfikn/nSZABlrkytwAHBxgtNFWA5ldUgZmfVinnIpbgyz/nn3FokSCzxQJieLsjstDkIYeBzQdQAziI2bFpevhxK0A7TilyfKZQE/0mhxoCGk30c83DFKLZ4GobXJtXtAJuyLkhuAlzbPybvpYjQpJhDK0gkp/rA+9RO5IE7DeYIXLQ5JcUlyYBMTT90lLbLnXgyPC9X5UAYzO8wNQ+1HTBOIec+qnquztnoRvD82cIgMidISiSjfIF4hw+Ym7jJXBmBMg8ixpLPD+GL6cEYGpQj0JKHGQ61DOYGOPaiDYYQDXkUkBD4h1wxs7v7eLVrzWRo0svHPSdnK5N7gGaop9ZBWZ+CWkgE1tPVr/mg4SYCWQktEiYofkBYeNaA1seQA2BE3cuLmKjsWPhbVjQQ5KAhrkzBDHaKdJga3FsDG6aBlcLwL4CTYdN06NuuiAkxd+ltQKb13wyrHCVBZGaP8K2wSGGgCpMAmbHGpvmWvycXBBxadaApt2g7yqMNW8DxodMfC/R4+SZMJEjw6c755S4Tia3LNTkqmdToa+5MXM1cJgZhHj+jd8bFgwbFDjgUAN4kKldjgwHmRi86FWzt0Gi72J6aismkEnfK8Hlyk3+lWCT3DwlAX/LIkYDGi65rLgzP7MKzPzSymlHRoaYFJCRCbSxZcCyxosM+wxwDkrw2xxOtNdEeRvztmk41GzC3+XDM5biP6c5TQsH6gFoGgc3zRJsKIlYq3lCb/zU15Jbtuz92h9xPKcmlgTsPhQ1mrTTp4q/Pet75PSwTPjVgGZwYY2urucwk+bQAGE+0YOEmClnBs4kgx1H1IZcmW4KGKVrM3G4qVhoKuZ6Ldq9oVpMlPRLmkJM1z1w3jeiejUHmAODmO287Dy1N/yEo+pc8UbvuraaW642dnROjaEro3ftIOVbiX5YARXEPw82u+bMaF+Orc/zZbTpmLNTHJpfRQVmfjpxtyIWWpLrS5DheTAELhJoGMgckIYY7szQMg1kAMWhAXxBqr3pxW5hWp+cGcrDEe6MlGQmKoRjoTM5HDB3rOjApsa1qbBpeuwOBmNtF5a/rE7snRnyZvQet4F44jCFtrq5gO5RNz3apgOaZtmaa4PQOaOx9VtOt0cELBwaTeytflhJNuVAMxiDCle8WuAhkk70MEwtBRuqjcQTeKeWhEWIieCmA1DBtSfEHRipDpUL2ZkBQI/RulaJNxbOiRnZ+Deg/UwN4nlgkc7ME454xGkBN8/MqZHwAmDRJUEPBzIU8nTT03Ljj6cf67nV366tfXcGbY251p8E0LVwk4T8FgjvD/rjl04Kb45Ba1uKfycGLDHQWWtVuOhnUoGZn0KpWksa3NAyWQtpx+YlwPyGKMhIgIk5NPSApF3hAMPfaGlZkEdDb3kUbsqRcGXmh+iD/23tK/yrB/hCPgdmaN15eAAODa6DwdNgUDUd8Nn9hFqFepEP47s74FAjQy8y74a2x6spV3WPTdPj2jS+i4EDwkKLjpeOg+ZbDpdAEmA01yc2XhzHMo9odmjsJ6B7oYOMboeH26jBPNpGjxo1+hle3P/d9Dvh9eC/76CwmpdbjOjrGqaertE4Bl0uANQY3jGS6HtgYaQdHnGKujWXbrcAFwDL/pTsK14AXKdcrk8z0LgdIudmGAz6tg6/37LOJikxntdA08JLmlPJNQONrLUIhPByxRJotPamJNSQNLjRlhf9zCow8+GVAzLafMqJkTCzQxJkbnFm6OclTHCgCV7OJMjIXBou7bWRNkTuDNzxcLCSm9AK9Ri8yM8kzMwwtMHLwaIdjHtr3of9KmmJpVQbaoTrRZumKXzidtcX/OG2XGu2lAdC/R31hwp/Pm+BZhOCDC+w+LFzt2Y12ZqdcjlgOZYdekr5atr9PJ4O1G+nC77gqm/XPpGaQFBvfDBMCO5RBzlLbh2PhtQNAs9jmltcNqOrIl57Z0yDFy3xl7cts8iZ+fOA9nkHtZfr6Zh9KHT624TL5XqxwIt9dcX5BDjuOwZoN0vHRcJMCmi4YjDD9y9ojgEIE8o5wFygg82FDhJLd4cDEr9HB2Xdop9VBWY+tG4FGTmt1Vq6A2So4NYKeOnMaCBDasW680NQlozSRgaWD6qrmOZQJPJn+NdlIZ7rysgk5wBkaNq5NOfpjdnWYX4MKUwAtlNtmrATy3p2EyxkzR8PQXbZeFzdY3O44Npulu2/yGOXQ+y6SWnwEnFk+HGHwGGC5eREDSaCVFM57RrP4w5Wjw7Uu7l3YACAesnuJrcGwJTwO0zrOYCiNoH8T4UHEUu0jYWUONxcgiTgg58fd3h6POL6vAWeGXgA8Wlgea4F4ATf0SCGpmMgIw+V3//SYbXK9xZwM+3bDDc8140ghv52L2yjWtFlxTbW5ot+JhWY+bBKVb2Wy2T1aw4wFFqS7cfQZ6L6tQYytzoza6Jn1fwdepBJVyb2UJKQQ66OlAAavros2NdghkOYzEEhqJnGL9jjCXBdJUzVeMMOI30fTuQMVLMF4QMkBsMiZ4YcCXJw3HZ843E1OtRNh2tzXbozsmBLwUzqyRFzZbRpRT7x1wZJwKM1AF78NiK5M9RPE+UM1dN55A6Mh0HKmamDmkt0DUZ2Rnt23v2+WjYdwpgHmDCkFDowvpo2rX/5usX5eYeXx713SIDbYCYFkjGY0dwZnj/Df5O2x7mCA40EmbV9H2R7UzzP7az8qCa+4TVHpgDNz6YCMz+F5GWMuTBg0zFHRgOZ6bvchclxZuSynDd77ojMDzvuqvAHniZuOdN3MR3HAEwt7vp1t+5B+ox4IX4LzLTsnDCACcYWeLG72aFxyZnLvJkRy84nnbxTozWY5nNGDGTjcVuc0e1rdG3t3RkCGl6QHaCfjxxp5wZiGi5ZVYpXk5bLAGC0U7ItgYwJvuxaAh78OSWHahDnSSYA82kZ7vPVuumeqoL95H1m8eXPWtgo4sqcscWl2+H8vHNuzOMmDPVIkNDGa+AYgxltWcq145KPnsXfrjItx4EzJPPkyI2hlxGalo3uSbdGOjAavBSg+ZlUYOZDKpW7sBHTKZDRGsKLgIxWwEtnZi3UJAs3roGtO8j1HpQv5b6haVBzFstYLSf+NV6Q58KMhBgJMvSz9gFXu8XZDjCfl9V/gbC3bE01ywUZWUE8woCHVfrJldnhMrsSddPhemiBtgnfvDncNGI6BjNaAScLUys/f40eF0kmAlONpmA7ilzjeT7URM6M97R8qIknAGNK8OUwI6FKgousMs17/ubho1i+zAJingXEaM4Mn5YwI8ElBTN8Ws7z+yEXZiyW25Lj2GcUYlahhruq9IOyIU0tb0Zzc1LPjaKPrAIzH178EsaqX9My2Y4Mz4+hgYOM9V+VAKPNp0BGOjPyeaI9lGkY6NhoQeyNS+bJ8M/4D3KIGeCrh07hNHqA0z7HgCwFMxrQLHZjg9bu1IRgt6oOMnbKpekQhmJIobNA9XD8mNyZYTBo29rVZCFokWMJsPIY+Onk5YUKMGJZRNJtksf4aoEH7s6wbT4MAGoEoSYeppP5Mk4caJw8UIYOjp2SsXkjf7xVXloed2aYK9MdQ4jh8MJhhkM2H8tpfi5iYCO3k5qWbp32+3y72m9o+x0DMf73Rn+DkLXo+Msab0hzwzYkX2CKO/MrqMDMh5YGMnxeAgwHGV66ELwQyNBylvArAeYg5jV40eBG04CwoOQP0PnZxPedH3+MioDlQ4rvANWOoKqe/GEonBrgPpiRQEPzQcG+TAiONZRH8jk1y3ZmZCKxgUHPXBk/PqNvKvSHM17avf42rgGN5ubz06vB6J1PGXI+vJtiw+rZ9Jt8X+opCXhwfWaF+UZhvowGNFpjchymemABLr7PpLDvJN4IXpAIPB7D5F4JLjGY4ccZm19zZ/h31hyU2MB/S5uO7VsKxuiek/fN7NIQ0FAejbZj8u+9NJ73K6nAzIeTFmLS2pJZAxkeYpKhpo3fhAYvsZwSWeBLoKGCURZ42jJAfwgHx3jPGxU90Hg7FnLb9GCkc8XapUnBjHwhlJvlD2g2vDzv0DUdqroWjb05D6Cfwh9+id6CsP+pET78ZLHDGR0q7KZm/clRGGuD8WDwPBhgaEKIlGBD146fQm1ag191cDWreGvF2rGExzj4nrPl7w9w+TOdu2qjfXF9ZZmefd9hSTdlXRsMc3Vrmh7FuXPjsDXiASbowZr6TpLzy36Wtnj+esTz4xF4boBHhLWGYtPSmdHOPSnmxmj3ZI5jQuOUGyPnU7+lbR9Id/g6A80OumRtRdJGrBNTcWd+BhWY+VDSoCXWlkysHRlZ9VpWv1ZAJhdepOQDLDa0Ypx6iKqSgDcgPC8pl0pCHj8/okG4mBuTcmlkCE5dp3OhJvhQE+9TiXdYKftN9kccz60ht4bX0pnzc/auc8xHe8KL3S+PN6ZUwXdYH5qDS3ulQIybfsYO57lNnC3O2E0dAGxxdr1id8DDVwBf4e4XrUbTADQDUHdXVPUVfbNBZWrWXUE/w4drh6ZDP03zHBgK45HoCvWoQdW1CVzO2KFDNUFOFfR67fbe58agrZewwu99zQlJuRo0rQF1DDK0v6vY35r8+479RmycA0sNQjdKbmMOc/GQk4QY6c4UJ+ZXUoGZD6M1kJFQI0FmK5YRzBDIHBFUv5ZhpVTBvKYYsOTa2UmtWSK5ICNbQxY9gecMKYCJAaHF3FKr7MyQd3RIiay8ijWv6VQh3t6J32ZkMCPMP0Y82hFX+9uy4MoJSfBlB7hq55Hh0+krjp9d4GXLYGU5vsxwU3c9dl9fHMhQ4f8VDmZoH6i/pumWeBiXUNOZOji/HGrI/aJl1FIw4EGGziS13iNBZq6ZxKapFd/z8w4vbQU8PyxBRgIN1y0OChD+SdD8PUq5LZp7qj0XaF9i9wtNE8BQqOlZ+d3ZoeE1G7mle8uBFdD52VRg5kMoFVqSIKM5MtKJ4SEl0Y5MzIlJFMbqs0R7VvCH9S1wE92glgisPXVjVdFjgJMBMtrxpwAmCkGvMHZEVfdziCno6XqGGN7zdujMcLclHLtzxoGID1Rd27k+A/C7i3xc8ZtyrplShROwdGJOfGhxPHE3hqYvszvD58mRCUCGIKZD2EGogBl0AGqXLNzUulPjA3du3LHG8QhypDvTzShZzdO8mvVlRjKlyjUl+vJQknQp5TIgfb5j8CMVe9rHQIVPa5CSM/B90u4bGmh9mR8E8b1nwD/vKN+Nml3gz8KUtJBSCTV9dBWY+eEl/zjlk2YtnKJ1EilhRkn0jUFN6qGl3U3SCo8BTAxuAsUeNlR6aUCjnQsNZBjQ8c0mQUQZYucrkTtSN10QYuJtzlArtmF4yTszHFyWYaRhOgzZ35MPaVH/TfR7AdBo1y42zedPYpjB5hWH0xOOtQOZwwQ02wlqCAEozBQ4MtKNkWGmEXPv2fPtQIVj49Z76GJQ4wJQF5G7Qy31YP4JOyX+mvkKyJ6wCWQIbPqu0qtdE8isQT2QPu9yOkfyETJg+bfL51Nh0rV5vn8xkBnYdzVHhovOVfB3zEEm1tVJ0c+uAjM/tGJvGVQAk6xYxuclyPAaSzu/egpgtFyZtXwZQH9j1AAm5chEH9IpgJHrxUCG5xLdEFZay4FZW8aGeIjJuy9hgGOYAadChzB8NGAZThoCZ6aCB6d6hqQ+KMRnoBl+C6+bvIZynoaIK7M5PWG7v8zwssMlcGF2QXDmjF131h0ZOU9lGfVWbeAhZkoKnj/f+/CTGa4wzQhT6/1jae3KEND0Aisd1IQgM7fk+7xz1d8lxOTAzBpArrk0EMvliwfdi0NkOnYv545pe2uD9ixJ3WNzDSdeG1FzZm6hvKKPrAIzP6w0kJGXS7oLtEwLIyUSfSXEaFCTemBJac+PHFcm9iAHsF4bgdahHeJvaxJkbsiPudWZkS5M0pm5omo6F2ZiwCFzXDRXhpZTGMoE63dRwCEA4o5MNa3vL5UBfgf+NRi8DPs84OTDCYor48NLfOCuDM+h2eGSBzJfxT5YOICx0+c0bQHsp3Wm8NOmAez4AjO0sI3LHaIzRTXIANl9gbtCA8wMNReGYXMNJuogsq19jgyBjObMaH8L/oKE06t/K4nvA+G9ykGCfy7XzX3BCeanv1neOWbqWOWzhMMx/w7td/A3zXdYVsmOnYQCOj+TCsx8GOVcKpnUyoeMVn0PkWmtYE85M7EHK394rbkywQM61lqsfFBJAOTu1A0gkwKYtc9ucmbGqfpwF7gxvGo2gYaN5M5wIKEeorkb4ysMe/Ch9bY4o0KHHXbQ2rYZvxj8W1bbXnNluDNzouGKw+kJO0M5Ms8zvFANJhpzVyYLZOg+4ueVAwzY/IA55IQ9gNE1stfU3qUZa3e2qYPKJciYwJU5sxDT3L/Snwe0j0c9P0ZO5/wdpM61tl5qDCzv5ZTkfXzA8vkgAP1T06NqOtSNS1AfB4NhMOjbGi+DAQbjAKdlgKO5MnxoxHh2Z/jfMoWZNmwj/EBKTszPrAIzH045tXP4k5235hsBmRjE0HSOGwHEIUY+kGNQI9/U1Bcn7YEUu435MWsODVMqdJTjxNzqzNh4iMkn/vagqsMeRkKXxoONh5gQYHiYaZhdm4qt9yTCTLOPYyzGLwZ/Dr8Dwyb9Zq05MxPUNKcnHPY+6VeFlyD7xLkyC3jRQIbCTNR7A4ELXX4ON+TK7JfTGziXZrRndKZCPYWOJOSRI+MTgKupRtNU/frr1oHMYwRk+PAszhuUcxn++NugRkIfsLxvIb4HLO9hgtXgeeEhZne4oDIetMd6amTws0XfVRgHg66tMQ7GwU1bY36piB0T3WuNmFeff7Tz2suO+lBhnxfg+agqMPNDKpX0G1tfc2S0WkzQQUaDmAPi4CKXpUDmlrfL2IM82GiO5PmQDk3kON7DmdEgZrGuCzFVdVjTiKCmYtDBXRkCmjpwaChh1kNMPXao2ivqDjjve5g6dGnou9y14Y3yzdO1wfjF4Hn4gkWoIAYzVNidXDXs3eEy58fwwfclLcBmPC8Tfp/hazBxkKEwE4EMXVM+Jphp4Gs87ZfTDyOwNVd0n6kPqx4XSowGMLAr5M9ePbU1U+PS7aYG8TYu6UgDGDmk/k74NL/1Y4W9tr4GONyR4RAjGUC7tw9iaF7x6XAOIGaHC+qp2wxqpHBuNbm26OsK3b7GOBr0bYVhOKPF3wBE7q8WPtTUwOdDtbSTWg4h4MHEJqZveaYU/cgqMPPDi1+iDRvTH3EsH4TghYeXkAcxcprvhhwD6w9a+jxmscfsdvVcaLlEWniJ1ufOzCR6mMuxBiwqiCjLaDk/r8FDn6ZfsTk94RiEXZYFO88j8YmyVPPHF/7kD+xwCQDmgQr7DtjXL9jvn9Hun3HeN7hghx3OgR8iE425xr3B8MWgHf6e56xNIIPDFbvDGTvjf2nLnJgdLqxyM3OU2qsHl1jB/FbdsJ0BYQo2bw2YFLTKrG075Whq4uWsRVhlOfa3t9zxcJqcGRrHfjfnnj4BOLTYND12hzO29XnqzDSsjWYwYIvLVF/MIfTcxo+pYPYj+o51q8Hhn/+dyX2cJWogFv2yKjDzw0kms8nlls1Lt0Grhs2qX2s2cQpiOMxouyQfqjFrmM/H3k5XQUZaxNr50M7Pg18sjysGNRrM5Lo1UYhp0RzOqJsex5onwIaOBVVZDtteoWrLIdTscMFuPC8BhjsXU7Ir9kCzB5p9i+u+xfHwhCdznIFmi3MAMtSiMBXiw2eDfw3WhQbaCnPOAwdSels+uWPeHC5z7SVeedlBjPM2aDoIjfH7YGRjTbLhY82V0dZNrCfrg+UoqA0WgxZ5zwBLyJDAQctmJ4JtT1tXSi6LgUzKZVRAxjluZ1RNj52Z2gMSMLOdeqcngOE42LP2fFADxo54SYFeKamKVlRukR9WMZDRIEYDGT7N2pGRBW0MYmg+5w5JQYyczoEZQDyE6e1L7oysjsnghaQBTGyQ8JICmdiyAGKu2Bwu7qFf90EoxXc96BuPOzAXZgEtAmp24xnb5ys2FIaRoRg+PcEMDZsDsNlfcdz/G+f9I871Do+R9jko+NWjwvi7nUMDXVvj2lbAYICW5YcweNsdeOsrl6CRPu4q+XCXc4c2rh+BFbhll5wX0BrEWLFcW4eO941PxE92RNB7lAYzBCZ8+RrQtBnfi4mfRw0S1qBrATXu2m73viq97ILChZkuMBhwwW5yZXp0Uy0x3lghANRNh2tzBZqN/rfF9zdLpVuDX00FZn4opUIoEm60hF8eUtkhWXMp5sLIZfwtEBnTGtTIcFLKsUmGmOS8UiOJPpIP4jX3ScJLyrmJQU2DOYeAHvaUO8Cb5pet324FwKw5MdvnKzb/RTwhVibOCpjBwY0f9sB+70JQ9e8dwkiJbLpv6pfI1Oj2Fbb7C/quQtdWrpZKW3moObyimUIPO8WVIbDZTW/z1QwyI+qu9/cEAQ3dW9Kd4Y3k8VskljsTKxQTcCOlhZgW0uCAxhxIGqTdGA4p/HsSOnKAhrS2Tgxk5pcgVzPtsOftAy1hhkKf1DRAhTpolDBMOLeomh6fmh4vdrP8u+L79mYVsPlZVWDmhxRdllRoic/LGjt8QLrTv5RTcWC7pEGGBjISXHKnV8NMVA2TH3ekfRgOH4fMsQYyEmoWAPMK2AGfmh7GjqibTrXdt1hCjXRiJNxIFydoQO4rnPOyVmVZg5kDQrCZht+6K/D//O9coPP2VMg/oSTOucfoukJV1+ibHl1buXZVbD3BHIELz5EJwcbn+5znBvzM8BLmyvBQE5csxHNBxop15PpAAG886XcNZMxUO212ZmKukQSZHKDhIKM5KPx3YuV1Cno0SJfwf/ChQ9nlBHdjeA9VgOtO44xd4MZcBJmezdadO/m3xs9bUVFC5Tb54SXBhi/XwkwENyJPZvGGhbwWf0mpMNBamEk6L7HxWkgB1MncK4JcGM09kccrj11+lhxCaDF2cO3DNL3rpHF+++zVN9V6atOFHvhrEBOEk2IQwwceVtLmCWYiIIParfsbHNCMxvcMTU330/QZu6kDxmoKH/To6wrG7mDtiPPzGCT9cjdGBxsPNRU61LyqtQYyfNpOn3MooeWpcWJ6tJ/wZtlXwD4swzo0TS4LXx4DGmAJMmsujPb52vo01ob5b8WHDjWY0RwaCjOFDTjuFrtQo3ehJjp3BWiKblS5RX44xeCFPtOqG3OI2bHPEX3Dyu5Q0iJ88JK0h2MsdLQGM3w6S0pSr3ybtIi7USmgm+fDxr9cFWoPLbxlXd5gHdnr/MGuPeTDkJKsrfSE3dcWjYQW7rjElhHA5MJMPS2fXpR/M1cM/8//sgbiqOqxS9mt0M3L6I37jC2MGXHe72DsiG0dd2M8vDCAoUrOXe+TmCU4a+4MkIYYPs0dmAyoiSk3IVjdHp+XzkwKaJpwM/NnuYATg0G+TxJolGcBhQ6XNezi4Sb6O5Hizt8I4+6FpgeaDmia5bFJJY85+0FS9BOpwMwPoxi8yM/or1i2KcMBR1TDXguv5DozLZvmDxJZ8GhA84wluMjpW5QKCWku1CkxP58PBzBUS4Ma/tpNNX1kX0hyntvsKajhhcAiT+Zri+ZP6HAi3Rju1nSIA85+OsYYyOzhr60B/m5b9P/zr6DSNIWaKnSTI9MFb9z91FbNud4m3RhK/iW44e3emOHFQwtPAl67D2KhHBIl/cqaTRGQ0cJLvGbTIIBm/tQ41+5qB8Aqf8+xp20qvCSX8RCVBmap8xULRcWcmeBvqcXuoLmJ3pUJw00EM76G3KLLDHiosdP5+yRrNRUVZarcLj+EYu2k8GkLHWpkaGmlu4K1EJN8iJFaeHs8ZmNroSYCGe7MaMMtWjxoE9MnLAEmGKcb/eKt7K4BDQFMCDJLqOHtx+xwxnF88jWT/osQXG6BFrHs9Svw9BXYNsBGQgzPoanZdZ0K+d/rP9F9DpvX61DPLsoZu+A88BwIXtiFSb99ADeUL1OhQz1OISbtHonlzADxQh3QE3o1iOHVsm2e83JT1W0eXord+xJiID6XACNDMDGQGcRYTvN9TISYmjl0eGFtIukww6eBZd9WbuxAcYfz5MxMDUA2HVq7Tx/nTaVWadH3V1GBmb9UWh4Mn9ZAhmoqyYRfPo1loS5dCP65FqKRMAPEw02xIQUvt4CM9jDLcZxOWMILm6a2MnitI62tDN41gAwz8X6UaizzZJJJwBxiaIi5MjIfRoKOAJvzBDJnALsO2LbAsZ3aotnDV9umBGE7Lf/s9mHzb+D4+QnPopaVWblgc8gAvr8papCPQMjO7+Nj8Ob+MMwbCaW1JTNg6bhIV0aGlmq4+6KGd6Wm6dcG6GrgbAi/dswzCgNiFB7hx7yAGv6nPCjL1sRdFH4M2vZztsXHa1JAwtqw41K673l3G2HL1G4awBSu9GeT+rzi9xJ5Ycljy3pmvBVeNu+wjaK/QgVm/hJpISX+mQYyNNYaxtsBOCJI+pUujDZwcJFjmpbSlsmHpXzj5OO1bfB1UkMMZOT0CQrIuPZfjqcnHM2Tb0FXCRPxz4wKMn6ady2gJf7SMhViYvkxWu0kPh0BmUvrQOaJTm0HWDvfIcvrwH9rgpzd1xbVnoWB2LFSH0W88wMAAZzwXAktbwIIc1BeLfDA3KGgAKf7o1Y3s4Qa/l3aXgRg+maDztQ4Yzu3/vOIUzB+wnEOml2wZW5V5d2GcdoBOwLYLO/zGNRo4R/ptGhOKJ+O/c3Flt0yABiGENjC6bD6Pp+28D2Rh4MNoHDexmDzXnjm5bFOaEkbsQGLZa/a8gcK0HxEFZj57lrLjUmBDAeaO0HmBF+oS4ixyjzEtNStIKMdtrZc7pNclgIYCTMnP01ujO/48BLAh+ai8N6pZbiJ90ZNQKN9n2omVe1LGmJ42IjAhY855CjODYHMf0cHMtQUngXmfox22nmXv98CdQdU+w4+yNQH1bVr9HPrrjzk5H5vCTQxDTAYjMFgrtjQ9TUIHRkNYvj9yQFIwhCDmdmB2TfCR3BXmXcuQWDzH5zmaUp1pVwinsS62LdbHBnpxKTch1tgRX6Wuy+R+RBA1kNsgwAcrXq7r7BtlvsbO6ZAHDwkvABLgJGwEgMabftFP6oKzHxXpUBGWx4DGZ7oK6ph5zgy0plJDVDGpNwHKLB8EMltSQs95RoRzEhwScGMcGN41wExVyYMM3mgCUFmCJbLBMgKHXZfW9fdQG7ui4SWFi70IsFGVMG+dh5kLvBDIAIabTkDGUzdI/AE3QrdFDKoWLggTJEFkD2OisMrX+a+HM5zWGkQgkzt3J7BAH3zCV1dqQDDx+TCnLFlvWVRtw8eZC4MaOaq60Mk1KSBSo47mfpcm177++PrrDgxcn4clqE1t5romBQ2mNbCkhxowiRr112Gehyx8/BuktYYV8pJBwrs/BgqMPOXSZ56DVz4eAf/lFb6XeLhoVghLx0aDVrkMIh1pHIfoLmnQYKLlsfDYSYGaYEr086tlvJqpTlVS7nTosGLDDGpLowWDoqEiAJAGaDDDF8+fYc7Mhd4V+a/WD5q7ZT7FADNXtmvDtiNZ9TGOzOuerbPe6jRzf3syHATH8cUtDRsgQ0PCXXTSprrArFsAhlyXkbr4IW6Ygj7GA+BZoDBBTsMMAG4UGjpCb4PK7pb/Hbqef8BV+i7fXsFhodvDzS5IHMLAMj1A2fGH2uOeDhJc2kCEBoNXjjMrMHWYqdTko4NAcriryNjW9q2SQVs/ioVmPlu4je8BjJyPgY25M6I7gpizkRq0MJIwfTUOJ3m1qw9ZNaAJubMSIiJDfxYT7HpeA/VGtDIBF2ZECzhhYDGhZl63YXhoCJBRpvn0yMbE8AMWMBNCmSkM2PBvgsGNJGwVtVeg7yZCh06VDPE9JM7EzaKtgSZVTcGU4N19iVM7JUQI90XBWA0WFlzZOiz8wJvl9MciHhBvHRmVoCGi/4+NINA/u1o82sOjLZ+ChbEdschhJiYExPmxJjFdedgswg9DcbZaPKYkufjFvCQ4SZAz4+5B2j49kgFbL6nCsz85dqIaSumtdASd2dwG8DEYAaY4IWm2dPVir6PNOWAzbztxFjCzCEyn3KcDgD1Un387Dt01HqmPuJZhZhYmEnCix3HsNfqNSeGXBctlCSBYoAHGQ41E9i8DsCl87WWCGI4yFzgH6kBMkugkdA17QflzVBdHgcwvYAY78gAvt0VPq9NL5I/rcGrfXFJwCxUFEAMc2AG4zqGpATeHtXcwF8MViTQUBcN9BnBCgEMbZNPy+/O4ZFJrq2UARhsHtDwZRxq1oAmVuAnHQws/1YzXJCXIT9Phksm/PrlEmqmzygBWO5rznFlScupiQHNm34Icfen6FuowMx3UcyV0ZYTtHAnRoIMhZxEb9g3Ac0rAxbahdjbs6iZIe3y2FshX0bTMYCheQ1aUjWXTuK4Tr6n6mP9NLfpwnup5sNaOxmL9mbGDlV7hRngeneWYaBUDswavEhnhMPLNP06AMMIXAdgmGCGg4s2zfy7UBxoNGfoq8+bCRq3WwQIhiDzgZSCmuWuTDkYBj4JmINM7eevdTwH5syck/NU68gDiFvuIGQJOyPs/B36LDYfuBGTKzMsnJkI0EhJ0ImVn5rLwpfnvkysKQJR42gAs3RlRjGtJUTLmkxumQ9BjTznKAVWWdIcGPl5DtDQuppu2aFSO+p7qMDMXyYNZKz4TAMbniuD0Lm4AWg+Hc4wUXjB/GB5ATC3Zqq9UdI4Bi/aNEnL0ZG5P9ox8XCScGMOpyds976PIw4tuUBTw7XSO+e/jOfQfSHwIMeEg4jmwvBcmByAoXmE8AI4gLnSMugQI+eTmn5vx5ORRRVt3m4Mz5Oh4SKKKtmGSPrn/U01GIPRXl3eDA8rKVWpz2anQox0VCTgrIWfckJSEmJ0kBkdmXGHE8Cih3cOMLGxVKqgT4LMFDbOdGPCab3145S0mktaraY5VDUYB30xgImCzT3uSQxogDzwiD0MYyouzbdWgZlvKvk+LGFFW8bhhdwYghi+/GFZ6KtOxXL4dPqK4+kp2DNukw+DgZkewn1b++bF+e7OK7NxDsjE4IVPZ8BYeLwuN4a7MTwnJgSX5fJlZsR5hpi5PZgUwGg1jFJJvoPy/WnZa+vclmE6ZwQugH8M0niADjIX9hmHmY0Yz3fe6GpCbSKQZccR1oxTAnCvwEtYm4kkUz5jCt70KW+G58pMIHPeOzfmErgvO+bAxHJdUs4MTxKup+rWZhFGmkFnrOe/FRoP83wYbpqdToIa6dIQsNDFWHNmUnoPRya1LUzHa0IA0dqW4Xk0sp0ZQAcaVxssEmKKgU1SObkrsVDSPeCRG5YqLs23UoGZd1esGp+FUoyIed52jMyNIaj5u/uc3IkbBwKZY/0EMz1oAGComf07hg/rtqmAZuPgYcAyGbfFsrVgflh8mtdMyoWZk7JsHq5oTk/YHS7YmWVHjj55l7cTEzoH8mE854OYEaYZYccX9z5tobeCDIRtogzwrdjyBzGFjaz4Htvug3HtwLySE0Mgw5wZmh9Gf9okzPyXLae757fI9NYo3R2w1nIHExZYXHKeV7a9V3PjeWzoagRhJQc0KYDZBu3CnLFl0JKGGl4gzzkxkwvTtZWvsUTnQLgyL3xeOjYAy02bXBq6JzjI3As1UrFtrLkzfGgBtBt0bYW+DvvrWgaZwmtvMILqwvmq7fXk9VUzPC6qZcf2W8Jf8FbEcw6lVj3KxI8hsUO54t8vScLfQgVm3k0axGj5MRJq+DQHGW3YuM8toq4LvkSWs5o91M4K4N+K3PT0SDKUv2Bg7IhxsLgO1r1RxmoXpRRzYrTxmjNzomnXpxJ1R7BwVaZp3rMQb4Jdky/EHOT1qGDqAUCPyrw450I7NgNfjdhtyGtUlhMQWfY9XphZ37T/Zvr+hn2fg852cnLO8DWZKCBJy+RddeTLatd30wwyDRtPOSqyeq18s86V/PbKyt6Vsa6m0gjLwkVLkFmrVr3mymjwQiEkcl1eNDjJFYWcePVtPCwhRs7fo2jZO/1m7DsJZ2QcwvNPf08x0QsDVWnnoUnf4cFUvV1Wy5aS7nBwXnheYexlMgUjOeR4L4DQujEwKuGn91KBmXdRLJzEP7diWgKNRRpkqPjBolXbBcgsgMa5F7Jmj2YNh41hWcAAdVPh2lRA0+TDTCwfhkNLbHrNjTm12Ey9W29rDy+pKtbURox8k1Rj98yd6VFPrc/2AF7CK605NSPSroz2XYIhGlvxPa7BF0WbAdjUwLZzUPPfzt1J5M5sETozwWAcxGxr4IEAptaH4O0ZYeNpKdngjC4LCwmVVKNpMNN5Xrgy3pmJgYxs9O4yLeOhJhlWovBR31aLkNEML4NxEDIwELCiKX2eUE85M+qJYesNm/BvQ4Ma/vmabjUQNCdk4cq44dpW6McanammXKTt/JWgbyUhCS7dfB082vZtNZ1j5Tikwxuciwf4Z2jKmVmDhVvCP7khpQE6BGnfL+Gnt6rAzLtLc2P4vFXG9JkscngwQLQnc0IcZL6wdb74pNgT/jPX7NnhDABB4S1DLeP0cBr3Fl1b49rUQBNxZ/jh84I6B2TkEA0x+dyYqu7Vhu60LgnCFnyXD13uxlB/QyOBDGkCmtG+oLYuHATNqUm5Mvwz3pLtoIwViFHnO2DTAtYA1rr2Zi6iBWDy/H6DAjEELdSDNndlGldzSAsxcZdGKst5SWi0U42mTnNlqqkTyDoKMn44zDDzhKNwZZwb0LeVu7fnwnT6exxYCEgbWyBog8kvdLCS6+BQDg3fxHuFmNak3VMSZIJ5B3z93p17jv9c8trzBgdDV8ZDzRCce2VfNZibz5OsKKFpB0zPvLhyIYV0i6vCwYZDTXFp3ksFZt6s3GrXMZDhbxRU7CRA5oQ00ASuTNjyrayiHMJLPz/sxylxj97Ae1TYHc74s024MyT5Ztmy6RTA8IEf43zMrt2Y3eGidg7JwWXZNky/CjQkemtXP6statsBuKKGMOvlXxKHF+3tl84HJf/SdAxmaDtyfgpzPdipd+zahZ041Mye3xRS2oicmDm0JJdZBxYyyVO2GcKVqsWUcml81WyDCtc5b8a1JWOYK+NDTAQ2MZCRFfKjANNuwoI7dr75OQeWoD7fERaLZg8ARJs+sMydkV/TCnBNb4Gf2L26AJsHdG2Nbu/DRVza35TBMHfMKQHGvTBZd13I9ZL7ISXP+UAL15wZIA9ogPfJaZHwwi8ud2G0i15cmntUYOZNWgMZLbQUAxnNmZlAhiBAg5gDlmGlL2Gib/h494/4sLZGz1wJM4VY3GcVelR1j+ZwRtsKd4ZDCqC/wQ5iPTqWLFfG5cYcT09zSOmIpzkHhpyZ2DzvU4n1ABNcRSpiDQuxmQns+Do1et9wGwcaGW6SISUJJ7Q+Jf8OCEEmBjNS9D1q+r8FHuol1GysgBiLEFxonufNUIip2cyFjnRofHshbl6DlFhCcLJ2k/0EdK4l4NECnann3+Y5F7y/pBBoDnjE38I7/utRBxiCGJrOkSxQ+b0de6IqIPPJjmEOjrZNDk98vbvENqaFc1KhpmnsQ031fA3TLhy1z7NTXZkelW89WYN+Lu38AFiGmlJa+1z7USR2ikuGlfgyAhq5jH6jAM1bVWDmbsX+KGI1ljSQ2SrzKyAjBwKZL37YfPlvkOi7fF91QNOjwo5Z79QPMkENFU5zIdJU6A9nvLT7ZaiIPxy5IzMgdGc4xMSmT5hzY7gbQ2Y1tdqrQYvW9QDvTylVVZgX0o4txumQJpAhGWDYG4xWSQzW3u5l6Glw2wC1KcOhZsAShjTxc0swE4GaB4KVGMTQ+Sd3hgHNYDxEEA7ycxVTbqiJA5BvUK2fG89zISZq+yWsFUOhC7qz3bQHGTc+4vFfJ1wfj0D7EIILn6Z5eY5j0xxeCORbmk40kAcHMX5bo4sP2of0Nc95UsdCkTlKOjJg58hMoaYq6nDKe4NcNF5NnrsyfVst+2SSikIirc/d7W+hVAiIdoIARAJMzKUpQPOeKjBzl9baj0nN54DMb/67GsicEDozbH7z5b84/f44V00+4XFuQC7mzFToccZWTQd2zkw3uzNV06FtdsvcGWkPS5AZsHRgYuODc5Z2hzMOe9+PkoeZJxY+CjuAlMt4lVHqT4lXxCbxsIkDO1I1Oza03gw3WmKwzJPREoI5vBDU8Ac5gQkp9VfKACYGNRSKWkAMhxlek4k1Ujf3CC1Q0B9iWHDJVoC5cqttj9ZgtC+w4zQdODK8sb7dNLhk4OeFMzOBzB+/AY/QIUZO+wPxY6ssa8Qy7kxauPwPJdREIMMbrHyxQxhqUt2HhO6FmJg7owHNPGymUFMdvc5SFArkrx3clRkGA7T1MtxFktdCPTe5zgw/0HuUghrNmdE+jwFN7PcK0OSowMzNWvtj0UBGJvry8BKHmkTnkUHoBXobMl+m0JKSQbB0Zp7gekB2fe24sFIPg11QIFFBssN5dmfaplvmztBbqXwQcsdBA5kFzCwbwOM9XVO/SuS08CrX3K3hxS8VnppbEKuZ008QMwYOlUUts35ZYnAD6LkHJN7OjAY1WlhB+wvlbjXvdJGug4QaDjNyrIWYpmEwmM8or74fa3NGU6wqdipviYvauBkmB5F6iaL2SmSIiefIzCDzB+IwI+dTBacEFyC8Pvz+D66hO04JMsYOy84pU1q7L+6RvF/5dARsrm2FvqNmC9Jy7cxU4jXD5z/1bYW+rbHIl5HSnDEeigtCTW9VTiKu5qRoSgFMbL7oHhWYuUmxtmRuaUOGpmXLvju4FkBYOzISYA4Iwknh0OL05REn8zjVWgqrYYedK1KYyXkQ1KIruTM0JgXtitTAeDJ4BoCBPdVl4a09CGVeTAAxrk+luulmN4YcJdkQHiX4GozJcFLKJZAFMbVWamDQA/M23NUbGfBV029tfVJx3cPUI6q96zW7+QoHBl8RdmnwFc50U/pcuinExEWuzFcsXJl5mgMPhxgONkqV7FF5Oki3K907TwiRqTZmHCS580/tGw3DC1w+Vz1vnVK5uVvTzcNUQKJG31UuPyZwFbB0YgYxLUFESpY3MeghV8aOKsRYcmaabvL2AGDjf39tgBjHloPvr8WcpKxtsxHjhDs0DgZjbYEp/MqvOtWGpNZ/Z3CZQpXz3UGuDO2jhH9+DLF9DoCGP0+ppaWYYheZP+Op6nkKamiH+fP+yr6jOSspgNGAprgzOSowk6UYxPDPuAMj25KR8BIbHuIQQ9McYP7pxp/++XUGmS/4Ayc8gvdFtOyn6DI5My5PhsNA6NIsq10CAPbuwfxkj3hp9uEbKbAEGppeODNXfJrai6maPmjBN9bxI03Hw0dhYSuhhc/76Wo6ZtnDs4cZ+VsaPNXosd2fUe/7uT+nhiDjK8KuC1IgQ+dsrUbTwLa9V36HpoEQYKwy3SAsxDAl4ipaBp3ceaElvFUR3qcTP7c81EQJ5zTtujUY0TcepH1bN27opvZhfFA0bCfpbuWAxJp7Y19niOEAAwA2WDbCUCvbw1S3vlH6biLHTYJHq3wGNqbPaDscZJrMQYKOxXxcsVpp1NRBhxpmQtTwbpmAhvp/W3OnUtchePbIQn+HEBj4mGvNzdkqy/jv8G3SNO2LBjV8micGl3Zo3qICM6uK5cfIZakaS6n2YyaIAcIQUmwsHBkOMif8Byc8MpgJwzMUZtoFMOMKZQkzPIeGay5O6hHmy4gnO+Jqj+5D3l6GBjYNgMbVTqqaDrvDBZXhVax91WrZ8aNsrD7YFyWUlE7wjXeaF8vnoG2SE6SBlPMGjm5sPNhU6FB3PXZfX1xHlWvODBCHGgmK3PXhbsxXuNuPcm80gKF5TOsZNrY+X4XOCxVTDvo8NBLIuOmwZ22LME8mlRg8wvflM5iwH58hCjD8arDvDAZBI2zy/HHFyji5Ts7QvAJNh03jHAvuwhgOMmac/t5qWDti0/RTUTWFXGRhHYMXDbI4xCyABsvk/VugZvqNVCe17qfC2oHBtQElemOqkh1rXPDGYQDc81SCi3RB1mAmdiPwm4cDDiX90lj77RSIxFwczaUpiqnAzE3ip0tzZGRSL7c9tZ5xWG5Mg2U+jAQZGsiR+bJ0ZGigwp+DDAccV+unm4sKCjVdWPFBYMMVhAvMCPP7iEvj8khkz8GyDxtjhyjA+GrV7rNUOzI7nIWDMopx+ACQbozsvoGrF+vrx+4LcoJAAOBt2YR9NHcuebruAifHjiPMMMIMLzADYMepGwMZbopBDndlCGiaaUzhoq/TurywM2yagIa1LRNATuQc+OPnIEPTw5w0HoaXQodGhjINhvna9KgB083hIwKXMORUBY6NvL6ztPBFSjFISH0+uxfD5FwMSxfG+PtzhmbTzTk442BcMnDDClcNZFJgQ4oBjXRa1gBm4c68zsekhXHpWnBHhjeKEIDN4N2Zu8JLHPTma/yA5QXjz+Sr+HwTmebrxG4eWRNJjjWlPqPwE992Ua4KzCSV046M1mbMji0jmCGIoZ5xlF6vT1iCi5w/Afinr7V0wn/wNwYxIcyE8EIhJnI3KE9EhppofBEPqvAdeJxDCef9LnoGORhQwa41bkcAI6tX83nelkxMsoDUp8PsDlrG1+Pr03mi7ct8Gr6Mg4x3ayK1rMwIGDiXi/kNATStAc+f8BDDc17ImeHQ4g6AX1A/pu+zwoLcEX9uB7iQHL13+/2lxGgfZqJ7yTtX0qGRkg3xSXDRQk6DOrZhi7IpaeWQ/Dx7cOElY0fUszMzzi6M21zoHBqYqT0dg4rnz1A3B9KZiRXqHG5IEmgGtr4GLVlhpgHGDjOYcY2w8L1kW7ZcCTEF+TLTujHXTLsO/HgW7swD/LOYHBO+Qe25HnvWa6Id5Tk11Dwl7RR1JKK5NLHtaaDDl5VQU0oFZrIUAxm+jP6aeJIvDzERyEwFP4cYDjMZwxrIEMxId0aGbyhJTxYHF1b48AfvsuAO80fiZ899nwPKGsAsx6HTwaWFi2TBKENLdMTUZQEvCGmev/GTwoIodIIICCXIyGmZ16Mv88BTmQ6mDq9HPXYww4h9/eJdmT0c3Cj5L+wA+IXxElW2X61+Hn1GBJ0xw9yZYb5mFHKyCIsyfr5ikhC6PFOuZtMMLmIMMFfwXpdenruYW6O4MnXTZUCMu750Zqq6n/f7xY5AYwE8LAvsFNQAy+vNXYuWbeNeqLGjc5yYw8Y1wNcBJMmniTtOkS+TCzISYlR3hiShhsYpmLHsu1KvyjIOKhcsG8cjsNG+J/fhljZoiqQKzNykWLsx0plJJfvipurWsufr5st/cPr8GIUY6n8pJ4mWN5BXoQJ3aYwohDi8SMdhOzklPN9ECwPxBu4ohKTNa9WtpeuRclRG+O4YwmrFS5CZe0kWhaXm3sSOj+a18BOvQk7nbVmwLWFGnutFja0Jcrr/eXI1qP7EMu+Fh5PWxJ2ZKd2Eg0wIMR5ux2l5Bd+ooJ32OQwyLGs4SdH1oRAFgCBPhgMM37LvV2xaPlIBGekdOiYJBkjMR1yZagKZynTzuUi5eZjy1gDnztRTyPY6GMBufCEtw0qxEBNfX0o6LbmhJjZQ8i//W9Ba1Jbz0p0Zp57IXV5T4jolnTC2/wQ0QAbUXBE+y3PvE209vl3aHm2b74QEGq2GE0kDmqI1FZjJVqxBPJ4bsxHzfDgiqK10wrLG0hfEO5Cc+lk67T24HPG0AJm/4XFO8uUAE/QkPZ6xfb6ib3qYmtqV2UFCDAGOVlDXDGZ4Qm44Xj68ZchI5s3IQr+KwIDMe5EQ4lTNhS0VtbwgpD3g36d52flmjrh/IGtYeVcm4cKIedmSMXfC6Pz1qNHvn7Brzjj+eXWt/WqFcipfc8CyZeDVY6VQUzimfeUAI+d9qMk/wJ2zo9U+W1bNprMkazjxt/6g/RaZjA4syx/N3decgdgyO2DT9LB2RFX3CIFlWaU9yJuZjr+a8meGwWBserwMFrAPS3iJuTNc3KngA32Wm/QbQM1rmMTMAM0fx/LmiYWYRq1atrxGORAjp+m7SaiJAEzM4fIHo8zz7VL3rtQPFE8U5kDD3SG+4wVe7lWBmai0cFLsM6LxlWrXMrR0wrKm0mk5zbsnWIKMq8H0N7ZM5sYEvUpTrZqvwKZ7gdk/o282MGZcuDQh4Pg3cGrRg36DetCN1SrSE2TDMBN3enhhLgsBDjO8cOMgQ26L/+1qLlQ8qFTB+rQd6mhz7ll5+pwU62CRtA4qOTATOlI7XGYgkrlDc1srpsLw9yccTet6nJa7SQ9hrWYUz7WxmNuYkWDhkY9yiLRuwzEn/9YMwEKva1kI0rWR8xq4zD0tw9dwkkAzH1+uOy/hBmI6BTIN5coMc5XltWtN55PnGgHAaCzqpnfhpmZqOGh4WM+XyX2S3+LKLBKBh7mKecxZo+U89Bu2Im3DEFMsr0kuzgUaILzuUahRti1/h0u7j2hZCwY1W/FhDGg4YVssazRJd6a4NWsqMJOlVKN4fGyhgwxrCO+0Mnzh4yua0xNOnx2gkOuijTnIkDOzqO78tUXdAQ+s/RH3938FmrNLREQYvuHieTU1epyxW4SEUg9xtZaPkh+z9FnYNsfpYWkMeAeZ1Nu1d2w8vMgHLw9N8T2hbXRiL3khKcNbtB1/jnxBrUHMcrkOOxLwqKYXX37GVlRXNhg/P+Non+fn+rTTYW0oGtfsM/EQl23McCDhoaaRLSPA0cJK4fk3SSjkBSE1iEfgQjWbeA0neuOfz8Ng9LZL7PwD4TwtywlpyIJ0cmU+Tbkk5MrwcKI/f8tr3U8J1UEekjWudWBr1rs6iBW+/Ji565ECmEPiMwt8IufJdMHxSLkXiSG4lkG/XhRi4ude7jOfjrphifPAj1ueA7lt7bva+UwBzRzaI6ChBGDAAU3s5qNq2zw0VYDlHhWYWSiW1c6hBWxaaz+GxlNoqUEEVuLLpBvDYSXWTcF2bovlEkDGokChXArjEj1H+2nuUFCKP3hlboT73Nd+km6KFiLS3IVg+ejeVKkGj5vG3Okw1eR5ba7opl6dK1PPhQLVqKD8Cg4IIVRUwfHKtmy9t+D2zq0TQo1MFubni86Nu3OWbaysOTZ0TghGn3BYVFknAKP9n6/JfoAZWmz4A5yqeht4eNFyEGkbw0tQy4r2r2Lr0DnnYSY65/68yjylCkv5ZTKZ+xnHud+ly9RJ4WXquJAPBDWBpHtB0kIwQFjYL4AFoUsxT4e5MvT3sB5mkiFZn4fUA7B2FH9pSBe42pNcu8Z8/zV4ObDl/PNDi6rpUDWh48b3nY7zJlG+zNrXYoDD5/nyITJtlfkcOBzEMskk5I4FYSdaYSO+cBXL+LQ86BvP5y+sAjOBtJYgpfsil1F/SpFaSzwfhoZTZP7E+leqQ3jh02vtx2gdLXpHAy4MMRVog6HG0XSngUR2OAcD6mub+iwyCPM5uBtD4RLNjZkdoxZh5IL/HQtn4aEGmgao91dU9XWGmjOzdDmYaG1iuHX8GyPfKx+CqmfHR9bImJeNZtHHjmxUjFdlXQszAQjCgwQwl2nZZQoZkjshZTACn4G/ja2DPwIZ/sC94RlJyd/u/AAyR2bpzshzHNYc48vX5gnXfU/ZO1C3BVqIic7HJzu6Ks68ANdySO6FmXms5cpIcMbi+tJ5pc/6eb7y7dHQMdBxxKa1z3g5yK95DF60Yf78ik3To55a6a7Y3zeXdu2TijWWpzlnsXnNVZHSmCDX3aH9kSATKznnkBN3WgaEDgy3jDZsOtY/k3RqinOjqcDMrLUmrfl6HG5oOgIyJ6gt9y6HKw5fHnHYPwUhJepHifpb8lWufc0kLUcmrDXj/jhG+wlmeMGrdQ6Hc2X8kXG3gYsewM4NqOcqtwMMKvQYYdTclzC5N3Rfts9XbDr4foSoATj5dxwrdBsAe3ccTQ2Y4QrTjEFv0xSaCO3+cIO86An7lqnm3AzKnZk/G+s5yXSYLPNoaEOI99PDG1aTVXll0vYFZ5yn/qDO2GKHHXo8KddqAAVf6n2PffviYIZApoV35rhbsXBmfFhR5k1xePFnhScD0+fLJbwqPF0jfy1s8NkIE0CMv/O38x3G8zEWsq+YE2jXnnQy94IXbFGgecWnpoexA6rGt9Ekk+bdddGdGp/bJZyaWAu7MXCJHR93ItbCTBrcNK9zlyPb+szOundmpNRrkdIgxtox8OmYeyJdl5TWYIaGNZDRwpPzcfCFEmIIXiCmSQVYblWBmag4sMS6KJDtyFBnktCrWxO4/DMcf/ryFb//41+qCyOXUdsxWuNzOsiIfJHpituGXJl4iInEt8ETFnnhw39by9epux5V+4LNVzhwkR0ktmxMpz92WQDXpgq5DQdgMwJ2fAFwnoGmR4Ua/ZRXUwfHIQtRX1T7pGKZ1UMOTNdWHmDayiUxtg/6w1gsI7fgBcB1fta9AtbH0j7Z0RUeeweoBDI77LDDOQiB+Ws0BCBj4JyCav/soNHAu1503ij8MswbCU/1OMIa57Z1qCdfhh+aUd0ZKboTHQh30/n1NZdioTsAzJnZTuElV5Q6h8b3wDx7IaM8COQ5M7ycScFMADTd3ECeMWEVdO5k0liCDE1TmI3XumMXAbAbfyz8uLSxJl4o82PQACZY5roe2R3OqGoPMbx1Z2DZLlDs7wzAfH2iLf+mjiHmuqQAh35DA5/UNdbcmdQ8/50ZaCjcxN0ZDi4btpzmC8DcqwIzAPJcmZy+l45uVXownJAGmX8CzT//jS+f/xXUUpK5MNypiYVqpPsBTA/NKbxkhrCQcSDjQ0zcqueiPBT6lD6nYpTmuVNEQENVwGeAocRjOc3ntYeEfLAY+J6PD361BwCVecFoO4zGBBAjC3vad16gcnemA09Tdr0wE8Rc28qdwHYT9sQswzfyjVMe2/zgfHAF1lRovVjg+bDH86FFczhjd7jgaJ4CqOmnvrV4wUHH5jNJOtSHDqO9ujCev6hO5NaMCEGG3Sry3HE3pkbP7g3vziDYlG8VFgCo80FfI81GroE7SR5kvDvTKxDD2xOaW5a1A4BNWDj5k6WHmgC9YJOuTHMNG8hbuDLLnBgth8rndU0hJj5tB1y1HKMYyEgngbsEfFmOK9MAaDqWJ+Mhhp41dt7/AdLRpWuqApqmHFeG5nPdFAkxclnsOkugSbkzHGC02lTBxjjYyJtO5tWsTRfwkSowo4qfFi1XRnZXEKl+fYIeYppA5vD/+X/x+/5f+B1/4Av+FcBLCDbP8zT1S6Q2pEbLRxaXH5Q3ZUtj78rwNyhp2/MCSra+6z4fw9AXh5j/AniGd2BiQEOf879brQ8hmv88rU+HN403BgCuGD53qFDBYBu8CWviEMPdmDkReAKZvq2dE0MQwwcONMAyF0M+u2IPTv5gPDVoDw3aU4vzYYvj5+epOvbTHAaThaVBmHj9NHV4OdoelXlx+VJf4ZOAR6gFiR2nJGwzgvKjuGtCYEP9McVcGXf4HlbM9D/1gSXhRS4jkKG8mct0l4XBTNZongz1aQUdd2FugZkZaFzopWIt/VL3DXTelzAThpxozAv7IJ9GhplSAEPjmAMBdnyxGkx8fADAQFr2Zk9AY9gVDdt+0tsMWii3leZbSiltXc4AfFkMVjUwmvcZ4TnVPuMu4OzOUJVs7YdlMnCBlHtUYCbZY6qEGAs9Z2aCGYt4q74RkPkH/he/41/4Hf+a82JiNZaOeJrhZemnOBemauN/CKP9FFS55a4MEG9DJVYbhz4zGH0SMrVG+xW+3yAa85BSDG74m46JTPNCiGtq9XZjgbrr0U/WOO+eQWrRoBeb7lGhH2t0bYX2eQe0tQsncYh5Rgg0uaEMYPlM48sO07ZPAFoHNX1b43LYottXOE7Jr6nq72fsfO2zGnDdaL64Wk4Q5zDyJAiTk5fdF1BGjJYjQkp1LSHbBpIwQ/BCINMJX5K3ObOQHQFrAfvgCxg6Vg1oSMk39tc5vERJvwQvVCVbJv+6n9RrL/FcpJ6tq7oZKZBZe5LT52sgM9Vcag5n1E2PyoRtBvGkdLf/Q/AMId2cN7Om6PUQgwZ0a2GmWBgR4nsxkJEQw/dllgwpxQ5SujUFbHJVYCaqtRZ/GcRIV+aEZZjphBlomn/+OwCZ/8H/4h/4v2iVa+7Q8KrLbuw6IAQA+TJHL6mjuMqjnSx/UwcWPx9L8dCV1hbMcXxybsx/4eCFD9ydWQOaqZ2wGWAaeJDh0xGQofWqlqoWe6dC5hCFeTMhHs7hpcmRmUGG4IVDjAY0Em74vAYzcp6g+Bkz1Ly0ezyfKgyDQX+oMRobwIxvlTks8mdNQGPHFzwobcvwp8HD4O4tyxKT6+keGOETv3mar1ZjjN9Xbm3/I7KWGAca+ozyYwhkyKnh+TLztWO1yowdXUI2tdNCxychBlhCXQxiprwmCi9R0i+d+1jyL7CEGDftu22gz3gODTDVyrKvUBt64/srrp+6LrDexszhOoPMtg5Dx76mYhfcCylpnw9a68yx+TVp1+qW72nJ3TQt9ysGMjJ5nC8b5E5qoaYBy+4OtIdcUUoFZgKlngZ0I/JpPkAPMZ0QhJg+ffmKL59daImDzD/wvzjhcXY4Fg5N94T9ny+xRleXmhyKucPAeTy9FRv+/hh3aOTDmUJZFHowcJ0eHv+84uFPAP+CgxcJNP+GHmKSQEP5Gw0WnR8GY+3y0MPkK7CpgXrspga+totClqT1y0Rv+y68VOPleedBRsKMXMbBJTbWAEYeB91DMpzVbtC2f0d/+gp8AYwZ5msi+7pynsYudAVqwAwtGs0pEiJI5mEm6Z3wXBpaVyvAZD4W3XMcaKiVX97ycj8BDBWjHmRqtl6tVo+fq2c3cCGN4UEHGl5urEAM1ULj4SXvxgTt3Apw0dti4d8Lzr0Zw7ZmNGCJTaeWxSCmAXB4xeZwCUCG4IXaOPI5Mx5mDEzwWNJcMglpgbRQn3YsMSdGrkPbjJ0TDh5rUCO3pU1LoFnsn5YIrO1YyoXRXJri3HD94jCzlvi7EevIvpcs5lpNc6xZDCc2fLni9MX3pfQ7/oUv8GDD4YW3HTODDK+6LB9uxs+/WswJvn2zmVvL9W+/cXufV0Omwsc5G761Xx4Tr9EDBqjqq+vod4KJ4Hk2imkq1Ds2P0HOtXMgAkzrUCu18o2HYIdcHqrdxN62zTDO+6EVpLwtmTmsRLVlxh26tsL1eauDzFthhq6dBjJ07Am9YI9HANU/fPaIbHCPjnKAwW4Ktm1xxri3GO0ZO/vi+3IC5i4NXhvMDRJ2horrMLQTtuMcpp+vtZLMAVoDGL78zOCFfoPDTT8692xgrcrO1eSpufzhIbgvVsdBgUTJ2RYvdph6tAbGYcRoDTrDa5SN4P2JayJ3am4uYcXZCKTdK2uORKxAXwMhcFctrO3Hnw0SRrXrGtwnUxK9qwX4kAcya4qBjbbNQZmW68p7IvVdmo4NAFxv21c23HOwGrQUkOH6xWGGi58KCTk8tMQH1hO2jD2fsHBnmtMTTuZx7kuJ+lUioOHtyMydRXZnDzI8p4QcDOvHEmIojBT2PVRFHzwyE4ceyFSMuCby+nnM80vqpkNTXwN3ZAFaXDz8Mrrh2gHXgV0BkdwbPMx5WIoPKw9G/lAOW5Gtp4JyKjznPJmNB5bvCTOxZUwv2OP5cES9X4aW+FWkcA3vqqGvK3T12fXlNF2f18bdP33zCV0dAotsQShsxzmEHV7YSbDhYaecPrHkbwdjlpgNIAQYYAkxuSCj6gEYHNTwIsTsCWDc3xHggYVDS0pazkkgqpUFpO+R1P5rzo62LwO/Xj1kVhRdKxLdW/JFiD9n5vukc9B5nZszwAoErOgWiAGW11suJ7UIz3MKYmgs/8YXx8FdGW1Y26miNRWYWYjnyvBBhpXYQAV4UBsAoruCFqfPj1OS7xNzZ/6YgYbXGKC+lBqCGBoA70yYaXrqlqCrXZJvqiDqWKGmJVzyfAV6GFNbMT2Dmi3OAfB0psa1vjpXRUDWLPp7HtkwPQBeW+DSOpihssgOU/cF5M5wl4a3VTOIbdNYiO+vDylxV6ZiSb9TnkwOyNA8z5tJjTWIkdPA8q9T+Wt9bk4u5FEvG7ejK+vugSd2/VnnnJ8tds0ZZnhZ3Ds+d8gXTFQ1+hyEe0KHRmstOTz33hXsoQE3tcJskhATVJMnB4buMznWCrIY1PDPF+7HA9DWuAIwdkDfVXPYk3KHugBqln2D8eVrzszcCnAKYjRQST3ZY2Aznb9xMK6z0fk6UhVsdz0Mwmr1sVynAFKnhib7tnbXi9pl0oAGWF4L7RjkOreADAe/te+sTWsg08Y2lJJcrzgvt6jADIC80xBzZTZ6y5mncPj8hdyYx6nmUjj/O/7lm7DvznPP1nNeCeWWAD53hA3UAN653s2wcmFVWKkQ4ja9BjJynpIbO1Qz1Ixz/aDL/DDe4Yy++YRN/bKsHRATA5tL54YrgGF0ULNtXGN4qkvDQaZj22IPRTO8wNbxRtzCxvH8cH7euvDScyS8FAOb94YZsOUxNRs8NUdU//DJqDJ/gwPD8u3ZYKwNTD0Gy2RhFHbE6QGG7iV+f3Gnz53vODTHXBgVcCTEUFI23Ut8rC3L+UwCDLTlD4A1syMEAKZ20AiE4aZYrhYXB75VaRADxO+RDCBW92k0GIxv15m3+cx7kZdhQe1+OY879O2Uf0btM6UcmRTQxNwYviwGNfIax9aRbpf8vpyWyf+BK6OFmOTAoaUAzL0qMBNIy6HR3JgtgnwamVAnoObT6SuO9bJFX8qZ+TK5MzPExJJkOe3TH9v0PB0tprfq8O05nPY1QS7YRSEG8AU+5WB0qLGb3BhpixuMOGPnkgab1uVhyDZiuMTf8msLDIPrW5b+tDdTLk3UpdkjDC3xEFPCleF5Mr7AZENHtZdEWzIxkLkVZvgDl19HOQ2xnjx/pAZ4afZ4tCPM72FNM7qyvqCJuXHOgZPhR+2tm8PwMtRU44JtEo5leFOGl+R4HM2cE9PPhWHtXAQ63/KcaPN8WU6hSctlwUYubLvBC4DRjhiHEb2tYY0MN/kNS7ChpOy5oT8FZOa2ZqgV4Bjorr008HW1aaaXqYsOMxiMxrftvAyhjcKBCcPX3NFzrWbXHkDp7yEWZgL0a5LjxqQAhM+nQEb7bmqaD/yeXKzAoUbqKr6zNl2gR+oXhplU8q8V68jQE6+ajSXInNj49IrjyYeVYsOXfz97J4Y37y+b+qeHKfvDp56v50beGMg84cBaTw1bUpXJfFp4wGCcIGaZS8PlGnOrMVAOBoXB5Kr8YTINw+TMDHAVFOfn0eg+AwBrhUsjz5HyUDTKAyv0LMx0zpgz01ZTw3gIgSXXoZFWsxxzSdjLLZS4Jhfsao94tAPs52WoiQoY6piS4IbWAsL2Qugcae4dz5kJQ02hqxLLoeCf8RyeRUgp1uKydm41pQBFfq69sfNrIefpN+20b3Dg0RlyZlxQ5j20aAVYOjO3wAz//o3yYSa/L/zaak4ehZa6tgqdtFSuTOBqZBxLCmTiB6Mv05y4Na7gACNfWKKuDJAGm6J79AvDDCl2CmQ7MwQxojYTd2Uk0JyAzekJp9q35ssTf+e8ma//xsP/IYSWEUuwoT+wqdYJ12h9bRwJMtR6Km8a/glHNRTgpn0BVqNfhCekDEYfemg+YdO8hI3cSZF7Qom/g/uTvkDADKY/9w7YDs6luQ7AtoYDvz3CUJPSInBwjubC1AaFJzkDgSvD4SQHZGh+FWJYmyFawboWZpLfYaGP1h6dQ7MPGzbsEO/1m8DONYS3rKYenjcThJM6dr/xJOC1N3ZeAAbjqWbS/BavhSRkQRiDmZxzpy3jhVoKaiymEFeN0Y7oCGpYuMkHasfApeFaTf6Vx/JWmMnUOBiM1oea3L56V4acGg1GqTNW7qi5vCZReykGLzlgcwvIaL+3tj7/PAdo1P3VXBkoK/KnHZTpohwVmJkl4YVPczeGuzMP8fDSAcDpiuMpDC1RrswX/IEv+AO/4w80/wfgf7EMmXDHgaZ58iv7exgM5URUC5DhwxnbuRUbLawEICjEDAYcpwKJ1pMyoBaAa3R1hb1p18Mn7ofmxF8CGQkzNH0dgd3owk3DAPxGYSZ5nthzItbxMHdn6JwFrkzMcclxaZIQc0FYWsp7jtW+eVyeq+AZuIAZAHaDZ3t04YnawwwPJ4WujF9eM+Chc6Q5dmr1aCVHa1G4iekkxKTe4KO5CeJU0rRWUMrptTd1DjH0Nzi7Mw94aV2rK9aG4aaYUnk02veiDeeR+L6tKQZA8/l0VdytHR3QTKEmt4oBtRVTo4MMJ8nQ4AJKuYMhB74POccQAxl+zVKKAQ1fngs00pkBEK+OLaFGblTbcQk2BXQ0FZhZFa+WvRHziDcPfgI2UweBsq8l7sr87d+ta2ju3/COjFZA8+TWfbhsMJgLpcvUXJoGMk844hGneVomHsq3cYCHH8K3epKFSxDu50LM4rWBb79E3mHiWIYplLQGM3PxP63/GwcZWcCx/oao4FimxVr2EKYHMMuV0WAlBTISZma9Tgsv8P5TrHVpiyC0+Qj92QaEJygYGvyJk2tQr6ZjD68fnYeBXe8zhUyhV6OmsQQZ2YgdDx2FhVyGE8Mhhp9POt61t3obGdP0WtkhC7LYMr7NFgA2eLEjurYOwk1aE3G80006p1oemqqUI3PvkzwCQcNgYOw4JwK7faVnQjwxvO8qJUfmYQkv2jWMASrfz1xHJrY8F5hoXT5OLQu2+yoWxsJMfJzagaIcFZi5WQQy01uSxbL1yGmoG/fOv51qKW2nVllpvkLvwiW8DRkJLhrI0O/W7nf65tO0Vd+PzVnADIcYmo65MW7sc2ZSb5kk6rNlizPqzx1++3p1x/QbOz56iLHj2YzAcQDOX92fNffA+JhSru3UsvFco6sOz/m8rHZV1aVjwJvHp9o4Z+xwft66ln4lrKw5MtpDehZ3YwhmaNiwMRe3AVj11Zj4sf9BX23wZI8wX1yjgT7XxbsoPgfGVdWmbg/ktZYFL89roS4GeNIuhZzCtm2Wjepdup3eeac8p2uFnnwjTwENP8V8LKc1SOBjtfB01bU7O304V9d2f0HUQWuHep5eE/X/1AMI2pqR+6kda44zoQ3tBlfrGgQcB4u66TBag6p2bc5Y+Eb/Ym7MOJjwmnL3YoC+TF5beQz3ODba8cpl2rz2Aian+TI16ZdezTjESKiRQCNBp+gWFZgJxN+UuRPDlwHLAgjKG9MrjHXNnVv2Luyblh+icfTgBY3+QA18wb33w/U34Kk+Bk6MBi58msPMAmLGaZ71cTMaqsocFogUnhlAzWpNzbIbwPzP/2HvWsjw+SscPPZ+frcH/r//An77E/jvyLyvCVysncaGOT6/A/ifafx3OGj6+zT/2Q1P+4PabSeF2mi4fN2ifTy6qtiPcAWqHMdAhhe+UZCRNRW0p6VFCDdT4bUobBDeZ89i/g/37Rfs8e/BYPxi0NcVztjO14taM7pghy3OOE+tO4d7pLsEqXaLuPOi5cXIKtYByEiI4c7MmjsDcUpTQJNTUPLvadvirs0slz8TlGm178uMQ4wEGtlLvdTc1gzfP80lShX4svC1bAyIY2rwYl/xYgdc2wqf7Iiq6Vw/XTSYce5CYr6WVNOMtyGTSoiX03JfY86KpjWQoXEO1OTeL4vPuAvLnVgNahBZJlVAJ1cFZrKkPSkRt30tAOq/JchMGKevsZi59odE8GLgQID/oe7D4XLYBHkwmhsjx0844rE7zcAim4IHptZU4eL148m1RSJDFDx8wVNrgSnE8T//wn5gj2ECMgIZNv1QA783wN9bBiysi4Z5nqb/Bx5eaPgMBzWfga+fP83ddJIT47vrPPraXd0Oz49H4HmzBBcaHrF0aqKODD3QrspYJvil3JmNn5Qgw4GGYIZcKbDx0ODP4Xd0pwrHz/XswlwmsDljhyOecOG9awtJ4I4Bi6yhJKtey64HZpB5VqrAp5yZe2CGpmMAwwulNYChc61qCTTGGsD4v3mCmLCTUzsvS4aaJMRozkwKALRCvVXWA+C7cdjgxb6ibSvI/qkWNc1SCfCqC4TwGsp90OZzFQOOGNDkbCu5P5oLy91Yvlw+F7i0fJqiHBWYAbDeR1NEsYfdNE29DfN0ywBuxgjUaA9mesYRAOyB6x44G5kbc0iCzCNOeOqO+POPE+ZutXnz77Qv0/68WODPwWA8GQx7HWR4GxrBuAbM//y//tnPnRkZHprmH6TTQMcu5wlg/s6Gz24ZuVVaztBieDwCz00IMo9YujKyoJWFLoA0yHCl3BlFWuEj7zdtcwOAYeqYsq1xOWzR7SvspjDkEU84Yzt3KOhO7bK1Wq5UDaVF1VzhxixyKdqH+PmUzsw9MCOn+bmJTa/8Tc/AEC1rHND0U/a5nfpygunmXJN6qjlG/WbReU3KjgieU7nOTKwwT4UuF+eBgw3wYl/9Hc0TtXMhJnYN30PaedBAZs3FyZbMiRvgW8ziyyTIyBcb/pwoIHOPCszcJJH8CyQeouMcVuK94soxgGU1Yu2qkDNBrgZzZagDhFiBrYLMYxPWIoiNGwBDg+epMa3xs51BhoedTOQ4sQf+QUBD7c8ozsw8zft0kjDDl3GAoeF34PUz8Pj5oAIeOTTkyjx/PYZ5MjGQSTkyc6HA38D4Q0laylyaK6MoVQBx+It95wS8tHs8H3Y4H3Y4np5wrqlPbdehKUE24N0Y7iDyROpUGzSLBvFijd7FQEZOvzfMrLkzb4YZAPYBL9ihHQw2zeR4NQCM6+eM2myJ9VYvQ3xqWzN8OuVA8WPLcWbkOVWhWfQ+ngMz2vZT11DqnpJKXl/5uzfpVdko/W3zcPJVzMdAhn8eUwkp3aJfHGZuOXxlXRsfPlnZqkno0ABTr87yXpZOjCzEGwCHmCtznDtJIHgJpr+eXEjlj8aHToD4w45+9wT3hj/8DX1boz9V6OswmTRV3RR74Mv/8y/s6xdX04vDSyOmNeeBxM8Jd2Zo+jfg6fNmhhctXybInXneAY9KnowGNxrIzNeOW8oDW8bHfPkG4esvhxoefrIIkoAbLAsgO+0b/wlZoLSYmgt4wMthjz+fd3g6nLE7nLHdX/CEI3jnlO5Ue7CxbNkoCuHYPG8wLeh+YGDHk4KYWJhJW8bPRWp6DWT4+jHnK+aAyO9NDk1QHE1A41YdF+cMWDo0xo4YBrbsLcdItxy/h/j3Giz/9lNj7bpo+TCIjPk0P6+x45Lb4OvIbSCy7irIpKBF/m3TMgkoEmZSIBNzZeRvFK3pF4cZTfJN2SrLHpariIHny3CgARAAjSrpRPB5kStDLfo+Qw8zcUdmBpk/4Kv9ag8J7SEzABge8NLu57BTt69xxFNWbSfUwPiPRxybKx44vIhkYHzFMgFamyZnhoDmM/D190/4jzklAMY7Ns9fj3h53OvQ8qgsI5cgCjIEM0D8QfSGNy1eQHDoe1bW5cB10AYHNc6pOeNy2KKaHAQKjXKAMezeBcJG9NyusfZpWFJosvXeWJ6MdMEkuGhwQ5K3Yey2TIEMfS8XZmK/QQ7G1Kie7CKAOmx0u5BbNZu1NZNyn7hi4AeE91IMCLTfkYChAYyEGWD5+/Iz7fppy29V6hwACAEmBhUaZEgHdm2cE36Wv1FAJlcFZpKSp0cJMWl/gNZZw1qeDC8UzPAStIkyg4s2TYknVO3Y1DPIrIWX5tASB5k/sAQZDWb429oUssDQ4Lmt0Z2eXHKwMQLU2DFimM9Ab2qc//6EU/0nGoKXmg0HLAvn2B3KazL9DrR/d3kyEup4wi+Fms7YOleGgOURy2ltCApPWXOB4uRcqRoK0okBlk9ump8KsBbxQpVfowObJjDQoKYhqNkDzRUywRPAogYLsKzxxhPJ3bzozVrrITkGL1o473vBDJ1uDWTWnBnlhcbpAS/Wp1cbO86t6ma3/Iup4Tz+W/w3U/slpQGN3O/Y8WmKgQy/NrnlMf/93Gt5ixYwIyFGwkzq5UQDGSANMfLz2PdjKmGnlArMZEtp44FPi8HasN6PBJpotWy+bb5NaldlD7R7BDkylAdCBbestaSCTC7MWEwAg9BKPj3g2v6Gf7cV+lM1t39DjYJxt4a3XdOjQrevcNw/4W9N610acmp+i5+WQAJkHvef5+N+nsJt0pWh6uuzK/MI3ZnRnJogX0XGxs9sOkf81fbGP0E6/7wgG+ABht606a2bQIYDTaNMNzzBE7jO994r0HQz5IzDsgB+kcti/SjJ+RTISGcmBjUyj0g7nfw8yXMpp9dAJhcagt/3nVL2bQWzdx0DxHoVX3yd2pqR+6ct0/ZNcyU4uGlwJH9Dk3ZNU9dGSvst+Sfxnq5MoJxahxI6SJpbowHKGvDwbclp+ZtFayow81ZF/vCM9dWUb94Ghxc5boDzvgk6j+T9LgXtqIxTbZ3HJiyYaciFGf754gHmkoMB+Ab9hHhNF2rQ74Id+r8/4rh/wn7/ssyZWTt1CsjQ8B+cmDNzWLhXrio24iAjB9WNuYoxf5hJ5dSWk8nA/EHG3Bn6SOMgAphGTEugaZQxDbyAawBXk6VxkNMgT2sFHI1j4aY1Z0ZbFnuSyeUawPDplDOjQZG2jgoJzq0ydkDfVXMfTmaG/7CqNoXrAIQ5M9q+x5T6+5YOiAZJudtfc8/kdmMgJmEq9pu37J/cVwDrICOX8Y3F4IZ/HoOhnG1pO8/XLYqpwEy2xM0kHxLij7prK3R1PTcyxnuynhu42z+j+e0Z+BOuNg7V9rFsWix73WMGmR682XjnhgTJmPwhqL6diGOR69GYnAAaP8sN+D6BqAl9ntBI+3XGFkccZ6x4whHH+gnH/3nC8fcnHH+/uhaR5T5p+/kZ+O/vG/zHnPCMI+u68294wiFwZh5xmsNPl69bVxVbwooceIELQK+CSWM+fcufFF9XhDAX21H65pFvwI2Y5+sAy3v2MI05yHCgkdO5LsgaeOR+ngMyWiiD75e8j1LzdPkkuEhAkb+fAzOiyraxY5AbJgFmnGoPAmCNCyrdAmj3rHauZP5KDChofMuzYm1Yc5NoLEE69vsxYNLgNPb5Tcm5tL7cSCokpcHLW0SVAopiKjCTlEbHQ/ix9rBt3QOo/+xr/BB8kJNC1amv+2dsPsMnvnIXRpk+7z8FPRdzoKG+kfib3stg8v6WtIe8fCAsIIatM/UJZP7pH9J8X/qpW4HL1Akmh5ojnnA0T9j+/Yzd3y8AwsbaeHs8ZnDT1I6M2iCgUpuL8mUCVyZVMPCCYD7QnDe3+YRETrRcLl2blZampfjPUsHOC1yL5XUkeOHTOTCTUyjx/XoLpOR8TxbQ8pzEdAvQaFADZZ1VmAGoyvYcMjq4kQQY3oCl+/tljdKl7tsUxGjnKuWWrEEgLbsVZmJOkAQYCYl0v6YGKNPq/mvP9dxaRlCWaSAjf+s9waZIU4GZhTS7XxLx9JYcARm0wEs79cSMmoWEPMTMDs3nBn//2i6bpSeImQDnWgOjdYX4mbWV0okWWYPqsvQgpMPg49gDS3tA0IMFCO+YAf7tHgDQ4N/4fW5Cn/aJjp3AZTu1bbLDaV62wxkHPKFGv2zvxLDqwsaNZQ0lrRo2B5knHPH052HpyqxBzUD3hAwnyQcgrRdL5uUnT7owcp6WZSh2jej6crDh4HIQy6wyji3DylgWLO8BNbHCmt+nmmIuTaywTjkypJgbwwtc7W9mcmh4QrCdajqpAAMAgwWGh9vgOxdmtHOkKXauUjAhr0sMaGianL+YO8MduLXfi00vDuIqVsxxZqAsSwFPTGtwk7LHijT94jCjFTipdXkBJvrNkQ+QdoPL1y3Oe98Zn3RlnnB0Bfu+xeaAMLRkgVfrOksc7SeM1vVee2YdJPbCmeEd+Y2wPtZ+y99E7OEgQxd8Xf7QmZrQH7+4FoM7+H6BniY3ZocznvGELS7zfAxmeBVhPqbzx/ukOk+uDy0PnJnuOPW/hPzG8ObzpoEMf6DFgGZNKVeGxOeVUBMQXl+toI3BBAcVDjUaxDRim0jMp2CGlq/ByVrhzOdTihXEsc80eJHLYy6BPPfcKeNjuITgfhoA6AAjz1duiCkFMzmPPFmODmIsl60BBW0zde/wZfQ8acW89ntryxb78oq8RN0cZyYFONqyAiffUr84zLyDYg/hFujaGv2+nmsbud6bKwY0wp2ZAGYwridsAhgervH5MmEvxWHTfBR/t5i7KqB9XTsO7bhi6/NCMPjeBs/DF3SnGv2pRj9VI6c8GWp11p2VUzCvwQwHGZr2LfnuFjW6qP2dOdw0nqZE6E28ETw5PxeUlCsjQUaEHOeDt0gDTQxgNso62p8nVSeN5M/ECgx5j0pHJgYvMZhJDWuFSg605Ayt2H6ucv4OYq4MfTfnPICtu9DGV7Xm3Ylo5y0HYjSoicGMlLZ/awAj9zEFE7F7UjtP3KHhUEPTMXiC+FybDlbiQBL725Z/47dCjFz+VpW8mZQKzABIFz6xZM9Ij8bs4XJtq8Ax8SGn7exSkDuz+9wGDgwHmI7BStgk/05s3/eZMw4UZnrQ/+i5NIiBGEub17Ll8k21BdA+4Pr8G/79vMXT4Yjj6QlP5ii8KX0aCBtr0xpw47W5tOke1ZQU/Dc8PYoG8jR3RgUZOmDZYVzqoUcnR05zSXjRlq/9aa5ADd+kvFYNm46BS2pZTiGeU/DluC9rBfOaM3OvYlDDz+WbYAYANvnwl3IQ3woz2rHL6dQ4BTRA3j1D55PW5/co2OdrgBvbJ/WAJNDwaenMALrDEoOY1GcFSL6FCswEkgXRFcA2su563gzaGpduN/eDcwmK7MsEMhfnzuwPonl4vcl4n3uznXNlOMjE2qrIPnwa84eAfNDQaeJvUPz4T9P4AOB5g+thM0HNBXXTYbsPK5bX6OczU6MTjQ0O7Cz4eQI5Di98TFt/+vOA6+MxrJL+KK9TZACQDzL0gNIaw6MTJ6WFlWLrcklaIUVCUBqASrcl5dRI+LHK9/k838W1QjB30FybN4PMq5inkySqwfOP5N9BCmzA1kVinra/Bn45QwxiYjATg5vYvsTGcpm2rdS9IwFGjrXfyQWahVIQI/+mb3Fh5GfaOreADN1kRTkqMKMq5tTIfAmsPGwe0LUV+rpmhazPlaEcjx3OqKZei9f6vCEgIpdn7tAPNoSaOZEQyz90TakHFIkKD+2tSp6DA8KG2R4d1FwPwHNzxeZwwe5wRlX3M3pQ9s8azNToFzW6FvPjDufnLdo//ub7X3pEfs5McL15zgwt5w89LnpY3QIvWqjpAXphK5XzsLMI+kSSdr7mwKy5NBxqOdDygkfuYqwgynFltEI6KnneuNYKH/rbZ1Cj/R3kuDW5SkEMTd/qyuTATGxa+/3YMlWyf6NN+H1+73CoAfKAhm8rBTGL5x7Pl5FAM7BlHGRiIKKBjdTa32YBlfdUgZksSYJnScBA/KHbuira3WdX2JILc57HW+ymPI8dziq8hL3qmqCKN9WWonZsZjdnnNqoyK2WTYo9BOjBg8iYgObgjzvdQJsDmz+b34DDFZ+a3oFN06MyHTi8hHDjwYYa4etFGK/vKgeQbe16xH588C0fPyMONFGYoe4KtIedhBt+YlKi3tc1cWeAA83a293ahbZwLuODLxxpsXRn1sBGFjIcZHhhpe2avMe0v50U7CwgWzs/XLnnSjprgL+WDGr4cWpQw5dxxdyYtTATsLw35T2bAzESZlKwGduPheS516osb7AARA41QAjXgH6P5cBKFqhxcElBjBZmWgObNa25Mjk5MSVvJqYCM6uiUJNWjc8i6HeGHjRseHnc4/lwRL13xS13ICr0QcEN+NZyaVo2eX5GmABMEDMgEl6S0MELLmD5dr72dikLq0GsJx80scKPpp83eGk2eD7sgKbDpulh7ABrR1RNH3R8WKGfpwcY1mCgnSEm6KH5WanSqhaIkWXfVDm1njSHYe0hqj3oZHVxJYxC07JAIWlhk9gyzR3XCkrpwuSAjXqd1kBPrhNbV1vHYlGLjIOMBjU0r+2CHNZgRp6fGNBoYPNWmFH1yj7U4EXbKB3kFgvHi58rfs9py+RmtfOkHX/wZR46khAjfyAXWOTFjq3LQ9D8e4NYh/+u9sdUgEZTgZlZVLjw0ln+lSvJwNSYFX/AEMw8AmiA58Nxdh2oto7BMBfOhrkNAIKmzTnMAFSLZwue/OsdHHE57QjYjR6T1v7eWrae34G0+CmSDx1t4DBz4PMPQNPg2jS4iv6A6qYLOjus0bnQ2loPzTkF5OoxkosyIO9BsyZaPxbGpO3K9fnncrn2eWq9CNCkCgqa1kIAfJcb8Z3YbqxBjFb4LrbLm6WP6R6IkZ+Lvyu5nxJqlK+o39UALzatgUvKoUlNy0NevYVzepXWlmnArgANEHdlAB1oboGa+RhiuTIQ09r9JP/WU0CRKlbpOcJ/U/6950JPbF9/TRWYCeCFixwZAS/zzXfBnOcgHzAcZg4A/mjwaE8w/wiDSLVvC3Qh2aMuQQ21okvty4yTQ8HXN2aEnXpAfgGWjoxW2HBpVnnq4bf20OGODB9iyxveH9ArrnYL2HF2bS52Ozc0NucGtTWCmluxAkArRLLEH2Z0YiTYrCmVi6VtIxdkbnEjEqI/hRyAiY1TPy/Peww0UwCafSAxrYEMfx7wzxQA1KBGwp1crxHL5fbkd2heu5dT97c8h9q9n5QWQpIAkAIC7SBJLOTJN5MCmpT7y6f5uQp+Wp7UWJj4FjjgIbQc5YIL7UsJO92iAjOq6IHGazNxiKEb6AJgp0MMFcyP7msvzR5PTYf6s88BkQ3C5XRM6Wvq7FgXBmHKbCD7CtdZIPJhhkuzeOX8AP0hbtk4Ci5ssGLaYgKbjcuzsd61cflAdgkw2gP8zYUjl+bOgC2LPdg0aE69eWnryXXuST7kv7nizvBxCmBiboS2CzkFdOw6LbZHLxexH+PKdWM27LPYG7JIptYKXLr3JbzQIEO1fB1tfem+xM5byuVKioc0NWiJAUzuvXkFsGPz9BxlycHA8lxq4cvY+eLLgr9vcmV4LsyAEGLk/udqDWT433bMkZFQI5droCP3oQDNLwwzGlHHlk0OTPBHQMuuy1BTgxBqpkK6tX/Dox1h9mGKr5QRNyyHHJ7w208JwIMGMXCd2b3YYYIBtvscamQujTZwpR4k9HDn2+XTEmgs4kCjjWe4afR9kPOxhzzfZ03zM4OSFmMWVewhco9LE7v3uHJAJgdiSJHOK/mYrhmfliAjoWZtd2IFz9oQSCt8pXLPkXQWNIhRzp8GDbQKPx8a1EiYWQMaDVpyIFC6ZdHzlgoXaQCTuhdj1+OMePtKrMHA2H2WOmd8GZ2PYH/4s1te7wHLY8gJNcnPUko5MmvwkgM0Rb8wzNwi/sdM7gzpCAybEGTIkWHOjBse8Ny43qVRh831A7zJfvd7WlP+VA2ZoIbCTBJozNQ7r5o3k1OgS+iJrSsfKhKEWjEfc2FyYIaP+f7HxqkH/ZvEE2r5sty3IzpRwLKQTJX+pNzcGO33tN9UNhGbToFM7GkSO6S1gjh5zagAygG82I5oBZgsOGLTzJ0BWyyhhi/nELMGM7ECWoMZCeuqExPLL8oFGs2lSX2fPkvdFLwNLwVo+PmTz6EYyMhdChyZlCvD10/9LeUWmalGWFOOTGpZCmiKO1NgBsCycOEuDL+RrFg+IAg1SXfmEYrT4XuXNiZ0ZcJm+z3QyNpOvtE8qs1jlsm/AIwdcEUFNdREhx07FfxtHAj//mjMH57SiYEy5m9bMUjJARkOZHy/pXLeXKXUvwieAKz9mHwIAXHXJTatzb8VYrTfo/2l706FiNycBBh5T2ggQ/OatOslC+AU1ASStbxyHuKpc6k5MxrEcIm2UyTY0FcH+HuXpmMwk5qPwYx27gJxiFmDGUCHl9R8znYkuHBZ9jkDGjpX0vFLwQyN512h8FKqg9jUublVMdeJ7/yaI8NvDLmsODQpFZhZFd1MFmFT8zRMLcS2Gw8xMjQiB7jepfFPAHNfkAZaS7eyAbkRdgYZngTMq3E7EKoAwCUB81ATl1bAcNDhb9va344scPjdJO8s7tIEYaPIsjXAyVHsjXUQ60RF15ySvTWIAe57CEqApu3FtpVr5afWl0RK97NS9TgGhxrUSLCJSZ53rSBeddHWCmf5Q5piIMOXaW+6PJ8m4s5wScDnIGPF97RjlstSMJN1nrTzlePU5AKN/L6UfNuREjWdYo7fGtAACPtU48ef2ncpfq1TkhAj1+dQQtvVoEb77ZTkM+nXdmcKzMyShYt2A/I/5gGukNtiTmYjVyYKMXze1XDCFwDGVb3WWr/lVbll+yoEMlr7Msa6Gk2jHfFiR8DapTsjC5cmMs3/puXfj3z4psTPhQY0KZdGTsvtavNaYRktJHMlnRSptdJcfs6dE/n5rXkxse+knCFMv6kATUwSYuSyNdG21yAm+xrlrpgqyPk2CCp5bRWN5FfcGfn3PyjL+E+ngEaCCx/PkgADhBAjYUQ7D/zznDya2DZondgNwd0S2VBhoqYTXxa97HQeeFckslsS2oA8P7c8GFIQIz+7NUdGfl6cmDUVmMmSdtMBHmQmqCF3JgYw/KsAXrDHvweD8YvBWJtFi7c1OlxEY3EAgnyZbgKaLnBpXJXlQOTO8Ldo/qYYAxkODmtv3vx0acu0t3jtzZXHx/m0ZdPiXKrTWuG49uJI44EvjIV+YnFx7uBpkg9wuSz1drX2QIs5Ctr0G36GxK9jLszQ9lMFtOo00Bel26ApdQ5jEMOXrQENHSxzEYBldeK1Fxu+CzGQiQHNvDsxF4aOVc6njj0HYHJdwntdAqWmU3Y5zs+FdKM0wFs7B5pyISb1t6Y5MhJaONzQPr1HW1c/pwrMrEq7mfgy/oejtDmTAhsAGFwOzXgyc4u3BDQVXNP+BDM1+qCbgx6VOk2NyQ3UczaXfEPksCLhRdtfuTzm1shTmHqwpx70muT2tb9/Wm8NYuTvBcf7AHedebyfajjxh2Rqw/KtUy6TSr3N8m2kHmCxhy2HLAlcSieV2q7lrJN6vvLlWuGs5n7I5FVZcOcoN8cD0J0Y+Rm9+QsXgV4SYpAXc2ZS96x2jmZJF4K+IJ0YreBeCxPd4szEtnkL0CRqOq1Kwi45P2eE50N7GOT8HWv7twYxOcWrlhNjlc+grKfp1w01FZjJkrzB+A3DbcwtMDx4kAHWC+kBABo8Dwafmj5o8baqPcRUk0vjvhLpw2mCGA8y049RmywQ+yAfrGu5KGtv3oMY07R0TVIQA+i/wQtJqyx7i2KFzsA/3OL2H9NA5r106/YIwmiabyMTZKyYl59JsI2ZHiQNYhYFNX1Je8vmBXaO1gox7sgAHlwslrBzFd9RauLkwEyOK0PbVCGPh2v4+YmdL3m8mmLuzS0hplzxgjq2HQ1oUu3iaOdkgH5+IL4bU+xveQ1iMp3QQLK8oe3E7sHizgAFZm5Q7I+O3oooUXQXgkwMauir88PK9VH00gDX5gpq8bZuOlwmsKmnnrUJXgDMAANgATFz67iackBmDV6kYjBD45yHu+qQRH5H/g1rLzEpZyaroCF35l7d8ye25s6kFNtXDjIZELN27mOfxYBHW6bBzKyUGwOkc0BSWivQcwpXLn7ziJo48p6HMs9/MgU00XPDXZlYOEVOc8WOMdeh0XSPG7N2DWM3ViqEFAs15YbdtD8C+ULApzWIkfsdq6GkuTJ8OwVW1lRgJiotpgnobyXc2pxivdydQWRTtLkBwAms8bgNYDeun6LmGjg2wVdniPEbJ7CZISbmyvAHLbfFG7aeTGaUUl0mMZZuyi1AIxUDFzkvQSb3+9r+zO7M95CWS5NSal25z3e4MSR5HTVHLOaSxeZlQZ0srDV3IVYg5Sr1Vi5rLUmnJrYd5iDQ3xP/O4MyTZtIAU2gmBuzlh+iHaf6A0xrDk1sPqV7r5XF0p2IAYkMKcVCklcxjkn+7Wwiy1Pr5ioFMXLZrxtS0lRgJqkcGqY3I/pjo6S1KRk49nYq30hbuH6cCCgY2DjHxvdRpO9GxIEZlEvMF/FkYNo3PpZfj0GNfBjLbcUe6qk31pRSYCLXSV1G+ZtJd8YibDDxVsUePBrExC4AlHVz1rsBYlLXQEILB9U1J0ZOLyAGWBbWENOpsElKOa4EHcytBYS8gURNHAnz8iu0jno+SBzwZLspMbchNi+V487I9dbOkQaDNJ/aF/qM/ia0G2wNsqRjJYFGu59yFQOZtfmYpEtDSkGM9sCLPQR/HRWYeZPoYUI3kijoCGikYjDzjGXz/vPwMDs2gVS4YPHkIVJ4xYBGPoBbZX1tPgYzsvBLvaXmgI2EGG1ZCqzkMcj90ob5uw/wnY+mHh6x5TlvaWvuTE6bFtrnmU6MLJdJKWjRuEs777FpAOvtotxaQMeUG2YC1l0ZLgIMiyApWG42da4WekV4vDE3Rib/gn0HWJ4zTbmgkws+fDkdrMz5iNHvGk1r+6KFkW4BmbW/2Rz3xSY+k9Kcf+64aMtj+6c5i7+eY1Ng5ibJG4Te1ClfhiTiqQQ0gzJwmImCjBjop2NjDjALCOGJc2w9XgUaYjpX8u2Sz2vuR2z/Y3el3E7Oy8nacaQgZgEzgDtnfCdky6aph9RblUooTn32RjeGtHYY2eDClYKYVGGcU0Dn7GhsG7FQU47omUDfBaJwEyjWU7UsqNcKabmN5I8q0o51DQJTkoU0n8/Zjgb4KXdm7dykvp9SrGbiW90Z6crk3GvFiZEqMJMlurlir6Za6IEeOls3PIvenVsxHKZBgxe+XBa0SCzjWswTGSgFHXdoNLjQ5jVQk8v5d28dk2IOS+yzWxSDGd52CIB1oCHx++aWneMP77U31RjE3OHEaPOabjWkFsqtpZRyYe51ZuT3uTQXQWtfZs3hsGwscyBoG/I7/Ddj54PPa2PSe0DfW/JrpGT7SfycrgGNBJnYcfLPYudFnku5PW2bwNKViYHMGtTQ721WptcUO9e/NuAUmFkVf8DF/gC1t3H+QB4AHIFWAI2EmmcsgeaApWsTK3Bj4ZkZDKZCxA6Yc28sgME6N0dzVWJhBzkvjyu2LNifzGW0LatM07xUDLA0NuAOTAxkpHOlAo20vflDfO1BsxZekjsM6G9+N0LMLU+AbLdFSnMcaF5CiwY3UL4nl6UUs921g0iBS6rgpX2OvWHL78kbWHOhUudJWyZ/RwvD3CrteO8FIwl3azeR/BuK7VcM3tYgObafmuTf5j1Qs6Z7QeTXDCtJ/aIwk5t3oN2UcnrtFPKCbOMBJuWiyHtaew5SQUtqxby2XWAGmU92BOzoaj3ZEXMCMYEN/S4V8M/sdzmA0XwMYGLOTGpa01vv1Bz40YAm+V0ONLE3K3og8/sgdr/J+bU3vTsgRn4tpnd1XwC9sNYghn+W+wadAgXtt2Pb4brVkdG2K9/IrfK55sakHBo+rZ1L/n05DdxwASO65Rxobx0a0PBlt5zntVAT/+wt0Ms/z1lW9FfoF4OZW5In10g71c6AfBPnhRnrnE47+6m3X4ICcmHoJ2VNJNV5eJ1BZtP0MNatzNukceqX1bqpTycZGiOAehb7FwMZKRW2Eusj8dm9z2gNYqBMk6IhJwasEmAXQEPS7iG+Y3y5BGn67Ru19he/ZlYkJRsy0wBGzt9TQK/t1JoDtvadNUdmrcDlBbTm1MjckTWIoc9S03J//gqIid08sZuOzk8MaOR512AjBblvdSsKqHwk/SIws1aVNQYxcn4Narg4WVwRtEETg5kYyMScBECvgcSfH4H74RwZYwfYKcxkRVXvQVTxHgeLa1sBbe3CZDGg0eBlDWhSd9/auZDLrLKc/07s5Z1DjDx/Medssb2chvU40ORADH2m3XM3Qsy9Lvc3c2HkOmvT2s68Z9iEb3MNXLTCN+f3pCtD28oBPu2zlKOj/T7/7bfmZsS2vbYtee42keWa6LMcd02DQW09TWvuzC9SZH5A/QJXJhdkUstSMVGaX4MaXuOJAQ2tcuuLk7xyHCi0aIYdJpBxvWlXTa9uthLz42DQ2QG9HfFiK6CxOtSkIEYDkByQ0bYpoUN+b83ZiUGPBi8pmFlsQyNT6cakQEZzCbV77s7E3lvXybofY7VvaFp+dk9BDfH5TTsYUaqweosjQ9+PhRshPkudq7X5GBzy9bTfl9P36NZzLwEmB2hSgLMGbbHzlysONN+6iHyrY1ZE+slh5laQyZmXn6Uk3RnLhgcPAVpXAmvOhOaG8CEAmVdgAhljBzc2rlcnKeqZe5YBqrpG3/To2gp9W+tQcwvIpLQGRW8BGSktnK+5WjGYoe8BWCYEc6WARoMYvvwGkImF7XIUu9+ikjWSUtO3hE/kZ7GdemuBvLYN+YauJazmJLFKSacmduzasrVzK/Ue54j0XoVuCmjeY39i5yx1njSVENNH008MM+8JMtpnqVATd2Pk/ICgt11yaG4uTJioEM74np2ghvp5MgxeaJogx2LEAOO6s6wHVHU/Q80i/ET7rUFMat+05fw7LZbbXduWvXGaz8vwE5CGmeAYHuAKeQ4ufBqIg8xavpYCMm+Bl5iyQUbCSKygzXFebnVl1pbfKs2mkyCzBjapbWoXJQZqa64MX3aLA/FXOAAc3PkfVMx5uSXshMRn9zoyMb032BQ35lvoJ4YZqVtBJgU1Wmhq7YHF57dY5NCkHAxtGS9oeT8wWmE/h5icK1OZbupvewzABcC83E0PGGGnXrsrjLDo6wpVXWMcDfq2Qtf2vkNL3g9UrKr32rFJkLFsOubU5Jyv4Hxg6V4t3Cw2LZN+ozADpIFG+6EU3KyAzD0w8+bnaG4HkDHnZc2F0QqitULrvZSTM3Pvdrm0pGBAP/aYwxBbX372vZQCzjWgAdIuzdo1WHOl3tOhAu6DmwIw31o/Kcys1VrS1o05MBJqcpJ/gfU3C2kTPIhaMiuiwiwGMsrfDrkydgIW7s4Q2GhQ41CmQ48aNTpU6DEag25fYbu/oO+quTYU9dod9NgtIYdLgxkCFw4yli2X5ynnOSFzbTRnhm8v5cZon60CDbC8X94IMqnbOgZcsRAdlOULpUDmFoi5BWDWduq9C6q1nJmc34zlzZBShW/MkVr7PHWecs5RTgF977leAxq+XLo09/xWbL4Axc+snxRmuFI5CSmHJQYyMTdHUwxo+FsJTVsAOx1oNBecDykHZNo9M4EMuTI1ugXEmGDwYONBpkeHysEMTODWoAYGGIywGEczw804mBlwSCOrMfXCa08R9DQPIcDwc6DVAks6JSvS8l/WHI/Y7yeBhq+QE2qibSi/lQMz2vdSQLMqHloC4iBzL9Dw6ZwQE9e3KKS0UNN7OzZA/FjXwlA5n92i94ZCbfsy7C7hJbb8PaDmrfdIyaH50fUTwkxuToKEGm0ZsASYW2BGrrf2B/GKOY/m3kGTqH6tQUuFXgUbt/4EJrAwGNGjAuXS9KjmcFQFuG8Zg8FM36kd3AAIAIfEq4IHjo6tMbdvw6Fm/iK8S5MKA2lujMYX8nvyt2xk+t2USgpmiyXI5PwFp/Zz1ZVJJfrGQCaWA6NNA/kQ863frGMFrtyfWMF7729q03wf5GfyPNwDfH/Fo186VtKlAdJQk9puSu9x3/B916a1h4QMpeXA/b37VvQTwgxJA5kYlEigScGNFd/P0RokfXtRvgyvrcTBhWQF6JCcK+NUoQdV4q6ACWsoWViADUbAONcGxgERaredcTRzVXAHOC7/pmvr6c+zRrQGjwQZDjS5Bby2nvZMucvJSG1wLQwBvFvnkJokwCRBRj5wJZC8BWTW3IdbT/p7PNRl4anZdTkuwy37cguo5ILM2rmL3ejvoRQ4aUDD11k7t2/dl3sUC5PFoGVtX9agJvZ3JV8CYtv/NfUTwwwpluwLuETcGMRIaIktS0m+za3tj5B09XOUEYIwU0DIz4fwIsNMftN+3jk5ZgYc9x1ycMZpl13YyX3uWxgeaZogZ5oGnJNj7Bi2bWM3y+OSBXEMbFLSXJaUa8PdnZt0y5tXpivz3UBGc2OAbwMyt+Q3fM+wCBAPMb1XwZsbSrplnbfsw3tKvjlobkvKjdH+OHN/9z0loSW31hX/Pu2XBPYcWOH6tcFF008GM7dWt9ZARgKOFoaS8yR5I6YSkTPCClKpUJK2GQvAvuLT1FCeMUs44TWX+Lx0bADvwnRsWY0+gBcAAmA8vMzgMokcHDfNQKj26/QAXgAszmXLxo2Y5nk1GnykIEYLO73bc4MeWPxt7g26N1coCTJaj9bA8mG7BjISUDSQyQ2jyM/fqtw/Iu1lhH9/La/mVt3jwnyUQk2zQmNQo4We6LOUvkX8V/69xoAGYj1tH1J/E/JvQf4dxaTdE79m2OkngplYwRADGYsQZLZIw0zKtQHcTbedpnNs8hsAJmdZpqYUXeHChDWYyLmRMEMigBnY1sYp2ARgATdu2RJcHKrwefcdatfG2HFeSwUakgSaHOdCPlvXoGbNlVksf9XWytiRFaWcmdSzXAO6hTSQiYWVAP2hm5rmY82NkTt2Ty7IWxVzAdbcBL6OXE9+lvP7a997T+fqeyW3am8KwLoTkzqvse1/S2nAkgOysXtfczvl92Lgg8SyX0s/CcxoN/hGmdYARQOZLZYQwyFHy2fQnJZ3esP8RveudGWW0zrMpLbFocYEMLPcVsqpmTYAHABrR5yRATS8oNdCTRrAWLE8BTWakp/fYhsnFAOY2D7GoCu5r7H8mFhYCZF1YhY6Esu1nbu3EH8vrRW8gO4YaAVw7u9ov7G27i2/FZP2/W8NOBo05uYrcb33fsaur3RTOdDI0FjMuUtBDI3Xwk63wv6vo58AZlKhHB4KioWJOMhwmJFQIyBG5m8Ezxlaj36Pv6VrJU3GTZkMDSB+Jan7Ahs6LhJYYg6NlEvp9a4MJf/63VwCSjjvP9ecGpqe83MMZtdlFWi4Yr2SS0mQ0aBmzZXJ+hH+oFpzY25M/k25SdquqC5SDGRiYSW5DMo0lO/zZXKn5WexdXL0lge9vL/uBZtc3QNu3yNnKKVbk5xjf7MpEEzBIsS6qd/IUSxckws0t/5GDPRpelDWLUrpJ4AZTRrgcKiRoSaCFwIYDjYRiGmwhJlBzM+ShdMGoaVPD4aVQiwrVMD2ceXq8vwYNx9Wy5YJwA5bjFg2BMBixOdBwu9i95fLqfp3sI85QEPPGunOaOdMOy8SWGLT2vxNekOujLyu77pfgA4yOfByT1hpDWJyDuRbFeipAlKzwd5jX94TXHLO3Vsf/bfuU46jkhNeygWbtd/K+Z6U5sLcmjN1i1sZm5bzBXaADw8zqZvUKp9Ld0aGmuSQgBheWEqISYINiTeqRkCTqdS9m7iilCsj25nh8CKX8+9y+PBZMib4THNzSNKlWfTQPSUMq4oBDT1feGeda8aHFkrSwk0aHGjLVKcjR5lgkwLTmJMkp6PitZZiIJMLL9LJAfseX7ZmlX+LcEqqEExp7c0/9UbxrR2ltxRi2ne/Z3GQgpu18FIO2Gi/dc9LhPwb5Q8JuU85YBODGLksx+2M7e+vqQ8OMzmSeTLSleEhJgVkYhCTAzPaEEgCzR0aImN1VY8sAOW3DEGeiybuyBCshAm/ZuHiaJLrSGfG9wXVYYSZu1sYYFCjd+3UWIOq6dAOBrDWtRZMx51yZDTJQj8GNzfBAd+4lszHP3/Dn9+9X13szj0gs/bQXXtQ8/nkzkXWS+mWC5+SdoJvyS15L6fle751p8DsW0sDjhisxNZd208JJrnXQQMauV8psJHbktvJdS/l92Pr/Jr6wDCjPURS4CITfzm4SKB50MGFxhrM3DMA8O5P4lJo36UaPHyadwEws9oG16ndFmNHWEOuy26GhZFBSTWBRMUcEnJUyLOhaVkV+z3Et0vzXFTD6ZMd8WIHwE73gQwxre0SgYk8f/wzPn5zaIlDAhDG3SGWTS1Bk7Rnp4QvbV05nXSQUg/2HJCR21lb9t56723nwmYKcO59S/7RCijNnlxbR9Mt5zMW3pNQs+boxLZ/q2IOnXZu1n5DA/p7wOZHu0/+Wn1gmJHKPRQNanZYgEyDJbho87ngInt95vMAosnFXGvbp/2SNXtsjZ7amtmH+TDkehDIACHckCsj24qRDs890twgHvDScmoAwNoRox3xYkcAGx1kYg3ncYjhy+Q4tuwWYEqKHlIakYgNa+Ai4eomiJH7wW096crkgsxVWQbkFx73ujLfC5DuDU3d81v3Kvd331oD6N59vcX5iYVgJWRq69173eR3Y9tIAWzs+7G/iRz3MuVmru3Xr6MPCjO5f4yaK8OX81pLAmQOSANNCmYkuPC3fPoMyjyQBhleEEuAkcuC4QEvtkJHtZpqnw9DCbt2Bhgf5qlY83iaO3OvM0OhqkH89l2yrwj6cNJgQ563xTaUsfxu9PdTOydBgIODfPjJt3kL1Z2515lZzMdqMKVAJvWQXQOZewBnTd/zzTTnrf/Wbb1FbzmHt4TLvodi5zbl0qyFneS2te1r6+R+fg/g5Pw95IJLcWWkPijMSMnDkA3acSeGQwwPL00FB4GMhJnYoIGMhAsOIhQGAvz9yNdJXZE1VyY5hOEmGD2sxNue4dLCShxschXm33iA4r/H61Mtvm8mILMDRmt8qCnmzqw6E9BbDU45MlIB8MhuAGKKhZtW/iRznBk5Hz0HqZyemHJA5ta4/j0P5r/qYZ77u2+KSzJ9r7ftHwFwJKSQUvk0ay6N3P57KcdlWssLuxdu7nUxf279BDAjD0GDGGDp0vDw0rTOASHIxICGL4+BDAGKzGXhu8bnY0AS011QU/ufPIABjW+Jl8MNQQaHFxrHuihIyQqQiSUec0Di0+77/qQYCjVZq7szt0gDGA6Y8ra66y+HQw5tYC3c9BAufoszs9gXuV9rrozmztwKMrckXebs+1u3JfVeBfg9BeePViDF9udbQk7KAYuFlGIAcW/tpVuVyqFZ237s7yMHeoq4fgKY4doo0ymgYZ/JMJIGNNp0DGQIYmSBSEPqMyntLTsGMTGoAeAKRp8/gwYYTTz5l1e/pnU0dyZXlItjMGCYakDFgCYFSWba/3FgJ+sWEJRhJ9kFAt/GoCy/SRwU5HIgHm5KbC5neXJedlsAvD/I5L49fsuH8z0F01sLw1t/4yPqvfY/x0XJCT2tOTprv/XW47n1+2uAfyvIfPT76X30AWEm5wFDhyUhhoMMb+kXS5CRMKOBzAHLcBIHGS0Zl0tCCi80NaVAhk/TNuTvWoDnzwyDQd30QM2rXIdwQ23IaO6M2yVqCXi9UKJ2bHxisUWsqneYYMzcGExVyWWoCYo7s9wBPeSinic2DMrnfBxVqvCW4aVYDRh2v8ee7yk3JhliohVyQmJyvVtqXfDP5c7lrrv2ndzv36JbQi8/YoGSEwr5K5W412dJp5J/95aaTBoEvSU5/a3K+XsoIHOLfpS7+k7x3dcayOPLec4MbywP8RBSDGpiMKOBDI2fES+EKBwVOzRaRzoKGtSo1bPZNi0Au8EVwNhM4aXBoGp6jMbO1bUJMqiC9lptpmXLv0uNk7eTcmT4b8RkzIhxmMJVFGoabHi8WlXr1LM9BTMayKxKowoNBrRw05UtF9W0tU2nfjK5onxYptwYud/a2+O9IaX30vf4ve99TN+iIE1t868sElKJvEA8pHQP1KT0rdzCXKDPyTkrECP1wWFGE7+xtfgAz5mZlmk5MXyQy/i8hBkNbPhZ1kJGueELDj8xkNEOWw2TbPACoB0MNhPUGDsCtYOJeiVnxu2G7pzEZIPK3tyRCcGGt0UcAxsKNQHQ3RkgPwE4B2aA8Bolr5d80KQghjs1/DO+08KdSTkzq/OvYqEMgaXCTDlhpW/5tpv6zkd6uH/L0Npb9SOAzlrtJC309Nbq2d/L8Xurc/mR7vPvqw8MMylXRnZVwNuU4aGmhzjA5Azkpmggw6elS/L8TofNFXNsNNdmBp8NYO3852HsMI0NYDh8KK7MOE0PEjZkJwVe1OCdMd6hkZ1a8rCWBBr63XFwQ9fWGAeDl7ZyzszwoANfTBq8aOvHgDAKoTl9tADhQ5hPy7fLRCN62s8kf5o7RINYlrPPKZDJ0dq6sYf1RwaZHxleblHucbxXsRKDmlyg4euT1t4EYvvwVt16//4s98z30weDGe1mlU6MLGHkKzZv6RdxR+aEDKCZbsJ2sw40jbIr2tlfe8tOfZd/JwYy0ilqHoC2dmEnO7X7wpKDA2dmAgnAQcwwT6/fRuPg81zIUZl78zY6zPDfpN8dBoNxsA5iBgMMxoFM+7CEOHk+5HlMgYsEnBTEBIfPW3TmD2PuwEjxh7MGNbS9h+Xim2AmlvirSXNl1n7sr8h1+RFB5lcviHKO/5aiJ+W8rIWdpHKvzVvuq+/xG0VSHwxmSNpuy2XkwMQcGsWViTk0Jzn/ik+HM3aH81y4XtsKaGtfqHKgecY6xADhizKNrbJc+94tzkwrvm9dLacXO6AHMAawocMEAA8UgIOKxX6FB/oC4GoHgHVJQIADYIYc+h33G/63AIS/x90YDWQ0qIlJc2aSYTrls4F/QA/gWwo2+k5Gldg112kh3v6NTOjVQkpr+5mr93iwx7bx3oWB9sa/tn7RfdLeLlKKOS8xl4Z0S420e+6ne++BH+F+/7n0QWGGxKtf0zwfYq4MS/wliNFA5qRMn67YHC7YHc441k8YYNCP9RT2qNC3tQ97tA9LV0aTVvjygpkXlGvb0SBGAg2UaTwA1uXRvMww4/+oyA0B4B0RwAOFdixyGQDXwJ1r5M791qsDHGCGHP89BkgERoNwJ3JA5l6I0c75GuCoP3Bl45gGtk7sAawkA98k6a7khpbkNkgxV+Yt25R6jwf7LftTAOWvUQ5I3gI0/Dtc8vvfE2ByfrPcf/fqg8MM11rezDact0hXxz5p4yua0xN2hwuO5gk7nAEAnanRmwpVXWM8XNC3Fbq2dm7NYIBn1paNplTBLN/ArRhz2LEr25AgI7dlp/M25dFQ2AlAGNIBQkdEHkNsrOadOIhy05FCXP59a9vO3Q+uXIgZsIQWbT74jXvyZjjQxMTCTdkiV0ZzX1IJvzEnh/aVH4O2PEff+sFeCoePI/6HF1Mqj2btu/z7t+o97qO13079RnFl1vQBYYa7MHyef669Rm/Cz9aqY0uQObVoDmccPz/jCAcyRzxhhMEWZ/So0aFCb2p0+wrb/QV9V6FrK7R2B9hGP9sp+OAAop0CbVsSaPj3rVimwgwcYAwNXprpDygFMGtuyBqMycuVK835iblBa8+hXIjRwoSqMyPzZlKhI+B9geZVWcZBRALKPYqBzK26F2S+ZY2por9eb3VpuO4t4t773nkLtBeQydEHhBlNWriJAEZzaLBeHZsDzanF4fSE7f6CI56CYYRBhxojLuhRoUM9j8faoKsrXJoeT/aIF+zD3Y7BC5/mrQrHxP/2Uy6P3IasOr4AjE24LW27a8vk9K0wk3OHas+C2PNBe9alIEZ74YtBzWLDmsWtWVWADjS0PKYYHUvFnJUcVyZn+zmfaft0z3b+ynZCPrrWQi4/kt7i0mjbIcntfat75b2Au4BMrj4QzGg3rFaabMTYwlfJFj1jp2DmBB9aOpxx2D9NjswFJzzO7gwA58hMzsx2hhq3rEKFuu6BE/AEOKBJuTAUAuJlnqyBpLkB8m8i1+nRYEJufw1ecj7j5fPaWE6vfbamnOeVBBcJOClXxirfm3VLErBWk0mr2bS2DSkJKLc8ICXgxH7rezyU32LT/0rKPdf3XJPvDUCarSuVAzXa9t5bt57PAjLvqQ8EM1xaH0x8WnZfwBOC4RNyte4JgsHlyBw/P2MHcmWeA2cG8DDDnZktzLzsjB3G2gAn4M/BAMPUQI1W2BNs0H3O24bhy4Hl1RvEtAYw0um5FWZugRdtffk7qfHaZ9p8bFlKGshIl4avq8Gk+puySnbOg1Srzp2r2Pqx/JdcNya1n9rvfo9qre/1vffUr1D4/JUAJP8Ypb638/Qt829+hXvpffVBYQaIv6pLiKF54crEAOZEgweZI55wwn9mkCFnhmBGhpe8M9OjQ+Vbt60BfAH+xMkBjQYztH+yoT0OOvyQc2BEAxPpInB44o0B8u/w6bWhVZZp+5MDLrc6ObkuDp0DeXx8nCPVmVlL0l370+MuTW7eTUwpkMlZP/Vb7wUyb3lL/StAphQ2+XpPyOBvHd/zd8v1/tH1QWAmluzL3JZFngwBzA6rHUoqQJMDMic8Tjkz1YQufFzPU5Z1oIgaGE8Gz4Nx+8QLT9licCOWaTDDpyXMNJHptaseKxvugRg+L5VyVVLjWwAo57d4A4epcQzOVstSC/8wvCdGRt9PAU1qJ2I1ksCW8bH2/YFNp37nHn0UkCkF2vvo3nZguNZcmrXfvUexfb11u2o8uuiN+gAwo1W5jkGN7EySuzRIQ8zJD59OX5Mgk+/MuDyaeuquce4WYO9az21xBIZNWPDLQQINV6zA1vI8NJDhy1LPhltCSRrI8BygHKUAJgdycoFGwswawECZ5roJanJEAJMDNGuQIWEl5dLEXBkNhN6it0DMe/x+jgrAfFvdku8idYtL8y215pr+KNv8ufVX3wVvEIeW1GcRVyYaYrrieOI1lnSQIcgZYYQjozszvI+hAQbjZ+taDuaNzmkwIyEnBh1aQa1BTMqduceRia2juRtrv6MdzxrIaMemratNS1cs5i7FoCbrWCSE5Dyk+EXmQMMlt5MTZoqBjFyPK9cJeu9aRn8lyJRC5PsrVtU6R/e4NLdqbd94SDhHxZ15b/3gMJN7c0snZgVkYs7MYQov1U8RgAlB5oT/YIRdODIX7AKoof6GApiBBX4HHgFch9/cPr4FZrRTQuM1x0JKcxxShXoKYFLuTCxUk+u6xCDtFqCh39YAJgU1qf1fVc6DTwOa2A/lOBwcVGJwI9ePbT8XZO55WP9VVa8LwPz1eg+XBvhri7VboGYNaIo7c4t+YJhJ3dAylKS1K8Na/KWcEZn4u2gkr3Wt+7LaShrI8GUEM2fsZnipp8RfghqDYQYZYOr9GVOv0CeDx8HgZdj7/UyFmu4J16SmpXgZqgFLDGQ0ENCghm9X/oa272sgkwIa7Zj5OHYcORCTBTLclRmwdFi0h5XcMAca/j0JHzFpwJKajrk9HIQQWUf7PFfv1S7Ht/jNou+rt7g0wI8BNnz/y332PfQDw4wUh5a19bgzgxBmNIfm5MbN4YyjeVrkxXiwWebPAAicGAk1Z+zCnqc5yMAAxiUE/zkY11Hl8JB2Z+55MU+dKk0pZ0ZblgIBeRwQ29KW3Qoya3Ajvy+PPQVlMaCR+5wt+Ta29valPZhzAULWOpJAEgOZGKn9lY7Ie/12KVg+ht7i0nDF3pK+p1J/4yXc9F76QDCjKebKJLouiIaZ2jnp97CAmMcgtBQ6M2aGFsqZuWA7w42ZsmVCmDHhUDuHpmt7159TY4B2sw4zt0QCYsvk51ZM50JMDGAkzGhuCLD8nRTI8GlZS2st3LQGM7nHeBfE8BpN9zzAcqAltr5WIykFMjn5OG9xZW6Birc+7AvAfFy9F9SQ/irX5t6QUQk15eoHhZm1G9cq62g5Mw+6I6NATXM4K04Md2eeF87M38ZHDMZMLf126FHjjB1qdOhQz1AztzMDLEGG1XCqmh59W2EYjO99u93oMKOBTGxM0zK0oom7I/cU8LmOTQoQUvDCpy2WvZJzyNG+J5fx407t090wIxvOuxVo7s0hiVXB5utfxTgGO9pv3AMytz6UC8QUkd6jOrcUf3v7HoqBSXFn3kM/KMykJG9kDjZKa7+8cJMgMwPNFbvDBTucscV5BhbqTFLWbJqHP68YzBWXwwhjRvQTuHBHhkBmZC0C96hmkKHE4A41atOj21foxxrWjhiazkNNY4H2jp6qaZAgs3blc92Y1DpaqGwNdkg5IEPDgPA6y2Vr28uFl9Q6SXFXRnugactuSarV1pWujFyeCzI51cBTJ+AeoHjLg70AzM+v9wQb+QaV+s0fuS+rog8IM1z85rJi+cYv1gpC/hXrYMQt8i4Kd1QA306Mm7YYDDBaYDBmbjzPdzzp4IWqbnN4AQBeu8n91oARBhYjRjOianqgrYCmw2hHF36yBuBVudcghk/nOgu3lCOpFwq5PAU+EnK0UJcGLtq1HRA6NVCm5bIYnKT2UwubRbV2Uu9xX9YARq4Tgxjts5RDc0++Tq4KxBTdqvcOQ31rlbDRt9IHgRltNyW8rHw99WYPBC4KnyYRyIxTteoRBqMFRvuJ5cHYBdB0qKethSCjiX6zRofO1KiaHmYwGIcRxg7o2xpAj5dh+v48ZueCt1mz5i5owCFzSe5RDGRyIIt/ZpUxkIYZCTTIGMf2TdvPnDZogo3ekxuigQaUz279PObGaMvudWTufVDfe7OVgqEIeLtbIx9+31upN8MCQDn6AWEmdSOu7a7yCr4CMbDAJxuHGDkPMLCxnzDa0JXhCb58mYMbGyQC+70eA0cIcEAzGIPRWIzWwAwGdtrPYYKYkUHMOC2bQcdONaO0Ali2JAwsHRE6Z7G/r9wwrwY2MVCIfVc6wWsww2+TlCMTc2a0ZWvuzGL/X5UDIt37cIpBTi7gpNyYW5Zpv6Gtk6PixhS9t0pI6FfUDwgzt2ojxsAiX2bNlbFLmNDEE3adM2PmEFMIMXXg1PAwEy2PybCHu8GIESMGM1Xhri3G0aCi/RlY2GuaJtDp7ejApq0B+xDmrVC7NRqQDJHlXPeAzJorM+sV0U4a+ffXru2gfK6NtW3n7G8SZN5LKeeEL5PL5WdrELS2zW8BMm89aQVkilK6J/z0V7szRW/RB7hy2i7GOp7kn7ECUXtjx3KZXWCJDjhz4q5xaw8LiNGdGlmDaXmkrqVgSgseYae1af0eo/HfHdj0WDOXZnQuzjAYBzVtBVjrEogBDzTfEmTkduV0FGSkDQOocMNDUbR6wn1LjjWQie1rVnjpVVt4o3JDQCmHJme9XDhKLS9txRT9qHrPnJr3cnxK2Ohb6APATI7oMIRLk0r8ZIOxy/CSJl77iEBFui6aK0N5MwOEs8NOv/b70i2SuTb8+0FysjEw+xF9V8HaER0lEKPGDAYUapJwooWbcnTLd1QQeAVwYTugPYQUwCGXJAUz/Ku5zoxcluUqSV3XVoh8R5uXIBODkJycmXuWy89i60gVgCn6EZQLIn+lO3PPm2IR6QPBjHRjZHgpcShrBd0kXp3aiJtK1mQix2WYai0NAdBQaMm7Mv2UMyO3pe0DH3NVbNpto1f3z7k7I0w9oLf1vNwVBwxo1nJVpG75W1tzN9QvDPANuMm+iDjgAL76PauunuPKaNMxgNGWSbBZ6BZX5taHV27DdrlA8pZEY22dtfVvVQGYovfUR6v5VHSLPhDM5IqVZDk5MxawVg8pEdBIkKFlMg9mENPkynRK1Wy+TRnS4iAVqyquAZHftvUhMzP6BuQw/TkPTfzKvwfI5Gxz4coQxGikIEGGPrdIQg2QhpjUfq45NKp40m8MNG6xmFON16WSelPL5Wep72nf1dZZW/9WFYgp+pZac2l+xNyZEppa0492xW7UijsjCy91eIWx3pGx0MEGQAAi/BsELn0ALjYJOpoIYghgOORItyiWREyQc8bWH4fBDDTjYPDSTH/MslaT/Bvmpsg9ZZSWk6O6M3zhmS2P9UBLreryTF8FakharhSfjkGMNh8VgQy5Szd9ObETKRfmlqTgW6t0566TWv8WlQd10ffUX13jqcDJe+uDwwzdkFeoJa58o9b6C3p+wPl5h0tN7f/u8IQjdlOhyqGE4MU5LtWcH3PGDpfpu9RH03lqT/iyWObXpZ616XNMLQePSpgrV2b6tmXbMTAwZnTQZke82NElBFuWWKuBzD25M3L9ZEgGCF2ZVAIrEMLNDQ+i1P7fDS9cHGRuUWp9DV40OMldlvrNHJBZ070gUx7oRT+iYu7Me0FQue/fWx8QZgaEAMNBht7YpwdrrMPD53C4Ph7xdDhjW59RT10SGAzYYYcLzjhji90EHSGinDHAqMAigaabujKgZa4fp+3cn5PvAsH9dlgHys1XLEeGKyd52cFRBWMHjNbgBQhhJQYysUuQs0x+tpo3s/bj2oNEPhQmdyald82xS7Unc6tkwnDMobm3dtM9bkxq3bXvrKk80Iv+St0LJm8Fmre2MVWk6QPBDEGLJg44NLxibjROc2WeATzC9c30/ICnxyOqf/SBD0N9KXkHxUHNDmdcsMUTjrMzo4GMHHdTKIpyaQhmOrj+mIwJwcVDjQHQgZwbLnJi+DyXxTgjkLEjrB0x2hEvlqDhIQ0yWjm19rm2/KbyLgdo6I/7r7yF3wNktIdULN8mt4G71PLU734vkCkP5qKPoFtfrN7j94ru1QeAGe2GkmElGWqiYaNDTAPvzDy6+Zdmj6emQ/25mx0RApgaxykwtMMzutmZqdBhhI06LTTuGBTN8MIgpm8rdG2NuunmUNBgTAA1AILwk6y2nXJnKINnnEJNbgO0vgg3rTk096aBJFeiQSuAb7V6GaR9M72nGwOsW10xSHmP5N/Y76fWz/nePdsrKvreeksy8D1AU+7/b6UPADMxkRsDLOGGVe/NCTU9AmiA1v4Nj3YE9g4AanRzaOk8hZwIQ7ZwISlyZghQuNtyYcu7qar2DDddhXEw6NratQEzGFzbCpumR910GK1BVTtPhRrNqxNhpnGqjp2q9j2vz0NNg42Hm25NBtbCR3zZaogpttGc25SD7XvrvUNJseUp8JDhpVuSf1O/e48bk/peSuVBXvQRtQY0pLVnz7euBfhr6weEmbW37o1YV2ap0kP/AmCLoH8iDjIHeJCxmDomfMCzPTn3osYMLM6d6efEYIcjR1QTzOSEkUbYAGDGwbiWeakX7BZAs8F1MBgHg6rpMA4GY2NQmW4+4lhtKw40cvmcBIwBxpo51AQgL9xE0zlAE9Ob/lZTDxMNYt7izry363KrtLyZGKTkgMytEJP6ztr37tleUdFfrRyHJeel6lvd5+XvJ0c/IMxo4jeSzJ2RhZnMMt34FmK1cBMNj7TZDf7E7xi/GJybHSrTMTzp8YTDNO8whRrNC/JflBDSOBjXV9JggHaz3M152OBlsGgHg00zOTEN4A0XBzYxcAnnh4VT42s1ufP2shZu4iCjQYy27Gb3hSv2YImFGyXEgC17C5j81W9KEmpi4SU5vwYy9zoxqe+mVB7ERR9B7wU09+ivftb8HPogMCOl5VPwtje4O7MJO1d8hndiGiwb1gMAbPCME2DHOexTNf0MNhSCImcmBjDXtnIdPbYPeqhFDg2cYzQ8AEMTFgMNYM04tSQcVt3WqnEbuH6e+Lyrrm19IvBg8cmO6XATH6+BjZQMKy3CTLJatkxqlQ8XCbXSpdPcGT5/r9472Y+OVQstaeuSUo6N/HwtAVhbL6YCMUW/gv4KoCkg8176oDBDkjcCd2d4IcncGXJoyJlZgAxtYuPCPk2Dq30Fmg6bpoexA+qmh7EjKtPN4aOurdC3tQ8dtQ/eBYoV6tp0cFgeaIwd0ZkaNXzICaDwkg3mpWaImSBohAncGYCFm4YH3ZUB8gAmpru+pz1ctIeJjIkBvpr+2vZv2ReuW+FmDSgGZaz9tgZAKTfmLU5M6vsxFYgp+sj6nkCT87dV/p5y9YPCzFvq//PCgKYFzHB3xkJ3Zwa+zgPQNLjaBlcLtM11dm2C3BcJMHwaGWNVDmg6Ao/aOTGUDNzBJwbbzERgY0aMg8udAVyrwHPtJsvOO3dl5HSuO3O3Ytf/rbfst3o4rJ2Me06WdFe0UNOtIPOtIOaWbRcV/cj6HkBTHJn31g8KMzHlPCwJZC7wJe/GDc/TKtydiYnCPlYbbwC7wbWpMSfvErzwMZ+GMpbTPLcnGNfoLeujyRrAUDKwmQ6Juj1wbdNQLk+0PyjrgIamAUwN6bHEWQrNWTGtza8NDfzf/1yuPwDYihO/dktuxLQVy62Yjrl3wG2Fr/Zwk/sqw570+7xdHNmB5ppSgJLaxvcGmaKin0m5QAPcVoyWv61vpQ8EMzkkzN0YKkimvBmq3cLzZmReiNyMCjJ8+mEJLbF5IF0O0e9Rf0nBvj3gBbu5Yra1rvPI0fjcmXGGGd8hguxxSsowQCKwAa/dBPhOKnNAhpYTuMhBrjcDjWi5OSn+gOEgo0ENH5P4b9zj/sntyRpTr2wd2WSAnM6taSTn1xraKyBTVPR25UYIYn8zqWdP7u8X5eoHhplbyJhEb8AEMOTO0HjrnRQCmthmyZmJujOIF9raENtlzZkZELozAIAHvNhqruU0DGbK3fEuDeCdGbfpsMPLWAeXgHBnAARAowELn5fnbMASdPi6/DgXP5CS/FyCC5///7f3tk2NK8na7u2WbAs3vVdPzMT5/7/vxMzeTDQLbCPB80FKKZXKLJVsAzbcV4RDst5taOrqzKwqLRm6V5PNJebcx5Lq8r1S97MRmdzIol7a7UIqvZQ6L8U5IsM/vISM4b+nj+SKZQZId9OF2qd/8JJmkP8VP6vtAHDX1rboTd7lozST3aaP1+ueyMB5b/fpyIyd1RproCrxAqApu/SSitIAwKY7UkdjdFTGm227LMdFw73QlGi7kXsickqqyXtN0k3y8zKf2yWKxljZyBGbOZaMWbNCHJFJMReByYm4nPoHlBEZQqZEPSs/4p5kCVcuM0C6C6780HXIXtJMulFcm/ed0Dw6t7NpkSg6Y2XGLnOKe719OjLjsgKwxWtZ4wj0g99tqiMOxRaApJoGgRlqZsY/7kIXAAOj3k0om3ZcnAqYFFBfSmRG35+km5YyJzIW2R+NQXOpaRBEaID4j1NOj6uU4MyJzEellvjHl3x1Lj08Q+o+5BRuQGaA4RdJ18146xKNKTGuw3hW15E/3LtWZqK0kNR+RFEZLTPAuD3IERmPpMRoVkC5xiv0oHfoRy6248540RhNUcbzOvVFweUqjtCc8tLXGaWblpIrMZb3nLvJQ3+2uZqXF+e9xvvFOkVkGI0hJJ/3jtJQZM7hRmTGouUGGIuOLgK22I+7G8uDlpkckangs6SNsEKUIzTyfFgDZanGoqm7pV8bUxTNaL1pir5XkyxtygnoxqCpujqUS0RjvKhOH53J+ZXUIvvRUuIQluHkRGdSpIqtUgW/Hyky/ANMvhuXlhr+G7oENyQzNjpjhUYTfSxv+90wQq8Ur+7RjsQ7JzKRzKQewe73Hl8XFntio69dtmknXUezrcYTUvbppKZLJ3VSo4XGfcSyQV0XOAJ43QMo18uExYt4Remp/rvIkZMy87h3xP58c8tiAKSLgZeklbzjUzASQ8jl8P495goOBebS3JDMeHiCA6R7qdjK3BptL6f1kHbSXYytvHgy4zVs0XsrMXORGXutPiqj93d1NNUBx/122GUkRUuNFRq9X49BA7R1Oa9lM0432ciUJy45ERk33TTHJ4qM97O0793ozDl/vKIITU5BcHTcJeAfZELG8N/EZ3GjMqPTDN62F7PuDVom++ygbR22wY1epTo+tbTrqfvJPe26vcYos7YC9mOhabopC+oufSTppByKsmlFR6WsXgCgLIYIjRaTFHLMIwah0a9JugnzbfZHkyOsF33O1MWiP5gUGULI9+RGZUaTU2Vu5aZElsREQnNv3nvnAXkN/dy97XZBiwzQScHQ0wloi4ObUuphynFvJUwLf0c1NXLhou0p1R5ft/NPlQ1QbttBA+dqaCqMp4+wIuO95PPJ0ust9pHkRt5CoYlSRzaSGI0xQwghJMUXkBlNjfFw8XYW5cx8ppUIKzL3aj1qwD0B8R43de/Utuj8/aqLnLwBdYnXssZr2eBHIiIjURhgkBgZeK9Eg7pou2gXXYTnsN+2qae6GKTm0TyvvPZmXb8e4X93Nl0F573He0jOXGTtXf8FzdXLfBbX9CyEEHJzMqOjMF6qSY7x0k3ePhOdsQ1XKiKjpSZKC0URFSTez21PCc2oTkVmv25PeIWPl3YaxgyWfRugOKApShXpKdriYJEaVON0krfuiYwXmYm6y3uSo7+D0rw/l1NE5uR0k43OLOWjBIMiQwi5Pm5MZiJsqmkuQmNbm3K8mkoz3avXb+SNEmwbWa8hzl1qRjUzan307BKpmUZpJgXCqEciI1GaDYY5n+qi6KVmg7bn1GG/aaXmcTdIlI3IaHGxImMjNLoQujbvU1wyMpMSmbP+1dgf6JwcLCn+fW8oMoSQ6+SLyAzgjxSsozDA4o9rIy1aZn5jWjszer0AZYN1dexrVZq67HsJvUpvoX5ZtkW8qSiEF4WJIhRWxNSYJ69oIzK1mTUbwEhktt3Ulk03C3e73ulOUXSPX2CzPeK5bHAoG7w83rXytFf3flTLlNjIeq2O01IDZ5+VOO97OYe56IzeVzvrH0JuN+/3uAchhHw+Ny4zUapJ719QN5Oqf/HqZn5jKjH3e6yrI7bVAZvqiE1xaOtOtAx0XaGbuujlpq6Ltri2LlrB2a/HDbktjLXr3tciz92LxSA0ct++l1PRwE5WKVLToETRP/8wiWX/eVCg+FmjKBscpEhYojRaVuzSExndw8n73HsMdTXe574kpVmmjskiNwpT47rqZT77/oQQkuYGZSbVeynqfh3VzSTwUkw2zTQqBh4k5u7nM7Y4YIMjdnjqEjRq5uqik4CiQLMdZrM+3m9x3G9w2G/xUjbAfot+1F2REiAv86BFptL7W6F5rZs+OmMjMyUabHDspUbLGNA++zChZRfdwQ7ltsFmux2iNPsNUFZjQUlJjU036bSS/mw23XTpmpmcfxVn1ctEvZiWnPtRUGQIIdfPDcqMxYvOWGGRRsNGaRx0jYtXLzMRmjf8uH/C7v4Jm+qIXfGEOzxh24nMHZ776Ma4GqUYbatR4Fgccfi5GSIcZYNX7NALjffRZWlTUfIZtND0YrMCuqiQ7a4NYJRmKvpojZ4iYRhheJCZBkds2nN/1thURxz3GzyVDV73m2H2bSszWmT0d25fVnQ83qNmJic6kySa0DKHnA/0nrJBkSGE3AZfQGZORX/01bDJi8Z4EZnfAP61R3X/hN39cy8xOzx3EjOs28jMEVtXaJ66M7fbI562OzyXDZ4AvJYbAOs43QS1TV6SirEi00tPG52x6EkqdY+moWfTPAUalEWD4mcb/Xl63LWRpnILPKrvWpirS5HPI+v2mPeoT7H3sVGv6JzweWwU5trHmLmW5yCEkHm+kcysnfU1+q9grj5mJDEA/rXHX/96wN32Cb/wZyIx7bb2vaRptLgcOqHRkvMHv7DDDk/Y9RGOXgakQBiY9vbR26LC4dJuk+hMiaZuZ9tOYWfitmxw9Hd01z2UdedhXepMnkvwokvy3JVa15GZS6eX9LOUzrreH5032Sf1L3rGdiswOemm6KapeZ4IIeR78EVlxtbUlM4+EZnuvScytti3f73g/vcf/N4+9NJy3wmNlphfeMQOT9jggAYljtj0UnPEZhKp2eKIPzj29SoFapTbNiLypy7wWv9sn1WiM14jHsmMJwoYCoGbZqjlAY6jYuDhWxzX1uRSbOu+LmcPAHU1PsBKQCQ1+nXpXkseS+8xkZk3jCXmBVOx8UTkUsW/p35BlCNCyG3xRWVGo8XGK4AwkZm53ku/ger3H/z++YDfaGVGXq3A/MEvPPZS8wt/sMGxlxeJyBywmQjOphOZDQ4jiai3BZr7Ao8yOB0w7vHTH+i8ZPskzYQ+1VTXRV/QqxFhKZ1UU+HIjt4nSbQCNY7YAkX3HUJcrPIjGV6qzEZngLTQXEJw9LVPqtF5w1hYakwlxltP5qoCGJ0hhHxvvoHMCF60Zg1gNf1ff6Ln0o/ff+P3X63IWJmxr3v8wT/w0NfMHLDt5eWopEbWJRojgtB0P54GJY7VFuvqiJe6AOp1uhg2K82EIW2FNkJTF1OpsSKjB9OTpJk9/mByVv1xBdBUbWqrbXpVhCZKk1VqPYrMnByhkeLcYBbu3GuH+0VSRGT0NrsOvI+QvHf4ihBCPp8vLjNR/9k1JvUyXopJS81vAL9f8Ov3ICu/8X9KaB77bcN6u2+Lw0Rk2vjLWGikqkancxo5vtjgWG3Q1AVepX7GBppSkZlIbLq6GUQ1L7CFwPVIYgojNI36zuXzHLrPBqCvoXkCBjGLRMaTmFSqKbvdXtDDaEnNzOj6SyQGOF9kUtEZCg0h5GvzxWVGWJul2a6LfxMFwNXvP/i1/dOLym884B946CMweruWGUkzWZHRQiPdmoWhtmYQnqdqh8O+mxW7WscN7ZzMmFST1M3MMZ3uoOm3CY2SnEMX59HrAHqh+aPFLBIZSafZSJQWmkV4IvOGMDqDU+4hEmPTS1ZivG2az0gdMV1FCLk9vqDM5Hwkic50x84V/94DuH/B7v55IjK/nZeO2vzjf/dY7YG3CjhsgWO1xqEQkWkjMrLU6ELhAzbYYYfn4mkcnbENsCcCOk0DZ72jqYtJj6ah7qWZiMwGh9F7OV4+hwyyZyMz/RnbErv7p7bruYylYz+Dfn4rNbb9nxUbT2J0SGdGaLKJin7lflpsvOfx5Mb7YN4HZnSGEPI9+YIyI0gUxuZizHavXsaJzlS//+BXMa6JEXn5J/4zyItIzeEBP//zCvwvgL+B1RaofgJV9QJsX/CyBY7VDxy2g9AIOvX0hDvssMNjt2VTHYfoTLlOR2ZS7/vjV/08UTZFNKynRaZA3U9GKecesRmJjAyup2mqdubtfV0AdTWukdHj40RRmZTcjLAi48nCJYTGppf0lx4JjY7iaBghIYSQXL6wzOSw8htIR2i21TCir3TBHr+GOpnfhwf8/P9fgf+glZlHtNGdv9FGPypgvQXWP1+xq/Y4bPc4Vmsci1ZqntSddnjuRaZAjU1xwLbadFMFqLSZzV5MamPMPrNe90IzXlpsjyabZhoKhtvzpV5Ijh9JUXFAU3VzUpVvQLnyBcZLM2UzJzKXQO7hSYtNN703jM4QQr4fX1BmaiQnkxzxhslAdMGrqQsctsNAdwdT79J0TXWNAk1ZANvX9tstMF4GjXJdFP01UjQoW/Hw6lwmURd1Hys3en2/RVMdcTxsUGxrbGRaApUakvd6vY26tDKjBwW04+nI+nM3pGAva4cdnh533XQHq2EOJz25pl7az5fFCmOhmWvQc6Mz+pq2sDcaTyY3KpNCy0r0Wc4RGnbzJoTcHl9QZgSZn0nkRpa6u+x4dVSrYRrRw36L5ucgMtJwH7BFrYTmiC3qosBb+YKVJy5GbOoCaMofoycfiVEQIemfN7XPExm359MKL/sNmuqAZtsWHZeqC7ZOLQ0yM0492e/FjnI8pM3GIvPyeAc8rtvo1R6x0My9kuQIjf5y5oRGR2IAP4XkvQfih019iJSA6B+ohkJDCPk+3KjM2JmzdUOUOl5PQCmNTEbX4H0bmZmKzGYiHv37AljbaMxMp6FIXGokojZzjbkVmaiepi5w2G9RlE07Yq+KzIwlZioy8ux2RGP7/tDVAPUiIxNQirxEQuOlyrzPmcQKzRye0KQkRt5H6SWdZorSTrkC4clGVBAcXZdCQwj5OtyozOTipZycRiTjf/6vtUQayonUHLv10XxLJbAWgfFSTAXwVgJNCTTlICp67FyPppFxYZyPpdetwETL/tV20T7s21m7y6J9kkhiAIy22ykabIpJvrORyDxWeQITCc3ilJNmLjoDxPU20ZxKWmTsDyTqnp0rDPZ5c4UmOpYQQr4OX1RmdAQm2iaNTic7s6mmAk1T4FhsRlMR2LqZWkSn/AGUr+OoTEaKSSMS0K4bufGEJoUnMqPPu8Jr2U4XWZbtjNcA+sJjAI7IDNsjgemjM80Wx/0Gh/22TS3tVWrpEeN1KzdRhAZYKDQ50Rl9wdLZbsXF7ouiMRa9/VQBOVdoGJ0hhHwNvpDMRKkmLTE2UlMPi9lU07of8l9HIXTdTKMbcV0E7ERlRk9ejGczsuiozWiAOy+woCXFq5MJIzPtZ3wtmz7dhG2b4hItOWI6J5P0XvIEpkGJ46EdF6euu15L+01bI5MSl7kojffZNRfrtGMv4k0QGaWP5pb6Gql7aqJU0nsKDSGEXD9fSGYsWlz0uikABvwGc9TIt8vDftMVyA6TRuq6mW4kmK5mRhUB66hMh04xWbQUeEyEZq4tioRGfz7Ztt/iBe10AwBQlEUrMYUvMbpmRiIwTV30AtN06avXugD227bXkpaYlNB4ETIkllksrZ3xIii5g+LliIyVoznOFRpCCPl6fGGZAaYFv7Z+5gXJ7tkmMtDUY5HRkZheYuaKgJ0U0yAv5URgahXpGO/I6D5sJQbwU0zyWQG0jf0gNEVZoyzV2DLBeigwddGmlOQ+kbzY7c73vzhCM0vU4KdG57Xrud2vveuf+vC5xb6XEB+mmggh188Ny4wnJzaN5J0jS1OImRGdaephXuuhu/FmXPir5UaKgHVUpiv87W9beJEZR14wk2aK2iKbfbNS437WQWiaLjIDjOUF/bb2gk1dtkW9dQFE8y3NpZSiFFM0zoz9zNlukIrO2IY7SitFtTNRoa93XnRPYa57tb2Od06u0BBCyO1ywzITYQt99R9ynXaSxuUZwC5uUB+H1+vDTzze/8LDz39gi3ZEYFslMuIn8M//739R/Q28bNu0kszNpLt2S9flh25SBBlT+AG/u3GFf+EZOzw13USTj87z2kZfY9+7BcDqfQUAK6Cu2pF5O16Db7s9LzFZ5JzQzIlOKjJjP1f0mUNSUQnvi7Rpp2iMGcHbtoRU92ogT1ZyUk5MSxFCbpcvKDPAOPygkT/wdxhHZ97GI9A+om3QHwE8YDTh5OP9L9z9fO6nhwR03ci499EBW9Q/C+x+Po9mzdaDyOmU1X/wT/wH/8J/8E88dFKj5ebp8Q4vD7+Ax9V8l2bv6/Dei0jIV6UjNiWAMkhnedfMlRktL1Zk7L6UvKSYbZvnamds5CT13kpOJDXnsHTMmDmhWRKdYSSHEHLd3LjMeKkmTdSa6SLgZ7Ryo3rYlBhFZEZCU1V4qH6j+GfdywygZoNW3bOlvmaDw6jbshYaPQaLiMy/O6nRkZk/h1/Ya5HRr6Uys0cXfcEgMlpmdI+nKJtxqsxYoclZ6ihSROo3OTxPhCZlPlZS7L45qfGuNfdBUvsjqbmE0DA6Qwi5TW5cZk5F1y9IqqlsozMiMp7QVO3rpfofPJQ1yr8adcWhlkZSR+1s2Fvs8DSKwNgB96Qbs4iMmnt7iMw8/AIe1r7EeBEN+3G9r8AiqSZbsGzba7tuhWOJ0KTWvfdReslu93pvLSaqb5lLJUXrueQ8cG7B76XuzegMIeR6+QIyE0VnUn/UpfF57s5dYxSdKTFEaR4wmj1bZtTel//AQ9kAP3VUpux7NB36RNQGd3hOjo4r663A/KNPM0mq6eE/v/H68HN4nlQPoJzIDNRxWlqAqcjo81PLOaGB84xz0pKTZrJtr5UYve5ew0s3eQfq4l15n4rEROu5shGFxCznRl8oKYSQ2+cLyIxH1A1bKDFujFR0pl6NIzO6dkbJDMoVHvEv1P8qUP9V9FEWeT1j161v+zSTNwGjnr9oJDBdVObhv7/bOpmH7hlS47LkFgDbbbrh99JNMMuciEzuthyByRGblMTo9cWBCyss9r3dFkVlUsKQ02vJXi91/rnpJKabCCG3xReRGS0vugWTfcC4VZOXSIxEZ0oAd4PQiMw8qOUoarHCHv/Af+oSzT/LfiRgicrIcovjZNJFO2JugwL/19XI/F8Xofnz33vs//0P4GE1yExujx9NSmZsI6+/Oq/XUyQZS9/PbUNw3By63gfO+uyJwtL6GH2DVN3MOaTE5pwITG50hlEcQsh18kVkBojHnbEtmfxB1iLTRWX6Y3fjXk2uyMhlV3ip/wf/qQscf29w3G66epk2OvOMO9zhWUVlNvCG/W9Q9L2WJLXURmRWwL8xRGUeMU7LnNI1226bi3TkRlaQONbbl9oWPWP0rDWmImPPDaUm6tnk3SySGisySyMbS0XB+zBLIjCMzhBCvg5fSGaAsdDIupYY+z9vLTLP5lq7Id3kSQwwaqxf9z/x3/0Gh98bHP7a4gm7/mXTTADc6Iz0Wvrz8KutkXnA9OWJSyoyE7VJczLjfMasF2bWo3unZGXuGTVeRGbpNUY4Ayz2271t+kYvwfaIuTFlPC4VgWF0hhByu3wxmRGkJYvkRpYywJ5ujUVq1u0w/DpCE4lNf/oa+/of7XD+vws03QSSVmaiSRmf/77D48Mv4KEaIjEPmNbLpCTGtpe663Vltntfm7ctekVdp3OjLfb9XKAgxdJAwsliMyc1l4hmnCI1EedEWBidIYTcBl9UZixWbl7Q9l4SRGrktR6OjRrwsAh3hZfHOzyVNYq/Wl2xqaX2iTqhaYp+XqNeZHR3cN0N28pMKjpixavCWGxsgW/qq0vdw37FXkQEwb5LRE+iKE/uZyOEEHLzfBOZ8RCBkSiNfqn/bc+NWKtffVpqjf3jrp2g8SewxcGVl8mkjCIyD5gOjKelxkvjpGTGikylzp1jSXrHk5OouDiSnlxyU1by84rO78mYuLPHi5hEA+RdM6dGXphqIoRcF99IZnR0Judjv6CfUdumdlIiI0tUeOomZmyqQGD2G4wmZdQiI0tPZuTjpGpVUiKjpy+AOj7CS1/Zc1Ny4rWZl/jNi+TF3k8+sz7PLQbO6vb0QVAWCCEkly8sM17djN6nozA296IjNmtfYqzcaJF5bO/yWu7wBPQCMzur9AOmIqPXH83x9qNALXVdTyrVBHMeMP2tiOpdNDk1LUuZEyxZptb1qMayfbGzeAdeg/AA1/MchBDyeXxBmUnN1xSFD7xcjfRicepmIolRItOur/CKHfZ1Aey3U4Gx9S85KSY9XUHUiMv9rbwAY7Hx0j/e15RK5USZilSUJmLpb2NK7EoMc1DppZUa1wVEgjW3Eik55zmZPiKE3CZfUGY8IsHRdTP6vamOTaWXPInR77EC9lVaYuS9FpcHzKeZUstUVEanXaKaFo8oCHCKoCxJc3n7U2k2eVmBsVJ3dlAjGjSPEELIR/LFZcaLxHh5GC0xNjrzNo6oRDIzkRhFibTEyHoUkfFkRn8ULw0UNeA2WmPTLuf8RswJSpm5z3vv4UXM7Pcrxc72M8N5P1sEfIuy8l7PzCgOIeR6+OIyo0nVzqzNuh5/5hnAbtw42jRTTlQhGh9Gr+fIjI5EILHuRWKs1OhUy6m/CbnCUi7cNnevOZGR78ITzonEpJhrsG9RcAgh5GvxRWUmmqvJbtfiotdlv+miLVIgDWRUj+I18J7ARJGZVJQmkhfvfZRe0tvmhGZpmiglK7nLnPunojGCFrbUa/S9lUgLzDVFIz5CpC6SjyOEkHfli8rMEtbOujUUtalSyyp4XwG4N/u10MAsdcRHF7BGcqTPj9a9GpqUuEQNPcyxpwrKOULjkUrXWbFJfYduqimaBkNLsewDpl9yRK4Y5AiTd533EC2KDCHk+vmiMqMFpVy43a6vh8Ot0Oj33uu+e4nIAFPJsJGSqOGNUiORvOilJzJzEQv9GXHiempbapmDF+HS36UWtkhkKrXUotk/tB4xGoglBpjWkKzN8bmicWpayzvPHjtXsHxNUSdCCMnni8rMKVi5WWPUEnuNfe5LN6pWavR6VN9hmRMYWRdpmRMb/fkqs5x72e8GGftSy1y0wNh1WyOk0d9FbY51ozNWWICpBOiT9bonMbnRGY9zRObSUHwIIdfDN5cZL9eSODSKYOTIjBYZYNrTRo71IjSpn5InMvLeSoxdjz6bTZ3NRW6Wyk1qW/T5LLZXWW3eeyJovxMvpTeJzpSIIzFCjrBosTlFaM4VGXYjJ4R8Xb65zADpln0NYJWWlzmhuX8DyhrYO+Pc6PbE63mUEppUIbDerqMzS6RGan6suNhtVmrsdSbb37r17qG7KR9+dMuI17oYb9hvgf0qMT+W8/k8ganMMX26KYrO6ItZvEhMlGJaIjRLRIYQQr4flBkAo9qYEeWw8BpuG7lwi4AP+FE2eO3vo9ACA6RH7PWI2lm8AVj5AmO3RZGmU9ZH1+skrmzwo2xQdLJSdBJT9u/VvmIQmqYZy0tjZObpcddODyFSoyVGS43+zPfO9+DVDmUHLbyu/rZr/xKWyEl07KmFwZ+RqiKEkMtAmRmxNq+OnAbfFZkXrKsjirLGEfCFBkiPhbKYNwyNKnyB8fDSS7kptGoqLkVZo+zXGxRFg6JrHEt08jJZqsazABr1wE0xyEyDApvtEcfDZpCasppGZh7hp5Wk8He2RmnuBzHX2IvQiOCcMtDcEjnJPZaSQgj5WlBmkjg9mazUhI3/G35UR+zun1CUDcqywRMSQpO6T/ZPSURG1p3JLPUrdd+RlMERNQDVC35UR2yqgysuJRoU/Wt4D0wFRgtOg0Fc6m59JDYocMAGz9sdNtsjnv++w1PZtLOQl2s1c7n6rPeYykyFsehNojOSanrO/P6tvOQeH7FEOk6NyJwC01uEkOviG8mM1+Xa9lrSL9mOWGCSUYwDNtUBm+2xbbC7VJIrNLW5licxUf1Hj4jMS3BCBtHnuTfL6g0/7p+wqQ7Y3T9jUxxccdngOJEaIC0ygghNDR2RKft9R2ywxRFP2KH4WaMoGxz2G+zLXRuleTTfkxYaSefpZVgIDMQTl1pSjfw50Zm5e1yr9BBCyMfwjWTmTLw6ljBi84YfZYNtdcQWh7aRLoCmLLCpDtOUk7QvqYJbKzg68gBgfl6h4POkBEa/+m17rLuI02Z7xA5P2OCALY6TCIyWmy2OAPJEBpjKjBYZAHjCbvx5tt15ddk1zdUgLvLs+r1XaO1GZ5b+E7HRGS/6EvVsuuR8RxQUQsj3gTIzx1ydiV0vAZT1kHLpGvQSDTbbY3/4SGi0zES9pWRZq/sIo+7EwKghmxUvTIXGisw9gPtxNGZXPOEOT9iiFRorLyI0GwxRGyCukymNzNTdtwYMAmOXEzqheQLwUhdAtZ4XF5tyclNNS/6ZaIGxQhMNpBcJzcRYcVnhScG6GkLI7UCZWYqX/plEbBpsq0NXO9Jgi8Nw/HZYHQlNifF4M9GrxjhFAjjRmTVGkZocofFqZO4B/AZw/9LX/9z9fMYOT9ihlZkdno3M+JEZT2ja9aHRFKHR0RgtMF79jKXZlmjqAk1d4PW+bHs6pb7PrOJrm2bKSSddSmhymIv+EELI14Yyk4P3LdltqhHse/Sg7l6yXrTLskBZNmjKBqiOg9AkewxhLB979Qyjtsw0bKnUWKonVv96wfr+uRWZ7SAvrcwM65G8zMvMVGwadXb7vnAFR1Oru+Fnu+2xLoD7ypdELzrjpfL6VJOdqiAiJTD6PTCtoUlFaD4jOkMIIbcBZSaXSGgmr7c+xbTFsa8lkZoRAECBviD4sN8OQlOtfYmxaaY9plIzis6U0+cLn9fcZyQzbyOR+YU/vcxIVKbdlheZGT7+OKUU1cuIxOh0k5YcfbyusWlQoKkKrKsjXqotUK2mEmOjXcm6GSAWGCsZsm1OaKC2nSI0hBBCBMrMEua+rb5epu5TTJJmOmCLEg0aERslNHVdtCknKzNRNKE7b5Ii8YqBc1NMsq6KfX/cP01ERuTlDk/4hcdFkRkhR2a8aIzepqmV/DRdN+q6KLCtNnipNkBVpSMzcxGaXhCjkXyHJxm2nSM07wVliBDyNaHMLCUV7QCAbkyZIcVUT+pm+ga5AIqfDY6HDQBgX+2A+9U4LeJFEaI6D9tW5URkvHRT12tJi4yOyvzCH9wpsfmFPyNxaW8Zp5k8bLdsHZWZRl5K2HFnGpS4w5O66zOanyUO+y1eRGZ0ui4SmjDVBKTTSyIpev1UocmJzlj5OTVys1SgmN4ihFwf30hmpLG4IIHQFKU0t20UZoMjjtigQI1WWzZAJzcHAJuuKHhfHdJRBJGZrLSIs89ui9IuFbCujrj7+awkZpCXeyU3uWmm9tbp+ZcELTHAvNzsRvU1ZZ9qOmKDbXXAS/WSTuHpsWi87wzAtFdTNAfTqULjXec9umwTQsjX4xvJzDtRjtd/mC7Zuvh3i2MrLwC00DRoUGxrPFW7aY1HKs0UjlyLsbzAOc6TmL5Wpo3K7ExdjBYZSTFJ1EYX986lmeaw0mLf2+JgHZU5YtNv2+CITXXEj+o4pPBsai3n5QY8UvMyAfMCk+rlJEThNqaLCCFE801lRhqRu/MvlWhXvF47wyBxUvtxwLHrr72tDngp74ByHaeAdAQhrPFAfkNtG/cuKiMD4m1xwAYH3PW9l55H6aY25fQUppWs5AjJ7tVGA0VkDtj063d4Qo0CG2yw7QTm2EXBDthiiwOecIdN0Y7E3I4MvFr2vSyKzmgpAZYJjH2fO/s2IzaEEAJ8W5kRpCFZY5xrqLt9NVCv2xoV7/WIVgLU8rXa4OnxDpu/2sZ020dfxo2x7akzIUoNWYmxUZq53jlRYz1Kk9W9eAwVKraruS5wTo/+216+fS8RlShSYwt89fsl0R3B/X5rZ/2kYIeXtqyd7ecIjZduilJQNmqTU1ezVIgoUISQ6+OLyszS+hhpIOyru474zYzItMs1jtUWz9UO5bZt6NOFrIPY1HXXcEcRFt0VOyUxUNv0MhXB6dffulUrJZ7U1Nh00zXMDZYn7z258MaNGR5vOl9TDqM5neqZX3MtNGdncDxJmbv5ewoNIYR8fb6ozGh0C28lR1ovb5tq2URkrNAEYvP6uMNT2U5fIHMIRQO/Rd2NAcQpIf2xoijNkpqQ0avuemPFLxk/Z4sj2iLnw6zEDJGZQU7kc+vB8tq01Kb/biRNJfvC72qOejWNyERt/skucEpaSZMrNHNEomP35V6DEEKum28gMxHeSKy6elYiM2/tkPieyDxikAgtNOUKL9UGz3/fYftzSDMJOjrTvnd+DFEayJMaKzgwxy1IMaErYLak5KaAP0P2XGSmwCB07WM0XR0MAGx68WmlpkaDAu1YPcM1Uqmn2RRTivC41FxNnnwseW8fIBKaS/d4YqqJEHLbfEOZ0b1N5H2JcarJRmfWfiQmSjntATxWbXSmOqIuptGEVAHsBC0mtgtxKjKTG52BWcdQsDt9Dds3XXGwFRk9uaQt460ngiLRmLHwydQP8ix1F6XRx3giY1NRTXNiJOckUvJhyYnYnCM0l4jOEELIbfBNZEaHLDS2cdC5hxLt/Mt3mMiMjcpYoem2v+7bYmDc5z3lpLbDRmJS9TL2Y0Ypqhmh0fNKSY2MfnnbC9STImARHF0ZBKAXkjIQFEkzNd15Ijs6IuN19fa6aw/fqxEarwD4ZPTPLJIPuy8VhdGiEaWcvGd4DzlJXZfRGULI9fCFZWauCDinPkF6Nb0NNRdzqabR+rrtEtxRmvSNTudMUjup9FKqJxMwHiHYG6cmkb4qygZFMZ4Q0uu5tFHLYf4pXTujBafBtmnTbXVRKCkZ1w2J2PQTcqr0Unt83e9ZjK2R0e9PKvyVLzDqnq1//5ZGYcrEed59NJFkUD4IIV+XLywzlig6A4xTTXKsbK8BPAPYDZIQFQFbqSkBVFscO1FpumVRjlvOomym0QMgFpm5yExKYPRXEKaYhsjHdKwYXSdzmKSVZJuOzpRNg83+BU35A0Xd9N+DiE3Rl/oW/X1kHitZnlL86/Z+ukgAYwXgTb2P5EO2eRGZU4QGyEs36eeKPrDdR9khhNwu30hmgKnQ2HA+MG0cJNWEYc6kKNXkpp9WeMUOKOt2ZuyyQVMWncCUvdj0QuN1Iz4nMpMRjRm2vQ3Po6Rl/L7uIjCt8GwdkdEpp+3hiKJ+xfYA1PUrAKApX0diIzVFQ4ppiMroZYQenNDiSmLE2dGZVGrJRmn0eiQ0SKxHzxNFbN4DChAh5Dr4ZjIj2P8la6nR0RkdmelqZ0RWHtUpcxGQeoVhVN81Xss3vJZ1LzZN2aAoa7zWRXusJhWZSR1nIzTRtnsMM2VXB2yr4yg9JIW+WmDGwuKLzLY5oKgbbPavKBtgVQPrGngrgbJpxabR31E/92YBLVHCuDdYOSpJPmDTlya3IwFvhmkN6gKv+820ptvpgd//2LPQ0ZlUga/dNhd5yVm3EpErFXOFwLlTKhBCyHXxxWXGq5uRMIb9Iw6zTY556V7P7bZ6NU4jyXoKHTkpgXZY/fVIbJrqCOy3fgNrRQXBPnnZ3kyRzNyP38s0BiIvEnWJBeaYJzJ7QNxk1X0Xw1f2OnxNReFGWaTGZjw/07Q8uRWbrZKcLQ77bRvtkshZrZaR1NhlSKqb9hye0Mg6kBedSYlGlH66dDSF0RlCyOfzxWUml6hgshMYrLtXib52RkdmtNx40RmRC90bqV+2YvMKDEXGXvvkyUzZXVPvz5WZEkZmXrCtDqO0kRYWHYHZdFGaIeXki8z60D2LLOU5JVLTRWkOSmhQACUaHDEsgfHYPI2Jwgzr2355xBZN00Zm+nGCThWYZHBipQ7y0CkkSyQx+kGWRmfeo3ZmLjpDoSGEfC7fQGZypzawf4x1w6CjM53YaJmRpU0vCVYuarOsANTradRA0CIjdTtQ50+uBV9ivBRUl2b6UbUzTG8wvDyBkTiIjP6bJTJaZkzgZQVgi0FohvqZGjISMPpTdUdvX2pkecAGx/1mmmJKRWXOyqbkSo1XCAz40cGceplT0kBz8kE5IYTcFt9AZjyi/34L0uDIH3WRGIyXOsWU+iZtnYoIia5/kTbJExm91LKyx1SM5PyUxMiyj8y8YFMdsCkOowJfm2qK0kxZIrNXz4/xZ18BaB3mtZ1KofALevUQfDql5C0blEOKyZMY+15zltjMSQ0QjzUjpKIzsj/V/VqOz01J5QgRozOEkOvlm8hMzpgzGh2REZF57vbp6wQRGouVGRES/dKNvNeYeufIY1qpkefQhcmRzHRRmd39cz/fkh47xkpLG7UZJpfsU06RyOzVexEwJ8pUdt9bUU9lxs5fpYfrs8s+1XTYDCmmnFqZuejM4gDInNTYFJEmJS5eqonRGULI9+abyAzgC43+Xyvg1xpokXGo13ERsFzuHsvSGd5j6PVIavS1PZHx1qsX7O6fsCkOKp0kqaVxqkliIrpLdomm735dNphGZOx7+Sw6OoW2hqYE+hoZrxDY1stEtTMHbKa9mHJEZo7o1ySJ9HrS0RJLJA3nFO++R3RmDgoQIeRz+EYyA4zrD3TLWpvtUNujP85SR/M/g9BodAMpkReJhtTOMRGeyGh50dex6ak5mbl/w/r+GZtqmAW7fUkRsB1TZhqx2WAYR2a1x1hi5L1EaFKZlaJLN9WvKLbTL2Xo0TSWmEMnMdJFe5Ji0r2YUmID5P085Lv1CM/NST155HSjTqWWPFJFxHNcQngIIeTyfDOZ8ZAGYYlV6HMB4H+A/Tpdg6FF5l5t15e24uLd3vpXtPTkxbz/cf+EbVcroyeN3JhUU1wr01Wv1F2Xax2ZsSKji4A1FdoxZrpnKhI/Bju+jMz8JALTp5p0islKTI7IeJExK4opklLzFu0MkJudM1FkrrTkRIJYO0MIuT6+ucxImEMLjbwH4okBBUlDdd2269UQgdCRGi0w3iVKs+6JjN5em2O8tiWKzJRvQDdg3+7+Cfc//2CHZ/V6whZWbg7Y4Wk0/kz7OqJsmrZOxkqLiI0nDd5n7GjKYTwZibYcJ4q1xTN2qsJng2fcdU+6wWG/wevjbjrlRE6ayZNC+33r79i+9yJnI+bGppmTiZQs2OhMFIU5Jzpjz/fQqTSKDSHk/fmmMpMqCNYtl52rSc6R/XcYCoRLjOZvslghKZ1X6hx7roeN7JQAqkFeZEbsbXVAUTa42z7hF1qZ+YU/3foT7vv1Vm52eMYGx345eu1fYoGxz1aYZYm2X3bVLl+2wLFaj6TliC2euqewrz/4Ndn2fNhh/7gDHrvBDfXLyk0UpUl9r9E2KzJ62+S6Vmhs1EWYCxHNzaZ9Crl1OrnRIYoNIeT9+aYy46EFx/tjriM2NueQGIMmJR7eS3fVjo63+0frXRqjrPGjOrazYJc1yrLBpjqiKJq+BuYX/uAOT/iFR+zwpN5PBUe/tl2kZtscsLUD44nUAJMxZUbPq4WmEJH5gUPRxoWGu9117+/61x/cq/Vf3foOf5pf+PPwC3isfImxQjMXpbHPHL3XPxMvMuNKjQiNnbcpYmmq6JzoTK7QLMUTm1PGgCKEkDGUmUkkJurxZFu4bvLJyRg0JYDVMK6KEMmLFRkvpWHTRcBIWtDNQv2jn5W7FZityEwhxbxqAkgcO1V47oXFisygE+1LojH9+v5lWvSrozPe16Yb/m27fKva9NJhq1NJkj7azbyU0DzeteklKzJeVMbW0HhEAhOJjHxe67r6/eRednZte5C3TV84JzpzblopIjc645EjMfZYSg0hxOcby4xtPKzQ5Iba9bQHsrwb18/ow1MSY1+2eLcEdM2LpI0k8gKIyLQCI1MNFPDX77oUkpYZ+/7O1NO0kZnjEJWxXbCt1Nivy34PBVAXkl7S1TpteskuvRTTc056KRKalHh5z51a2nOiKE+/zUZn5tJK+maXmD37EtGZc4RmKSwuJoT4fGOZsaQiNKk/oFpkoK7RCY1OOT3CF5m9ep9qUKuXicTY6EvZ9zg6BOvH/ripvDyPojGSfpLXqChYojIS4dARmSi9BLTpJZViequG9JIMetcW9w6FvU/ua9Cvp2aHp8fdNL3kpZq0xHhTR0Q+YUXMW5fzU/U37n1s8XkkLOcMdHdKuikXCg0h5HP55jJjpcUr/p1DiwzM9ZTQSMNnhWaPqeB4DWoJoGywro4TiZFIiycwG5Na0ut3fVzjabTuic2w/xm7w9MQlZGXOrDncQAAKxRJREFUfH2NWk+1b53U1AXQlMOYMToqIxEZEZxn7PDYVfLY9NLLw69YZLyojBax6Meqa168qJK3bx+s23uNojNyUCpdFP1iRBEYb99S6ciNzpxy7XOg0BBCxnxzmQF8oQHG/9WOzvP22VqAGaHRw/t7RagqvfRD9UTabI8jiRnGhDmMIjBWYLToeDKzxcGIzHO3/ThEZ/avQ62MSIGWGG85+izD+6ZEX/Q79GAa+ksNURjVY0ktH//+FaeXtMREUpOKhNl/HZOUn3Os/jnqddlnBad/kxrvaEnjndud+9TozLWIxLU8ByHkGqDMAPALf/Ufd9vA2OOjuZuEu3YAN09iZFh/M7z/iBJAWWNjRGaD6VKLjScw4zTTs0ohHScRGru+wRG7v/fTcWUO6lmTxa768wBvJdCUP/r0kh7dRkaykSLfcXqpq5mJ0ksPGNfKRBLjFetGeCLjiU0q2qPvMZKZFca/Z3o9injYxjx3gLucCMqp0ZyPjM4QQsgAZaYn6iqa+h+znPMEYNcd86z26eiNGsTNSozd5kY1mnZG6bJxReaui6ro7VZobNRGRKUdFO95IjVbpRhbHKfppdz6EGCok9kOy8O27cE0DIw3pJOejbxMey/t8OfhV9t76QFjifGiM6lUEzCtjdHkiIw+T36GntTYaM7ku5MojRw4l3LyLnJudGYOppsIIdcFZSaJ17rZ0YKlNZcGyP43XcagUT2cPIkpMW1odaO43+Kwb+tkDsUWBRrUKFB0s0nLiLmb7s4NCneyRkujZqOW82UWauAImTKgQYHDdoOi3qPKkRjvN+tn9/oL+PuvH/iz/dWPFfMHv/p6mAf8xv/hNx7wj/69vP4Pv9vj/3vf1sk8rFqJeUAsNJ7E6JoZL50kP94lcjP3HehfmfCic43zJeZTWiocucJ06vUJIeQ8KDMTorFmbI8TeW/HCfEKXtSxugGdk5mR1KzQ1EX7Kgscig0KNGgCqWn3FSgyG5VGne9tE6k5VmsU9Ys/SognBXr9J/D2E/jz1xp/il9DhAW/8Kcbd/gRv3qREYH50w/f96sXmf2//zGIjE0vzYmMV5ukn9c6bCQtKZnR17BlWPqci7b51xSp+CihuabPTAj5LCgzLjnTHdgZi0uzLsfKoHpdF+79qt2t62TmZKZ7ve43OHSppqYoR5LRak0bTQHGkZncKM3wCQvn2gWO2AAFUFQNgFesbSNdmPdm+8tP4M9f1SgKM8jMIDRaZGxU5uG/v8cio19RiskbKC9VAJz6V5GSmuELjMXoZgIWl5AERmgIIR8DZSYkisbYY3SqCRgXA8vX26WZUtGZOZmpAdQlmrrAYb/phGZIAQGSHmq3lb3IDOt1IDUiQVaGZFl3BboAUKDBYbvpjnvFWp5dicxb97HbbtdtkS8APG13vbCI0Ixl5tckraRF5vHvX9jb1FLq5UlMrZZRjyuPuaiM3eet18F52e39uXUtSyeuPOUeFhueeg8YnSHku0OZWYQVHGA8loeuedDFwCVGczfp7tiRzNhUyB7AfoXXcoOmbNDUBeqi6PRkXC8jLInIjIVokBuJ+MjyIHfZAiI0ZTkVl6Zsn09kq0HhRmGeZ2SmF5+/f+Hx37+Bf6+Bf2NIK+lXFJXR36Feamw9S0pObLooOt6L0IRtuvRoeo4O+ADeM5LCKA0h5P2gzGShWyXbu0n/rzAqwJBC4JfhfB2d0TIzkhfnVZVD7cy27GtmRBpEamwUJqd+xtbLDPU37RJGl5ptiaY8oKgbV14kouPLTCs0KZnp1//+3YrMw3oqL9G6yIwVmVThctSbSYgkxmYWreBokYlqbHoioTmn4PYSIhFdY0lU5D2FhtEZQr4zlJkkc/M3yTrgj/uhKbttdxhFZ2y6IZlmQh+dkdqZYjttHKy4ROklD11M3F6rFaY2zXSACE2DAlsc0RQFNsWhl5YDNkPvJ7PuicsT7vr6GVdmtMj8G+3rAdPeS7K0kZmo2BcA8IZREbccY8UDwdJLM0UCA0zb8jDN9BEN82dFSj4i7UQI+W5QZhZhhUbwGh9v0j7ppm3STamaGRupqdDXzgzRmSEaoutegLyIjP9JC0gSq+7kRgtN0d1rg0MvK4PQbJ1tvsx4QqNrZPrU0gMGkdFppsfEelTg20sMMKQHV8NurzdTtIxkJpKb2ciMx7m1MqeSe61T7vkeMsXoDCHfFcrMLKn5m/QxFlv5aUQGJUYza3syYwtX5X21wmtdoK4LNE3RFQIXvXQAQ0QlFZGxaSQRlHGdTDE6p5WTYVJLqcsZxh7e9Oklu81GYLTMPOAfeMZdP47Mw39/t8W+nsg8II7G6G0j3szPQtZlqaIz3r+KSGRS22x0JpKkJJcu0L2mBp9RGkLIZaDMZJEjNBabTxCR0SMDm+jMI8aFwY9I/G++6geYLf4aC0uDoo+YSDfrIXpT9gJit03rXTaj6Q8A9FeTo9pP1/TSIqklLTISofHGlPF6M03GkYlSS95L5A/AWGCAcSO+sAH1fuTRr8FF2+ZoDrDohi/Bempb6t6EEHL9UGZORnfZnkMKhqVhErHp0hvS+KYkRtdX9AGFCvu6QFOXON5v0GyHsWG22ODY68gwncHGaIYc8YQ7E0sZ5nICMJGXQskNgNGZkmoaX23bT0VgB8vr00+HX3h63LUj+/470f06EpjR2DE2laR/bjDb5ctdTWtsUr2evPoY2907Wtr1EZGE6N+h1Oex66nv4QzBc+9FCCEfD2Umm2gyyugrtI2MtHbd4Hn9Uk1zkIzEqMvqhrFe46Uu8acu0NwXqH+2MjOVk0FtItFp13dqbqd2XidgkJZ2fZAa2edPezl+gn6CyG6Ky5HY/Pe+nf36oZr2TtIvT2TcqIyXTtI/jwVE6SJbMKyPn1suEpnouCUig4Xb7HMwFUQIuV4oMxfF680kIqPHotGjAgPAbqiH0UIj2PIbvV4DqFd4rX/isaujOd5vux5Gg7DoKIyWmucu+qLl5xl3fQTn2dTcjKVmWPfSSlZotMQ8ddNZPv7dRmPaCSNX0x5Kel1vk+/KRmUAtFEZqVG6EKn6F/mZaKnJjcyMHMFKyBKxia5ht0cCdCqMyhBCPh/KzCKiaQ50ugKY1svYAmCJ0AhdUbCIjCz1JQX7v/vRa0g7basNjtURm2KTlJp2Fu02paQjM3p7CpkDappWmgqNzID9hDs8H3ZDSulx5RfxPjjbbBRGr/df+3P3yokmOL1qcoMQWkZOEZkRb3aDOtCmmCJ5OVVkGJUhhNw2lJnFpIqB5Y++HlRPkOhMFC24A7AbeuFkpZi8V5t2eqk2WFfHpNRIBKbArk8pScqpUPUyOYispITmCTs8/303RGKsxHivh2B7OMeSNPipyIb9+dlJQ53vNRWVsfsws5x1g5xoR0pq5qIvlxIZRmUIIdcBZeYkUjNr63X7X3E9EaX31a+Bej2OzOjT3RST89qvgKrCS1XhpXrBj+qITXXA7v55JDVSyCsRGJ1yKgOZsV215b0nMM/YtdsPGxz2m7YmZr9NS8w+2G5fkpYbpZYk4vUM4Mn/fgGkBzcMiKTFq6FJRWTsNevkhkxyRCbnul+hp9M1dT0nhHwUlJmT0SP/ev9Vt0ITCYxGjT/zmDhsJC7O6x5tz6gKQLXGa7XGvtrguN9iUx2wrY7YbI99BEanlqTXkkRpBDvVQfsYMp+T7YrtCMx+NS3WTRXyzq2P2v03DGklERmRx0hWMiVGMxeZscfCbE9GZ7wISZRi0lGZJamlnONTz5Q6lhBCPg/KzEXxZtoWdJ2MTjfV6lzhV9v4J6MvGMTF21/Zl5KaLlJzrNrZt4ui6aMxIjQiN4AvL3piSgB4anY47jc47Ld42W8GgbEpISsmS5ayPpIY+V5f1FJ/19o8BC2bNloWfO+RvHjrqdTSRctPPlpkKDGEkOuEMnMxUhEZYNzAPAHYYVw/IzU1EjH4NY3Q6Mb1PngET2ZGwrPGa11iXxc47retzJQ1ttWxF5sCdV8cDIxlpkGJpulEpptSoe6u9brfAI/raaTIExpPUlLHumPI6CjFM/yoxVwEZo3x956BJzeyLstUZCZ6P78j4/icXkunRmTeg4+8FyHkq0KZOZu5+hl5X5p1aUSezXFPGLpvqy7bundTquzG2z45bgWUBV7VlsMeKGXyyrJAUwyD5InAaHkBgKabI+q1LoDHKl9icqQlFBnAT7t4yMCEsi5fxtrsV1EZYP47lkewEpO77r0fYaN6KebEZGnPpI9MLb2HyDB6RMh3hDJzUXQLZ1NOXl5CnweMx59ZD9fYI41tfFMyI7ffr4GyxGtZ47Vs0JQFmi5Ko6UGGEdgRgJTF+11dNrrFEmJtrkiI2PIzImM/tBWaqzYGBmdlcEOG3yTbbYmxq5fpA23jfbc6L72QXL2E0LIbUCZeVe00EiDmWp8dS2NbmCN0HjykvpJ6qjOXm9b9TtfAbzWxURqAAxpJBGYupzW9MxJTCQr0fYagchIoa8WGdso6x4tnsiUZp+JygDz36nG+5HOSc3J6DSa9/lT2+Dsi46ZO/4c3kuaGJUh5LtCmbkIOq1kC051oekcMsVBiXHX4q6x3SNPXKLIgpWaEkC5Aso2SoOynkgNgLag15MYKx1LU0upKIwsJ8W+kch4328kMnb7yv++7HpETuTlQ4IeOTdmjQwh5OtBmXkXvB40wLzQSK2Mxy8ApiA4itB48qIFRtfg9I9rpKYu8NrJDPZb9PNHWYmJUkynSIy9bo+NyNjvKBIZvW5FRqeazPeUE5XRz+cdW5vXRcipidHbvktEBmBUhpDvDWXmYngjAwPjYk6vYlTjTXWguUOfcvKkxeKlTKzY6H1aaoBWbAD0Y8R46Z9U2iha987T23tSvZYibH1SJDIqtaRPjb4zjXXVd6mJWYqNVJ3bY+mWRIYQ8t2hzFwUr2eTtHx21NnU/5rvkJQaGSVYkPUosqC32d5R+hFLjBvq0qSUUnUtWlhyozL2/B5PYmSZU/SrP9wCkdHfVS62vttGZKLXLN4I0vbGUQRG89nRmLn7XQJGZQj57lBmLk5KaGQ9OqfGuFbmDtOu22ivVZsamlSqyXvpxnciMWpbJDJaRuaiMVZovFQVgHmJAcaNOBDPk2X3CYmITCSAgP8vxfvucjkrghM13qnCYL19yTXPhSJDCHl/KDPvgu5JI9RqmxetEe7MdhtCkcjCzkQzAlKyY6MxXuOckhkrKanIjCyTxb3RwHe1s26x4pKqk8n4fubwvjPZfvFambkHAfJ6OHm8pwwwtUQI+RgoM+9KakA9LTS6+7YMqqfXdcsoDfobJhGGOVLRm7ml95viRTOQ2Gb3923dyuxIYbtdy3nrYFuQWsqRvNR3lFrXeIXCXoRstDGa70vW7QjT9ndHv5/Dm3zzUlgRJ4SQ94Ey8+7kDKkfnec1VLpuZJ3fRkQ/6TmRscd6YpMjL6koUPIBAb9hLtU+2zPpTInxjkt9Vg9vn/aPicwIIio60lKq97IuN8iRkIk9Oni/o5cUHPfDEkLIRaDMfCheNEa/twLj/Y87uGwqteHJx7mRmajxT6171x5FZ+Z+HdWoyP17KzB63amNqcyzz4mM98z2mh51Yr+Vmhrds+qISxS9k302FZnzu7JUKCIJP1VyLi00rJchhLRQZj6EVHRGN1i2UYoEp0bymqn2Ioqq5IjMkshMdK48nxud0UITpU30iXKcRGHkRkGBrycy1cyzpkTG3sMTGG+bF6Ua7bBRlxrj3xNNKp2UimidIxV23rElMEJDCLk8lJlPI+r1ZFNKXmNuTjkl1TQXmbFtnhdhyREX/T663+j5cyI0gD+ab6KXkiczFfK+g9x/Jd53Bmebfk0+uxZWHZ2xERrbddv+nix92FN5z5obQgjJgzLz4eiogk03yTYbiQHGPyqnV4+VmtzoTE4jnisx3vn2fQ3/+pNG3d4YmBZIyzEzEnOqzHiPkMJ20fa6bGs3caVGb9RSqyUnEl1NbgQk+oBLJYdSQwj5PCgzV4M0Snrdzvlk/zfu9GiK2qBcaYka8SUSE72POuiEQQL5bGu0n9U+0Mx4MZG8pGQGwbY5bMpoaZRm9Nm9dKI+KRIGT2ByIzXetTzmJOfU+xFCyOlQZj6MaDJKvd32UtENlzQSMg6NajCWRGWAacO9JDrhSYq9tne83p7qdRxixSYjGpOSGb0efQ677j1fVOibitJogZkIjf5cEoGyqSYgXexrheKSgpHzw5q7X27UiBBC8qDMfBpWaIBx2gBmXctN7ezH6RKj940aXCUNqciLlRW7bU6O9HpWG5fZ1XqJzNhnid5baYwiLtF++4z6nEmqSReBa4nRNTVWbiLOKdr1mJMaRmgIIR8HZeZDyR1zxmugpLXTItNdz3bNltcebaMdvde49R2rYZ+9dVR4nJILocIwerE8j342+1wWKwVz3a2jY3SaySMVbYkiSpGgRMdaqQEwLQSG817Ljlw4WrecMu4R4MtJSmreW2goTISQFsrMp+L9193+D9o2FjrNVAN4a6VDC0uJQQ70tAKlWbeRFTjbakynMYjkKVWvk5IqXW+SSuXYa17qlUsYSUlsy7lWuE0e0KaabNRmTmgiTi3yBfILjiPhWPJl5T6ThpJDyHeCMnN12O7X+n/msl+iMmokYJEYHenQYmNFZk5o9CPkyIzFyoKNzESRnqh98xp+T0py5WWpzCyVmFRbbfe50RkRFiBONQlzQpMqGM7Fq8uRe+vrfYbQeFByCPlOUGY+HTe/0y31dj2AmkiMk2rSM2lbmdEiA8QNvF7X1/UmitRCYtHXs5GYJSKj0V9NjsikPmfqt/9cYYmO80QmvEYq1SSyIx/inAiNMFe0K+gH9kYjtsekeE+h8XjvaRsIIZ8BZebDWTJXk2007HXMpJM2jWMlxhMZvR5FZrTA2KV9TDlfttvfMC1IcNaXkCst3rpeWrzsn372S7W/VnJG183p1WQHUoyExmNJLc3cKMI5tStzEaKPFBqL92+LEHJLUGauAq/Vt9hCYNnmpJp0NEZLTPQeZh/Muo3EeKkmj0hk9Ec4R2Tscy6RF71N39sTmaVtbRSJWbruiosddwaYF5pU4a5HlFKS6+trRELjfWnXLDSaS/f8IoS8N5SZqyH3D7k0CNokpDFbTSXGCowmJyUjj5Z6RY9v5UGLghUIvZ7zWxnJylz0JUdo7LMsWS4lFBnBm94gSjOdEqGR4+1DabwIoSfXOUKT4pqERmCPKUJuAcrMp7Ak1eShBeYFwHN3vfW0bmbuhZltcrucWhdbC6LXo9SN/Vje+Qi2R+ISiUzqut6zXEpY5L4paXHvYVNN8jO30Zo5obGkIi+CVwejz/WGDbDb7T5vv+VahQag1BByvVBmbgKbUrjDOCqzRis0XXRGp4M8URFypEY/gpcaikRGN9x2m94eMSciOVJzKnMikys3ufuT1/PkBYi78HtCMxd5EbwIjFzHPrQckyM0X4Wv+rkIuX0oM5/GXHTGDkevt+v/oUtURtZ3g8gAvpjA7JuL0ET1LXMNdc623ONz00Xe/ug57XabBpuTkbkAwiWiOD3ebNkpicmJ0KQk59TUksdXSDcJjNIQco1QZq4Sm5Px/mesX89qf5du2mOMFpB7dYscofEafW89JRz2fW6UJXfde+8Rpbfm0mc563Y5J4HZ7bXuyaTTi3NpJu8LSUmyxhOKJSmic6MY1yw0AKM0hFwXlJlPxftfs91mwwW6Xsa+JN0ETIRGThWR8Yb3R7BNkxONyZUae89oX+ra0TZBR1i8aItdj17AvKjkyos+1j7DBKmbsWa5tKHXkiF4YpM7Zsy1y8ZHQKEh5FqgzFwVVlyAcVpB/09ci4tG0lB3AHZ+N+oKeTID5P+GeNew+73jU/ddIjQekbx423JegC8kS5f2vrNIJEav25TQ3AjAXgrJE5u5NMolGvDca9gf9DXKE4WGkGuAMvPpzEVnvHFFJDoj6zrVZP/g3wH1qu2WbRvnOZHISePknKMfKWdSSGSuex8XZl+UVoqOzZGZHLGx+7375DxXj/19yEXLjZzvRVxsoxwV90bP9hEN+tJQISHku0CZuQqiBkrXSegCUK8ewmugpNW8Q592ktGB9YzRS6IzUX2L96qcc+wj2+NzojVe2+q9LxHXDun30TZ57dV2e05OFGZJKstFd9FOFfOWzrpGi40nNUuF5hpSTTl/wt77GRmdIeSzocxcDVpYrMDo9/JH06aY7rpt8odbUk16XRUH627bQCwOCJZ2mxYRkRjdhkTuZUUmkqwo8hNFQqSdlWkd7HbvXO+l03T2Hgj25a5HeF4afvAcubHvtRTLtSOhuRSfKT65oTxCyK1CmblqPKHRWKHRDYas6xa87K53167XK/9ve07Kx74XcRB5kIklgelvmReRsTKTSj3Zxj613GN8vhUae3xuqsk7z14v9X42GjPHXOQFGATGi8hE24DpeDZWem45EuGa4gW45e+EkNuHMnOVSIsL5AlNbY7TA+vpQfXu1LqIjSzRra/8hjYVJdHRGGA8Q7Z1qtQ1o8Lk6L5WMKxkRNGnSGTs8pw0kyaSGbueZIU8gYmERu+L5OXSEZprbuDfS2oIIZ8BZeaqsHPq5AoN1Dn2WC0sWmjk2muMZcauO4Izl/qRY3IlRotMJDP99jegrIG6u2i98uVAIkXCo/OMQhSZsT3B7LEI9kXX9+63iFOFBphGZJZIzhIp+cx00inc2vMSQjwoM1dNSmhk/1KeMR41uMY4UmNHFNYyZMTGkxm55N7ZFqVt9Efx5OccvMhHTjppSYpJLxG8z8XLFCYPtjdcm/dzF5F9nqy8JPbNXW/peYQQcjqUmasjNWie7aZtu2xHURud79HRlxe1TdbtM3jnroddVj68KIyeWkFEJxIc2/ZOvG2ltpman0g8on2XlBm7fg5JV9XRGW+Au3PHh0nJTWofIYR8HpSZm8ATGr2eakWtHGmBiSI92ihsREgfsxpOE0HxxAVqXypKo6XBeyShNr+2OdEXb5t+eYMLpmQGwbaInGMWZzzmpiPwti9Fj2dkWSI3lzI9QgiZQpm5SrxxZ2wIREdUIqHxxMdGZYC0zMi6vY+TerLiok/XsuOJDdS6F52ZPKrTE8uKh9127ktfE8G2U4gkJozQSHTGO+mUiRCtsKTOTe3Tz3Mr0RtKFiFfAcrMTeEJzJzQAL7UyPWAPJnRUmO3m8ktde+huchMFK2BOm/yWGoQuZRwWEnyjp+L0thxZpBYX0J0nv26k0TFwKc8RCTDep8WlCVRmei6hBByGSgzV8uSUYE1OVJjr+Ftj2RG94Ky250IjSy1tMwJjL5Naj3KfFh5sdvPjcwg47187lNwI1H2oBWAtxMuOEdO0W/0xdtrpI4nhJDLQZm5auzkf4KWDtlvG4y56I1tJe29bI8nKzMSpdH36OaBSqWUcupm9GNFQSL9VaSEI4rKeOeeIzMeNsKUwvONWQeJumqfkmKKSEVgbj3CQski5KtAmbkJPKnRLfxco+KJzJzc2POs1NypdT1myW4sLXaZUwCc8idbZ2OJxOgciZm7n4f9l+XJWXROjanEpe41wRtjJoUVlpyi32gbozKEkI+HMnNTpKQGSIuNtgLbWJXONtnuRWDsflU3I8dqeUnVytjt+lGjKI1NNeVEUE4VmDmZibAiYrencOuEEIjQym54B7zfDUZlCCHXA2XmJkmln4SciI2+XjTysBUYYJhCoTTbJC3V1c94QqNfVha86Qf0XE96Cef86OV1u94n9nlypUm1g6ekk3KOTZ63NHSja6L0Nr200Rrv2CX3PIUl117yp4wiQ8hXgzJz00RFwoAvITkD8kXnRg2ATHYp8iRSsx4LihyS8xvnpZnsUh4plULaB6+cCEy0tM+Z+gzR+5xOSEukB0Asrqd2s7ZcuwDkGOa1fwZCyKlQZm6eU4QGGIsLMF9QnEIsQERGBGc9lRmo9ahRP0Vm4KxredkntuUKTS4pkck5Z7HYRDv1B5ibzwtI18rcMh/xeW497UbIbUOZ+RLMCQ2QJzW2piZXanTu6Mm8X/ldtSNOkRmYpaynojKRyETXzSUVgckNdi2KykTds6PpCJZEcD6zVuarCRUh5D2hzHwZojoaISU10SB6qbFuNBKR0REaQfVuslwiMpNaWonJTTXBrOvnShGJTHReKhqjj1ncruf0XkpFai6J9wGWzMJ9C3ylz0LIbUKZ+XLkSg3gR2L0MVERsRex0QJTqmM6IbL1M/Zx9HKpzETXWSIwntDAeR+RIzJLU0kniYw9IXWBUyM1Odf+LlBkCLkGKDNfljmpAfxIjD4n1Vh5M3bL69k/vgbwaE7xHilXZuwjehISFQEvKQa2z2ZZKjL6veeW+rMuwjasuhYmdbGl48qQFooMIdcCZebLY//gptJGVmr08XN/uHWayaaa1DFWaLzUzhKZ0Y9v33syY2XFdvP2rpmTYgKWSY39yq3UvItDpKRmri7m0g33raeabvnZCfl6UGa+HSm5SaWgopRTdN07ZAuNvn2NdiyZJTJjH11v8wp/Pamxz+Cte/ePemql9sv7iwrMkvRSSmpyx5BZ8rDvZmefBEWGkGuDMvPtmRuAT0tMSmi8VjtDaLzUjgyOt0RmPKy8pLplA/6zLCVHdGpn3Xt/MksGS8w5dknjfesRlxRf9XMRcvtQZkhHqnu3oE3CrksrLNeQfXfdvidzLVUUrC8/F5kR0clBS0tqqe8dRWUsnpSk0mA5/9IuFrzI7VLvpRC1jMgH8o7z8mPeNfTx9thbEp9beU5CvieUGaLwhMZrie0klbpR8uZ90ktNCdSrIUIjEiO39CRhSfRCR3rsepRqSkmMfhYE66dGkT6MqHpafxhPaOx2ez1PVICpAN2a0FzzsxFChKv7U0uuFTvZpBUaYNoIAsM8Tjbl9NJtu0M/sF4kLjoiI+tC6jfYpq5S7+Eso2tqcoXGitC7Y1OCXnE3MJaOXKGBOsdef05WcoXmM+tsKDCE3BqUGWKIZua2AmPf5/wPWwuN7v59hz7tFEVmrNBoUr/FUfdrvR1Y3m7aoIZsu6p/Uamfje1+L8foDxMJjb3GnNTkCM01QIkh5Fa5qj+95JqYq6GxQpMaUdaGL4CpRch11lPJKDGVG01udCYSmSR6uoCVf337HLlppg9pzz1xsNvk5xfVy9ho23tEVj4r3USBIeQrQJkhCbyZtKNtuegGUVJNck29XmIyr1OqADjq+hy9QrS8eBKwGu/2SlCsyFxNxMZLNUVio/fZCEwUfUld+5qiMxQYQr4aV/EnllwzKXmxKScPkRZ9rL6WLK3IrNX21VhC7MSVdl3eR4W9I97UAZpUF3QjNPYZTpGXs9v1Oan0ZAIY/yy99TkhAWIZmjs/5xkvKTyUGEK+KpQZshDdUns1NNGxmmdMZUaWWmbWzvbVfJ1L1GGn503tOGW+oUT6zYvWfNi/sqhLdir6ovfnCg2C86Pr22dM1eFcGgoMId8BygzJwNbPWKERtNBEURgrQLKUmbet0HjRGv0sco8gWtJjBSaSmQh9z7f4fjZYcYrYnNW+ezeIxEP/DL2UUko85qI0udIyJzSnCg8lhpDvBGWGZOIJDTCVGtvLycOTGt2IevKjozWR0Hjb5Br6me17i9cQ6me6QzLdBPhtsLetTqzP1vqIoKWmJ9BEkRfdk8nKiO26DXOM7BOitFJuuunax50hhFwjlBlyJlFeJZWCikQnpweVJ0pyf4nuyLZIXuwAf6eg6nn04+n1U6IwtsZnUVAi9Xls5MVb97pmy3WXSM2SOpkcPrNYmBByC1BmyAIi2UgJTE6RsHftqKZGohBWjvR53rX1Uh8XXSeHZ7hCkxKZuehM9N6NyuTiRV5ScnOO1KRSS5GU5MgKhYYQEkOZIQuZi54sERjB1uBE9TZRdyHdyEUpIrvfaxijhjaFRIPMd5Jqd3NlZjY6Y6XOu5gnAVZurAyeIzVzY9XYZ/BgqokQsgzKDDkBT2iskNiu2HN1NN41bDRGamWszOj0EjD9tY5kJ0debEHzXLHqXCGysz3yqlBkbFRmSb2MFQzvPTAvNaleTyl5YXSGEHJ5KDPkRJYIDTCN0qSiNjZNZXs/yfn2HGTsm+s+fk7aSe49IzQ522brZbyojGWJwORGZHJ7Pc3JC8AIDCHkUlBmyBno/50LVmhkfyQ1OfU2usE8V2b0c3nvo8/i8QJg52yfEZpcmXF5Mzu92p8Ul4jIeNsuLSbR9RidIYRMocyQC5DbbTs3zeSJjW20vaiQxhanes+s0YWw9tnlWe6c6zxhsdDkpp8m6PTSqfLibcuNyGg8IVoaCdLPAlBUCCGnQpkhFyKK0pxynShCA8SS8ozlv85etMgrctURmgsIzUnttZ52IepqrvEiG5HQyLWWppmWCA0hhLwflBlyYaLICRCnmlLXyikkthEg7/653cK9e1kiobG1PVKwrKTmZJGx4uKJTKo2SMiJisxtnxOaufueCyM4hJAxlBnyTqS6cHtFt6m6m1TNjZe6mpOl1PGpe+njl2KiNLPYCTC1xETdy1O1QDnHX4priMZcwzMQQj4Kygx5R5YKDRBHSGRfVI8D55hconSTt/2UqICN0qSeQRNFYaL0ktd4p66by2dFQigkhJA8KDPknZlLO3mFpaliXLlWVPB7auM3J0FzBcUa3QMr+ow519DHegKTKy8598yVlZwu1+fAFBIhZDmUGfJBRFEar6u1F6XRx1wiGpMiJ9XkIb2uPHnRtTR2X0RKYE6Vlugec0SS8VF1MoQQEkOZIR9IbnFwdHw0lkzUmyf1DPbYuVRTCp1GsveycjN3vVTqKKfAN/c+lyZXXC6VOqIoEUIGKDPkE5irpQHmR+WdSwktaTCXFP9avEiMlRuRmhezLefa9hnt9o+EAkEIuU4oM+STSHWjBpZFYew1TmlwU/IS7fOkLNVTSw/+513L4716IJ0yVYM+N/Vz+IiRgAkhZIAyQ66AaDReIRKAHMnJwRu3xkZrciI1WmR0dMau55BT3JuKcFmiImTv/NzvkpEaQsh1QJkhV0huDcwlGtLUuDVL01WCJzV2Xd7PMRex8dJcmpx6o3OiNHKP1Pd07n5CCElDmSE3QiqScA6ptJCVGlvvskR2cqM79hyPJZGTS/b8SnUXT41IPNedPFUPRMkhhMxDmSE3xntIzZIRia2URMJwbiO8ZATjc1gqNzkiM9eVfEmvLEIImYcyQ26UuTqbpcwJjd4WCU3urOBzz5EitxD5PckRmZyRh8+ZbuGU74kQ8lWhzJAvwiXkJjXFghaYJUXBcp1zCm3POT71PZyacopGJZ6bfsHbZ7fnwKJjQsgYygz5ouR04fZITbGwVGii6M4pfNbcSJZTRea9xY0Q8p2hzJBvRk5PqWjgvpTAzEVpTk0FfeSYLblzSJ0qMrlRmXPrhShChHw3KDOEhKLhzQl1ia7bc3xWFOaUMXBSNTI5ML1ECDkfygwhANK9pHTqaW7qg5QUXDLtlHPtFEtFzHZLn+uCnROVySkSTh3rwagMId8RygwhIyKpiWbuXlpEe43RhZTY5EhMtD0VtXkPkSGEfFcoM4S4pKRmyczeUS+rS9fCePfW91kqXfZ8Tc4EmJ7ILOm27V1zDkZlCPmuUGYISeKlb1I9nuxxgC8/l0Rfc06qotGNU+fP3TMq4KXIEEI+BsoMIbMsERoExwr2nEvNL2WfQT9HSlyi9JmH7Q2V6oWUM8rvpUY5psgQ8t2hzBCSRW7kRY4VcmcAP4fUJJL6GU6t+7HHp0Tl1H3RcXNQZAghlBlCLkBqmoKU2KSOzSUnChRFY+SYueLfnHFgcqM00fnecSkoMYSQAcoMIdnMdd8WcsTmUuRGgbxojD1GH2ev7ZFT/5ITjVkaraLIEELGUGYIWczcmC5e4/wR/9RS81Pl9rK6lFicEs059V6EkO8OZYaQk1iSPgI+ZqyUuekIcrpwR+R0Kc/tqcR0EiHkslBmCDmbnPmePoK5iNDSLtiplFPqPNbEEEI+FsoMIe/CRzbGS1JeqUjMqT2vTqmtOeU6hBDiQ5kh5OZZMi9Tbs8rZFwzRz44yzUh5P2hzBDyJUj1tLJE4+NE1zwFSgwh5OOgzBDypXgPqVkCU0qEkI+HMkPIl2RJb6vUWDNzsHs1IeTzocwQ8uVZEq0BYkEpE/uWPAchhFwWygwh34alUmM5RWQoMISQ94cyQ8i3Y2mvpXOuTQgh7w9lhpBvT46AaOGhsBBCrgvKDCEkAwoMIeR6+fHZD0AIIYQQcg6UGUIIIYTcNJQZQgghhNw0lBlCCCGE3DSUGUIIIYTcNJQZQgghhNw0lBlCCCGE3DSUGUIIIYTcNJQZQgghhNw0lBlCCCGE3DSUGUIIIYTcNJQZQgghhNw0lBlCCCGE3DSUGUIIIYTcNJQZQgghhNw0lBlCCCGE3DSUGUIIIYTcNJQZQgghhNw0lBlCCCGE3DSUGUIIIYTcNJQZQgghhNw0lBlCCCGE3DSUGUIIIYTcNJQZQgghhNw0lBlCCCGE3DSUGUIIIYTcNJQZQgghhNw0lBlCCCGE3DSUGUIIIYTcNJQZQgghhNw0lBlCCCGE3DSUGUIIIYTcNJQZQgghhNw0lBlCCCGE3DSUGUIIIYTcNJQZQgghhNw0lBlCCCGE3DSUGUIIIYTcNJQZQgghhNw0lBlCCCGE3DSUGUIIIYTcNJQZQgghhNw0/w9qrjElDFrLbwAAAABJRU5ErkJggg==" id="imagefe2c97ad5b" transform="scale(1 -1) translate(0 -270.24)" x="42.113906" y="-44.38525" width="270.24" height="270.24"/>
   </g>
   <g id="patch_3">
    <path d="M 177.001906 230.32025 
C 190.416675 230.32025 203.283815 224.990506 212.769489 215.504832 
C 222.255162 206.019159 227.584906 193.152018 227.584906 179.73725 
C 227.584906 166.322482 222.255162 153.455341 212.769489 143.969668 
C 203.283815 134.483994 190.416675 129.15425 177.001906 129.15425 
C 163.587138 129.15425 150.719998 134.483994 141.234324 143.969668 
C 131.74865 153.455341 126.418906 166.322482 126.418906 179.73725 
C 126.418906 193.152018 131.74865 206.019159 141.234324 215.504832 
C 150.719998 224.990506 163.587138 230.32025 177.001906 230.32025 
L 177.001906 230.32025 
z
" clip-path="url(#p5ab084d5f0)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 177.001906 264.04225 
C 199.359854 264.04225 220.805087 255.159343 236.614543 239.349887 
C 252.424 223.540431 261.306906 202.095197 261.306906 179.73725 
C 261.306906 157.379303 252.424 135.934069 236.614543 120.124613 
C 220.805087 104.315157 199.359854 95.43225 177.001906 95.43225 
C 154.643959 95.43225 133.198725 104.315157 117.389269 120.124613 
C 101.579813 135.934069 92.696906 157.379303 92.696906 179.73725 
C 92.696906 202.095197 101.579813 223.540431 117.389269 239.349887 
C 133.198725 255.159343 154.643959 264.04225 177.001906 264.04225 
L 177.001906 264.04225 
z
" clip-path="url(#p5ab084d5f0)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 177.001906 314.62525 
C 212.774622 314.62525 247.086996 300.412599 272.382126 275.11747 
C 297.677256 249.82234 311.889906 215.509966 311.889906 179.73725 
C 311.889906 143.964534 297.677256 109.65216 272.382126 84.35703 
C 247.086996 59.061901 212.774622 44.84925 177.001906 44.84925 
C 141.22919 44.84925 106.916817 59.061901 81.621687 84.35703 
C 56.326557 109.65216 42.113906 143.964534 42.113906 179.73725 
C 42.113906 215.509966 56.326557 249.82234 81.621687 275.11747 
C 106.916817 300.412599 141.22919 314.62525 177.001906 314.62525 
L 177.001906 314.62525 
z
" clip-path="url(#p5ab084d5f0)" style="fill: none; opacity: 0.7; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" clip-path="url(#p5ab084d5f0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="m4da27f03f4" d="M 0 0 
L 0 3.5 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m4da27f03f4" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −8 -->
      <g transform="translate(35.479922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 75.835906 314.62525 
L 75.835906 44.84925 
" clip-path="url(#p5ab084d5f0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#m4da27f03f4" x="75.835906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −6 -->
      <g transform="translate(69.201922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 109.557906 314.62525 
L 109.557906 44.84925 
" clip-path="url(#p5ab084d5f0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#m4da27f03f4" x="109.557906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_3">
      <!-- −4 -->
      <g transform="translate(102.923922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 143.279906 314.62525 
L 143.279906 44.84925 
" clip-path="url(#p5ab084d5f0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#m4da27f03f4" x="143.279906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_4">
      <!-- −2 -->
      <g transform="translate(136.645922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 177.001906 314.62525 
L 177.001906 44.84925 
" clip-path="url(#p5ab084d5f0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#m4da27f03f4" x="177.001906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(174.138781 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 210.723906 314.62525 
L 210.723906 44.84925 
" clip-path="url(#p5ab084d5f0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#m4da27f03f4" x="210.723906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 2 -->
      <g transform="translate(207.860781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 244.445906 314.62525 
L 244.445906 44.84925 
" clip-path="url(#p5ab084d5f0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#m4da27f03f4" x="244.445906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 4 -->
      <g transform="translate(241.582781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <path d="M 278.167906 314.62525 
L 278.167906 44.84925 
" clip-path="url(#p5ab084d5f0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#m4da27f03f4" x="278.167906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 6 -->
      <g transform="translate(275.304781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_17">
      <path d="M 311.889906 314.62525 
L 311.889906 44.84925 
" clip-path="url(#p5ab084d5f0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#m4da27f03f4" x="311.889906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 8 -->
      <g transform="translate(309.026781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- Relative X Position (m) -->
     <g transform="translate(105.68925 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-52" d="M 2297 2597 
Q 2675 2597 2839 2737 
Q 3003 2878 3003 3200 
Q 3003 3519 2839 3656 
Q 2675 3794 2297 3794 
L 1791 3794 
L 1791 2597 
L 2297 2597 
z
M 1791 1766 
L 1791 0 
L 588 0 
L 588 4666 
L 2425 4666 
Q 3347 4666 3776 4356 
Q 4206 4047 4206 3378 
Q 4206 2916 3982 2619 
Q 3759 2322 3309 2181 
Q 3556 2125 3751 1926 
Q 3947 1728 4147 1325 
L 4800 0 
L 3519 0 
L 2950 1159 
Q 2778 1509 2601 1637 
Q 2425 1766 2131 1766 
L 1791 1766 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6c" d="M 538 4863 
L 1656 4863 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-76" d="M 97 3500 
L 1216 3500 
L 2088 1081 
L 2956 3500 
L 4078 3500 
L 2700 0 
L 1472 0 
L 97 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-58" d="M 3188 2381 
L 4806 0 
L 3553 0 
L 2463 1594 
L 1381 0 
L 122 0 
L 1741 2381 
L 184 4666 
L 1441 4666 
L 2463 3163 
L 3481 4666 
L 4744 4666 
L 3188 2381 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-50" d="M 588 4666 
L 2584 4666 
Q 3475 4666 3951 4270 
Q 4428 3875 4428 3144 
Q 4428 2409 3951 2014 
Q 3475 1619 2584 1619 
L 1791 1619 
L 1791 0 
L 588 0 
L 588 4666 
z
M 1791 3794 
L 1791 2491 
L 2456 2491 
Q 2806 2491 2997 2661 
Q 3188 2831 3188 3144 
Q 3188 3456 2997 3625 
Q 2806 3794 2456 3794 
L 1791 3794 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6f" d="M 2203 2784 
Q 1831 2784 1636 2517 
Q 1441 2250 1441 1747 
Q 1441 1244 1636 976 
Q 1831 709 2203 709 
Q 2569 709 2762 976 
Q 2956 1244 2956 1747 
Q 2956 2250 2762 2517 
Q 2569 2784 2203 2784 
z
M 2203 3584 
Q 3106 3584 3614 3096 
Q 4122 2609 4122 1747 
Q 4122 884 3614 396 
Q 3106 -91 2203 -91 
Q 1297 -91 786 396 
Q 275 884 275 1747 
Q 275 2609 786 3096 
Q 1297 3584 2203 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-28" d="M 2413 -844 
L 1484 -844 
Q 1006 -72 778 623 
Q 550 1319 550 2003 
Q 550 2688 779 3389 
Q 1009 4091 1484 4856 
L 2413 4856 
Q 2013 4116 1813 3408 
Q 1613 2700 1613 2009 
Q 1613 1319 1811 609 
Q 2009 -100 2413 -844 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6d" d="M 3781 2919 
Q 3994 3244 4286 3414 
Q 4578 3584 4928 3584 
Q 5531 3584 5847 3212 
Q 6163 2841 6163 2131 
L 6163 0 
L 5038 0 
L 5038 1825 
Q 5041 1866 5042 1909 
Q 5044 1953 5044 2034 
Q 5044 2406 4934 2573 
Q 4825 2741 4581 2741 
Q 4263 2741 4089 2478 
Q 3916 2216 3909 1719 
L 3909 0 
L 2784 0 
L 2784 1825 
Q 2784 2406 2684 2573 
Q 2584 2741 2328 2741 
Q 2006 2741 1831 2477 
Q 1656 2213 1656 1722 
L 1656 0 
L 531 0 
L 531 3500 
L 1656 3500 
L 1656 2988 
Q 1863 3284 2130 3434 
Q 2397 3584 2719 3584 
Q 3081 3584 3359 3409 
Q 3638 3234 3781 2919 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-29" d="M 513 -844 
Q 913 -100 1113 609 
Q 1313 1319 1313 2009 
Q 1313 2700 1113 3408 
Q 913 4116 513 4856 
L 1441 4856 
Q 1916 4091 2145 3389 
Q 2375 2688 2375 2003 
Q 2375 1319 2147 623 
Q 1919 -72 1441 -844 
L 513 -844 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-58" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="573.583984"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="608.398438"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="681.689453"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="750.390625"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="809.912109"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="844.189453"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="891.992188"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="926.269531"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="994.970703"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1066.162109"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1100.976562"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1146.679688"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1250.878906"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_19">
      <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" clip-path="url(#p5ab084d5f0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <defs>
       <path id="m8c1081cc1c" d="M 0 0 
L -3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m8c1081cc1c" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_11">
      <!-- −8 -->
      <g transform="translate(21.845937 318.044547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_21">
      <path d="M 42.113906 280.90325 
L 311.889906 280.90325 
" clip-path="url(#p5ab084d5f0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#m8c1081cc1c" x="42.113906" y="280.90325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_12">
      <!-- −6 -->
      <g transform="translate(21.845937 284.322547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_23">
      <path d="M 42.113906 247.18125 
L 311.889906 247.18125 
" clip-path="url(#p5ab084d5f0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#m8c1081cc1c" x="42.113906" y="247.18125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_13">
      <!-- −4 -->
      <g transform="translate(21.845937 250.600547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_25">
      <path d="M 42.113906 213.45925 
L 311.889906 213.45925 
" clip-path="url(#p5ab084d5f0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#m8c1081cc1c" x="42.113906" y="213.45925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_14">
      <!-- −2 -->
      <g transform="translate(21.845937 216.878547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_27">
      <path d="M 42.113906 179.73725 
L 311.889906 179.73725 
" clip-path="url(#p5ab084d5f0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#m8c1081cc1c" x="42.113906" y="179.73725" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 0 -->
      <g transform="translate(29.387656 183.156547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_29">
      <path d="M 42.113906 146.01525 
L 311.889906 146.01525 
" clip-path="url(#p5ab084d5f0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#m8c1081cc1c" x="42.113906" y="146.01525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 2 -->
      <g transform="translate(29.387656 149.434547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_31">
      <path d="M 42.113906 112.29325 
L 311.889906 112.29325 
" clip-path="url(#p5ab084d5f0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#m8c1081cc1c" x="42.113906" y="112.29325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 4 -->
      <g transform="translate(29.387656 115.712547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_33">
      <path d="M 42.113906 78.57125 
L 311.889906 78.57125 
" clip-path="url(#p5ab084d5f0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#m8c1081cc1c" x="42.113906" y="78.57125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 6 -->
      <g transform="translate(29.387656 81.990547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_35">
      <path d="M 42.113906 44.84925 
L 311.889906 44.84925 
" clip-path="url(#p5ab084d5f0)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#m8c1081cc1c" x="42.113906" y="44.84925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 8 -->
      <g transform="translate(29.387656 48.268547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_20">
     <!-- Relative Y Position (m) -->
     <g transform="translate(15.558281 250.792094) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-59" d="M -63 4666 
L 1253 4666 
L 2316 3003 
L 3378 4666 
L 4697 4666 
L 2919 1966 
L 2919 0 
L 1716 0 
L 1716 1966 
L -63 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-59" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="568.896484"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="603.710938"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="677.001953"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="745.703125"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="805.224609"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="839.501953"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="887.304688"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="921.582031"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="990.283203"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1061.474609"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1096.289062"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1141.992188"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1246.191406"/>
     </g>
    </g>
   </g>
   <g id="patch_6">
    <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_21">
    <g id="patch_8">
     <path d="M 200.10902 150.124824 
L 225.429957 150.124824 
Q 227.229957 150.124824 227.229957 148.324824 
L 227.229957 139.614511 
Q 227.229957 137.814511 225.429957 137.814511 
L 200.10902 137.814511 
Q 198.30902 137.814511 198.30902 139.614511 
L 198.30902 148.324824 
Q 198.30902 150.124824 200.10902 150.124824 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 3.0m -->
    <g style="fill: #ffffff" transform="translate(200.10902 146.453105) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-33" d="M 2981 2516 
Q 3453 2394 3698 2092 
Q 3944 1791 3944 1325 
Q 3944 631 3412 270 
Q 2881 -91 1863 -91 
Q 1503 -91 1142 -33 
Q 781 25 428 141 
L 428 1069 
Q 766 900 1098 814 
Q 1431 728 1753 728 
Q 2231 728 2486 893 
Q 2741 1059 2741 1369 
Q 2741 1688 2480 1852 
Q 2219 2016 1709 2016 
L 1228 2016 
L 1228 2791 
L 1734 2791 
Q 2188 2791 2409 2933 
Q 2631 3075 2631 3366 
Q 2631 3634 2415 3781 
Q 2200 3928 1806 3928 
Q 1516 3928 1219 3862 
Q 922 3797 628 3669 
L 628 4550 
Q 984 4650 1334 4700 
Q 1684 4750 2022 4750 
Q 2931 4750 3382 4451 
Q 3834 4153 3834 3553 
Q 3834 3144 3618 2883 
Q 3403 2622 2981 2516 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2e" d="M 653 1209 
L 1778 1209 
L 1778 0 
L 653 0 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-30" d="M 2944 2338 
Q 2944 3213 2780 3570 
Q 2616 3928 2228 3928 
Q 1841 3928 1675 3570 
Q 1509 3213 1509 2338 
Q 1509 1453 1675 1090 
Q 1841 728 2228 728 
Q 2613 728 2778 1090 
Q 2944 1453 2944 2338 
z
M 4147 2328 
Q 4147 1169 3647 539 
Q 3147 -91 2228 -91 
Q 1306 -91 806 539 
Q 306 1169 306 2328 
Q 306 3491 806 4120 
Q 1306 4750 2228 4750 
Q 3147 4750 3647 4120 
Q 4147 3491 4147 2328 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-33"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_22">
    <g id="patch_9">
     <path d="M 223.954075 126.279769 
L 249.275012 126.279769 
Q 251.075012 126.279769 251.075012 124.479769 
L 251.075012 115.769457 
Q 251.075012 113.969457 249.275012 113.969457 
L 223.954075 113.969457 
Q 222.154075 113.969457 222.154075 115.769457 
L 222.154075 124.479769 
Q 222.154075 126.279769 223.954075 126.279769 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 5.0m -->
    <g style="fill: #ffffff" transform="translate(223.954075 122.60805) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-35" d="M 678 4666 
L 3669 4666 
L 3669 3781 
L 1638 3781 
L 1638 3059 
Q 1775 3097 1914 3117 
Q 2053 3138 2203 3138 
Q 3056 3138 3531 2711 
Q 4006 2284 4006 1522 
Q 4006 766 3489 337 
Q 2972 -91 2053 -91 
Q 1656 -91 1267 -14 
Q 878 63 494 219 
L 494 1166 
Q 875 947 1217 837 
Q 1559 728 1863 728 
Q 2300 728 2551 942 
Q 2803 1156 2803 1522 
Q 2803 1891 2551 2103 
Q 2300 2316 1863 2316 
Q 1603 2316 1309 2248 
Q 1016 2181 678 2041 
L 678 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-35"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_23">
    <g id="patch_10">
     <path d="M 259.721657 90.512187 
L 285.042595 90.512187 
Q 286.842595 90.512187 286.842595 88.712187 
L 286.842595 80.001874 
Q 286.842595 78.201874 285.042595 78.201874 
L 259.721657 78.201874 
Q 257.921657 78.201874 257.921657 80.001874 
L 257.921657 88.712187 
Q 257.921657 90.512187 259.721657 90.512187 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 8.0m -->
    <g style="fill: #ffffff" transform="translate(259.721657 86.840468) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-38" d="M 2228 2088 
Q 1891 2088 1709 1903 
Q 1528 1719 1528 1375 
Q 1528 1031 1709 848 
Q 1891 666 2228 666 
Q 2563 666 2741 848 
Q 2919 1031 2919 1375 
Q 2919 1722 2741 1905 
Q 2563 2088 2228 2088 
z
M 1350 2484 
Q 925 2613 709 2878 
Q 494 3144 494 3541 
Q 494 4131 934 4440 
Q 1375 4750 2228 4750 
Q 3075 4750 3515 4442 
Q 3956 4134 3956 3541 
Q 3956 3144 3739 2878 
Q 3522 2613 3097 2484 
Q 3572 2353 3814 2058 
Q 4056 1763 4056 1313 
Q 4056 619 3595 264 
Q 3134 -91 2228 -91 
Q 1319 -91 855 264 
Q 391 619 391 1313 
Q 391 1763 633 2058 
Q 875 2353 1350 2484 
z
M 1631 3419 
Q 1631 3141 1786 2991 
Q 1941 2841 2228 2841 
Q 2509 2841 2662 2991 
Q 2816 3141 2816 3419 
Q 2816 3697 2662 3845 
Q 2509 3994 2228 3994 
Q 1941 3994 1786 3844 
Q 1631 3694 1631 3419 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-38"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_24">
    <!-- Mid-Distance Cell (Neuron 55) -->
    <g transform="translate(75.364719 16.411875) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-4d" d="M 588 4666 
L 2119 4666 
L 3181 2169 
L 4250 4666 
L 5778 4666 
L 5778 0 
L 4641 0 
L 4641 3413 
L 3566 897 
L 2803 897 
L 1728 3413 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-64" d="M 2919 2988 
L 2919 4863 
L 4044 4863 
L 4044 0 
L 2919 0 
L 2919 506 
Q 2688 197 2409 53 
Q 2131 -91 1766 -91 
Q 1119 -91 703 423 
Q 288 938 288 1747 
Q 288 2556 703 3070 
Q 1119 3584 1766 3584 
Q 2128 3584 2408 3439 
Q 2688 3294 2919 2988 
z
M 2181 722 
Q 2541 722 2730 984 
Q 2919 1247 2919 1747 
Q 2919 2247 2730 2509 
Q 2541 2772 2181 2772 
Q 1825 2772 1636 2509 
Q 1447 2247 1447 1747 
Q 1447 1247 1636 984 
Q 1825 722 2181 722 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2d" d="M 347 2297 
L 2309 2297 
L 2309 1388 
L 347 1388 
L 347 2297 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-44" d="M 1791 3756 
L 1791 909 
L 2222 909 
Q 2959 909 3348 1275 
Q 3738 1641 3738 2338 
Q 3738 3031 3350 3393 
Q 2963 3756 2222 3756 
L 1791 3756 
z
M 588 4666 
L 1856 4666 
Q 2919 4666 3439 4514 
Q 3959 4363 4331 4000 
Q 4659 3684 4818 3271 
Q 4978 2859 4978 2338 
Q 4978 1809 4818 1395 
Q 4659 981 4331 666 
Q 3956 303 3431 151 
Q 2906 0 1856 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4e" d="M 588 4666 
L 1931 4666 
L 3628 1466 
L 3628 4666 
L 4769 4666 
L 4769 0 
L 3425 0 
L 1728 3200 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-75" d="M 500 1363 
L 500 3500 
L 1625 3500 
L 1625 3150 
Q 1625 2866 1622 2436 
Q 1619 2006 1619 1863 
Q 1619 1441 1641 1255 
Q 1663 1069 1716 984 
Q 1784 875 1895 815 
Q 2006 756 2150 756 
Q 2500 756 2700 1025 
Q 2900 1294 2900 1772 
L 2900 3500 
L 4019 3500 
L 4019 0 
L 2900 0 
L 2900 506 
Q 2647 200 2364 54 
Q 2081 -91 1741 -91 
Q 1134 -91 817 281 
Q 500 653 500 1363 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-4d"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="99.511719"/>
     <use xlink:href="#DejaVuSans-Bold-64" x="133.789062"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="205.371094"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="246.875"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="329.882812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="364.160156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="423.681641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.484375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="538.964844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="610.15625"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="669.433594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="737.255859"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="772.070312"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="845.458984"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="913.28125"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="947.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="981.835938"/>
     <use xlink:href="#DejaVuSans-Bold-28" x="1016.650391"/>
     <use xlink:href="#DejaVuSans-Bold-4e" x="1062.353516"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1146.044922"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1213.867188"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1285.058594"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="1334.375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1403.076172"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1474.267578"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="1509.082031"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="1578.662109"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="1648.242188"/>
    </g>
    <!-- 2D Activation Map -->
    <g transform="translate(114.950656 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-32" d="M 1844 884 
L 3897 884 
L 3897 0 
L 506 0 
L 506 884 
L 2209 2388 
Q 2438 2594 2547 2791 
Q 2656 2988 2656 3200 
Q 2656 3528 2436 3728 
Q 2216 3928 1850 3928 
Q 1569 3928 1234 3808 
Q 900 3688 519 3450 
L 519 4475 
Q 925 4609 1322 4679 
Q 1719 4750 2100 4750 
Q 2938 4750 3402 4381 
Q 3866 4013 3866 3353 
Q 3866 2972 3669 2642 
Q 3472 2313 2841 1759 
L 1844 884 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-41" d="M 3419 850 
L 1538 850 
L 1241 0 
L 31 0 
L 1759 4666 
L 3194 4666 
L 4922 0 
L 3713 0 
L 3419 850 
z
M 1838 1716 
L 3116 1716 
L 2478 3572 
L 1838 1716 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-32"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="152.587891"/>
     <use xlink:href="#DejaVuSans-Bold-41" x="187.402344"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="264.794922"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="324.072266"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="371.875"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="406.152344"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.337891"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="538.818359"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="586.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="620.898438"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="689.599609"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="760.791016"/>
     <use xlink:href="#DejaVuSans-Bold-4d" x="795.605469"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="895.117188"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="962.597656"/>
    </g>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="mda0ce63bac" d="M 0 -5.477226 
L -1.229714 -1.692556 
L -5.209151 -1.692556 
L -1.989719 0.646499 
L -3.219432 4.431169 
L -0 2.092114 
L 3.219432 4.431169 
L 1.989719 0.646499 
L 5.209151 -1.692556 
L 1.229714 -1.692556 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#p5ab084d5f0)">
     <use xlink:href="#mda0ce63bac" x="177.001906" y="179.73725" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_11">
     <path d="M 242.388656 62.99175 
L 307.889906 62.99175 
L 307.889906 48.84925 
L 242.388656 48.84925 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="PathCollection_2">
     <g>
      <use xlink:href="#mda0ce63bac" x="253.588656" y="56.028" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
     </g>
    </g>
    <g id="text_25">
     <!-- Observer -->
     <g transform="translate(267.988656 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4f"/>
      <use xlink:href="#DejaVuSans-62" x="78.710938"/>
      <use xlink:href="#DejaVuSans-73" x="142.1875"/>
      <use xlink:href="#DejaVuSans-65" x="194.287109"/>
      <use xlink:href="#DejaVuSans-72" x="255.810547"/>
      <use xlink:href="#DejaVuSans-76" x="296.923828"/>
      <use xlink:href="#DejaVuSans-65" x="356.103516"/>
      <use xlink:href="#DejaVuSans-72" x="417.626953"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_12">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
L 813.937288 44.84925 
L 436.259015 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="m31f339acc6" d="M 440.97851 -37.642188 
L 440.97851 -37.642188 
L 450.4175 -42.436195 
L 459.85649 -40.639502 
L 469.29548 -44.607035 
L 478.73447 -39.997422 
L 488.17346 -45.049212 
L 497.61245 -56.274529 
L 507.051441 -67.947332 
L 516.490431 -74.858002 
L 525.929421 -78.049404 
L 535.368411 -97.166915 
L 544.807401 -79.455486 
L 554.246391 -108.050655 
L 563.685381 -105.358524 
L 573.124371 -120.079096 
L 582.563361 -117.639571 
L 592.002351 -154.668741 
L 601.441341 -163.329419 
L 610.880331 -150.177411 
L 620.319321 -244.245218 
L 629.758311 -255.983001 
L 639.197301 -294.571711 
L 648.636291 -189.877215 
L 658.075281 -192.562521 
L 667.514271 -160.997986 
L 676.953261 -203.351419 
L 686.392251 -281.729978 
L 695.831241 -187.89753 
L 705.270231 -254.250718 
L 714.709221 -270.265985 
L 714.709221 -37.642188 
L 714.709221 -37.642188 
L 705.270231 -37.642188 
L 695.831241 -37.642188 
L 686.392251 -37.642188 
L 676.953261 -37.642188 
L 667.514271 -37.642188 
L 658.075281 -37.642188 
L 648.636291 -37.642188 
L 639.197301 -37.642188 
L 629.758311 -37.642188 
L 620.319321 -37.642188 
L 610.880331 -37.642188 
L 601.441341 -37.642188 
L 592.002351 -37.642188 
L 582.563361 -37.642188 
L 573.124371 -37.642188 
L 563.685381 -37.642188 
L 554.246391 -37.642188 
L 544.807401 -37.642188 
L 535.368411 -37.642188 
L 525.929421 -37.642188 
L 516.490431 -37.642188 
L 507.051441 -37.642188 
L 497.61245 -37.642188 
L 488.17346 -37.642188 
L 478.73447 -37.642188 
L 469.29548 -37.642188 
L 459.85649 -37.642188 
L 450.4175 -37.642188 
L 440.97851 -37.642188 
z
" style="stroke: #1f78b4; stroke-opacity: 0.3"/>
    </defs>
    <g clip-path="url(#paa0093ae95)">
     <use xlink:href="#m31f339acc6" x="0" y="352.267438" style="fill: #1f78b4; fill-opacity: 0.3; stroke: #1f78b4; stroke-opacity: 0.3"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10">
     <g id="line2d_37">
      <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" clip-path="url(#paa0093ae95)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#m4da27f03f4" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 0 -->
      <g transform="translate(433.39589 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_39">
      <path d="M 483.468799 314.62525 
L 483.468799 44.84925 
" clip-path="url(#paa0093ae95)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#m4da27f03f4" x="483.468799" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 1 -->
      <g transform="translate(480.605674 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_41">
      <path d="M 530.678584 314.62525 
L 530.678584 44.84925 
" clip-path="url(#paa0093ae95)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#m4da27f03f4" x="530.678584" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 2 -->
      <g transform="translate(527.815459 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_43">
      <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#paa0093ae95)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#m4da27f03f4" x="577.888368" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 3 -->
      <g transform="translate(575.025243 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_45">
      <path d="M 625.098152 314.62525 
L 625.098152 44.84925 
" clip-path="url(#paa0093ae95)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#m4da27f03f4" x="625.098152" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 4 -->
      <g transform="translate(622.235027 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_47">
      <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#paa0093ae95)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#m4da27f03f4" x="672.307936" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 5 -->
      <g transform="translate(669.444811 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_49">
      <path d="M 719.51772 314.62525 
L 719.51772 44.84925 
" clip-path="url(#paa0093ae95)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#m4da27f03f4" x="719.51772" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 6 -->
      <g transform="translate(716.654595 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_51">
      <path d="M 766.727504 314.62525 
L 766.727504 44.84925 
" clip-path="url(#paa0093ae95)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#m4da27f03f4" x="766.727504" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 7 -->
      <g transform="translate(763.864379 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-37"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_53">
      <path d="M 813.937288 314.62525 
L 813.937288 44.84925 
" clip-path="url(#paa0093ae95)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#m4da27f03f4" x="813.937288" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 8 -->
      <g transform="translate(811.074163 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_35">
     <!-- Inter-Agent Distance (m) -->
     <g transform="translate(547.755261 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-49" d="M 588 4666 
L 1791 4666 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-49"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="37.207031"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="108.398438"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="156.201172"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="224.023438"/>
      <use xlink:href="#DejaVuSans-Bold-2d" x="273.339844"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="314.84375"/>
      <use xlink:href="#DejaVuSans-Bold-67" x="392.236328"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="463.818359"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="531.640625"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="602.832031"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="650.634766"/>
      <use xlink:href="#DejaVuSans-Bold-44" x="685.449219"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="768.457031"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="802.734375"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="862.255859"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="910.058594"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="977.539062"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="1048.730469"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="1108.007812"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1175.830078"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1210.644531"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1256.347656"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1360.546875"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_55">
      <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" clip-path="url(#paa0093ae95)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#m8c1081cc1c" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 0.0 -->
      <g transform="translate(414.946203 318.044547) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_57">
      <path d="M 436.259015 277.106351 
L 813.937288 277.106351 
" clip-path="url(#paa0093ae95)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#m8c1081cc1c" x="436.259015" y="277.106351" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 0.2 -->
      <g transform="translate(414.946203 280.525647) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_59">
      <path d="M 436.259015 239.587451 
L 813.937288 239.587451 
" clip-path="url(#paa0093ae95)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#m8c1081cc1c" x="436.259015" y="239.587451" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 0.4 -->
      <g transform="translate(414.946203 243.006748) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_61">
      <path d="M 436.259015 202.068552 
L 813.937288 202.068552 
" clip-path="url(#paa0093ae95)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#m8c1081cc1c" x="436.259015" y="202.068552" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 0.6 -->
      <g transform="translate(414.946203 205.487848) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_63">
      <path d="M 436.259015 164.549652 
L 813.937288 164.549652 
" clip-path="url(#paa0093ae95)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#m8c1081cc1c" x="436.259015" y="164.549652" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 0.8 -->
      <g transform="translate(414.946203 167.968949) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-38" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_65">
      <path d="M 436.259015 127.030753 
L 813.937288 127.030753 
" clip-path="url(#paa0093ae95)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#m8c1081cc1c" x="436.259015" y="127.030753" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 1.0 -->
      <g transform="translate(414.946203 130.45005) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_67">
      <path d="M 436.259015 89.511853 
L 813.937288 89.511853 
" clip-path="url(#paa0093ae95)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_68">
      <g>
       <use xlink:href="#m8c1081cc1c" x="436.259015" y="89.511853" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_42">
      <!-- 1.2 -->
      <g transform="translate(414.946203 92.93115) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_69">
      <path d="M 436.259015 51.992954 
L 813.937288 51.992954 
" clip-path="url(#paa0093ae95)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_70">
      <g>
       <use xlink:href="#m8c1081cc1c" x="436.259015" y="51.992954" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_43">
      <!-- 1.4 -->
      <g transform="translate(414.946203 55.412251) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_44">
     <!-- Neural Activation -->
     <g transform="translate(408.658547 233.746391) rotate(-90) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="line2d_71">
    <path d="M 440.97851 314.62525 
L 450.4175 309.831243 
L 459.85649 311.627935 
L 469.29548 307.660403 
L 478.73447 312.270015 
L 488.17346 307.218225 
L 497.61245 295.992909 
L 507.051441 284.320105 
L 516.490431 277.409436 
L 525.929421 274.218033 
L 535.368411 255.100522 
L 544.807401 272.811951 
L 554.246391 244.216782 
L 563.685381 246.908914 
L 573.124371 232.188342 
L 582.563361 234.627866 
L 592.002351 197.598696 
L 601.441341 188.938018 
L 610.880331 202.090027 
L 620.319321 108.022219 
L 629.758311 96.284436 
L 639.197301 57.695726 
L 648.636291 162.390222 
L 658.075281 159.704917 
L 667.514271 191.269451 
L 676.953261 148.916019 
L 686.392251 70.537459 
L 695.831241 164.369907 
L 705.270231 98.016719 
L 714.709221 82.001452 
" clip-path="url(#paa0093ae95)" style="fill: none; stroke: #1f78b4; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_72">
    <path d="M 639.197301 314.62525 
L 639.197301 44.84925 
" clip-path="url(#paa0093ae95)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #1f78b4; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_73">
    <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#paa0093ae95)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_74">
    <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#paa0093ae95)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="patch_13">
    <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_14">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_45">
    <g id="patch_15">
     <path d="M 700.612124 114.0718 
L 795.053374 114.0718 
Q 798.253374 114.0718 798.253374 110.8718 
L 798.253374 58.33805 
Q 798.253374 55.13805 795.053374 55.13805 
L 700.612124 55.13805 
Q 697.412124 55.13805 697.412124 58.33805 
L 697.412124 110.8718 
Q 697.412124 114.0718 700.612124 114.0718 
z
" style="fill: #ffffff; opacity: 0.9; stroke: #808080; stroke-linejoin: miter"/>
    </g>
    <!-- Peak Distance: 4.3m -->
    <g transform="translate(712.787124 64.4168) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-3a" d="M 750 794 
L 1409 794 
L 1409 0 
L 750 0 
L 750 794 
z
M 750 3309 
L 1409 3309 
L 1409 2516 
L 750 2516 
L 750 3309 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-44" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="346.179688"/>
     <use xlink:href="#DejaVuSans-73" x="373.962891"/>
     <use xlink:href="#DejaVuSans-74" x="426.0625"/>
     <use xlink:href="#DejaVuSans-61" x="465.271484"/>
     <use xlink:href="#DejaVuSans-6e" x="526.550781"/>
     <use xlink:href="#DejaVuSans-63" x="589.929688"/>
     <use xlink:href="#DejaVuSans-65" x="644.910156"/>
     <use xlink:href="#DejaVuSans-3a" x="706.433594"/>
     <use xlink:href="#DejaVuSans-20" x="740.125"/>
     <use xlink:href="#DejaVuSans-34" x="771.912109"/>
     <use xlink:href="#DejaVuSans-2e" x="835.535156"/>
     <use xlink:href="#DejaVuSans-33" x="867.322266"/>
     <use xlink:href="#DejaVuSans-6d" x="930.945312"/>
    </g>
    <!-- Max Activation: 1.370 -->
    <g transform="translate(707.779624 73.37505) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-4d"/>
     <use xlink:href="#DejaVuSans-61" x="86.279297"/>
     <use xlink:href="#DejaVuSans-78" x="147.558594"/>
     <use xlink:href="#DejaVuSans-20" x="206.738281"/>
     <use xlink:href="#DejaVuSans-41" x="238.525391"/>
     <use xlink:href="#DejaVuSans-63" x="305.183594"/>
     <use xlink:href="#DejaVuSans-74" x="360.164062"/>
     <use xlink:href="#DejaVuSans-69" x="399.373047"/>
     <use xlink:href="#DejaVuSans-76" x="427.15625"/>
     <use xlink:href="#DejaVuSans-61" x="486.335938"/>
     <use xlink:href="#DejaVuSans-74" x="547.615234"/>
     <use xlink:href="#DejaVuSans-69" x="586.824219"/>
     <use xlink:href="#DejaVuSans-6f" x="614.607422"/>
     <use xlink:href="#DejaVuSans-6e" x="675.789062"/>
     <use xlink:href="#DejaVuSans-3a" x="739.167969"/>
     <use xlink:href="#DejaVuSans-20" x="772.859375"/>
     <use xlink:href="#DejaVuSans-31" x="804.646484"/>
     <use xlink:href="#DejaVuSans-2e" x="868.269531"/>
     <use xlink:href="#DejaVuSans-33" x="900.056641"/>
     <use xlink:href="#DejaVuSans-37" x="963.679688"/>
     <use xlink:href="#DejaVuSans-30" x="1027.302734"/>
    </g>
    <!-- Sparsity: 0.406 -->
    <g transform="translate(734.883374 82.3333) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-70" x="63.476562"/>
     <use xlink:href="#DejaVuSans-61" x="126.953125"/>
     <use xlink:href="#DejaVuSans-72" x="188.232422"/>
     <use xlink:href="#DejaVuSans-73" x="229.345703"/>
     <use xlink:href="#DejaVuSans-69" x="281.445312"/>
     <use xlink:href="#DejaVuSans-74" x="309.228516"/>
     <use xlink:href="#DejaVuSans-79" x="348.4375"/>
     <use xlink:href="#DejaVuSans-3a" x="400.367188"/>
     <use xlink:href="#DejaVuSans-20" x="434.058594"/>
     <use xlink:href="#DejaVuSans-30" x="465.845703"/>
     <use xlink:href="#DejaVuSans-2e" x="529.46875"/>
     <use xlink:href="#DejaVuSans-34" x="561.255859"/>
     <use xlink:href="#DejaVuSans-30" x="624.878906"/>
     <use xlink:href="#DejaVuSans-36" x="688.501953"/>
    </g>
    <!-- Peak Significance: 2.77 -->
    <g transform="translate(702.065874 91.29155) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-53" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="332.654297"/>
     <use xlink:href="#DejaVuSans-67" x="360.4375"/>
     <use xlink:href="#DejaVuSans-6e" x="423.914062"/>
     <use xlink:href="#DejaVuSans-69" x="487.292969"/>
     <use xlink:href="#DejaVuSans-66" x="515.076172"/>
     <use xlink:href="#DejaVuSans-69" x="550.28125"/>
     <use xlink:href="#DejaVuSans-63" x="578.064453"/>
     <use xlink:href="#DejaVuSans-61" x="633.044922"/>
     <use xlink:href="#DejaVuSans-6e" x="694.324219"/>
     <use xlink:href="#DejaVuSans-63" x="757.703125"/>
     <use xlink:href="#DejaVuSans-65" x="812.683594"/>
     <use xlink:href="#DejaVuSans-3a" x="874.207031"/>
     <use xlink:href="#DejaVuSans-20" x="907.898438"/>
     <use xlink:href="#DejaVuSans-32" x="939.685547"/>
     <use xlink:href="#DejaVuSans-2e" x="1003.308594"/>
     <use xlink:href="#DejaVuSans-37" x="1035.095703"/>
     <use xlink:href="#DejaVuSans-37" x="1098.71875"/>
    </g>
    <!-- Selectivity Index: 0.491 -->
    <g transform="translate(700.612124 100.2498) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-39" d="M 703 97 
L 703 672 
Q 941 559 1184 500 
Q 1428 441 1663 441 
Q 2288 441 2617 861 
Q 2947 1281 2994 2138 
Q 2813 1869 2534 1725 
Q 2256 1581 1919 1581 
Q 1219 1581 811 2004 
Q 403 2428 403 3163 
Q 403 3881 828 4315 
Q 1253 4750 1959 4750 
Q 2769 4750 3195 4129 
Q 3622 3509 3622 2328 
Q 3622 1225 3098 567 
Q 2575 -91 1691 -91 
Q 1453 -91 1209 -44 
Q 966 3 703 97 
z
M 1959 2075 
Q 2384 2075 2632 2365 
Q 2881 2656 2881 3163 
Q 2881 3666 2632 3958 
Q 2384 4250 1959 4250 
Q 1534 4250 1286 3958 
Q 1038 3666 1038 3163 
Q 1038 2656 1286 2365 
Q 1534 2075 1959 2075 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-65" x="63.476562"/>
     <use xlink:href="#DejaVuSans-6c" x="125"/>
     <use xlink:href="#DejaVuSans-65" x="152.783203"/>
     <use xlink:href="#DejaVuSans-63" x="214.306641"/>
     <use xlink:href="#DejaVuSans-74" x="269.287109"/>
     <use xlink:href="#DejaVuSans-69" x="308.496094"/>
     <use xlink:href="#DejaVuSans-76" x="336.279297"/>
     <use xlink:href="#DejaVuSans-69" x="395.458984"/>
     <use xlink:href="#DejaVuSans-74" x="423.242188"/>
     <use xlink:href="#DejaVuSans-79" x="462.451172"/>
     <use xlink:href="#DejaVuSans-20" x="521.630859"/>
     <use xlink:href="#DejaVuSans-49" x="553.417969"/>
     <use xlink:href="#DejaVuSans-6e" x="582.910156"/>
     <use xlink:href="#DejaVuSans-64" x="646.289062"/>
     <use xlink:href="#DejaVuSans-65" x="709.765625"/>
     <use xlink:href="#DejaVuSans-78" x="769.539062"/>
     <use xlink:href="#DejaVuSans-3a" x="828.71875"/>
     <use xlink:href="#DejaVuSans-20" x="862.410156"/>
     <use xlink:href="#DejaVuSans-30" x="894.197266"/>
     <use xlink:href="#DejaVuSans-2e" x="957.820312"/>
     <use xlink:href="#DejaVuSans-34" x="989.607422"/>
     <use xlink:href="#DejaVuSans-39" x="1053.230469"/>
     <use xlink:href="#DejaVuSans-31" x="1116.853516"/>
    </g>
    <!-- Peak Width: 2.0m -->
    <g transform="translate(724.529624 109.20805) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-57" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="365.804688"/>
     <use xlink:href="#DejaVuSans-64" x="393.587891"/>
     <use xlink:href="#DejaVuSans-74" x="457.064453"/>
     <use xlink:href="#DejaVuSans-68" x="496.273438"/>
     <use xlink:href="#DejaVuSans-3a" x="559.652344"/>
     <use xlink:href="#DejaVuSans-20" x="593.34375"/>
     <use xlink:href="#DejaVuSans-32" x="625.130859"/>
     <use xlink:href="#DejaVuSans-2e" x="688.753906"/>
     <use xlink:href="#DejaVuSans-30" x="720.541016"/>
     <use xlink:href="#DejaVuSans-6d" x="784.164062"/>
    </g>
   </g>
   <g id="text_46">
    <!-- Distance Tuning Curve -->
    <g transform="translate(549.284402 16.318125) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-54" d="M 31 4666 
L 4331 4666 
L 4331 3756 
L 2784 3756 
L 2784 0 
L 1581 0 
L 1581 3756 
L 31 3756 
L 31 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-44"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
     <use xlink:href="#DejaVuSans-Bold-54" x="525.195312"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="582.408203"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="653.599609"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="724.791016"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="759.068359"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="830.259766"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="901.841797"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="936.65625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1010.044922"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1081.236328"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="1130.552734"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1195.738281"/>
    </g>
    <!-- Peak: 4.3m, Sparsity: 0.406 -->
    <g transform="translate(531.359402 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-6b" d="M 538 4863 
L 1656 4863 
L 1656 2216 
L 2944 3500 
L 4244 3500 
L 2534 1894 
L 4378 0 
L 3022 0 
L 1656 1459 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-3a" d="M 716 3500 
L 1844 3500 
L 1844 2291 
L 716 2291 
L 716 3500 
z
M 716 1209 
L 1844 1209 
L 1844 0 
L 716 0 
L 716 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-34" d="M 2356 3675 
L 1038 1722 
L 2356 1722 
L 2356 3675 
z
M 2156 4666 
L 3494 4666 
L 3494 1722 
L 4159 1722 
L 4159 850 
L 3494 850 
L 3494 0 
L 2356 0 
L 2356 850 
L 288 850 
L 288 1881 
L 2156 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2c" d="M 653 1209 
L 1778 1209 
L 1778 256 
L 1006 -909 
L 341 -909 
L 653 256 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-53" d="M 3834 4519 
L 3834 3531 
Q 3450 3703 3084 3790 
Q 2719 3878 2394 3878 
Q 1963 3878 1756 3759 
Q 1550 3641 1550 3391 
Q 1550 3203 1689 3098 
Q 1828 2994 2194 2919 
L 2706 2816 
Q 3484 2659 3812 2340 
Q 4141 2022 4141 1434 
Q 4141 663 3683 286 
Q 3225 -91 2284 -91 
Q 1841 -91 1394 -6 
Q 947 78 500 244 
L 500 1259 
Q 947 1022 1364 901 
Q 1781 781 2169 781 
Q 2563 781 2772 912 
Q 2981 1044 2981 1288 
Q 2981 1506 2839 1625 
Q 2697 1744 2272 1838 
L 1806 1941 
Q 1106 2091 782 2419 
Q 459 2747 459 3303 
Q 459 4000 909 4375 
Q 1359 4750 2203 4750 
Q 2588 4750 2994 4692 
Q 3400 4634 3834 4519 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-79" d="M 78 3500 
L 1197 3500 
L 2138 1125 
L 2938 3500 
L 4056 3500 
L 2584 -331 
Q 2363 -916 2067 -1148 
Q 1772 -1381 1288 -1381 
L 641 -1381 
L 641 -647 
L 991 -647 
Q 1275 -647 1404 -556 
Q 1534 -466 1606 -231 
L 1638 -134 
L 78 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-36" d="M 2316 2303 
Q 2000 2303 1842 2098 
Q 1684 1894 1684 1484 
Q 1684 1075 1842 870 
Q 2000 666 2316 666 
Q 2634 666 2792 870 
Q 2950 1075 2950 1484 
Q 2950 1894 2792 2098 
Q 2634 2303 2316 2303 
z
M 3803 4544 
L 3803 3681 
Q 3506 3822 3243 3889 
Q 2981 3956 2731 3956 
Q 2194 3956 1894 3657 
Q 1594 3359 1544 2772 
Q 1750 2925 1990 3001 
Q 2231 3078 2516 3078 
Q 3231 3078 3670 2659 
Q 4109 2241 4109 1563 
Q 4109 813 3618 361 
Q 3128 -91 2303 -91 
Q 1394 -91 895 523 
Q 397 1138 397 2266 
Q 397 3422 980 4083 
Q 1563 4744 2578 4744 
Q 2900 4744 3203 4694 
Q 3506 4644 3803 4544 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-50"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="73.291016"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="141.113281"/>
     <use xlink:href="#DejaVuSans-Bold-6b" x="208.59375"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="275.097656"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="315.087891"/>
     <use xlink:href="#DejaVuSans-Bold-34" x="349.902344"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="419.482422"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="457.470703"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="527.050781"/>
     <use xlink:href="#DejaVuSans-Bold-2c" x="631.25"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="669.238281"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="704.052734"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="776.074219"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="847.65625"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="915.136719"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="964.453125"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1023.974609"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="1058.251953"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="1106.054688"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="1171.240234"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1211.230469"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1246.044922"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="1315.625"/>
     <use xlink:href="#DejaVuSans-Bold-34" x="1353.613281"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1423.193359"/>
     <use xlink:href="#DejaVuSans-Bold-36" x="1492.773438"/>
    </g>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="m10c3f88f20" d="M 0 5 
C 1.326016 5 2.597899 4.473168 3.535534 3.535534 
C 4.473168 2.597899 5 1.326016 5 0 
C 5 -1.326016 4.473168 -2.597899 3.535534 -3.535534 
C 2.597899 -4.473168 1.326016 -5 0 -5 
C -1.326016 -5 -2.597899 -4.473168 -3.535534 -3.535534 
C -4.473168 -2.597899 -5 -1.326016 -5 0 
C -5 1.326016 -4.473168 2.597899 -3.535534 3.535534 
C -2.597899 4.473168 -1.326016 5 0 5 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#paa0093ae95)">
     <use xlink:href="#m10c3f88f20" x="639.197301" y="57.695726" style="fill: #1f78b4; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="patch_16">
     <path d="M 441.859015 86.47675 
L 558.766515 86.47675 
Q 560.366515 86.47675 560.366515 84.87675 
L 560.366515 50.44925 
Q 560.366515 48.84925 558.766515 48.84925 
L 441.859015 48.84925 
Q 440.259015 48.84925 440.259015 50.44925 
L 440.259015 84.87675 
Q 440.259015 86.47675 441.859015 86.47675 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_75">
     <path d="M 443.459015 55.328 
L 451.459015 55.328 
L 459.459015 55.328 
" style="fill: none; stroke: #1f78b4; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
    </g>
    <g id="text_47">
     <!-- Tuning Curve -->
     <g transform="translate(465.859015 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-54"/>
      <use xlink:href="#DejaVuSans-75" x="45.958984"/>
      <use xlink:href="#DejaVuSans-6e" x="109.337891"/>
      <use xlink:href="#DejaVuSans-69" x="172.716797"/>
      <use xlink:href="#DejaVuSans-6e" x="200.5"/>
      <use xlink:href="#DejaVuSans-67" x="263.878906"/>
      <use xlink:href="#DejaVuSans-20" x="327.355469"/>
      <use xlink:href="#DejaVuSans-43" x="359.142578"/>
      <use xlink:href="#DejaVuSans-75" x="428.966797"/>
      <use xlink:href="#DejaVuSans-72" x="492.345703"/>
      <use xlink:href="#DejaVuSans-76" x="533.458984"/>
      <use xlink:href="#DejaVuSans-65" x="592.638672"/>
     </g>
    </g>
    <g id="line2d_76">
     <path d="M 443.459015 67.0705 
L 451.459015 67.0705 
L 459.459015 67.0705 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_48">
     <!-- Close threshold (3.0m) -->
     <g transform="translate(465.859015 69.8705) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6c" x="69.824219"/>
      <use xlink:href="#DejaVuSans-6f" x="97.607422"/>
      <use xlink:href="#DejaVuSans-73" x="158.789062"/>
      <use xlink:href="#DejaVuSans-65" x="210.888672"/>
      <use xlink:href="#DejaVuSans-20" x="272.412109"/>
      <use xlink:href="#DejaVuSans-74" x="304.199219"/>
      <use xlink:href="#DejaVuSans-68" x="343.408203"/>
      <use xlink:href="#DejaVuSans-72" x="406.787109"/>
      <use xlink:href="#DejaVuSans-65" x="445.650391"/>
      <use xlink:href="#DejaVuSans-73" x="507.173828"/>
      <use xlink:href="#DejaVuSans-68" x="559.273438"/>
      <use xlink:href="#DejaVuSans-6f" x="622.652344"/>
      <use xlink:href="#DejaVuSans-6c" x="683.833984"/>
      <use xlink:href="#DejaVuSans-64" x="711.617188"/>
      <use xlink:href="#DejaVuSans-20" x="775.09375"/>
      <use xlink:href="#DejaVuSans-28" x="806.880859"/>
      <use xlink:href="#DejaVuSans-33" x="845.894531"/>
      <use xlink:href="#DejaVuSans-2e" x="909.517578"/>
      <use xlink:href="#DejaVuSans-30" x="941.304688"/>
      <use xlink:href="#DejaVuSans-6d" x="1004.927734"/>
      <use xlink:href="#DejaVuSans-29" x="1102.339844"/>
     </g>
    </g>
    <g id="line2d_77">
     <path d="M 443.459015 78.813 
L 451.459015 78.813 
L 459.459015 78.813 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_49">
     <!-- Far threshold (5.0m) -->
     <g transform="translate(465.859015 81.613) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" x="48.394531"/>
      <use xlink:href="#DejaVuSans-72" x="109.673828"/>
      <use xlink:href="#DejaVuSans-20" x="150.787109"/>
      <use xlink:href="#DejaVuSans-74" x="182.574219"/>
      <use xlink:href="#DejaVuSans-68" x="221.783203"/>
      <use xlink:href="#DejaVuSans-72" x="285.162109"/>
      <use xlink:href="#DejaVuSans-65" x="324.025391"/>
      <use xlink:href="#DejaVuSans-73" x="385.548828"/>
      <use xlink:href="#DejaVuSans-68" x="437.648438"/>
      <use xlink:href="#DejaVuSans-6f" x="501.027344"/>
      <use xlink:href="#DejaVuSans-6c" x="562.208984"/>
      <use xlink:href="#DejaVuSans-64" x="589.992188"/>
      <use xlink:href="#DejaVuSans-20" x="653.46875"/>
      <use xlink:href="#DejaVuSans-28" x="685.255859"/>
      <use xlink:href="#DejaVuSans-35" x="724.269531"/>
      <use xlink:href="#DejaVuSans-2e" x="787.892578"/>
      <use xlink:href="#DejaVuSans-30" x="819.679688"/>
      <use xlink:href="#DejaVuSans-6d" x="883.302734"/>
      <use xlink:href="#DejaVuSans-29" x="980.714844"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_17">
    <path d="M 330.77382 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 330.77382 71.82685 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAABcAAAHBCAYAAACCOiAmAAACGUlEQVR4nO2b0W0bURDEnqRz3+k7VpwCjvkcAQzIAhbEzuzpLMiPc379nBHXOddq9nnOJp9zrnO+ZsPX5u0ch7eWO1URqYqIuope811bWgtSFZGqiFRFpJ0jvVog6ip6zXue3/Ee0eOcn9mXOVPzhv9nw6/z2A0fm7+Ww3cPxfVadg/FzJmqiJgD9ZpXRcAcqNe8KgLmQL3mVREwB+o1r4qAOVCveVUEzIF6zYdVHJsPe24ONHPAe0Q9z5HMkal5a0EyR3oqIuZAM//0cO9aMkd6KiLmQDPn4bNfWs3X8p4Nzxy5nq/v3fDry7qWlzZQs/myit61PLXmJ/M7a/PlEYnXkvmdqogUKJL5v4bver5dS0dEeM2rIuIN1GteFRFvoF5z9Vo6ojtVEamKSFX8/HDvWjoiZHpE6kC95lURhpsDzfxOR4R0RIjavCrCcHOgXvOqCMPNgWZ+x3tEfSWCZI5URUQdaOY4vPeWO95Ax+bvZRW/tVV8/5kNHwe6W7nY/Hrsar7+rxxtoJnz8KoIZI5M22JeS+aAt4renRco4g3Ua16gSOZIVUQKFPF+EpnX4jXv/D893LuWqohURaRAEa+5eS1VEfCaFyiSOdKfLUjmiLeK3p0XKOIN1GteoEjmSFVErt/aQIe/hVzvfPmWK16L1nwovt65dXiBIu2ch7cWoioiVRGpiojXvECRjgipikhVRLw7967lLyPgue9DbEF4AAAAAElFTkSuQmCC" id="image50fc3bda66" transform="scale(1 -1) translate(0 -215.52)" x="330.72" y="-71.52" width="11.04" height="215.52"/>
   <g id="matplotlib.axis_5"/>
   <g id="matplotlib.axis_6">
    <g id="ytick_18">
     <g id="line2d_78">
      <defs>
       <path id="mdbcfd3e3a2" d="M 0 0 
L 3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#mdbcfd3e3a2" x="341.56486" y="287.64765" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_50">
      <!-- 0.0 -->
      <g transform="translate(348.56486 291.066947) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_79">
      <g>
       <use xlink:href="#mdbcfd3e3a2" x="341.56486" y="244.48349" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_51">
      <!-- 0.2 -->
      <g transform="translate(348.56486 247.902787) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_80">
      <g>
       <use xlink:href="#mdbcfd3e3a2" x="341.56486" y="201.31933" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_52">
      <!-- 0.4 -->
      <g transform="translate(348.56486 204.738627) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_81">
      <g>
       <use xlink:href="#mdbcfd3e3a2" x="341.56486" y="158.15517" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_53">
      <!-- 0.6 -->
      <g transform="translate(348.56486 161.574467) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_82">
      <g>
       <use xlink:href="#mdbcfd3e3a2" x="341.56486" y="114.99101" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_54">
      <!-- 0.8 -->
      <g transform="translate(348.56486 118.410307) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-38" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_23">
     <g id="line2d_83">
      <g>
       <use xlink:href="#mdbcfd3e3a2" x="341.56486" y="71.82685" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_55">
      <!-- 1.0 -->
      <g transform="translate(348.56486 75.246147) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_56">
     <!-- Neural Activation -->
     <g transform="translate(369.519391 125.728109) rotate(-270) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_18">
    <path d="M 330.77382 287.64765 
L 336.16934 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 336.16934 71.82685 
L 330.77382 71.82685 
L 330.77382 287.64765 
z
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p5ab084d5f0">
   <rect x="42.113906" y="44.84925" width="269.776" height="269.776"/>
  </clipPath>
  <clipPath id="paa0093ae95">
   <rect x="436.259015" y="44.84925" width="377.678273" height="269.776"/>
  </clipPath>
 </defs>
</svg>
