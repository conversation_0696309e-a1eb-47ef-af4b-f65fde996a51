<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="826.256413pt" height="352.267438pt" viewBox="0 0 826.256413 352.267438" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:55:05.930646</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 352.267438 
L 826.256413 352.267438 
L 826.256413 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
L 311.889906 44.84925 
L 42.113906 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g clip-path="url(#p22344b5046)">
    <image xlink:href="data:image/png;base64,
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" id="imagebf3d087528" transform="scale(1 -1) translate(0 -270.24)" x="42.113906" y="-44.38525" width="270.24" height="270.24"/>
   </g>
   <g id="patch_3">
    <path d="M 177.001906 230.32025 
C 190.416675 230.32025 203.283815 224.990506 212.769489 215.504832 
C 222.255162 206.019159 227.584906 193.152018 227.584906 179.73725 
C 227.584906 166.322482 222.255162 153.455341 212.769489 143.969668 
C 203.283815 134.483994 190.416675 129.15425 177.001906 129.15425 
C 163.587138 129.15425 150.719998 134.483994 141.234324 143.969668 
C 131.74865 153.455341 126.418906 166.322482 126.418906 179.73725 
C 126.418906 193.152018 131.74865 206.019159 141.234324 215.504832 
C 150.719998 224.990506 163.587138 230.32025 177.001906 230.32025 
L 177.001906 230.32025 
z
" clip-path="url(#p22344b5046)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 177.001906 264.04225 
C 199.359854 264.04225 220.805087 255.159343 236.614543 239.349887 
C 252.424 223.540431 261.306906 202.095197 261.306906 179.73725 
C 261.306906 157.379303 252.424 135.934069 236.614543 120.124613 
C 220.805087 104.315157 199.359854 95.43225 177.001906 95.43225 
C 154.643959 95.43225 133.198725 104.315157 117.389269 120.124613 
C 101.579813 135.934069 92.696906 157.379303 92.696906 179.73725 
C 92.696906 202.095197 101.579813 223.540431 117.389269 239.349887 
C 133.198725 255.159343 154.643959 264.04225 177.001906 264.04225 
L 177.001906 264.04225 
z
" clip-path="url(#p22344b5046)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 177.001906 314.62525 
C 212.774622 314.62525 247.086996 300.412599 272.382126 275.11747 
C 297.677256 249.82234 311.889906 215.509966 311.889906 179.73725 
C 311.889906 143.964534 297.677256 109.65216 272.382126 84.35703 
C 247.086996 59.061901 212.774622 44.84925 177.001906 44.84925 
C 141.22919 44.84925 106.916817 59.061901 81.621687 84.35703 
C 56.326557 109.65216 42.113906 143.964534 42.113906 179.73725 
C 42.113906 215.509966 56.326557 249.82234 81.621687 275.11747 
C 106.916817 300.412599 141.22919 314.62525 177.001906 314.62525 
L 177.001906 314.62525 
z
" clip-path="url(#p22344b5046)" style="fill: none; opacity: 0.7; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" clip-path="url(#p22344b5046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="mce834b5fe3" d="M 0 0 
L 0 3.5 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#mce834b5fe3" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −8 -->
      <g transform="translate(35.479922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 75.835906 314.62525 
L 75.835906 44.84925 
" clip-path="url(#p22344b5046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#mce834b5fe3" x="75.835906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −6 -->
      <g transform="translate(69.201922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 109.557906 314.62525 
L 109.557906 44.84925 
" clip-path="url(#p22344b5046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#mce834b5fe3" x="109.557906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_3">
      <!-- −4 -->
      <g transform="translate(102.923922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 143.279906 314.62525 
L 143.279906 44.84925 
" clip-path="url(#p22344b5046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#mce834b5fe3" x="143.279906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_4">
      <!-- −2 -->
      <g transform="translate(136.645922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 177.001906 314.62525 
L 177.001906 44.84925 
" clip-path="url(#p22344b5046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#mce834b5fe3" x="177.001906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(174.138781 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 210.723906 314.62525 
L 210.723906 44.84925 
" clip-path="url(#p22344b5046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#mce834b5fe3" x="210.723906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 2 -->
      <g transform="translate(207.860781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 244.445906 314.62525 
L 244.445906 44.84925 
" clip-path="url(#p22344b5046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#mce834b5fe3" x="244.445906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 4 -->
      <g transform="translate(241.582781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <path d="M 278.167906 314.62525 
L 278.167906 44.84925 
" clip-path="url(#p22344b5046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#mce834b5fe3" x="278.167906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 6 -->
      <g transform="translate(275.304781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_17">
      <path d="M 311.889906 314.62525 
L 311.889906 44.84925 
" clip-path="url(#p22344b5046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#mce834b5fe3" x="311.889906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 8 -->
      <g transform="translate(309.026781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- Relative X Position (m) -->
     <g transform="translate(105.68925 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-52" d="M 2297 2597 
Q 2675 2597 2839 2737 
Q 3003 2878 3003 3200 
Q 3003 3519 2839 3656 
Q 2675 3794 2297 3794 
L 1791 3794 
L 1791 2597 
L 2297 2597 
z
M 1791 1766 
L 1791 0 
L 588 0 
L 588 4666 
L 2425 4666 
Q 3347 4666 3776 4356 
Q 4206 4047 4206 3378 
Q 4206 2916 3982 2619 
Q 3759 2322 3309 2181 
Q 3556 2125 3751 1926 
Q 3947 1728 4147 1325 
L 4800 0 
L 3519 0 
L 2950 1159 
Q 2778 1509 2601 1637 
Q 2425 1766 2131 1766 
L 1791 1766 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6c" d="M 538 4863 
L 1656 4863 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-76" d="M 97 3500 
L 1216 3500 
L 2088 1081 
L 2956 3500 
L 4078 3500 
L 2700 0 
L 1472 0 
L 97 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-58" d="M 3188 2381 
L 4806 0 
L 3553 0 
L 2463 1594 
L 1381 0 
L 122 0 
L 1741 2381 
L 184 4666 
L 1441 4666 
L 2463 3163 
L 3481 4666 
L 4744 4666 
L 3188 2381 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-50" d="M 588 4666 
L 2584 4666 
Q 3475 4666 3951 4270 
Q 4428 3875 4428 3144 
Q 4428 2409 3951 2014 
Q 3475 1619 2584 1619 
L 1791 1619 
L 1791 0 
L 588 0 
L 588 4666 
z
M 1791 3794 
L 1791 2491 
L 2456 2491 
Q 2806 2491 2997 2661 
Q 3188 2831 3188 3144 
Q 3188 3456 2997 3625 
Q 2806 3794 2456 3794 
L 1791 3794 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6f" d="M 2203 2784 
Q 1831 2784 1636 2517 
Q 1441 2250 1441 1747 
Q 1441 1244 1636 976 
Q 1831 709 2203 709 
Q 2569 709 2762 976 
Q 2956 1244 2956 1747 
Q 2956 2250 2762 2517 
Q 2569 2784 2203 2784 
z
M 2203 3584 
Q 3106 3584 3614 3096 
Q 4122 2609 4122 1747 
Q 4122 884 3614 396 
Q 3106 -91 2203 -91 
Q 1297 -91 786 396 
Q 275 884 275 1747 
Q 275 2609 786 3096 
Q 1297 3584 2203 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-28" d="M 2413 -844 
L 1484 -844 
Q 1006 -72 778 623 
Q 550 1319 550 2003 
Q 550 2688 779 3389 
Q 1009 4091 1484 4856 
L 2413 4856 
Q 2013 4116 1813 3408 
Q 1613 2700 1613 2009 
Q 1613 1319 1811 609 
Q 2009 -100 2413 -844 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6d" d="M 3781 2919 
Q 3994 3244 4286 3414 
Q 4578 3584 4928 3584 
Q 5531 3584 5847 3212 
Q 6163 2841 6163 2131 
L 6163 0 
L 5038 0 
L 5038 1825 
Q 5041 1866 5042 1909 
Q 5044 1953 5044 2034 
Q 5044 2406 4934 2573 
Q 4825 2741 4581 2741 
Q 4263 2741 4089 2478 
Q 3916 2216 3909 1719 
L 3909 0 
L 2784 0 
L 2784 1825 
Q 2784 2406 2684 2573 
Q 2584 2741 2328 2741 
Q 2006 2741 1831 2477 
Q 1656 2213 1656 1722 
L 1656 0 
L 531 0 
L 531 3500 
L 1656 3500 
L 1656 2988 
Q 1863 3284 2130 3434 
Q 2397 3584 2719 3584 
Q 3081 3584 3359 3409 
Q 3638 3234 3781 2919 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-29" d="M 513 -844 
Q 913 -100 1113 609 
Q 1313 1319 1313 2009 
Q 1313 2700 1113 3408 
Q 913 4116 513 4856 
L 1441 4856 
Q 1916 4091 2145 3389 
Q 2375 2688 2375 2003 
Q 2375 1319 2147 623 
Q 1919 -72 1441 -844 
L 513 -844 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-58" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="573.583984"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="608.398438"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="681.689453"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="750.390625"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="809.912109"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="844.189453"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="891.992188"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="926.269531"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="994.970703"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1066.162109"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1100.976562"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1146.679688"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1250.878906"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_19">
      <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" clip-path="url(#p22344b5046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <defs>
       <path id="mfe31b2f9bd" d="M 0 0 
L -3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#mfe31b2f9bd" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_11">
      <!-- −8 -->
      <g transform="translate(21.845938 318.044547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_21">
      <path d="M 42.113906 280.90325 
L 311.889906 280.90325 
" clip-path="url(#p22344b5046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#mfe31b2f9bd" x="42.113906" y="280.90325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_12">
      <!-- −6 -->
      <g transform="translate(21.845938 284.322547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_23">
      <path d="M 42.113906 247.18125 
L 311.889906 247.18125 
" clip-path="url(#p22344b5046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#mfe31b2f9bd" x="42.113906" y="247.18125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_13">
      <!-- −4 -->
      <g transform="translate(21.845938 250.600547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_25">
      <path d="M 42.113906 213.45925 
L 311.889906 213.45925 
" clip-path="url(#p22344b5046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#mfe31b2f9bd" x="42.113906" y="213.45925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_14">
      <!-- −2 -->
      <g transform="translate(21.845938 216.878547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_27">
      <path d="M 42.113906 179.73725 
L 311.889906 179.73725 
" clip-path="url(#p22344b5046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#mfe31b2f9bd" x="42.113906" y="179.73725" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 0 -->
      <g transform="translate(29.387656 183.156547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_29">
      <path d="M 42.113906 146.01525 
L 311.889906 146.01525 
" clip-path="url(#p22344b5046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#mfe31b2f9bd" x="42.113906" y="146.01525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 2 -->
      <g transform="translate(29.387656 149.434547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_31">
      <path d="M 42.113906 112.29325 
L 311.889906 112.29325 
" clip-path="url(#p22344b5046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#mfe31b2f9bd" x="42.113906" y="112.29325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 4 -->
      <g transform="translate(29.387656 115.712547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_33">
      <path d="M 42.113906 78.57125 
L 311.889906 78.57125 
" clip-path="url(#p22344b5046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#mfe31b2f9bd" x="42.113906" y="78.57125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 6 -->
      <g transform="translate(29.387656 81.990547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_35">
      <path d="M 42.113906 44.84925 
L 311.889906 44.84925 
" clip-path="url(#p22344b5046)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#mfe31b2f9bd" x="42.113906" y="44.84925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 8 -->
      <g transform="translate(29.387656 48.268547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_20">
     <!-- Relative Y Position (m) -->
     <g transform="translate(15.558281 250.792094) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-59" d="M -63 4666 
L 1253 4666 
L 2316 3003 
L 3378 4666 
L 4697 4666 
L 2919 1966 
L 2919 0 
L 1716 0 
L 1716 1966 
L -63 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-59" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="568.896484"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="603.710938"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="677.001953"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="745.703125"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="805.224609"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="839.501953"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="887.304688"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="921.582031"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="990.283203"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1061.474609"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1096.289062"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1141.992188"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1246.191406"/>
     </g>
    </g>
   </g>
   <g id="patch_6">
    <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_21">
    <g id="patch_8">
     <path d="M 200.10902 150.124824 
L 225.429957 150.124824 
Q 227.229957 150.124824 227.229957 148.324824 
L 227.229957 139.614511 
Q 227.229957 137.814511 225.429957 137.814511 
L 200.10902 137.814511 
Q 198.30902 137.814511 198.30902 139.614511 
L 198.30902 148.324824 
Q 198.30902 150.124824 200.10902 150.124824 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 3.0m -->
    <g style="fill: #ffffff" transform="translate(200.10902 146.453105) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-33" d="M 2981 2516 
Q 3453 2394 3698 2092 
Q 3944 1791 3944 1325 
Q 3944 631 3412 270 
Q 2881 -91 1863 -91 
Q 1503 -91 1142 -33 
Q 781 25 428 141 
L 428 1069 
Q 766 900 1098 814 
Q 1431 728 1753 728 
Q 2231 728 2486 893 
Q 2741 1059 2741 1369 
Q 2741 1688 2480 1852 
Q 2219 2016 1709 2016 
L 1228 2016 
L 1228 2791 
L 1734 2791 
Q 2188 2791 2409 2933 
Q 2631 3075 2631 3366 
Q 2631 3634 2415 3781 
Q 2200 3928 1806 3928 
Q 1516 3928 1219 3862 
Q 922 3797 628 3669 
L 628 4550 
Q 984 4650 1334 4700 
Q 1684 4750 2022 4750 
Q 2931 4750 3382 4451 
Q 3834 4153 3834 3553 
Q 3834 3144 3618 2883 
Q 3403 2622 2981 2516 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2e" d="M 653 1209 
L 1778 1209 
L 1778 0 
L 653 0 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-30" d="M 2944 2338 
Q 2944 3213 2780 3570 
Q 2616 3928 2228 3928 
Q 1841 3928 1675 3570 
Q 1509 3213 1509 2338 
Q 1509 1453 1675 1090 
Q 1841 728 2228 728 
Q 2613 728 2778 1090 
Q 2944 1453 2944 2338 
z
M 4147 2328 
Q 4147 1169 3647 539 
Q 3147 -91 2228 -91 
Q 1306 -91 806 539 
Q 306 1169 306 2328 
Q 306 3491 806 4120 
Q 1306 4750 2228 4750 
Q 3147 4750 3647 4120 
Q 4147 3491 4147 2328 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-33"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_22">
    <g id="patch_9">
     <path d="M 223.954075 126.279769 
L 249.275012 126.279769 
Q 251.075012 126.279769 251.075012 124.479769 
L 251.075012 115.769457 
Q 251.075012 113.969457 249.275012 113.969457 
L 223.954075 113.969457 
Q 222.154075 113.969457 222.154075 115.769457 
L 222.154075 124.479769 
Q 222.154075 126.279769 223.954075 126.279769 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 5.0m -->
    <g style="fill: #ffffff" transform="translate(223.954075 122.60805) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-35" d="M 678 4666 
L 3669 4666 
L 3669 3781 
L 1638 3781 
L 1638 3059 
Q 1775 3097 1914 3117 
Q 2053 3138 2203 3138 
Q 3056 3138 3531 2711 
Q 4006 2284 4006 1522 
Q 4006 766 3489 337 
Q 2972 -91 2053 -91 
Q 1656 -91 1267 -14 
Q 878 63 494 219 
L 494 1166 
Q 875 947 1217 837 
Q 1559 728 1863 728 
Q 2300 728 2551 942 
Q 2803 1156 2803 1522 
Q 2803 1891 2551 2103 
Q 2300 2316 1863 2316 
Q 1603 2316 1309 2248 
Q 1016 2181 678 2041 
L 678 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-35"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_23">
    <g id="patch_10">
     <path d="M 259.721657 90.512187 
L 285.042595 90.512187 
Q 286.842595 90.512187 286.842595 88.712187 
L 286.842595 80.001874 
Q 286.842595 78.201874 285.042595 78.201874 
L 259.721657 78.201874 
Q 257.921657 78.201874 257.921657 80.001874 
L 257.921657 88.712187 
Q 257.921657 90.512187 259.721657 90.512187 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 8.0m -->
    <g style="fill: #ffffff" transform="translate(259.721657 86.840468) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-38" d="M 2228 2088 
Q 1891 2088 1709 1903 
Q 1528 1719 1528 1375 
Q 1528 1031 1709 848 
Q 1891 666 2228 666 
Q 2563 666 2741 848 
Q 2919 1031 2919 1375 
Q 2919 1722 2741 1905 
Q 2563 2088 2228 2088 
z
M 1350 2484 
Q 925 2613 709 2878 
Q 494 3144 494 3541 
Q 494 4131 934 4440 
Q 1375 4750 2228 4750 
Q 3075 4750 3515 4442 
Q 3956 4134 3956 3541 
Q 3956 3144 3739 2878 
Q 3522 2613 3097 2484 
Q 3572 2353 3814 2058 
Q 4056 1763 4056 1313 
Q 4056 619 3595 264 
Q 3134 -91 2228 -91 
Q 1319 -91 855 264 
Q 391 619 391 1313 
Q 391 1763 633 2058 
Q 875 2353 1350 2484 
z
M 1631 3419 
Q 1631 3141 1786 2991 
Q 1941 2841 2228 2841 
Q 2509 2841 2662 2991 
Q 2816 3141 2816 3419 
Q 2816 3697 2662 3845 
Q 2509 3994 2228 3994 
Q 1941 3994 1786 3844 
Q 1631 3694 1631 3419 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-38"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_24">
    <!-- Mid-Distance Cell (Neuron 47) -->
    <g transform="translate(75.364719 16.411875) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-4d" d="M 588 4666 
L 2119 4666 
L 3181 2169 
L 4250 4666 
L 5778 4666 
L 5778 0 
L 4641 0 
L 4641 3413 
L 3566 897 
L 2803 897 
L 1728 3413 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-64" d="M 2919 2988 
L 2919 4863 
L 4044 4863 
L 4044 0 
L 2919 0 
L 2919 506 
Q 2688 197 2409 53 
Q 2131 -91 1766 -91 
Q 1119 -91 703 423 
Q 288 938 288 1747 
Q 288 2556 703 3070 
Q 1119 3584 1766 3584 
Q 2128 3584 2408 3439 
Q 2688 3294 2919 2988 
z
M 2181 722 
Q 2541 722 2730 984 
Q 2919 1247 2919 1747 
Q 2919 2247 2730 2509 
Q 2541 2772 2181 2772 
Q 1825 2772 1636 2509 
Q 1447 2247 1447 1747 
Q 1447 1247 1636 984 
Q 1825 722 2181 722 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2d" d="M 347 2297 
L 2309 2297 
L 2309 1388 
L 347 1388 
L 347 2297 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-44" d="M 1791 3756 
L 1791 909 
L 2222 909 
Q 2959 909 3348 1275 
Q 3738 1641 3738 2338 
Q 3738 3031 3350 3393 
Q 2963 3756 2222 3756 
L 1791 3756 
z
M 588 4666 
L 1856 4666 
Q 2919 4666 3439 4514 
Q 3959 4363 4331 4000 
Q 4659 3684 4818 3271 
Q 4978 2859 4978 2338 
Q 4978 1809 4818 1395 
Q 4659 981 4331 666 
Q 3956 303 3431 151 
Q 2906 0 1856 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4e" d="M 588 4666 
L 1931 4666 
L 3628 1466 
L 3628 4666 
L 4769 4666 
L 4769 0 
L 3425 0 
L 1728 3200 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-75" d="M 500 1363 
L 500 3500 
L 1625 3500 
L 1625 3150 
Q 1625 2866 1622 2436 
Q 1619 2006 1619 1863 
Q 1619 1441 1641 1255 
Q 1663 1069 1716 984 
Q 1784 875 1895 815 
Q 2006 756 2150 756 
Q 2500 756 2700 1025 
Q 2900 1294 2900 1772 
L 2900 3500 
L 4019 3500 
L 4019 0 
L 2900 0 
L 2900 506 
Q 2647 200 2364 54 
Q 2081 -91 1741 -91 
Q 1134 -91 817 281 
Q 500 653 500 1363 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-34" d="M 2356 3675 
L 1038 1722 
L 2356 1722 
L 2356 3675 
z
M 2156 4666 
L 3494 4666 
L 3494 1722 
L 4159 1722 
L 4159 850 
L 3494 850 
L 3494 0 
L 2356 0 
L 2356 850 
L 288 850 
L 288 1881 
L 2156 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-37" d="M 428 4666 
L 3944 4666 
L 3944 3988 
L 2125 0 
L 953 0 
L 2675 3781 
L 428 3781 
L 428 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-4d"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="99.511719"/>
     <use xlink:href="#DejaVuSans-Bold-64" x="133.789062"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="205.371094"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="246.875"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="329.882812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="364.160156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="423.681641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.484375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="538.964844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="610.15625"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="669.433594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="737.255859"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="772.070312"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="845.458984"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="913.28125"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="947.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="981.835938"/>
     <use xlink:href="#DejaVuSans-Bold-28" x="1016.650391"/>
     <use xlink:href="#DejaVuSans-Bold-4e" x="1062.353516"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1146.044922"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1213.867188"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1285.058594"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="1334.375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1403.076172"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1474.267578"/>
     <use xlink:href="#DejaVuSans-Bold-34" x="1509.082031"/>
     <use xlink:href="#DejaVuSans-Bold-37" x="1578.662109"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="1648.242188"/>
    </g>
    <!-- 2D Activation Map -->
    <g transform="translate(114.950656 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-32" d="M 1844 884 
L 3897 884 
L 3897 0 
L 506 0 
L 506 884 
L 2209 2388 
Q 2438 2594 2547 2791 
Q 2656 2988 2656 3200 
Q 2656 3528 2436 3728 
Q 2216 3928 1850 3928 
Q 1569 3928 1234 3808 
Q 900 3688 519 3450 
L 519 4475 
Q 925 4609 1322 4679 
Q 1719 4750 2100 4750 
Q 2938 4750 3402 4381 
Q 3866 4013 3866 3353 
Q 3866 2972 3669 2642 
Q 3472 2313 2841 1759 
L 1844 884 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-41" d="M 3419 850 
L 1538 850 
L 1241 0 
L 31 0 
L 1759 4666 
L 3194 4666 
L 4922 0 
L 3713 0 
L 3419 850 
z
M 1838 1716 
L 3116 1716 
L 2478 3572 
L 1838 1716 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-32"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="152.587891"/>
     <use xlink:href="#DejaVuSans-Bold-41" x="187.402344"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="264.794922"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="324.072266"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="371.875"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="406.152344"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.337891"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="538.818359"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="586.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="620.898438"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="689.599609"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="760.791016"/>
     <use xlink:href="#DejaVuSans-Bold-4d" x="795.605469"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="895.117188"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="962.597656"/>
    </g>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="mb033706ebd" d="M 0 -5.477226 
L -1.229714 -1.692556 
L -5.209151 -1.692556 
L -1.989719 0.646499 
L -3.219432 4.431169 
L -0 2.092114 
L 3.219432 4.431169 
L 1.989719 0.646499 
L 5.209151 -1.692556 
L 1.229714 -1.692556 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#p22344b5046)">
     <use xlink:href="#mb033706ebd" x="177.001906" y="179.73725" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_11">
     <path d="M 242.388656 62.99175 
L 307.889906 62.99175 
L 307.889906 48.84925 
L 242.388656 48.84925 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="PathCollection_2">
     <g>
      <use xlink:href="#mb033706ebd" x="253.588656" y="56.028" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
     </g>
    </g>
    <g id="text_25">
     <!-- Observer -->
     <g transform="translate(267.988656 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4f"/>
      <use xlink:href="#DejaVuSans-62" x="78.710938"/>
      <use xlink:href="#DejaVuSans-73" x="142.1875"/>
      <use xlink:href="#DejaVuSans-65" x="194.287109"/>
      <use xlink:href="#DejaVuSans-72" x="255.810547"/>
      <use xlink:href="#DejaVuSans-76" x="296.923828"/>
      <use xlink:href="#DejaVuSans-65" x="356.103516"/>
      <use xlink:href="#DejaVuSans-72" x="417.626953"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_12">
    <path d="M 441.335015 314.62525 
L 816.193288 314.62525 
L 816.193288 44.84925 
L 441.335015 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="m4b2c34d8b0" d="M 446.019271 -37.642188 
L 446.019271 -37.642188 
L 455.387784 -49.522239 
L 464.756296 -47.86691 
L 474.124808 -47.599531 
L 483.49332 -44.758716 
L 492.861832 -48.319404 
L 502.230344 -51.671758 
L 511.598857 -60.368717 
L 520.967369 -108.884849 
L 530.335881 -53.355358 
L 539.704393 -92.057756 
L 549.072905 -113.238526 
L 558.441418 -151.249149 
L 567.80993 -168.800554 
L 577.178442 -73.589398 
L 586.546954 -66.543187 
L 595.915466 -76.198284 
L 605.283978 -212.061582 
L 614.652491 -175.781944 
L 624.021003 -268.918961 
L 633.389515 -260.25577 
L 642.758027 -294.571711 
L 652.126539 -179.31427 
L 661.495051 -252.113407 
L 670.863564 -120.979172 
L 680.232076 -205.390725 
L 689.600588 -167.29544 
L 698.9691 -176.170619 
L 708.337612 -243.967781 
L 717.706124 -292.45494 
L 717.706124 -37.642188 
L 717.706124 -37.642188 
L 708.337612 -37.642188 
L 698.9691 -37.642188 
L 689.600588 -37.642188 
L 680.232076 -37.642188 
L 670.863564 -37.642188 
L 661.495051 -37.642188 
L 652.126539 -37.642188 
L 642.758027 -37.642188 
L 633.389515 -37.642188 
L 624.021003 -37.642188 
L 614.652491 -37.642188 
L 605.283978 -37.642188 
L 595.915466 -37.642188 
L 586.546954 -37.642188 
L 577.178442 -37.642188 
L 567.80993 -37.642188 
L 558.441418 -37.642188 
L 549.072905 -37.642188 
L 539.704393 -37.642188 
L 530.335881 -37.642188 
L 520.967369 -37.642188 
L 511.598857 -37.642188 
L 502.230344 -37.642188 
L 492.861832 -37.642188 
L 483.49332 -37.642188 
L 474.124808 -37.642188 
L 464.756296 -37.642188 
L 455.387784 -37.642188 
L 446.019271 -37.642188 
z
" style="stroke: #1f78b4; stroke-opacity: 0.3"/>
    </defs>
    <g clip-path="url(#pf6cadea988)">
     <use xlink:href="#m4b2c34d8b0" x="0" y="352.267438" style="fill: #1f78b4; fill-opacity: 0.3; stroke: #1f78b4; stroke-opacity: 0.3"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10">
     <g id="line2d_37">
      <path d="M 441.335015 314.62525 
L 441.335015 44.84925 
" clip-path="url(#pf6cadea988)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#mce834b5fe3" x="441.335015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 0 -->
      <g transform="translate(438.47189 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_39">
      <path d="M 488.192299 314.62525 
L 488.192299 44.84925 
" clip-path="url(#pf6cadea988)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#mce834b5fe3" x="488.192299" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 1 -->
      <g transform="translate(485.329174 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_41">
      <path d="M 535.049584 314.62525 
L 535.049584 44.84925 
" clip-path="url(#pf6cadea988)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#mce834b5fe3" x="535.049584" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 2 -->
      <g transform="translate(532.186459 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_43">
      <path d="M 581.906868 314.62525 
L 581.906868 44.84925 
" clip-path="url(#pf6cadea988)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#mce834b5fe3" x="581.906868" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 3 -->
      <g transform="translate(579.043743 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_45">
      <path d="M 628.764152 314.62525 
L 628.764152 44.84925 
" clip-path="url(#pf6cadea988)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#mce834b5fe3" x="628.764152" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 4 -->
      <g transform="translate(625.901027 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_47">
      <path d="M 675.621436 314.62525 
L 675.621436 44.84925 
" clip-path="url(#pf6cadea988)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#mce834b5fe3" x="675.621436" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 5 -->
      <g transform="translate(672.758311 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_49">
      <path d="M 722.47872 314.62525 
L 722.47872 44.84925 
" clip-path="url(#pf6cadea988)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#mce834b5fe3" x="722.47872" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 6 -->
      <g transform="translate(719.615595 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_51">
      <path d="M 769.336004 314.62525 
L 769.336004 44.84925 
" clip-path="url(#pf6cadea988)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#mce834b5fe3" x="769.336004" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 7 -->
      <g transform="translate(766.472879 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-37"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_53">
      <path d="M 816.193288 314.62525 
L 816.193288 44.84925 
" clip-path="url(#pf6cadea988)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#mce834b5fe3" x="816.193288" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 8 -->
      <g transform="translate(813.330163 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_35">
     <!-- Inter-Agent Distance (m) -->
     <g transform="translate(551.421261 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-49" d="M 588 4666 
L 1791 4666 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-49"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="37.207031"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="108.398438"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="156.201172"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="224.023438"/>
      <use xlink:href="#DejaVuSans-Bold-2d" x="273.339844"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="314.84375"/>
      <use xlink:href="#DejaVuSans-Bold-67" x="392.236328"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="463.818359"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="531.640625"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="602.832031"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="650.634766"/>
      <use xlink:href="#DejaVuSans-Bold-44" x="685.449219"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="768.457031"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="802.734375"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="862.255859"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="910.058594"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="977.539062"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="1048.730469"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="1108.007812"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1175.830078"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1210.644531"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1256.347656"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1360.546875"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_55">
      <path d="M 441.335015 314.62525 
L 816.193288 314.62525 
" clip-path="url(#pf6cadea988)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#mfe31b2f9bd" x="441.335015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 0.00 -->
      <g transform="translate(414.295953 318.044547) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_57">
      <path d="M 441.335015 268.801935 
L 816.193288 268.801935 
" clip-path="url(#pf6cadea988)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#mfe31b2f9bd" x="441.335015" y="268.801935" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 0.05 -->
      <g transform="translate(414.295953 272.221232) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_59">
      <path d="M 441.335015 222.97862 
L 816.193288 222.97862 
" clip-path="url(#pf6cadea988)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#mfe31b2f9bd" x="441.335015" y="222.97862" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 0.10 -->
      <g transform="translate(414.295953 226.397917) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_61">
      <path d="M 441.335015 177.155305 
L 816.193288 177.155305 
" clip-path="url(#pf6cadea988)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#mfe31b2f9bd" x="441.335015" y="177.155305" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 0.15 -->
      <g transform="translate(414.295953 180.574602) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_63">
      <path d="M 441.335015 131.33199 
L 816.193288 131.33199 
" clip-path="url(#pf6cadea988)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#mfe31b2f9bd" x="441.335015" y="131.33199" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 0.20 -->
      <g transform="translate(414.295953 134.751287) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
       <use xlink:href="#DejaVuSans-30" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_65">
      <path d="M 441.335015 85.508675 
L 816.193288 85.508675 
" clip-path="url(#pf6cadea988)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#mfe31b2f9bd" x="441.335015" y="85.508675" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 0.25 -->
      <g transform="translate(414.295953 88.927972) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
       <use xlink:href="#DejaVuSans-35" x="159.033203"/>
      </g>
     </g>
    </g>
    <g id="text_42">
     <!-- Neural Activation -->
     <g transform="translate(408.008297 233.746391) rotate(-90) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="line2d_67">
    <path d="M 446.019271 314.62525 
L 455.387784 302.745198 
L 464.756296 304.400528 
L 474.124808 304.667907 
L 483.49332 307.508721 
L 492.861832 303.948034 
L 502.230344 300.595679 
L 511.598857 291.89872 
L 520.967369 243.382589 
L 530.335881 298.91208 
L 539.704393 260.209682 
L 549.072905 239.028911 
L 558.441418 201.018289 
L 567.80993 183.466884 
L 577.178442 278.67804 
L 586.546954 285.72425 
L 595.915466 276.069153 
L 605.283978 140.205856 
L 614.652491 176.485494 
L 624.021003 83.348477 
L 633.389515 92.011668 
L 642.758027 57.695726 
L 652.126539 172.953167 
L 661.495051 100.15403 
L 670.863564 231.288266 
L 680.232076 146.876713 
L 689.600588 184.971998 
L 698.9691 176.096819 
L 708.337612 108.299657 
L 717.706124 59.812497 
" clip-path="url(#pf6cadea988)" style="fill: none; stroke: #1f78b4; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_68">
    <path d="M 642.758027 314.62525 
L 642.758027 44.84925 
" clip-path="url(#pf6cadea988)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #1f78b4; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_69">
    <path d="M 581.906868 314.62525 
L 581.906868 44.84925 
" clip-path="url(#pf6cadea988)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_70">
    <path d="M 675.621436 314.62525 
L 675.621436 44.84925 
" clip-path="url(#pf6cadea988)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="patch_13">
    <path d="M 441.335015 314.62525 
L 441.335015 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_14">
    <path d="M 441.335015 314.62525 
L 816.193288 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_43">
    <g id="patch_15">
     <path d="M 703.009124 114.0718 
L 797.450374 114.0718 
Q 800.650374 114.0718 800.650374 110.8718 
L 800.650374 58.33805 
Q 800.650374 55.13805 797.450374 55.13805 
L 703.009124 55.13805 
Q 699.809124 55.13805 699.809124 58.33805 
L 699.809124 110.8718 
Q 699.809124 114.0718 703.009124 114.0718 
z
" style="fill: #ffffff; opacity: 0.9; stroke: #808080; stroke-linejoin: miter"/>
    </g>
    <!-- Peak Distance: 4.3m -->
    <g transform="translate(715.184124 64.4168) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-3a" d="M 750 794 
L 1409 794 
L 1409 0 
L 750 0 
L 750 794 
z
M 750 3309 
L 1409 3309 
L 1409 2516 
L 750 2516 
L 750 3309 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-44" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="346.179688"/>
     <use xlink:href="#DejaVuSans-73" x="373.962891"/>
     <use xlink:href="#DejaVuSans-74" x="426.0625"/>
     <use xlink:href="#DejaVuSans-61" x="465.271484"/>
     <use xlink:href="#DejaVuSans-6e" x="526.550781"/>
     <use xlink:href="#DejaVuSans-63" x="589.929688"/>
     <use xlink:href="#DejaVuSans-65" x="644.910156"/>
     <use xlink:href="#DejaVuSans-3a" x="706.433594"/>
     <use xlink:href="#DejaVuSans-20" x="740.125"/>
     <use xlink:href="#DejaVuSans-34" x="771.912109"/>
     <use xlink:href="#DejaVuSans-2e" x="835.535156"/>
     <use xlink:href="#DejaVuSans-33" x="867.322266"/>
     <use xlink:href="#DejaVuSans-6d" x="930.945312"/>
    </g>
    <!-- Max Activation: 0.280 -->
    <g transform="translate(710.176624 73.37505) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-4d"/>
     <use xlink:href="#DejaVuSans-61" x="86.279297"/>
     <use xlink:href="#DejaVuSans-78" x="147.558594"/>
     <use xlink:href="#DejaVuSans-20" x="206.738281"/>
     <use xlink:href="#DejaVuSans-41" x="238.525391"/>
     <use xlink:href="#DejaVuSans-63" x="305.183594"/>
     <use xlink:href="#DejaVuSans-74" x="360.164062"/>
     <use xlink:href="#DejaVuSans-69" x="399.373047"/>
     <use xlink:href="#DejaVuSans-76" x="427.15625"/>
     <use xlink:href="#DejaVuSans-61" x="486.335938"/>
     <use xlink:href="#DejaVuSans-74" x="547.615234"/>
     <use xlink:href="#DejaVuSans-69" x="586.824219"/>
     <use xlink:href="#DejaVuSans-6f" x="614.607422"/>
     <use xlink:href="#DejaVuSans-6e" x="675.789062"/>
     <use xlink:href="#DejaVuSans-3a" x="739.167969"/>
     <use xlink:href="#DejaVuSans-20" x="772.859375"/>
     <use xlink:href="#DejaVuSans-30" x="804.646484"/>
     <use xlink:href="#DejaVuSans-2e" x="868.269531"/>
     <use xlink:href="#DejaVuSans-32" x="900.056641"/>
     <use xlink:href="#DejaVuSans-38" x="963.679688"/>
     <use xlink:href="#DejaVuSans-30" x="1027.302734"/>
    </g>
    <!-- Sparsity: 0.424 -->
    <g transform="translate(737.280374 82.3333) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-70" x="63.476562"/>
     <use xlink:href="#DejaVuSans-61" x="126.953125"/>
     <use xlink:href="#DejaVuSans-72" x="188.232422"/>
     <use xlink:href="#DejaVuSans-73" x="229.345703"/>
     <use xlink:href="#DejaVuSans-69" x="281.445312"/>
     <use xlink:href="#DejaVuSans-74" x="309.228516"/>
     <use xlink:href="#DejaVuSans-79" x="348.4375"/>
     <use xlink:href="#DejaVuSans-3a" x="400.367188"/>
     <use xlink:href="#DejaVuSans-20" x="434.058594"/>
     <use xlink:href="#DejaVuSans-30" x="465.845703"/>
     <use xlink:href="#DejaVuSans-2e" x="529.46875"/>
     <use xlink:href="#DejaVuSans-34" x="561.255859"/>
     <use xlink:href="#DejaVuSans-32" x="624.878906"/>
     <use xlink:href="#DejaVuSans-34" x="688.501953"/>
    </g>
    <!-- Peak Significance: 2.86 -->
    <g transform="translate(704.462874 91.29155) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-53" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="332.654297"/>
     <use xlink:href="#DejaVuSans-67" x="360.4375"/>
     <use xlink:href="#DejaVuSans-6e" x="423.914062"/>
     <use xlink:href="#DejaVuSans-69" x="487.292969"/>
     <use xlink:href="#DejaVuSans-66" x="515.076172"/>
     <use xlink:href="#DejaVuSans-69" x="550.28125"/>
     <use xlink:href="#DejaVuSans-63" x="578.064453"/>
     <use xlink:href="#DejaVuSans-61" x="633.044922"/>
     <use xlink:href="#DejaVuSans-6e" x="694.324219"/>
     <use xlink:href="#DejaVuSans-63" x="757.703125"/>
     <use xlink:href="#DejaVuSans-65" x="812.683594"/>
     <use xlink:href="#DejaVuSans-3a" x="874.207031"/>
     <use xlink:href="#DejaVuSans-20" x="907.898438"/>
     <use xlink:href="#DejaVuSans-32" x="939.685547"/>
     <use xlink:href="#DejaVuSans-2e" x="1003.308594"/>
     <use xlink:href="#DejaVuSans-38" x="1035.095703"/>
     <use xlink:href="#DejaVuSans-36" x="1098.71875"/>
    </g>
    <!-- Selectivity Index: 0.433 -->
    <g transform="translate(703.009124 100.2498) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-65" x="63.476562"/>
     <use xlink:href="#DejaVuSans-6c" x="125"/>
     <use xlink:href="#DejaVuSans-65" x="152.783203"/>
     <use xlink:href="#DejaVuSans-63" x="214.306641"/>
     <use xlink:href="#DejaVuSans-74" x="269.287109"/>
     <use xlink:href="#DejaVuSans-69" x="308.496094"/>
     <use xlink:href="#DejaVuSans-76" x="336.279297"/>
     <use xlink:href="#DejaVuSans-69" x="395.458984"/>
     <use xlink:href="#DejaVuSans-74" x="423.242188"/>
     <use xlink:href="#DejaVuSans-79" x="462.451172"/>
     <use xlink:href="#DejaVuSans-20" x="521.630859"/>
     <use xlink:href="#DejaVuSans-49" x="553.417969"/>
     <use xlink:href="#DejaVuSans-6e" x="582.910156"/>
     <use xlink:href="#DejaVuSans-64" x="646.289062"/>
     <use xlink:href="#DejaVuSans-65" x="709.765625"/>
     <use xlink:href="#DejaVuSans-78" x="769.539062"/>
     <use xlink:href="#DejaVuSans-3a" x="828.71875"/>
     <use xlink:href="#DejaVuSans-20" x="862.410156"/>
     <use xlink:href="#DejaVuSans-30" x="894.197266"/>
     <use xlink:href="#DejaVuSans-2e" x="957.820312"/>
     <use xlink:href="#DejaVuSans-34" x="989.607422"/>
     <use xlink:href="#DejaVuSans-33" x="1053.230469"/>
     <use xlink:href="#DejaVuSans-33" x="1116.853516"/>
    </g>
    <!-- Peak Width: 3.2m -->
    <g transform="translate(726.926624 109.20805) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-57" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="365.804688"/>
     <use xlink:href="#DejaVuSans-64" x="393.587891"/>
     <use xlink:href="#DejaVuSans-74" x="457.064453"/>
     <use xlink:href="#DejaVuSans-68" x="496.273438"/>
     <use xlink:href="#DejaVuSans-3a" x="559.652344"/>
     <use xlink:href="#DejaVuSans-20" x="593.34375"/>
     <use xlink:href="#DejaVuSans-33" x="625.130859"/>
     <use xlink:href="#DejaVuSans-2e" x="688.753906"/>
     <use xlink:href="#DejaVuSans-32" x="720.541016"/>
     <use xlink:href="#DejaVuSans-6d" x="784.164062"/>
    </g>
   </g>
   <g id="text_44">
    <!-- Distance Tuning Curve -->
    <g transform="translate(552.950402 16.318125) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-54" d="M 31 4666 
L 4331 4666 
L 4331 3756 
L 2784 3756 
L 2784 0 
L 1581 0 
L 1581 3756 
L 31 3756 
L 31 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-44"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
     <use xlink:href="#DejaVuSans-Bold-54" x="525.195312"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="582.408203"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="653.599609"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="724.791016"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="759.068359"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="830.259766"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="901.841797"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="936.65625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1010.044922"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1081.236328"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="1130.552734"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1195.738281"/>
    </g>
    <!-- Peak: 4.3m, Sparsity: 0.424 -->
    <g transform="translate(535.025402 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-6b" d="M 538 4863 
L 1656 4863 
L 1656 2216 
L 2944 3500 
L 4244 3500 
L 2534 1894 
L 4378 0 
L 3022 0 
L 1656 1459 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-3a" d="M 716 3500 
L 1844 3500 
L 1844 2291 
L 716 2291 
L 716 3500 
z
M 716 1209 
L 1844 1209 
L 1844 0 
L 716 0 
L 716 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2c" d="M 653 1209 
L 1778 1209 
L 1778 256 
L 1006 -909 
L 341 -909 
L 653 256 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-53" d="M 3834 4519 
L 3834 3531 
Q 3450 3703 3084 3790 
Q 2719 3878 2394 3878 
Q 1963 3878 1756 3759 
Q 1550 3641 1550 3391 
Q 1550 3203 1689 3098 
Q 1828 2994 2194 2919 
L 2706 2816 
Q 3484 2659 3812 2340 
Q 4141 2022 4141 1434 
Q 4141 663 3683 286 
Q 3225 -91 2284 -91 
Q 1841 -91 1394 -6 
Q 947 78 500 244 
L 500 1259 
Q 947 1022 1364 901 
Q 1781 781 2169 781 
Q 2563 781 2772 912 
Q 2981 1044 2981 1288 
Q 2981 1506 2839 1625 
Q 2697 1744 2272 1838 
L 1806 1941 
Q 1106 2091 782 2419 
Q 459 2747 459 3303 
Q 459 4000 909 4375 
Q 1359 4750 2203 4750 
Q 2588 4750 2994 4692 
Q 3400 4634 3834 4519 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-79" d="M 78 3500 
L 1197 3500 
L 2138 1125 
L 2938 3500 
L 4056 3500 
L 2584 -331 
Q 2363 -916 2067 -1148 
Q 1772 -1381 1288 -1381 
L 641 -1381 
L 641 -647 
L 991 -647 
Q 1275 -647 1404 -556 
Q 1534 -466 1606 -231 
L 1638 -134 
L 78 3500 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-50"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="73.291016"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="141.113281"/>
     <use xlink:href="#DejaVuSans-Bold-6b" x="208.59375"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="275.097656"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="315.087891"/>
     <use xlink:href="#DejaVuSans-Bold-34" x="349.902344"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="419.482422"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="457.470703"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="527.050781"/>
     <use xlink:href="#DejaVuSans-Bold-2c" x="631.25"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="669.238281"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="704.052734"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="776.074219"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="847.65625"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="915.136719"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="964.453125"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1023.974609"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="1058.251953"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="1106.054688"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="1171.240234"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1211.230469"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1246.044922"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="1315.625"/>
     <use xlink:href="#DejaVuSans-Bold-34" x="1353.613281"/>
     <use xlink:href="#DejaVuSans-Bold-32" x="1423.193359"/>
     <use xlink:href="#DejaVuSans-Bold-34" x="1492.773438"/>
    </g>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="ma5b9855dbf" d="M 0 5 
C 1.326016 5 2.597899 4.473168 3.535534 3.535534 
C 4.473168 2.597899 5 1.326016 5 0 
C 5 -1.326016 4.473168 -2.597899 3.535534 -3.535534 
C 2.597899 -4.473168 1.326016 -5 0 -5 
C -1.326016 -5 -2.597899 -4.473168 -3.535534 -3.535534 
C -4.473168 -2.597899 -5 -1.326016 -5 0 
C -5 1.326016 -4.473168 2.597899 -3.535534 3.535534 
C -2.597899 4.473168 -1.326016 5 0 5 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#pf6cadea988)">
     <use xlink:href="#ma5b9855dbf" x="642.758027" y="57.695726" style="fill: #1f78b4; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="patch_16">
     <path d="M 446.935015 86.47675 
L 563.842515 86.47675 
Q 565.442515 86.47675 565.442515 84.87675 
L 565.442515 50.44925 
Q 565.442515 48.84925 563.842515 48.84925 
L 446.935015 48.84925 
Q 445.335015 48.84925 445.335015 50.44925 
L 445.335015 84.87675 
Q 445.335015 86.47675 446.935015 86.47675 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_71">
     <path d="M 448.535015 55.328 
L 456.535015 55.328 
L 464.535015 55.328 
" style="fill: none; stroke: #1f78b4; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
    </g>
    <g id="text_45">
     <!-- Tuning Curve -->
     <g transform="translate(470.935015 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-54"/>
      <use xlink:href="#DejaVuSans-75" x="45.958984"/>
      <use xlink:href="#DejaVuSans-6e" x="109.337891"/>
      <use xlink:href="#DejaVuSans-69" x="172.716797"/>
      <use xlink:href="#DejaVuSans-6e" x="200.5"/>
      <use xlink:href="#DejaVuSans-67" x="263.878906"/>
      <use xlink:href="#DejaVuSans-20" x="327.355469"/>
      <use xlink:href="#DejaVuSans-43" x="359.142578"/>
      <use xlink:href="#DejaVuSans-75" x="428.966797"/>
      <use xlink:href="#DejaVuSans-72" x="492.345703"/>
      <use xlink:href="#DejaVuSans-76" x="533.458984"/>
      <use xlink:href="#DejaVuSans-65" x="592.638672"/>
     </g>
    </g>
    <g id="line2d_72">
     <path d="M 448.535015 67.0705 
L 456.535015 67.0705 
L 464.535015 67.0705 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_46">
     <!-- Close threshold (3.0m) -->
     <g transform="translate(470.935015 69.8705) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6c" x="69.824219"/>
      <use xlink:href="#DejaVuSans-6f" x="97.607422"/>
      <use xlink:href="#DejaVuSans-73" x="158.789062"/>
      <use xlink:href="#DejaVuSans-65" x="210.888672"/>
      <use xlink:href="#DejaVuSans-20" x="272.412109"/>
      <use xlink:href="#DejaVuSans-74" x="304.199219"/>
      <use xlink:href="#DejaVuSans-68" x="343.408203"/>
      <use xlink:href="#DejaVuSans-72" x="406.787109"/>
      <use xlink:href="#DejaVuSans-65" x="445.650391"/>
      <use xlink:href="#DejaVuSans-73" x="507.173828"/>
      <use xlink:href="#DejaVuSans-68" x="559.273438"/>
      <use xlink:href="#DejaVuSans-6f" x="622.652344"/>
      <use xlink:href="#DejaVuSans-6c" x="683.833984"/>
      <use xlink:href="#DejaVuSans-64" x="711.617188"/>
      <use xlink:href="#DejaVuSans-20" x="775.09375"/>
      <use xlink:href="#DejaVuSans-28" x="806.880859"/>
      <use xlink:href="#DejaVuSans-33" x="845.894531"/>
      <use xlink:href="#DejaVuSans-2e" x="909.517578"/>
      <use xlink:href="#DejaVuSans-30" x="941.304688"/>
      <use xlink:href="#DejaVuSans-6d" x="1004.927734"/>
      <use xlink:href="#DejaVuSans-29" x="1102.339844"/>
     </g>
    </g>
    <g id="line2d_73">
     <path d="M 448.535015 78.813 
L 456.535015 78.813 
L 464.535015 78.813 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_47">
     <!-- Far threshold (5.0m) -->
     <g transform="translate(470.935015 81.613) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" x="48.394531"/>
      <use xlink:href="#DejaVuSans-72" x="109.673828"/>
      <use xlink:href="#DejaVuSans-20" x="150.787109"/>
      <use xlink:href="#DejaVuSans-74" x="182.574219"/>
      <use xlink:href="#DejaVuSans-68" x="221.783203"/>
      <use xlink:href="#DejaVuSans-72" x="285.162109"/>
      <use xlink:href="#DejaVuSans-65" x="324.025391"/>
      <use xlink:href="#DejaVuSans-73" x="385.548828"/>
      <use xlink:href="#DejaVuSans-68" x="437.648438"/>
      <use xlink:href="#DejaVuSans-6f" x="501.027344"/>
      <use xlink:href="#DejaVuSans-6c" x="562.208984"/>
      <use xlink:href="#DejaVuSans-64" x="589.992188"/>
      <use xlink:href="#DejaVuSans-20" x="653.46875"/>
      <use xlink:href="#DejaVuSans-28" x="685.255859"/>
      <use xlink:href="#DejaVuSans-35" x="724.269531"/>
      <use xlink:href="#DejaVuSans-2e" x="787.892578"/>
      <use xlink:href="#DejaVuSans-30" x="819.679688"/>
      <use xlink:href="#DejaVuSans-6d" x="883.302734"/>
      <use xlink:href="#DejaVuSans-29" x="980.714844"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_17">
    <path d="M 330.63282 287.64765 
L 341.42386 287.64765 
L 341.42386 71.82685 
L 330.63282 71.82685 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAABYAAAHBCAYAAABt+EsYAAACDUlEQVR4nO2bS25CQQwEDUzunXsHQhYz9MsFelFS1QGskrv9+AhuM9/vKbBmVmPu3CtTZ2bNfFUGN43dcQa7io11C9YtIOvGM+60wlUE6xasW7BuwR0HX/4Dsm48Y5/HG96B3GbelS9CasYOBg9ec+sMLho/WoM7D7fmKjoPN40vrFsghscztm4HYng8Y+t2IIbHM7ZuB2J4PGPrdiCGxzO2bgdieDzjUt2KxqUeE8PT+MA7EJ/HQeNQM3YVQePg0y0Qw9O4PZi3Co2DT7dADE/ja3Dl1zzVVbwqgzUO6/54dgavL9oqHrjwiMatuvFWcccZj8abpnHrQICr0Hhj3YLhBY3/D+70uLcKD+QDz9i6BV54PGPrFnjh8YyRq/BANtYtWLdg3fqDeavwQELtQJDh8Yyt2xlMDE/jjQcSPJCANLZuZzAxPJ6xdTuDieFpvOEdiF8rBI2DdQvI8DTOYN9XbHjhFY1frbo9cXV7/VYGF8PrrBhovG6dGjf/XYELT+NrsHU7aBxqrSCuQuMDr268HRte4IXHMza8oHGwbsHwAu8VhLgKnrEn3R7MW4V1C9YtGF7gGRNXYd0OPGPDCxoHPyoEjQOvbrwdG17ghcczNrygcbBuYf3gwiv9jq6549a7TeAqcMYl4eaOaYMNL7jja7Cr+GDdgnUL1i3wjA0veCDBugXrFng75q3iD9EGue+xdPB4AAAAAElFTkSuQmCC" id="image417d55f1e5" transform="scale(1 -1) translate(0 -215.52)" x="330.72" y="-71.52" width="10.56" height="215.52"/>
   <g id="matplotlib.axis_5"/>
   <g id="matplotlib.axis_6">
    <g id="ytick_16">
     <g id="line2d_74">
      <defs>
       <path id="m9d9c4eda4e" d="M 0 0 
L 3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m9d9c4eda4e" x="341.42386" y="287.64765" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_48">
      <!-- 0.0 -->
      <g transform="translate(348.42386 291.066947) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_75">
      <g>
       <use xlink:href="#m9d9c4eda4e" x="341.42386" y="244.48349" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_49">
      <!-- 0.2 -->
      <g transform="translate(348.42386 247.902787) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_76">
      <g>
       <use xlink:href="#m9d9c4eda4e" x="341.42386" y="201.31933" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_50">
      <!-- 0.4 -->
      <g transform="translate(348.42386 204.738627) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_77">
      <g>
       <use xlink:href="#m9d9c4eda4e" x="341.42386" y="158.15517" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_51">
      <!-- 0.6 -->
      <g transform="translate(348.42386 161.574467) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_78">
      <g>
       <use xlink:href="#m9d9c4eda4e" x="341.42386" y="114.99101" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_52">
      <!-- 0.8 -->
      <g transform="translate(348.42386 118.410307) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-38" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_79">
      <g>
       <use xlink:href="#m9d9c4eda4e" x="341.42386" y="71.82685" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_53">
      <!-- 1.0 -->
      <g transform="translate(348.42386 75.246147) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_54">
     <!-- Neural Activation -->
     <g transform="translate(369.378391 125.728109) rotate(-270) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_18">
    <path d="M 330.63282 287.64765 
L 336.02834 287.64765 
L 341.42386 287.64765 
L 341.42386 71.82685 
L 336.02834 71.82685 
L 330.63282 71.82685 
L 330.63282 287.64765 
z
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p22344b5046">
   <rect x="42.113906" y="44.84925" width="269.776" height="269.776"/>
  </clipPath>
  <clipPath id="pf6cadea988">
   <rect x="441.335015" y="44.84925" width="374.858273" height="269.776"/>
  </clipPath>
 </defs>
</svg>
