<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="824.000413pt" height="352.267438pt" viewBox="0 0 824.000413 352.267438" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:55:08.579759</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 352.267438 
L 824.000413 352.267438 
L 824.000413 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
L 311.889906 44.84925 
L 42.113906 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g clip-path="url(#p1c8996a634)">
    <image xlink:href="data:image/png;base64,
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" id="image1038386692" transform="scale(1 -1) translate(0 -270.24)" x="42.113906" y="-44.38525" width="270.24" height="270.24"/>
   </g>
   <g id="patch_3">
    <path d="M 177.001906 230.32025 
C 190.416675 230.32025 203.283815 224.990506 212.769489 215.504832 
C 222.255162 206.019159 227.584906 193.152018 227.584906 179.73725 
C 227.584906 166.322482 222.255162 153.455341 212.769489 143.969668 
C 203.283815 134.483994 190.416675 129.15425 177.001906 129.15425 
C 163.587138 129.15425 150.719998 134.483994 141.234324 143.969668 
C 131.74865 153.455341 126.418906 166.322482 126.418906 179.73725 
C 126.418906 193.152018 131.74865 206.019159 141.234324 215.504832 
C 150.719998 224.990506 163.587138 230.32025 177.001906 230.32025 
L 177.001906 230.32025 
z
" clip-path="url(#p1c8996a634)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 177.001906 264.04225 
C 199.359854 264.04225 220.805087 255.159343 236.614543 239.349887 
C 252.424 223.540431 261.306906 202.095197 261.306906 179.73725 
C 261.306906 157.379303 252.424 135.934069 236.614543 120.124613 
C 220.805087 104.315157 199.359854 95.43225 177.001906 95.43225 
C 154.643959 95.43225 133.198725 104.315157 117.389269 120.124613 
C 101.579813 135.934069 92.696906 157.379303 92.696906 179.73725 
C 92.696906 202.095197 101.579813 223.540431 117.389269 239.349887 
C 133.198725 255.159343 154.643959 264.04225 177.001906 264.04225 
L 177.001906 264.04225 
z
" clip-path="url(#p1c8996a634)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 177.001906 314.62525 
C 212.774622 314.62525 247.086996 300.412599 272.382126 275.11747 
C 297.677256 249.82234 311.889906 215.509966 311.889906 179.73725 
C 311.889906 143.964534 297.677256 109.65216 272.382126 84.35703 
C 247.086996 59.061901 212.774622 44.84925 177.001906 44.84925 
C 141.22919 44.84925 106.916817 59.061901 81.621687 84.35703 
C 56.326557 109.65216 42.113906 143.964534 42.113906 179.73725 
C 42.113906 215.509966 56.326557 249.82234 81.621687 275.11747 
C 106.916817 300.412599 141.22919 314.62525 177.001906 314.62525 
L 177.001906 314.62525 
z
" clip-path="url(#p1c8996a634)" style="fill: none; opacity: 0.7; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" clip-path="url(#p1c8996a634)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="mb027894e4d" d="M 0 0 
L 0 3.5 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#mb027894e4d" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −8 -->
      <g transform="translate(35.479922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 75.835906 314.62525 
L 75.835906 44.84925 
" clip-path="url(#p1c8996a634)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#mb027894e4d" x="75.835906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −6 -->
      <g transform="translate(69.201922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 109.557906 314.62525 
L 109.557906 44.84925 
" clip-path="url(#p1c8996a634)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#mb027894e4d" x="109.557906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_3">
      <!-- −4 -->
      <g transform="translate(102.923922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 143.279906 314.62525 
L 143.279906 44.84925 
" clip-path="url(#p1c8996a634)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#mb027894e4d" x="143.279906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_4">
      <!-- −2 -->
      <g transform="translate(136.645922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 177.001906 314.62525 
L 177.001906 44.84925 
" clip-path="url(#p1c8996a634)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#mb027894e4d" x="177.001906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(174.138781 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 210.723906 314.62525 
L 210.723906 44.84925 
" clip-path="url(#p1c8996a634)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#mb027894e4d" x="210.723906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 2 -->
      <g transform="translate(207.860781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 244.445906 314.62525 
L 244.445906 44.84925 
" clip-path="url(#p1c8996a634)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#mb027894e4d" x="244.445906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 4 -->
      <g transform="translate(241.582781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <path d="M 278.167906 314.62525 
L 278.167906 44.84925 
" clip-path="url(#p1c8996a634)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#mb027894e4d" x="278.167906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 6 -->
      <g transform="translate(275.304781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_17">
      <path d="M 311.889906 314.62525 
L 311.889906 44.84925 
" clip-path="url(#p1c8996a634)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#mb027894e4d" x="311.889906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 8 -->
      <g transform="translate(309.026781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- Relative X Position (m) -->
     <g transform="translate(105.68925 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-52" d="M 2297 2597 
Q 2675 2597 2839 2737 
Q 3003 2878 3003 3200 
Q 3003 3519 2839 3656 
Q 2675 3794 2297 3794 
L 1791 3794 
L 1791 2597 
L 2297 2597 
z
M 1791 1766 
L 1791 0 
L 588 0 
L 588 4666 
L 2425 4666 
Q 3347 4666 3776 4356 
Q 4206 4047 4206 3378 
Q 4206 2916 3982 2619 
Q 3759 2322 3309 2181 
Q 3556 2125 3751 1926 
Q 3947 1728 4147 1325 
L 4800 0 
L 3519 0 
L 2950 1159 
Q 2778 1509 2601 1637 
Q 2425 1766 2131 1766 
L 1791 1766 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6c" d="M 538 4863 
L 1656 4863 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-76" d="M 97 3500 
L 1216 3500 
L 2088 1081 
L 2956 3500 
L 4078 3500 
L 2700 0 
L 1472 0 
L 97 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-58" d="M 3188 2381 
L 4806 0 
L 3553 0 
L 2463 1594 
L 1381 0 
L 122 0 
L 1741 2381 
L 184 4666 
L 1441 4666 
L 2463 3163 
L 3481 4666 
L 4744 4666 
L 3188 2381 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-50" d="M 588 4666 
L 2584 4666 
Q 3475 4666 3951 4270 
Q 4428 3875 4428 3144 
Q 4428 2409 3951 2014 
Q 3475 1619 2584 1619 
L 1791 1619 
L 1791 0 
L 588 0 
L 588 4666 
z
M 1791 3794 
L 1791 2491 
L 2456 2491 
Q 2806 2491 2997 2661 
Q 3188 2831 3188 3144 
Q 3188 3456 2997 3625 
Q 2806 3794 2456 3794 
L 1791 3794 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6f" d="M 2203 2784 
Q 1831 2784 1636 2517 
Q 1441 2250 1441 1747 
Q 1441 1244 1636 976 
Q 1831 709 2203 709 
Q 2569 709 2762 976 
Q 2956 1244 2956 1747 
Q 2956 2250 2762 2517 
Q 2569 2784 2203 2784 
z
M 2203 3584 
Q 3106 3584 3614 3096 
Q 4122 2609 4122 1747 
Q 4122 884 3614 396 
Q 3106 -91 2203 -91 
Q 1297 -91 786 396 
Q 275 884 275 1747 
Q 275 2609 786 3096 
Q 1297 3584 2203 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-28" d="M 2413 -844 
L 1484 -844 
Q 1006 -72 778 623 
Q 550 1319 550 2003 
Q 550 2688 779 3389 
Q 1009 4091 1484 4856 
L 2413 4856 
Q 2013 4116 1813 3408 
Q 1613 2700 1613 2009 
Q 1613 1319 1811 609 
Q 2009 -100 2413 -844 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6d" d="M 3781 2919 
Q 3994 3244 4286 3414 
Q 4578 3584 4928 3584 
Q 5531 3584 5847 3212 
Q 6163 2841 6163 2131 
L 6163 0 
L 5038 0 
L 5038 1825 
Q 5041 1866 5042 1909 
Q 5044 1953 5044 2034 
Q 5044 2406 4934 2573 
Q 4825 2741 4581 2741 
Q 4263 2741 4089 2478 
Q 3916 2216 3909 1719 
L 3909 0 
L 2784 0 
L 2784 1825 
Q 2784 2406 2684 2573 
Q 2584 2741 2328 2741 
Q 2006 2741 1831 2477 
Q 1656 2213 1656 1722 
L 1656 0 
L 531 0 
L 531 3500 
L 1656 3500 
L 1656 2988 
Q 1863 3284 2130 3434 
Q 2397 3584 2719 3584 
Q 3081 3584 3359 3409 
Q 3638 3234 3781 2919 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-29" d="M 513 -844 
Q 913 -100 1113 609 
Q 1313 1319 1313 2009 
Q 1313 2700 1113 3408 
Q 913 4116 513 4856 
L 1441 4856 
Q 1916 4091 2145 3389 
Q 2375 2688 2375 2003 
Q 2375 1319 2147 623 
Q 1919 -72 1441 -844 
L 513 -844 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-58" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="573.583984"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="608.398438"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="681.689453"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="750.390625"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="809.912109"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="844.189453"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="891.992188"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="926.269531"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="994.970703"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1066.162109"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1100.976562"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1146.679688"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1250.878906"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_19">
      <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" clip-path="url(#p1c8996a634)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <defs>
       <path id="ma4cdaa7888" d="M 0 0 
L -3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#ma4cdaa7888" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_11">
      <!-- −8 -->
      <g transform="translate(21.845937 318.044547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_21">
      <path d="M 42.113906 280.90325 
L 311.889906 280.90325 
" clip-path="url(#p1c8996a634)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#ma4cdaa7888" x="42.113906" y="280.90325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_12">
      <!-- −6 -->
      <g transform="translate(21.845937 284.322547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_23">
      <path d="M 42.113906 247.18125 
L 311.889906 247.18125 
" clip-path="url(#p1c8996a634)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#ma4cdaa7888" x="42.113906" y="247.18125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_13">
      <!-- −4 -->
      <g transform="translate(21.845937 250.600547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_25">
      <path d="M 42.113906 213.45925 
L 311.889906 213.45925 
" clip-path="url(#p1c8996a634)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#ma4cdaa7888" x="42.113906" y="213.45925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_14">
      <!-- −2 -->
      <g transform="translate(21.845937 216.878547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_27">
      <path d="M 42.113906 179.73725 
L 311.889906 179.73725 
" clip-path="url(#p1c8996a634)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#ma4cdaa7888" x="42.113906" y="179.73725" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 0 -->
      <g transform="translate(29.387656 183.156547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_29">
      <path d="M 42.113906 146.01525 
L 311.889906 146.01525 
" clip-path="url(#p1c8996a634)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#ma4cdaa7888" x="42.113906" y="146.01525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 2 -->
      <g transform="translate(29.387656 149.434547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_31">
      <path d="M 42.113906 112.29325 
L 311.889906 112.29325 
" clip-path="url(#p1c8996a634)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#ma4cdaa7888" x="42.113906" y="112.29325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 4 -->
      <g transform="translate(29.387656 115.712547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_33">
      <path d="M 42.113906 78.57125 
L 311.889906 78.57125 
" clip-path="url(#p1c8996a634)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#ma4cdaa7888" x="42.113906" y="78.57125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 6 -->
      <g transform="translate(29.387656 81.990547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_35">
      <path d="M 42.113906 44.84925 
L 311.889906 44.84925 
" clip-path="url(#p1c8996a634)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#ma4cdaa7888" x="42.113906" y="44.84925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 8 -->
      <g transform="translate(29.387656 48.268547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_20">
     <!-- Relative Y Position (m) -->
     <g transform="translate(15.558281 250.792094) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-59" d="M -63 4666 
L 1253 4666 
L 2316 3003 
L 3378 4666 
L 4697 4666 
L 2919 1966 
L 2919 0 
L 1716 0 
L 1716 1966 
L -63 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-59" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="568.896484"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="603.710938"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="677.001953"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="745.703125"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="805.224609"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="839.501953"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="887.304688"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="921.582031"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="990.283203"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1061.474609"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1096.289062"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1141.992188"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1246.191406"/>
     </g>
    </g>
   </g>
   <g id="patch_6">
    <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_21">
    <g id="patch_8">
     <path d="M 200.10902 150.124824 
L 225.429957 150.124824 
Q 227.229957 150.124824 227.229957 148.324824 
L 227.229957 139.614511 
Q 227.229957 137.814511 225.429957 137.814511 
L 200.10902 137.814511 
Q 198.30902 137.814511 198.30902 139.614511 
L 198.30902 148.324824 
Q 198.30902 150.124824 200.10902 150.124824 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 3.0m -->
    <g style="fill: #ffffff" transform="translate(200.10902 146.453105) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-33" d="M 2981 2516 
Q 3453 2394 3698 2092 
Q 3944 1791 3944 1325 
Q 3944 631 3412 270 
Q 2881 -91 1863 -91 
Q 1503 -91 1142 -33 
Q 781 25 428 141 
L 428 1069 
Q 766 900 1098 814 
Q 1431 728 1753 728 
Q 2231 728 2486 893 
Q 2741 1059 2741 1369 
Q 2741 1688 2480 1852 
Q 2219 2016 1709 2016 
L 1228 2016 
L 1228 2791 
L 1734 2791 
Q 2188 2791 2409 2933 
Q 2631 3075 2631 3366 
Q 2631 3634 2415 3781 
Q 2200 3928 1806 3928 
Q 1516 3928 1219 3862 
Q 922 3797 628 3669 
L 628 4550 
Q 984 4650 1334 4700 
Q 1684 4750 2022 4750 
Q 2931 4750 3382 4451 
Q 3834 4153 3834 3553 
Q 3834 3144 3618 2883 
Q 3403 2622 2981 2516 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2e" d="M 653 1209 
L 1778 1209 
L 1778 0 
L 653 0 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-30" d="M 2944 2338 
Q 2944 3213 2780 3570 
Q 2616 3928 2228 3928 
Q 1841 3928 1675 3570 
Q 1509 3213 1509 2338 
Q 1509 1453 1675 1090 
Q 1841 728 2228 728 
Q 2613 728 2778 1090 
Q 2944 1453 2944 2338 
z
M 4147 2328 
Q 4147 1169 3647 539 
Q 3147 -91 2228 -91 
Q 1306 -91 806 539 
Q 306 1169 306 2328 
Q 306 3491 806 4120 
Q 1306 4750 2228 4750 
Q 3147 4750 3647 4120 
Q 4147 3491 4147 2328 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-33"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_22">
    <g id="patch_9">
     <path d="M 223.954075 126.279769 
L 249.275012 126.279769 
Q 251.075012 126.279769 251.075012 124.479769 
L 251.075012 115.769457 
Q 251.075012 113.969457 249.275012 113.969457 
L 223.954075 113.969457 
Q 222.154075 113.969457 222.154075 115.769457 
L 222.154075 124.479769 
Q 222.154075 126.279769 223.954075 126.279769 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 5.0m -->
    <g style="fill: #ffffff" transform="translate(223.954075 122.60805) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-35" d="M 678 4666 
L 3669 4666 
L 3669 3781 
L 1638 3781 
L 1638 3059 
Q 1775 3097 1914 3117 
Q 2053 3138 2203 3138 
Q 3056 3138 3531 2711 
Q 4006 2284 4006 1522 
Q 4006 766 3489 337 
Q 2972 -91 2053 -91 
Q 1656 -91 1267 -14 
Q 878 63 494 219 
L 494 1166 
Q 875 947 1217 837 
Q 1559 728 1863 728 
Q 2300 728 2551 942 
Q 2803 1156 2803 1522 
Q 2803 1891 2551 2103 
Q 2300 2316 1863 2316 
Q 1603 2316 1309 2248 
Q 1016 2181 678 2041 
L 678 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-35"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_23">
    <g id="patch_10">
     <path d="M 259.721657 90.512187 
L 285.042595 90.512187 
Q 286.842595 90.512187 286.842595 88.712187 
L 286.842595 80.001874 
Q 286.842595 78.201874 285.042595 78.201874 
L 259.721657 78.201874 
Q 257.921657 78.201874 257.921657 80.001874 
L 257.921657 88.712187 
Q 257.921657 90.512187 259.721657 90.512187 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 8.0m -->
    <g style="fill: #ffffff" transform="translate(259.721657 86.840468) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-38" d="M 2228 2088 
Q 1891 2088 1709 1903 
Q 1528 1719 1528 1375 
Q 1528 1031 1709 848 
Q 1891 666 2228 666 
Q 2563 666 2741 848 
Q 2919 1031 2919 1375 
Q 2919 1722 2741 1905 
Q 2563 2088 2228 2088 
z
M 1350 2484 
Q 925 2613 709 2878 
Q 494 3144 494 3541 
Q 494 4131 934 4440 
Q 1375 4750 2228 4750 
Q 3075 4750 3515 4442 
Q 3956 4134 3956 3541 
Q 3956 3144 3739 2878 
Q 3522 2613 3097 2484 
Q 3572 2353 3814 2058 
Q 4056 1763 4056 1313 
Q 4056 619 3595 264 
Q 3134 -91 2228 -91 
Q 1319 -91 855 264 
Q 391 619 391 1313 
Q 391 1763 633 2058 
Q 875 2353 1350 2484 
z
M 1631 3419 
Q 1631 3141 1786 2991 
Q 1941 2841 2228 2841 
Q 2509 2841 2662 2991 
Q 2816 3141 2816 3419 
Q 2816 3697 2662 3845 
Q 2509 3994 2228 3994 
Q 1941 3994 1786 3844 
Q 1631 3694 1631 3419 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-38"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_24">
    <!-- Mid-Distance Cell (Neuron 30) -->
    <g transform="translate(75.364719 16.411875) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-4d" d="M 588 4666 
L 2119 4666 
L 3181 2169 
L 4250 4666 
L 5778 4666 
L 5778 0 
L 4641 0 
L 4641 3413 
L 3566 897 
L 2803 897 
L 1728 3413 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-64" d="M 2919 2988 
L 2919 4863 
L 4044 4863 
L 4044 0 
L 2919 0 
L 2919 506 
Q 2688 197 2409 53 
Q 2131 -91 1766 -91 
Q 1119 -91 703 423 
Q 288 938 288 1747 
Q 288 2556 703 3070 
Q 1119 3584 1766 3584 
Q 2128 3584 2408 3439 
Q 2688 3294 2919 2988 
z
M 2181 722 
Q 2541 722 2730 984 
Q 2919 1247 2919 1747 
Q 2919 2247 2730 2509 
Q 2541 2772 2181 2772 
Q 1825 2772 1636 2509 
Q 1447 2247 1447 1747 
Q 1447 1247 1636 984 
Q 1825 722 2181 722 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2d" d="M 347 2297 
L 2309 2297 
L 2309 1388 
L 347 1388 
L 347 2297 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-44" d="M 1791 3756 
L 1791 909 
L 2222 909 
Q 2959 909 3348 1275 
Q 3738 1641 3738 2338 
Q 3738 3031 3350 3393 
Q 2963 3756 2222 3756 
L 1791 3756 
z
M 588 4666 
L 1856 4666 
Q 2919 4666 3439 4514 
Q 3959 4363 4331 4000 
Q 4659 3684 4818 3271 
Q 4978 2859 4978 2338 
Q 4978 1809 4818 1395 
Q 4659 981 4331 666 
Q 3956 303 3431 151 
Q 2906 0 1856 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4e" d="M 588 4666 
L 1931 4666 
L 3628 1466 
L 3628 4666 
L 4769 4666 
L 4769 0 
L 3425 0 
L 1728 3200 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-75" d="M 500 1363 
L 500 3500 
L 1625 3500 
L 1625 3150 
Q 1625 2866 1622 2436 
Q 1619 2006 1619 1863 
Q 1619 1441 1641 1255 
Q 1663 1069 1716 984 
Q 1784 875 1895 815 
Q 2006 756 2150 756 
Q 2500 756 2700 1025 
Q 2900 1294 2900 1772 
L 2900 3500 
L 4019 3500 
L 4019 0 
L 2900 0 
L 2900 506 
Q 2647 200 2364 54 
Q 2081 -91 1741 -91 
Q 1134 -91 817 281 
Q 500 653 500 1363 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-4d"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="99.511719"/>
     <use xlink:href="#DejaVuSans-Bold-64" x="133.789062"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="205.371094"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="246.875"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="329.882812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="364.160156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="423.681641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.484375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="538.964844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="610.15625"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="669.433594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="737.255859"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="772.070312"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="845.458984"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="913.28125"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="947.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="981.835938"/>
     <use xlink:href="#DejaVuSans-Bold-28" x="1016.650391"/>
     <use xlink:href="#DejaVuSans-Bold-4e" x="1062.353516"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1146.044922"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1213.867188"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1285.058594"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="1334.375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1403.076172"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1474.267578"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="1509.082031"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1578.662109"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="1648.242188"/>
    </g>
    <!-- 2D Activation Map -->
    <g transform="translate(114.950656 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-32" d="M 1844 884 
L 3897 884 
L 3897 0 
L 506 0 
L 506 884 
L 2209 2388 
Q 2438 2594 2547 2791 
Q 2656 2988 2656 3200 
Q 2656 3528 2436 3728 
Q 2216 3928 1850 3928 
Q 1569 3928 1234 3808 
Q 900 3688 519 3450 
L 519 4475 
Q 925 4609 1322 4679 
Q 1719 4750 2100 4750 
Q 2938 4750 3402 4381 
Q 3866 4013 3866 3353 
Q 3866 2972 3669 2642 
Q 3472 2313 2841 1759 
L 1844 884 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-41" d="M 3419 850 
L 1538 850 
L 1241 0 
L 31 0 
L 1759 4666 
L 3194 4666 
L 4922 0 
L 3713 0 
L 3419 850 
z
M 1838 1716 
L 3116 1716 
L 2478 3572 
L 1838 1716 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-32"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="152.587891"/>
     <use xlink:href="#DejaVuSans-Bold-41" x="187.402344"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="264.794922"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="324.072266"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="371.875"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="406.152344"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.337891"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="538.818359"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="586.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="620.898438"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="689.599609"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="760.791016"/>
     <use xlink:href="#DejaVuSans-Bold-4d" x="795.605469"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="895.117188"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="962.597656"/>
    </g>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="m589645aa64" d="M 0 -5.477226 
L -1.229714 -1.692556 
L -5.209151 -1.692556 
L -1.989719 0.646499 
L -3.219432 4.431169 
L -0 2.092114 
L 3.219432 4.431169 
L 1.989719 0.646499 
L 5.209151 -1.692556 
L 1.229714 -1.692556 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#p1c8996a634)">
     <use xlink:href="#m589645aa64" x="177.001906" y="179.73725" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_11">
     <path d="M 242.388656 62.99175 
L 307.889906 62.99175 
L 307.889906 48.84925 
L 242.388656 48.84925 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="PathCollection_2">
     <g>
      <use xlink:href="#m589645aa64" x="253.588656" y="56.028" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
     </g>
    </g>
    <g id="text_25">
     <!-- Observer -->
     <g transform="translate(267.988656 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4f"/>
      <use xlink:href="#DejaVuSans-62" x="78.710938"/>
      <use xlink:href="#DejaVuSans-73" x="142.1875"/>
      <use xlink:href="#DejaVuSans-65" x="194.287109"/>
      <use xlink:href="#DejaVuSans-72" x="255.810547"/>
      <use xlink:href="#DejaVuSans-76" x="296.923828"/>
      <use xlink:href="#DejaVuSans-65" x="356.103516"/>
      <use xlink:href="#DejaVuSans-72" x="417.626953"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_12">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
L 813.937288 44.84925 
L 436.259015 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="m4e67907cd6" d="M 440.97851 -37.642188 
L 440.97851 -37.642188 
L 450.4175 -61.618239 
L 459.85649 -41.264631 
L 469.29548 -62.558759 
L 478.73447 -40.872586 
L 488.17346 -59.119102 
L 497.61245 -76.132683 
L 507.051441 -93.827057 
L 516.490431 -104.299438 
L 525.929421 -95.593918 
L 535.368411 -109.263226 
L 544.807401 -96.789942 
L 554.246391 -137.319176 
L 563.685381 -128.825804 
L 573.124371 -125.06263 
L 582.563361 -115.144565 
L 592.002351 -151.181975 
L 601.441341 -178.910341 
L 610.880331 -142.934531 
L 620.319321 -242.052167 
L 629.758311 -245.770556 
L 639.197301 -294.571711 
L 648.636291 -176.649361 
L 658.075281 -176.929387 
L 667.514271 -121.842054 
L 676.953261 -168.946816 
L 686.392251 -242.695694 
L 695.831241 -134.456506 
L 705.270231 -213.392214 
L 714.709221 -208.128066 
L 714.709221 -37.642188 
L 714.709221 -37.642188 
L 705.270231 -37.642188 
L 695.831241 -37.642188 
L 686.392251 -37.642188 
L 676.953261 -37.642188 
L 667.514271 -37.642188 
L 658.075281 -37.642188 
L 648.636291 -37.642188 
L 639.197301 -37.642188 
L 629.758311 -37.642188 
L 620.319321 -37.642188 
L 610.880331 -37.642188 
L 601.441341 -37.642188 
L 592.002351 -37.642188 
L 582.563361 -37.642188 
L 573.124371 -37.642188 
L 563.685381 -37.642188 
L 554.246391 -37.642188 
L 544.807401 -37.642188 
L 535.368411 -37.642188 
L 525.929421 -37.642188 
L 516.490431 -37.642188 
L 507.051441 -37.642188 
L 497.61245 -37.642188 
L 488.17346 -37.642188 
L 478.73447 -37.642188 
L 469.29548 -37.642188 
L 459.85649 -37.642188 
L 450.4175 -37.642188 
L 440.97851 -37.642188 
z
" style="stroke: #1f78b4; stroke-opacity: 0.3"/>
    </defs>
    <g clip-path="url(#pff30a22d3e)">
     <use xlink:href="#m4e67907cd6" x="0" y="352.267438" style="fill: #1f78b4; fill-opacity: 0.3; stroke: #1f78b4; stroke-opacity: 0.3"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10">
     <g id="line2d_37">
      <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" clip-path="url(#pff30a22d3e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#mb027894e4d" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 0 -->
      <g transform="translate(433.39589 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_39">
      <path d="M 483.468799 314.62525 
L 483.468799 44.84925 
" clip-path="url(#pff30a22d3e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#mb027894e4d" x="483.468799" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 1 -->
      <g transform="translate(480.605674 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_41">
      <path d="M 530.678584 314.62525 
L 530.678584 44.84925 
" clip-path="url(#pff30a22d3e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#mb027894e4d" x="530.678584" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 2 -->
      <g transform="translate(527.815459 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_43">
      <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#pff30a22d3e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#mb027894e4d" x="577.888368" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 3 -->
      <g transform="translate(575.025243 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_45">
      <path d="M 625.098152 314.62525 
L 625.098152 44.84925 
" clip-path="url(#pff30a22d3e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#mb027894e4d" x="625.098152" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 4 -->
      <g transform="translate(622.235027 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_47">
      <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#pff30a22d3e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#mb027894e4d" x="672.307936" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 5 -->
      <g transform="translate(669.444811 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_49">
      <path d="M 719.51772 314.62525 
L 719.51772 44.84925 
" clip-path="url(#pff30a22d3e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#mb027894e4d" x="719.51772" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 6 -->
      <g transform="translate(716.654595 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_51">
      <path d="M 766.727504 314.62525 
L 766.727504 44.84925 
" clip-path="url(#pff30a22d3e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#mb027894e4d" x="766.727504" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 7 -->
      <g transform="translate(763.864379 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-37"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_53">
      <path d="M 813.937288 314.62525 
L 813.937288 44.84925 
" clip-path="url(#pff30a22d3e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#mb027894e4d" x="813.937288" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 8 -->
      <g transform="translate(811.074163 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_35">
     <!-- Inter-Agent Distance (m) -->
     <g transform="translate(547.755261 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-49" d="M 588 4666 
L 1791 4666 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-49"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="37.207031"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="108.398438"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="156.201172"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="224.023438"/>
      <use xlink:href="#DejaVuSans-Bold-2d" x="273.339844"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="314.84375"/>
      <use xlink:href="#DejaVuSans-Bold-67" x="392.236328"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="463.818359"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="531.640625"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="602.832031"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="650.634766"/>
      <use xlink:href="#DejaVuSans-Bold-44" x="685.449219"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="768.457031"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="802.734375"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="862.255859"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="910.058594"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="977.539062"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="1048.730469"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="1108.007812"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1175.830078"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1210.644531"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1256.347656"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1360.546875"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_55">
      <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" clip-path="url(#pff30a22d3e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#ma4cdaa7888" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 0.0 -->
      <g transform="translate(414.946203 318.044547) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_57">
      <path d="M 436.259015 278.048924 
L 813.937288 278.048924 
" clip-path="url(#pff30a22d3e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#ma4cdaa7888" x="436.259015" y="278.048924" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 0.1 -->
      <g transform="translate(414.946203 281.468221) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-31" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_59">
      <path d="M 436.259015 241.472597 
L 813.937288 241.472597 
" clip-path="url(#pff30a22d3e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#ma4cdaa7888" x="436.259015" y="241.472597" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 0.2 -->
      <g transform="translate(414.946203 244.891894) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_61">
      <path d="M 436.259015 204.896271 
L 813.937288 204.896271 
" clip-path="url(#pff30a22d3e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#ma4cdaa7888" x="436.259015" y="204.896271" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 0.3 -->
      <g transform="translate(414.946203 208.315568) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-33" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_63">
      <path d="M 436.259015 168.319945 
L 813.937288 168.319945 
" clip-path="url(#pff30a22d3e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#ma4cdaa7888" x="436.259015" y="168.319945" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 0.4 -->
      <g transform="translate(414.946203 171.739242) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_65">
      <path d="M 436.259015 131.743618 
L 813.937288 131.743618 
" clip-path="url(#pff30a22d3e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#ma4cdaa7888" x="436.259015" y="131.743618" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 0.5 -->
      <g transform="translate(414.946203 135.162915) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-35" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_16">
     <g id="line2d_67">
      <path d="M 436.259015 95.167292 
L 813.937288 95.167292 
" clip-path="url(#pff30a22d3e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_68">
      <g>
       <use xlink:href="#ma4cdaa7888" x="436.259015" y="95.167292" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_42">
      <!-- 0.6 -->
      <g transform="translate(414.946203 98.586589) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_69">
      <path d="M 436.259015 58.590966 
L 813.937288 58.590966 
" clip-path="url(#pff30a22d3e)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_70">
      <g>
       <use xlink:href="#ma4cdaa7888" x="436.259015" y="58.590966" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_43">
      <!-- 0.7 -->
      <g transform="translate(414.946203 62.010263) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-37" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_44">
     <!-- Neural Activation -->
     <g transform="translate(408.658547 233.746391) rotate(-90) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="line2d_71">
    <path d="M 440.97851 314.62525 
L 450.4175 290.649199 
L 459.85649 311.002807 
L 469.29548 289.708679 
L 478.73447 311.394851 
L 488.17346 293.148336 
L 497.61245 276.134754 
L 507.051441 258.44038 
L 516.490431 247.967999 
L 525.929421 256.673519 
L 535.368411 243.004212 
L 544.807401 255.477495 
L 554.246391 214.948261 
L 563.685381 223.441634 
L 573.124371 227.204808 
L 582.563361 237.122872 
L 592.002351 201.085462 
L 601.441341 173.357097 
L 610.880331 209.332907 
L 620.319321 110.215271 
L 629.758311 106.496882 
L 639.197301 57.695726 
L 648.636291 175.618076 
L 658.075281 175.338051 
L 667.514271 230.425383 
L 676.953261 183.320622 
L 686.392251 109.571743 
L 695.831241 217.810932 
L 705.270231 138.875224 
L 714.709221 144.139371 
" clip-path="url(#pff30a22d3e)" style="fill: none; stroke: #1f78b4; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_72">
    <path d="M 639.197301 314.62525 
L 639.197301 44.84925 
" clip-path="url(#pff30a22d3e)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #1f78b4; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_73">
    <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#pff30a22d3e)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_74">
    <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#pff30a22d3e)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="patch_13">
    <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_14">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_45">
    <g id="patch_15">
     <path d="M 700.612124 114.0718 
L 795.053374 114.0718 
Q 798.253374 114.0718 798.253374 110.8718 
L 798.253374 58.33805 
Q 798.253374 55.13805 795.053374 55.13805 
L 700.612124 55.13805 
Q 697.412124 55.13805 697.412124 58.33805 
L 697.412124 110.8718 
Q 697.412124 114.0718 700.612124 114.0718 
z
" style="fill: #ffffff; opacity: 0.9; stroke: #808080; stroke-linejoin: miter"/>
    </g>
    <!-- Peak Distance: 4.3m -->
    <g transform="translate(712.787124 64.4168) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-3a" d="M 750 794 
L 1409 794 
L 1409 0 
L 750 0 
L 750 794 
z
M 750 3309 
L 1409 3309 
L 1409 2516 
L 750 2516 
L 750 3309 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-44" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="346.179688"/>
     <use xlink:href="#DejaVuSans-73" x="373.962891"/>
     <use xlink:href="#DejaVuSans-74" x="426.0625"/>
     <use xlink:href="#DejaVuSans-61" x="465.271484"/>
     <use xlink:href="#DejaVuSans-6e" x="526.550781"/>
     <use xlink:href="#DejaVuSans-63" x="589.929688"/>
     <use xlink:href="#DejaVuSans-65" x="644.910156"/>
     <use xlink:href="#DejaVuSans-3a" x="706.433594"/>
     <use xlink:href="#DejaVuSans-20" x="740.125"/>
     <use xlink:href="#DejaVuSans-34" x="771.912109"/>
     <use xlink:href="#DejaVuSans-2e" x="835.535156"/>
     <use xlink:href="#DejaVuSans-33" x="867.322266"/>
     <use xlink:href="#DejaVuSans-6d" x="930.945312"/>
    </g>
    <!-- Max Activation: 0.702 -->
    <g transform="translate(707.779624 73.37505) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-4d"/>
     <use xlink:href="#DejaVuSans-61" x="86.279297"/>
     <use xlink:href="#DejaVuSans-78" x="147.558594"/>
     <use xlink:href="#DejaVuSans-20" x="206.738281"/>
     <use xlink:href="#DejaVuSans-41" x="238.525391"/>
     <use xlink:href="#DejaVuSans-63" x="305.183594"/>
     <use xlink:href="#DejaVuSans-74" x="360.164062"/>
     <use xlink:href="#DejaVuSans-69" x="399.373047"/>
     <use xlink:href="#DejaVuSans-76" x="427.15625"/>
     <use xlink:href="#DejaVuSans-61" x="486.335938"/>
     <use xlink:href="#DejaVuSans-74" x="547.615234"/>
     <use xlink:href="#DejaVuSans-69" x="586.824219"/>
     <use xlink:href="#DejaVuSans-6f" x="614.607422"/>
     <use xlink:href="#DejaVuSans-6e" x="675.789062"/>
     <use xlink:href="#DejaVuSans-3a" x="739.167969"/>
     <use xlink:href="#DejaVuSans-20" x="772.859375"/>
     <use xlink:href="#DejaVuSans-30" x="804.646484"/>
     <use xlink:href="#DejaVuSans-2e" x="868.269531"/>
     <use xlink:href="#DejaVuSans-37" x="900.056641"/>
     <use xlink:href="#DejaVuSans-30" x="963.679688"/>
     <use xlink:href="#DejaVuSans-32" x="1027.302734"/>
    </g>
    <!-- Sparsity: 0.325 -->
    <g transform="translate(734.883374 82.3333) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-70" x="63.476562"/>
     <use xlink:href="#DejaVuSans-61" x="126.953125"/>
     <use xlink:href="#DejaVuSans-72" x="188.232422"/>
     <use xlink:href="#DejaVuSans-73" x="229.345703"/>
     <use xlink:href="#DejaVuSans-69" x="281.445312"/>
     <use xlink:href="#DejaVuSans-74" x="309.228516"/>
     <use xlink:href="#DejaVuSans-79" x="348.4375"/>
     <use xlink:href="#DejaVuSans-3a" x="400.367188"/>
     <use xlink:href="#DejaVuSans-20" x="434.058594"/>
     <use xlink:href="#DejaVuSans-30" x="465.845703"/>
     <use xlink:href="#DejaVuSans-2e" x="529.46875"/>
     <use xlink:href="#DejaVuSans-33" x="561.255859"/>
     <use xlink:href="#DejaVuSans-32" x="624.878906"/>
     <use xlink:href="#DejaVuSans-35" x="688.501953"/>
    </g>
    <!-- Peak Significance: 3.01 -->
    <g transform="translate(702.065874 91.29155) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-53" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="332.654297"/>
     <use xlink:href="#DejaVuSans-67" x="360.4375"/>
     <use xlink:href="#DejaVuSans-6e" x="423.914062"/>
     <use xlink:href="#DejaVuSans-69" x="487.292969"/>
     <use xlink:href="#DejaVuSans-66" x="515.076172"/>
     <use xlink:href="#DejaVuSans-69" x="550.28125"/>
     <use xlink:href="#DejaVuSans-63" x="578.064453"/>
     <use xlink:href="#DejaVuSans-61" x="633.044922"/>
     <use xlink:href="#DejaVuSans-6e" x="694.324219"/>
     <use xlink:href="#DejaVuSans-63" x="757.703125"/>
     <use xlink:href="#DejaVuSans-65" x="812.683594"/>
     <use xlink:href="#DejaVuSans-3a" x="874.207031"/>
     <use xlink:href="#DejaVuSans-20" x="907.898438"/>
     <use xlink:href="#DejaVuSans-33" x="939.685547"/>
     <use xlink:href="#DejaVuSans-2e" x="1003.308594"/>
     <use xlink:href="#DejaVuSans-30" x="1035.095703"/>
     <use xlink:href="#DejaVuSans-31" x="1098.71875"/>
    </g>
    <!-- Selectivity Index: 0.343 -->
    <g transform="translate(700.612124 100.2498) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-65" x="63.476562"/>
     <use xlink:href="#DejaVuSans-6c" x="125"/>
     <use xlink:href="#DejaVuSans-65" x="152.783203"/>
     <use xlink:href="#DejaVuSans-63" x="214.306641"/>
     <use xlink:href="#DejaVuSans-74" x="269.287109"/>
     <use xlink:href="#DejaVuSans-69" x="308.496094"/>
     <use xlink:href="#DejaVuSans-76" x="336.279297"/>
     <use xlink:href="#DejaVuSans-69" x="395.458984"/>
     <use xlink:href="#DejaVuSans-74" x="423.242188"/>
     <use xlink:href="#DejaVuSans-79" x="462.451172"/>
     <use xlink:href="#DejaVuSans-20" x="521.630859"/>
     <use xlink:href="#DejaVuSans-49" x="553.417969"/>
     <use xlink:href="#DejaVuSans-6e" x="582.910156"/>
     <use xlink:href="#DejaVuSans-64" x="646.289062"/>
     <use xlink:href="#DejaVuSans-65" x="709.765625"/>
     <use xlink:href="#DejaVuSans-78" x="769.539062"/>
     <use xlink:href="#DejaVuSans-3a" x="828.71875"/>
     <use xlink:href="#DejaVuSans-20" x="862.410156"/>
     <use xlink:href="#DejaVuSans-30" x="894.197266"/>
     <use xlink:href="#DejaVuSans-2e" x="957.820312"/>
     <use xlink:href="#DejaVuSans-33" x="989.607422"/>
     <use xlink:href="#DejaVuSans-34" x="1053.230469"/>
     <use xlink:href="#DejaVuSans-33" x="1116.853516"/>
    </g>
    <!-- Peak Width: 2.4m -->
    <g transform="translate(724.529624 109.20805) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-57" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="365.804688"/>
     <use xlink:href="#DejaVuSans-64" x="393.587891"/>
     <use xlink:href="#DejaVuSans-74" x="457.064453"/>
     <use xlink:href="#DejaVuSans-68" x="496.273438"/>
     <use xlink:href="#DejaVuSans-3a" x="559.652344"/>
     <use xlink:href="#DejaVuSans-20" x="593.34375"/>
     <use xlink:href="#DejaVuSans-32" x="625.130859"/>
     <use xlink:href="#DejaVuSans-2e" x="688.753906"/>
     <use xlink:href="#DejaVuSans-34" x="720.541016"/>
     <use xlink:href="#DejaVuSans-6d" x="784.164062"/>
    </g>
   </g>
   <g id="text_46">
    <!-- Distance Tuning Curve -->
    <g transform="translate(549.284402 16.318125) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-54" d="M 31 4666 
L 4331 4666 
L 4331 3756 
L 2784 3756 
L 2784 0 
L 1581 0 
L 1581 3756 
L 31 3756 
L 31 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-44"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
     <use xlink:href="#DejaVuSans-Bold-54" x="525.195312"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="582.408203"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="653.599609"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="724.791016"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="759.068359"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="830.259766"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="901.841797"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="936.65625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1010.044922"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1081.236328"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="1130.552734"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1195.738281"/>
    </g>
    <!-- Peak: 4.3m, Sparsity: 0.325 -->
    <g transform="translate(531.359402 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-6b" d="M 538 4863 
L 1656 4863 
L 1656 2216 
L 2944 3500 
L 4244 3500 
L 2534 1894 
L 4378 0 
L 3022 0 
L 1656 1459 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-3a" d="M 716 3500 
L 1844 3500 
L 1844 2291 
L 716 2291 
L 716 3500 
z
M 716 1209 
L 1844 1209 
L 1844 0 
L 716 0 
L 716 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-34" d="M 2356 3675 
L 1038 1722 
L 2356 1722 
L 2356 3675 
z
M 2156 4666 
L 3494 4666 
L 3494 1722 
L 4159 1722 
L 4159 850 
L 3494 850 
L 3494 0 
L 2356 0 
L 2356 850 
L 288 850 
L 288 1881 
L 2156 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2c" d="M 653 1209 
L 1778 1209 
L 1778 256 
L 1006 -909 
L 341 -909 
L 653 256 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-53" d="M 3834 4519 
L 3834 3531 
Q 3450 3703 3084 3790 
Q 2719 3878 2394 3878 
Q 1963 3878 1756 3759 
Q 1550 3641 1550 3391 
Q 1550 3203 1689 3098 
Q 1828 2994 2194 2919 
L 2706 2816 
Q 3484 2659 3812 2340 
Q 4141 2022 4141 1434 
Q 4141 663 3683 286 
Q 3225 -91 2284 -91 
Q 1841 -91 1394 -6 
Q 947 78 500 244 
L 500 1259 
Q 947 1022 1364 901 
Q 1781 781 2169 781 
Q 2563 781 2772 912 
Q 2981 1044 2981 1288 
Q 2981 1506 2839 1625 
Q 2697 1744 2272 1838 
L 1806 1941 
Q 1106 2091 782 2419 
Q 459 2747 459 3303 
Q 459 4000 909 4375 
Q 1359 4750 2203 4750 
Q 2588 4750 2994 4692 
Q 3400 4634 3834 4519 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-79" d="M 78 3500 
L 1197 3500 
L 2138 1125 
L 2938 3500 
L 4056 3500 
L 2584 -331 
Q 2363 -916 2067 -1148 
Q 1772 -1381 1288 -1381 
L 641 -1381 
L 641 -647 
L 991 -647 
Q 1275 -647 1404 -556 
Q 1534 -466 1606 -231 
L 1638 -134 
L 78 3500 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-50"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="73.291016"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="141.113281"/>
     <use xlink:href="#DejaVuSans-Bold-6b" x="208.59375"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="275.097656"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="315.087891"/>
     <use xlink:href="#DejaVuSans-Bold-34" x="349.902344"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="419.482422"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="457.470703"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="527.050781"/>
     <use xlink:href="#DejaVuSans-Bold-2c" x="631.25"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="669.238281"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="704.052734"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="776.074219"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="847.65625"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="915.136719"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="964.453125"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1023.974609"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="1058.251953"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="1106.054688"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="1171.240234"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1211.230469"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1246.044922"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="1315.625"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="1353.613281"/>
     <use xlink:href="#DejaVuSans-Bold-32" x="1423.193359"/>
     <use xlink:href="#DejaVuSans-Bold-35" x="1492.773438"/>
    </g>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="ma4ccb5741d" d="M 0 5 
C 1.326016 5 2.597899 4.473168 3.535534 3.535534 
C 4.473168 2.597899 5 1.326016 5 0 
C 5 -1.326016 4.473168 -2.597899 3.535534 -3.535534 
C 2.597899 -4.473168 1.326016 -5 0 -5 
C -1.326016 -5 -2.597899 -4.473168 -3.535534 -3.535534 
C -4.473168 -2.597899 -5 -1.326016 -5 0 
C -5 1.326016 -4.473168 2.597899 -3.535534 3.535534 
C -2.597899 4.473168 -1.326016 5 0 5 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#pff30a22d3e)">
     <use xlink:href="#ma4ccb5741d" x="639.197301" y="57.695726" style="fill: #1f78b4; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="patch_16">
     <path d="M 691.429788 86.47675 
L 808.337288 86.47675 
Q 809.937288 86.47675 809.937288 84.87675 
L 809.937288 50.44925 
Q 809.937288 48.84925 808.337288 48.84925 
L 691.429788 48.84925 
Q 689.829788 48.84925 689.829788 50.44925 
L 689.829788 84.87675 
Q 689.829788 86.47675 691.429788 86.47675 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_75">
     <path d="M 693.029788 55.328 
L 701.029788 55.328 
L 709.029788 55.328 
" style="fill: none; stroke: #1f78b4; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
    </g>
    <g id="text_47">
     <!-- Tuning Curve -->
     <g transform="translate(715.429788 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-54"/>
      <use xlink:href="#DejaVuSans-75" x="45.958984"/>
      <use xlink:href="#DejaVuSans-6e" x="109.337891"/>
      <use xlink:href="#DejaVuSans-69" x="172.716797"/>
      <use xlink:href="#DejaVuSans-6e" x="200.5"/>
      <use xlink:href="#DejaVuSans-67" x="263.878906"/>
      <use xlink:href="#DejaVuSans-20" x="327.355469"/>
      <use xlink:href="#DejaVuSans-43" x="359.142578"/>
      <use xlink:href="#DejaVuSans-75" x="428.966797"/>
      <use xlink:href="#DejaVuSans-72" x="492.345703"/>
      <use xlink:href="#DejaVuSans-76" x="533.458984"/>
      <use xlink:href="#DejaVuSans-65" x="592.638672"/>
     </g>
    </g>
    <g id="line2d_76">
     <path d="M 693.029788 67.0705 
L 701.029788 67.0705 
L 709.029788 67.0705 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_48">
     <!-- Close threshold (3.0m) -->
     <g transform="translate(715.429788 69.8705) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6c" x="69.824219"/>
      <use xlink:href="#DejaVuSans-6f" x="97.607422"/>
      <use xlink:href="#DejaVuSans-73" x="158.789062"/>
      <use xlink:href="#DejaVuSans-65" x="210.888672"/>
      <use xlink:href="#DejaVuSans-20" x="272.412109"/>
      <use xlink:href="#DejaVuSans-74" x="304.199219"/>
      <use xlink:href="#DejaVuSans-68" x="343.408203"/>
      <use xlink:href="#DejaVuSans-72" x="406.787109"/>
      <use xlink:href="#DejaVuSans-65" x="445.650391"/>
      <use xlink:href="#DejaVuSans-73" x="507.173828"/>
      <use xlink:href="#DejaVuSans-68" x="559.273438"/>
      <use xlink:href="#DejaVuSans-6f" x="622.652344"/>
      <use xlink:href="#DejaVuSans-6c" x="683.833984"/>
      <use xlink:href="#DejaVuSans-64" x="711.617188"/>
      <use xlink:href="#DejaVuSans-20" x="775.09375"/>
      <use xlink:href="#DejaVuSans-28" x="806.880859"/>
      <use xlink:href="#DejaVuSans-33" x="845.894531"/>
      <use xlink:href="#DejaVuSans-2e" x="909.517578"/>
      <use xlink:href="#DejaVuSans-30" x="941.304688"/>
      <use xlink:href="#DejaVuSans-6d" x="1004.927734"/>
      <use xlink:href="#DejaVuSans-29" x="1102.339844"/>
     </g>
    </g>
    <g id="line2d_77">
     <path d="M 693.029788 78.813 
L 701.029788 78.813 
L 709.029788 78.813 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_49">
     <!-- Far threshold (5.0m) -->
     <g transform="translate(715.429788 81.613) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" x="48.394531"/>
      <use xlink:href="#DejaVuSans-72" x="109.673828"/>
      <use xlink:href="#DejaVuSans-20" x="150.787109"/>
      <use xlink:href="#DejaVuSans-74" x="182.574219"/>
      <use xlink:href="#DejaVuSans-68" x="221.783203"/>
      <use xlink:href="#DejaVuSans-72" x="285.162109"/>
      <use xlink:href="#DejaVuSans-65" x="324.025391"/>
      <use xlink:href="#DejaVuSans-73" x="385.548828"/>
      <use xlink:href="#DejaVuSans-68" x="437.648438"/>
      <use xlink:href="#DejaVuSans-6f" x="501.027344"/>
      <use xlink:href="#DejaVuSans-6c" x="562.208984"/>
      <use xlink:href="#DejaVuSans-64" x="589.992188"/>
      <use xlink:href="#DejaVuSans-20" x="653.46875"/>
      <use xlink:href="#DejaVuSans-28" x="685.255859"/>
      <use xlink:href="#DejaVuSans-35" x="724.269531"/>
      <use xlink:href="#DejaVuSans-2e" x="787.892578"/>
      <use xlink:href="#DejaVuSans-30" x="819.679688"/>
      <use xlink:href="#DejaVuSans-6d" x="883.302734"/>
      <use xlink:href="#DejaVuSans-29" x="980.714844"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_17">
    <path d="M 330.77382 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 330.77382 71.82685 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAABcAAAHBCAYAAACCOiAmAAACGUlEQVR4nO2b0W0bURDEnqRz3+k7VpwCjvkcAQzIAhbEzuzpLMiPc379nBHXOddq9nnOJp9zrnO+ZsPX5u0ch7eWO1URqYqIuope811bWgtSFZGqiFRFpJ0jvVog6ip6zXue3/Ee0eOcn9mXOVPzhv9nw6/z2A0fm7+Ww3cPxfVadg/FzJmqiJgD9ZpXRcAcqNe8KgLmQL3mVREwB+o1r4qAOVCveVUEzIF6zYdVHJsPe24ONHPAe0Q9z5HMkal5a0EyR3oqIuZAM//0cO9aMkd6KiLmQDPn4bNfWs3X8p4Nzxy5nq/v3fDry7qWlzZQs/myit61PLXmJ/M7a/PlEYnXkvmdqogUKJL5v4bver5dS0dEeM2rIuIN1GteFRFvoF5z9Vo6ojtVEamKSFX8/HDvWjoiZHpE6kC95lURhpsDzfxOR4R0RIjavCrCcHOgXvOqCMPNgWZ+x3tEfSWCZI5URUQdaOY4vPeWO95Ax+bvZRW/tVV8/5kNHwe6W7nY/Hrsar7+rxxtoJnz8KoIZI5M22JeS+aAt4renRco4g3Ua16gSOZIVUQKFPF+EpnX4jXv/D893LuWqohURaRAEa+5eS1VEfCaFyiSOdKfLUjmiLeK3p0XKOIN1GteoEjmSFVErt/aQIe/hVzvfPmWK16L1nwovt65dXiBIu2ch7cWoioiVRGpiojXvECRjgipikhVRLw7967lLyPgue9DbEF4AAAAAElFTkSuQmCC" id="image171b60d0af" transform="scale(1 -1) translate(0 -215.52)" x="330.72" y="-71.52" width="11.04" height="215.52"/>
   <g id="matplotlib.axis_5"/>
   <g id="matplotlib.axis_6">
    <g id="ytick_18">
     <g id="line2d_78">
      <defs>
       <path id="m13ddc9d7e5" d="M 0 0 
L 3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m13ddc9d7e5" x="341.56486" y="287.64765" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_50">
      <!-- 0.0 -->
      <g transform="translate(348.56486 291.066947) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_79">
      <g>
       <use xlink:href="#m13ddc9d7e5" x="341.56486" y="244.48349" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_51">
      <!-- 0.2 -->
      <g transform="translate(348.56486 247.902787) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_80">
      <g>
       <use xlink:href="#m13ddc9d7e5" x="341.56486" y="201.31933" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_52">
      <!-- 0.4 -->
      <g transform="translate(348.56486 204.738627) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_81">
      <g>
       <use xlink:href="#m13ddc9d7e5" x="341.56486" y="158.15517" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_53">
      <!-- 0.6 -->
      <g transform="translate(348.56486 161.574467) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_22">
     <g id="line2d_82">
      <g>
       <use xlink:href="#m13ddc9d7e5" x="341.56486" y="114.99101" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_54">
      <!-- 0.8 -->
      <g transform="translate(348.56486 118.410307) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-38" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_23">
     <g id="line2d_83">
      <g>
       <use xlink:href="#m13ddc9d7e5" x="341.56486" y="71.82685" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_55">
      <!-- 1.0 -->
      <g transform="translate(348.56486 75.246147) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_56">
     <!-- Neural Activation -->
     <g transform="translate(369.519391 125.728109) rotate(-270) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_18">
    <path d="M 330.77382 287.64765 
L 336.16934 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 336.16934 71.82685 
L 330.77382 71.82685 
L 330.77382 287.64765 
z
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p1c8996a634">
   <rect x="42.113906" y="44.84925" width="269.776" height="269.776"/>
  </clipPath>
  <clipPath id="pff30a22d3e">
   <rect x="436.259015" y="44.84925" width="377.678273" height="269.776"/>
  </clipPath>
 </defs>
</svg>
