<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns:xlink="http://www.w3.org/1999/xlink" width="824.000413pt" height="352.267438pt" viewBox="0 0 824.000413 352.267438" xmlns="http://www.w3.org/2000/svg" version="1.1">
 <metadata>
  <rdf:RDF xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
   <cc:Work>
    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/>
    <dc:date>2025-08-05T17:55:07.698535</dc:date>
    <dc:format>image/svg+xml</dc:format>
    <dc:creator>
     <cc:Agent>
      <dc:title>Matplotlib v3.7.5, https://matplotlib.org/</dc:title>
     </cc:Agent>
    </dc:creator>
   </cc:Work>
  </rdf:RDF>
 </metadata>
 <defs>
  <style type="text/css">*{stroke-linejoin: round; stroke-linecap: butt}</style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 352.267438 
L 824.000413 352.267438 
L 824.000413 0 
L 0 0 
z
" style="fill: #ffffff"/>
  </g>
  <g id="axes_1">
   <g id="patch_2">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
L 311.889906 44.84925 
L 42.113906 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g clip-path="url(#p7a841bf3ed)">
    <image xlink:href="data:image/png;base64,
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" id="image0582911ac8" transform="scale(1 -1) translate(0 -270.24)" x="42.113906" y="-44.38525" width="270.24" height="270.24"/>
   </g>
   <g id="patch_3">
    <path d="M 177.001906 230.32025 
C 190.416675 230.32025 203.283815 224.990506 212.769489 215.504832 
C 222.255162 206.019159 227.584906 193.152018 227.584906 179.73725 
C 227.584906 166.322482 222.255162 153.455341 212.769489 143.969668 
C 203.283815 134.483994 190.416675 129.15425 177.001906 129.15425 
C 163.587138 129.15425 150.719998 134.483994 141.234324 143.969668 
C 131.74865 153.455341 126.418906 166.322482 126.418906 179.73725 
C 126.418906 193.152018 131.74865 206.019159 141.234324 215.504832 
C 150.719998 224.990506 163.587138 230.32025 177.001906 230.32025 
L 177.001906 230.32025 
z
" clip-path="url(#p7a841bf3ed)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_4">
    <path d="M 177.001906 264.04225 
C 199.359854 264.04225 220.805087 255.159343 236.614543 239.349887 
C 252.424 223.540431 261.306906 202.095197 261.306906 179.73725 
C 261.306906 157.379303 252.424 135.934069 236.614543 120.124613 
C 220.805087 104.315157 199.359854 95.43225 177.001906 95.43225 
C 154.643959 95.43225 133.198725 104.315157 117.389269 120.124613 
C 101.579813 135.934069 92.696906 157.379303 92.696906 179.73725 
C 92.696906 202.095197 101.579813 223.540431 117.389269 239.349887 
C 133.198725 255.159343 154.643959 264.04225 177.001906 264.04225 
L 177.001906 264.04225 
z
" clip-path="url(#p7a841bf3ed)" style="fill: none; opacity: 0.7; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="patch_5">
    <path d="M 177.001906 314.62525 
C 212.774622 314.62525 247.086996 300.412599 272.382126 275.11747 
C 297.677256 249.82234 311.889906 215.509966 311.889906 179.73725 
C 311.889906 143.964534 297.677256 109.65216 272.382126 84.35703 
C 247.086996 59.061901 212.774622 44.84925 177.001906 44.84925 
C 141.22919 44.84925 106.916817 59.061901 81.621687 84.35703 
C 56.326557 109.65216 42.113906 143.964534 42.113906 179.73725 
C 42.113906 215.509966 56.326557 249.82234 81.621687 275.11747 
C 106.916817 300.412599 141.22919 314.62525 177.001906 314.62525 
L 177.001906 314.62525 
z
" clip-path="url(#p7a841bf3ed)" style="fill: none; opacity: 0.7; stroke: #ffffff; stroke-width: 1.5; stroke-linejoin: miter"/>
   </g>
   <g id="matplotlib.axis_1">
    <g id="xtick_1">
     <g id="line2d_1">
      <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" clip-path="url(#p7a841bf3ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_2">
      <defs>
       <path id="m48f467c95e" d="M 0 0 
L 0 3.5 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m48f467c95e" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_1">
      <!-- −8 -->
      <g transform="translate(35.479922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2212" d="M 678 2272 
L 4684 2272 
L 4684 1741 
L 678 1741 
L 678 2272 
z
" transform="scale(0.015625)"/>
        <path id="DejaVuSans-38" d="M 2034 2216 
Q 1584 2216 1326 1975 
Q 1069 1734 1069 1313 
Q 1069 891 1326 650 
Q 1584 409 2034 409 
Q 2484 409 2743 651 
Q 3003 894 3003 1313 
Q 3003 1734 2745 1975 
Q 2488 2216 2034 2216 
z
M 1403 2484 
Q 997 2584 770 2862 
Q 544 3141 544 3541 
Q 544 4100 942 4425 
Q 1341 4750 2034 4750 
Q 2731 4750 3128 4425 
Q 3525 4100 3525 3541 
Q 3525 3141 3298 2862 
Q 3072 2584 2669 2484 
Q 3125 2378 3379 2068 
Q 3634 1759 3634 1313 
Q 3634 634 3220 271 
Q 2806 -91 2034 -91 
Q 1263 -91 848 271 
Q 434 634 434 1313 
Q 434 1759 690 2068 
Q 947 2378 1403 2484 
z
M 1172 3481 
Q 1172 3119 1398 2916 
Q 1625 2713 2034 2713 
Q 2441 2713 2670 2916 
Q 2900 3119 2900 3481 
Q 2900 3844 2670 4047 
Q 2441 4250 2034 4250 
Q 1625 4250 1398 4047 
Q 1172 3844 1172 3481 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_2">
     <g id="line2d_3">
      <path d="M 75.835906 314.62525 
L 75.835906 44.84925 
" clip-path="url(#p7a841bf3ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_4">
      <g>
       <use xlink:href="#m48f467c95e" x="75.835906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_2">
      <!-- −6 -->
      <g transform="translate(69.201922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-36" d="M 2113 2584 
Q 1688 2584 1439 2293 
Q 1191 2003 1191 1497 
Q 1191 994 1439 701 
Q 1688 409 2113 409 
Q 2538 409 2786 701 
Q 3034 994 3034 1497 
Q 3034 2003 2786 2293 
Q 2538 2584 2113 2584 
z
M 3366 4563 
L 3366 3988 
Q 3128 4100 2886 4159 
Q 2644 4219 2406 4219 
Q 1781 4219 1451 3797 
Q 1122 3375 1075 2522 
Q 1259 2794 1537 2939 
Q 1816 3084 2150 3084 
Q 2853 3084 3261 2657 
Q 3669 2231 3669 1497 
Q 3669 778 3244 343 
Q 2819 -91 2113 -91 
Q 1303 -91 875 529 
Q 447 1150 447 2328 
Q 447 3434 972 4092 
Q 1497 4750 2381 4750 
Q 2619 4750 2861 4703 
Q 3103 4656 3366 4563 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_3">
     <g id="line2d_5">
      <path d="M 109.557906 314.62525 
L 109.557906 44.84925 
" clip-path="url(#p7a841bf3ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_6">
      <g>
       <use xlink:href="#m48f467c95e" x="109.557906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_3">
      <!-- −4 -->
      <g transform="translate(102.923922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-34" d="M 2419 4116 
L 825 1625 
L 2419 1625 
L 2419 4116 
z
M 2253 4666 
L 3047 4666 
L 3047 1625 
L 3713 1625 
L 3713 1100 
L 3047 1100 
L 3047 0 
L 2419 0 
L 2419 1100 
L 313 1100 
L 313 1709 
L 2253 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_4">
     <g id="line2d_7">
      <path d="M 143.279906 314.62525 
L 143.279906 44.84925 
" clip-path="url(#p7a841bf3ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_8">
      <g>
       <use xlink:href="#m48f467c95e" x="143.279906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_4">
      <!-- −2 -->
      <g transform="translate(136.645922 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-32" d="M 1228 531 
L 3431 531 
L 3431 0 
L 469 0 
L 469 531 
Q 828 903 1448 1529 
Q 2069 2156 2228 2338 
Q 2531 2678 2651 2914 
Q 2772 3150 2772 3378 
Q 2772 3750 2511 3984 
Q 2250 4219 1831 4219 
Q 1534 4219 1204 4116 
Q 875 4013 500 3803 
L 500 4441 
Q 881 4594 1212 4672 
Q 1544 4750 1819 4750 
Q 2544 4750 2975 4387 
Q 3406 4025 3406 3419 
Q 3406 3131 3298 2873 
Q 3191 2616 2906 2266 
Q 2828 2175 2409 1742 
Q 1991 1309 1228 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="xtick_5">
     <g id="line2d_9">
      <path d="M 177.001906 314.62525 
L 177.001906 44.84925 
" clip-path="url(#p7a841bf3ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_10">
      <g>
       <use xlink:href="#m48f467c95e" x="177.001906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_5">
      <!-- 0 -->
      <g transform="translate(174.138781 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-30" d="M 2034 4250 
Q 1547 4250 1301 3770 
Q 1056 3291 1056 2328 
Q 1056 1369 1301 889 
Q 1547 409 2034 409 
Q 2525 409 2770 889 
Q 3016 1369 3016 2328 
Q 3016 3291 2770 3770 
Q 2525 4250 2034 4250 
z
M 2034 4750 
Q 2819 4750 3233 4129 
Q 3647 3509 3647 2328 
Q 3647 1150 3233 529 
Q 2819 -91 2034 -91 
Q 1250 -91 836 529 
Q 422 1150 422 2328 
Q 422 3509 836 4129 
Q 1250 4750 2034 4750 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_6">
     <g id="line2d_11">
      <path d="M 210.723906 314.62525 
L 210.723906 44.84925 
" clip-path="url(#p7a841bf3ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_12">
      <g>
       <use xlink:href="#m48f467c95e" x="210.723906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_6">
      <!-- 2 -->
      <g transform="translate(207.860781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_7">
     <g id="line2d_13">
      <path d="M 244.445906 314.62525 
L 244.445906 44.84925 
" clip-path="url(#p7a841bf3ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_14">
      <g>
       <use xlink:href="#m48f467c95e" x="244.445906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_7">
      <!-- 4 -->
      <g transform="translate(241.582781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_8">
     <g id="line2d_15">
      <path d="M 278.167906 314.62525 
L 278.167906 44.84925 
" clip-path="url(#p7a841bf3ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_16">
      <g>
       <use xlink:href="#m48f467c95e" x="278.167906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_8">
      <!-- 6 -->
      <g transform="translate(275.304781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_9">
     <g id="line2d_17">
      <path d="M 311.889906 314.62525 
L 311.889906 44.84925 
" clip-path="url(#p7a841bf3ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_18">
      <g>
       <use xlink:href="#m48f467c95e" x="311.889906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_9">
      <!-- 8 -->
      <g transform="translate(309.026781 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_10">
     <!-- Relative X Position (m) -->
     <g transform="translate(105.68925 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-52" d="M 2297 2597 
Q 2675 2597 2839 2737 
Q 3003 2878 3003 3200 
Q 3003 3519 2839 3656 
Q 2675 3794 2297 3794 
L 1791 3794 
L 1791 2597 
L 2297 2597 
z
M 1791 1766 
L 1791 0 
L 588 0 
L 588 4666 
L 2425 4666 
Q 3347 4666 3776 4356 
Q 4206 4047 4206 3378 
Q 4206 2916 3982 2619 
Q 3759 2322 3309 2181 
Q 3556 2125 3751 1926 
Q 3947 1728 4147 1325 
L 4800 0 
L 3519 0 
L 2950 1159 
Q 2778 1509 2601 1637 
Q 2425 1766 2131 1766 
L 1791 1766 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-65" d="M 4031 1759 
L 4031 1441 
L 1416 1441 
Q 1456 1047 1700 850 
Q 1944 653 2381 653 
Q 2734 653 3104 758 
Q 3475 863 3866 1075 
L 3866 213 
Q 3469 63 3072 -14 
Q 2675 -91 2278 -91 
Q 1328 -91 801 392 
Q 275 875 275 1747 
Q 275 2603 792 3093 
Q 1309 3584 2216 3584 
Q 3041 3584 3536 3087 
Q 4031 2591 4031 1759 
z
M 2881 2131 
Q 2881 2450 2695 2645 
Q 2509 2841 2209 2841 
Q 1884 2841 1681 2658 
Q 1478 2475 1428 2131 
L 2881 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6c" d="M 538 4863 
L 1656 4863 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-61" d="M 2106 1575 
Q 1756 1575 1579 1456 
Q 1403 1338 1403 1106 
Q 1403 894 1545 773 
Q 1688 653 1941 653 
Q 2256 653 2472 879 
Q 2688 1106 2688 1447 
L 2688 1575 
L 2106 1575 
z
M 3816 1997 
L 3816 0 
L 2688 0 
L 2688 519 
Q 2463 200 2181 54 
Q 1900 -91 1497 -91 
Q 953 -91 614 226 
Q 275 544 275 1050 
Q 275 1666 698 1953 
Q 1122 2241 2028 2241 
L 2688 2241 
L 2688 2328 
Q 2688 2594 2478 2717 
Q 2269 2841 1825 2841 
Q 1466 2841 1156 2769 
Q 847 2697 581 2553 
L 581 3406 
Q 941 3494 1303 3539 
Q 1666 3584 2028 3584 
Q 2975 3584 3395 3211 
Q 3816 2838 3816 1997 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-74" d="M 1759 4494 
L 1759 3500 
L 2913 3500 
L 2913 2700 
L 1759 2700 
L 1759 1216 
Q 1759 972 1856 886 
Q 1953 800 2241 800 
L 2816 800 
L 2816 0 
L 1856 0 
Q 1194 0 917 276 
Q 641 553 641 1216 
L 641 2700 
L 84 2700 
L 84 3500 
L 641 3500 
L 641 4494 
L 1759 4494 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-69" d="M 538 3500 
L 1656 3500 
L 1656 0 
L 538 0 
L 538 3500 
z
M 538 4863 
L 1656 4863 
L 1656 3950 
L 538 3950 
L 538 4863 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-76" d="M 97 3500 
L 1216 3500 
L 2088 1081 
L 2956 3500 
L 4078 3500 
L 2700 0 
L 1472 0 
L 97 3500 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-20" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-58" d="M 3188 2381 
L 4806 0 
L 3553 0 
L 2463 1594 
L 1381 0 
L 122 0 
L 1741 2381 
L 184 4666 
L 1441 4666 
L 2463 3163 
L 3481 4666 
L 4744 4666 
L 3188 2381 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-50" d="M 588 4666 
L 2584 4666 
Q 3475 4666 3951 4270 
Q 4428 3875 4428 3144 
Q 4428 2409 3951 2014 
Q 3475 1619 2584 1619 
L 1791 1619 
L 1791 0 
L 588 0 
L 588 4666 
z
M 1791 3794 
L 1791 2491 
L 2456 2491 
Q 2806 2491 2997 2661 
Q 3188 2831 3188 3144 
Q 3188 3456 2997 3625 
Q 2806 3794 2456 3794 
L 1791 3794 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6f" d="M 2203 2784 
Q 1831 2784 1636 2517 
Q 1441 2250 1441 1747 
Q 1441 1244 1636 976 
Q 1831 709 2203 709 
Q 2569 709 2762 976 
Q 2956 1244 2956 1747 
Q 2956 2250 2762 2517 
Q 2569 2784 2203 2784 
z
M 2203 3584 
Q 3106 3584 3614 3096 
Q 4122 2609 4122 1747 
Q 4122 884 3614 396 
Q 3106 -91 2203 -91 
Q 1297 -91 786 396 
Q 275 884 275 1747 
Q 275 2609 786 3096 
Q 1297 3584 2203 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-73" d="M 3272 3391 
L 3272 2541 
Q 2913 2691 2578 2766 
Q 2244 2841 1947 2841 
Q 1628 2841 1473 2761 
Q 1319 2681 1319 2516 
Q 1319 2381 1436 2309 
Q 1553 2238 1856 2203 
L 2053 2175 
Q 2913 2066 3209 1816 
Q 3506 1566 3506 1031 
Q 3506 472 3093 190 
Q 2681 -91 1863 -91 
Q 1516 -91 1145 -36 
Q 775 19 384 128 
L 384 978 
Q 719 816 1070 734 
Q 1422 653 1784 653 
Q 2113 653 2278 743 
Q 2444 834 2444 1013 
Q 2444 1163 2330 1236 
Q 2216 1309 1875 1350 
L 1678 1375 
Q 931 1469 631 1722 
Q 331 1975 331 2491 
Q 331 3047 712 3315 
Q 1094 3584 1881 3584 
Q 2191 3584 2531 3537 
Q 2872 3491 3272 3391 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6e" d="M 4056 2131 
L 4056 0 
L 2931 0 
L 2931 347 
L 2931 1631 
Q 2931 2084 2911 2256 
Q 2891 2428 2841 2509 
Q 2775 2619 2662 2680 
Q 2550 2741 2406 2741 
Q 2056 2741 1856 2470 
Q 1656 2200 1656 1722 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1909 3294 2193 3439 
Q 2478 3584 2822 3584 
Q 3428 3584 3742 3212 
Q 4056 2841 4056 2131 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-28" d="M 2413 -844 
L 1484 -844 
Q 1006 -72 778 623 
Q 550 1319 550 2003 
Q 550 2688 779 3389 
Q 1009 4091 1484 4856 
L 2413 4856 
Q 2013 4116 1813 3408 
Q 1613 2700 1613 2009 
Q 1613 1319 1811 609 
Q 2009 -100 2413 -844 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-6d" d="M 3781 2919 
Q 3994 3244 4286 3414 
Q 4578 3584 4928 3584 
Q 5531 3584 5847 3212 
Q 6163 2841 6163 2131 
L 6163 0 
L 5038 0 
L 5038 1825 
Q 5041 1866 5042 1909 
Q 5044 1953 5044 2034 
Q 5044 2406 4934 2573 
Q 4825 2741 4581 2741 
Q 4263 2741 4089 2478 
Q 3916 2216 3909 1719 
L 3909 0 
L 2784 0 
L 2784 1825 
Q 2784 2406 2684 2573 
Q 2584 2741 2328 2741 
Q 2006 2741 1831 2477 
Q 1656 2213 1656 1722 
L 1656 0 
L 531 0 
L 531 3500 
L 1656 3500 
L 1656 2988 
Q 1863 3284 2130 3434 
Q 2397 3584 2719 3584 
Q 3081 3584 3359 3409 
Q 3638 3234 3781 2919 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-29" d="M 513 -844 
Q 913 -100 1113 609 
Q 1313 1319 1313 2009 
Q 1313 2700 1113 3408 
Q 913 4116 513 4856 
L 1441 4856 
Q 1916 4091 2145 3389 
Q 2375 2688 2375 2003 
Q 2375 1319 2147 623 
Q 1919 -72 1441 -844 
L 513 -844 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-58" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="573.583984"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="608.398438"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="681.689453"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="750.390625"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="809.912109"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="844.189453"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="891.992188"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="926.269531"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="994.970703"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1066.162109"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1100.976562"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1146.679688"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1250.878906"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_2">
    <g id="ytick_1">
     <g id="line2d_19">
      <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" clip-path="url(#p7a841bf3ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_20">
      <defs>
       <path id="m3a3388ed00" d="M 0 0 
L -3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#m3a3388ed00" x="42.113906" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_11">
      <!-- −8 -->
      <g transform="translate(21.845937 318.044547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-38" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_2">
     <g id="line2d_21">
      <path d="M 42.113906 280.90325 
L 311.889906 280.90325 
" clip-path="url(#p7a841bf3ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_22">
      <g>
       <use xlink:href="#m3a3388ed00" x="42.113906" y="280.90325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_12">
      <!-- −6 -->
      <g transform="translate(21.845937 284.322547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-36" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_3">
     <g id="line2d_23">
      <path d="M 42.113906 247.18125 
L 311.889906 247.18125 
" clip-path="url(#p7a841bf3ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_24">
      <g>
       <use xlink:href="#m3a3388ed00" x="42.113906" y="247.18125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_13">
      <!-- −4 -->
      <g transform="translate(21.845937 250.600547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-34" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_4">
     <g id="line2d_25">
      <path d="M 42.113906 213.45925 
L 311.889906 213.45925 
" clip-path="url(#p7a841bf3ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_26">
      <g>
       <use xlink:href="#m3a3388ed00" x="42.113906" y="213.45925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_14">
      <!-- −2 -->
      <g transform="translate(21.845937 216.878547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-2212"/>
       <use xlink:href="#DejaVuSans-32" x="83.789062"/>
      </g>
     </g>
    </g>
    <g id="ytick_5">
     <g id="line2d_27">
      <path d="M 42.113906 179.73725 
L 311.889906 179.73725 
" clip-path="url(#p7a841bf3ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_28">
      <g>
       <use xlink:href="#m3a3388ed00" x="42.113906" y="179.73725" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_15">
      <!-- 0 -->
      <g transform="translate(29.387656 183.156547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="ytick_6">
     <g id="line2d_29">
      <path d="M 42.113906 146.01525 
L 311.889906 146.01525 
" clip-path="url(#p7a841bf3ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_30">
      <g>
       <use xlink:href="#m3a3388ed00" x="42.113906" y="146.01525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_16">
      <!-- 2 -->
      <g transform="translate(29.387656 149.434547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="ytick_7">
     <g id="line2d_31">
      <path d="M 42.113906 112.29325 
L 311.889906 112.29325 
" clip-path="url(#p7a841bf3ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_32">
      <g>
       <use xlink:href="#m3a3388ed00" x="42.113906" y="112.29325" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_17">
      <!-- 4 -->
      <g transform="translate(29.387656 115.712547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="ytick_8">
     <g id="line2d_33">
      <path d="M 42.113906 78.57125 
L 311.889906 78.57125 
" clip-path="url(#p7a841bf3ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_34">
      <g>
       <use xlink:href="#m3a3388ed00" x="42.113906" y="78.57125" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_18">
      <!-- 6 -->
      <g transform="translate(29.387656 81.990547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="ytick_9">
     <g id="line2d_35">
      <path d="M 42.113906 44.84925 
L 311.889906 44.84925 
" clip-path="url(#p7a841bf3ed)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #ffffff; stroke-opacity: 0.3; stroke-width: 0.8"/>
     </g>
     <g id="line2d_36">
      <g>
       <use xlink:href="#m3a3388ed00" x="42.113906" y="44.84925" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_19">
      <!-- 8 -->
      <g transform="translate(29.387656 48.268547) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_20">
     <!-- Relative Y Position (m) -->
     <g transform="translate(15.558281 250.792094) rotate(-90) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-59" d="M -63 4666 
L 1253 4666 
L 2316 3003 
L 3378 4666 
L 4697 4666 
L 2919 1966 
L 2919 0 
L 1716 0 
L 1716 1966 
L -63 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-52"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="77.001953"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="144.824219"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="179.101562"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="246.582031"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="294.384766"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="328.662109"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="393.847656"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="461.669922"/>
      <use xlink:href="#DejaVuSans-Bold-59" x="496.484375"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="568.896484"/>
      <use xlink:href="#DejaVuSans-Bold-50" x="603.710938"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="677.001953"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="745.703125"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="805.224609"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="839.501953"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="887.304688"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="921.582031"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="990.283203"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1061.474609"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1096.289062"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1141.992188"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1246.191406"/>
     </g>
    </g>
   </g>
   <g id="patch_6">
    <path d="M 42.113906 314.62525 
L 42.113906 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_7">
    <path d="M 42.113906 314.62525 
L 311.889906 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_21">
    <g id="patch_8">
     <path d="M 200.10902 150.124824 
L 225.429957 150.124824 
Q 227.229957 150.124824 227.229957 148.324824 
L 227.229957 139.614511 
Q 227.229957 137.814511 225.429957 137.814511 
L 200.10902 137.814511 
Q 198.30902 137.814511 198.30902 139.614511 
L 198.30902 148.324824 
Q 198.30902 150.124824 200.10902 150.124824 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 3.0m -->
    <g style="fill: #ffffff" transform="translate(200.10902 146.453105) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-33" d="M 2981 2516 
Q 3453 2394 3698 2092 
Q 3944 1791 3944 1325 
Q 3944 631 3412 270 
Q 2881 -91 1863 -91 
Q 1503 -91 1142 -33 
Q 781 25 428 141 
L 428 1069 
Q 766 900 1098 814 
Q 1431 728 1753 728 
Q 2231 728 2486 893 
Q 2741 1059 2741 1369 
Q 2741 1688 2480 1852 
Q 2219 2016 1709 2016 
L 1228 2016 
L 1228 2791 
L 1734 2791 
Q 2188 2791 2409 2933 
Q 2631 3075 2631 3366 
Q 2631 3634 2415 3781 
Q 2200 3928 1806 3928 
Q 1516 3928 1219 3862 
Q 922 3797 628 3669 
L 628 4550 
Q 984 4650 1334 4700 
Q 1684 4750 2022 4750 
Q 2931 4750 3382 4451 
Q 3834 4153 3834 3553 
Q 3834 3144 3618 2883 
Q 3403 2622 2981 2516 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2e" d="M 653 1209 
L 1778 1209 
L 1778 0 
L 653 0 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-30" d="M 2944 2338 
Q 2944 3213 2780 3570 
Q 2616 3928 2228 3928 
Q 1841 3928 1675 3570 
Q 1509 3213 1509 2338 
Q 1509 1453 1675 1090 
Q 1841 728 2228 728 
Q 2613 728 2778 1090 
Q 2944 1453 2944 2338 
z
M 4147 2328 
Q 4147 1169 3647 539 
Q 3147 -91 2228 -91 
Q 1306 -91 806 539 
Q 306 1169 306 2328 
Q 306 3491 806 4120 
Q 1306 4750 2228 4750 
Q 3147 4750 3647 4120 
Q 4147 3491 4147 2328 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-33"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_22">
    <g id="patch_9">
     <path d="M 223.954075 126.279769 
L 249.275012 126.279769 
Q 251.075012 126.279769 251.075012 124.479769 
L 251.075012 115.769457 
Q 251.075012 113.969457 249.275012 113.969457 
L 223.954075 113.969457 
Q 222.154075 113.969457 222.154075 115.769457 
L 222.154075 124.479769 
Q 222.154075 126.279769 223.954075 126.279769 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 5.0m -->
    <g style="fill: #ffffff" transform="translate(223.954075 122.60805) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-35" d="M 678 4666 
L 3669 4666 
L 3669 3781 
L 1638 3781 
L 1638 3059 
Q 1775 3097 1914 3117 
Q 2053 3138 2203 3138 
Q 3056 3138 3531 2711 
Q 4006 2284 4006 1522 
Q 4006 766 3489 337 
Q 2972 -91 2053 -91 
Q 1656 -91 1267 -14 
Q 878 63 494 219 
L 494 1166 
Q 875 947 1217 837 
Q 1559 728 1863 728 
Q 2300 728 2551 942 
Q 2803 1156 2803 1522 
Q 2803 1891 2551 2103 
Q 2300 2316 1863 2316 
Q 1603 2316 1309 2248 
Q 1016 2181 678 2041 
L 678 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-35"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_23">
    <g id="patch_10">
     <path d="M 259.721657 90.512187 
L 285.042595 90.512187 
Q 286.842595 90.512187 286.842595 88.712187 
L 286.842595 80.001874 
Q 286.842595 78.201874 285.042595 78.201874 
L 259.721657 78.201874 
Q 257.921657 78.201874 257.921657 80.001874 
L 257.921657 88.712187 
Q 257.921657 90.512187 259.721657 90.512187 
z
" style="opacity: 0.7; stroke: #000000; stroke-linejoin: miter"/>
    </g>
    <!-- 8.0m -->
    <g style="fill: #ffffff" transform="translate(259.721657 86.840468) scale(0.09 -0.09)">
     <defs>
      <path id="DejaVuSans-Bold-38" d="M 2228 2088 
Q 1891 2088 1709 1903 
Q 1528 1719 1528 1375 
Q 1528 1031 1709 848 
Q 1891 666 2228 666 
Q 2563 666 2741 848 
Q 2919 1031 2919 1375 
Q 2919 1722 2741 1905 
Q 2563 2088 2228 2088 
z
M 1350 2484 
Q 925 2613 709 2878 
Q 494 3144 494 3541 
Q 494 4131 934 4440 
Q 1375 4750 2228 4750 
Q 3075 4750 3515 4442 
Q 3956 4134 3956 3541 
Q 3956 3144 3739 2878 
Q 3522 2613 3097 2484 
Q 3572 2353 3814 2058 
Q 4056 1763 4056 1313 
Q 4056 619 3595 264 
Q 3134 -91 2228 -91 
Q 1319 -91 855 264 
Q 391 619 391 1313 
Q 391 1763 633 2058 
Q 875 2353 1350 2484 
z
M 1631 3419 
Q 1631 3141 1786 2991 
Q 1941 2841 2228 2841 
Q 2509 2841 2662 2991 
Q 2816 3141 2816 3419 
Q 2816 3697 2662 3845 
Q 2509 3994 2228 3994 
Q 1941 3994 1786 3844 
Q 1631 3694 1631 3419 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-38"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="107.568359"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="177.148438"/>
    </g>
   </g>
   <g id="text_24">
    <!-- Mid-Distance Cell (Neuron 14) -->
    <g transform="translate(75.364719 16.411875) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-4d" d="M 588 4666 
L 2119 4666 
L 3181 2169 
L 4250 4666 
L 5778 4666 
L 5778 0 
L 4641 0 
L 4641 3413 
L 3566 897 
L 2803 897 
L 1728 3413 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-64" d="M 2919 2988 
L 2919 4863 
L 4044 4863 
L 4044 0 
L 2919 0 
L 2919 506 
Q 2688 197 2409 53 
Q 2131 -91 1766 -91 
Q 1119 -91 703 423 
Q 288 938 288 1747 
Q 288 2556 703 3070 
Q 1119 3584 1766 3584 
Q 2128 3584 2408 3439 
Q 2688 3294 2919 2988 
z
M 2181 722 
Q 2541 722 2730 984 
Q 2919 1247 2919 1747 
Q 2919 2247 2730 2509 
Q 2541 2772 2181 2772 
Q 1825 2772 1636 2509 
Q 1447 2247 1447 1747 
Q 1447 1247 1636 984 
Q 1825 722 2181 722 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2d" d="M 347 2297 
L 2309 2297 
L 2309 1388 
L 347 1388 
L 347 2297 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-44" d="M 1791 3756 
L 1791 909 
L 2222 909 
Q 2959 909 3348 1275 
Q 3738 1641 3738 2338 
Q 3738 3031 3350 3393 
Q 2963 3756 2222 3756 
L 1791 3756 
z
M 588 4666 
L 1856 4666 
Q 2919 4666 3439 4514 
Q 3959 4363 4331 4000 
Q 4659 3684 4818 3271 
Q 4978 2859 4978 2338 
Q 4978 1809 4818 1395 
Q 4659 981 4331 666 
Q 3956 303 3431 151 
Q 2906 0 1856 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-63" d="M 3366 3391 
L 3366 2478 
Q 3138 2634 2908 2709 
Q 2678 2784 2431 2784 
Q 1963 2784 1702 2511 
Q 1441 2238 1441 1747 
Q 1441 1256 1702 982 
Q 1963 709 2431 709 
Q 2694 709 2930 787 
Q 3166 866 3366 1019 
L 3366 103 
Q 3103 6 2833 -42 
Q 2563 -91 2291 -91 
Q 1344 -91 809 395 
Q 275 881 275 1747 
Q 275 2613 809 3098 
Q 1344 3584 2291 3584 
Q 2566 3584 2833 3536 
Q 3100 3488 3366 3391 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-43" d="M 4288 256 
Q 3956 84 3597 -3 
Q 3238 -91 2847 -91 
Q 1681 -91 1000 561 
Q 319 1213 319 2328 
Q 319 3447 1000 4098 
Q 1681 4750 2847 4750 
Q 3238 4750 3597 4662 
Q 3956 4575 4288 4403 
L 4288 3438 
Q 3953 3666 3628 3772 
Q 3303 3878 2944 3878 
Q 2300 3878 1931 3465 
Q 1563 3053 1563 2328 
Q 1563 1606 1931 1193 
Q 2300 781 2944 781 
Q 3303 781 3628 887 
Q 3953 994 4288 1222 
L 4288 256 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-4e" d="M 588 4666 
L 1931 4666 
L 3628 1466 
L 3628 4666 
L 4769 4666 
L 4769 0 
L 3425 0 
L 1728 3200 
L 1728 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-75" d="M 500 1363 
L 500 3500 
L 1625 3500 
L 1625 3150 
Q 1625 2866 1622 2436 
Q 1619 2006 1619 1863 
Q 1619 1441 1641 1255 
Q 1663 1069 1716 984 
Q 1784 875 1895 815 
Q 2006 756 2150 756 
Q 2500 756 2700 1025 
Q 2900 1294 2900 1772 
L 2900 3500 
L 4019 3500 
L 4019 0 
L 2900 0 
L 2900 506 
Q 2647 200 2364 54 
Q 2081 -91 1741 -91 
Q 1134 -91 817 281 
Q 500 653 500 1363 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-72" d="M 3138 2547 
Q 2991 2616 2845 2648 
Q 2700 2681 2553 2681 
Q 2122 2681 1889 2404 
Q 1656 2128 1656 1613 
L 1656 0 
L 538 0 
L 538 3500 
L 1656 3500 
L 1656 2925 
Q 1872 3269 2151 3426 
Q 2431 3584 2822 3584 
Q 2878 3584 2943 3579 
Q 3009 3575 3134 3559 
L 3138 2547 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-31" d="M 750 831 
L 1813 831 
L 1813 3847 
L 722 3622 
L 722 4441 
L 1806 4666 
L 2950 4666 
L 2950 831 
L 4013 831 
L 4013 0 
L 750 0 
L 750 831 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-34" d="M 2356 3675 
L 1038 1722 
L 2356 1722 
L 2356 3675 
z
M 2156 4666 
L 3494 4666 
L 3494 1722 
L 4159 1722 
L 4159 850 
L 3494 850 
L 3494 0 
L 2356 0 
L 2356 850 
L 288 850 
L 288 1881 
L 2156 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-4d"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="99.511719"/>
     <use xlink:href="#DejaVuSans-Bold-64" x="133.789062"/>
     <use xlink:href="#DejaVuSans-Bold-2d" x="205.371094"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="246.875"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="329.882812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="364.160156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="423.681641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.484375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="538.964844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="610.15625"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="669.433594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="737.255859"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="772.070312"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="845.458984"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="913.28125"/>
     <use xlink:href="#DejaVuSans-Bold-6c" x="947.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="981.835938"/>
     <use xlink:href="#DejaVuSans-Bold-28" x="1016.650391"/>
     <use xlink:href="#DejaVuSans-Bold-4e" x="1062.353516"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1146.044922"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1213.867188"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1285.058594"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="1334.375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="1403.076172"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1474.267578"/>
     <use xlink:href="#DejaVuSans-Bold-31" x="1509.082031"/>
     <use xlink:href="#DejaVuSans-Bold-34" x="1578.662109"/>
     <use xlink:href="#DejaVuSans-Bold-29" x="1648.242188"/>
    </g>
    <!-- 2D Activation Map -->
    <g transform="translate(114.950656 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-32" d="M 1844 884 
L 3897 884 
L 3897 0 
L 506 0 
L 506 884 
L 2209 2388 
Q 2438 2594 2547 2791 
Q 2656 2988 2656 3200 
Q 2656 3528 2436 3728 
Q 2216 3928 1850 3928 
Q 1569 3928 1234 3808 
Q 900 3688 519 3450 
L 519 4475 
Q 925 4609 1322 4679 
Q 1719 4750 2100 4750 
Q 2938 4750 3402 4381 
Q 3866 4013 3866 3353 
Q 3866 2972 3669 2642 
Q 3472 2313 2841 1759 
L 1844 884 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-41" d="M 3419 850 
L 1538 850 
L 1241 0 
L 31 0 
L 1759 4666 
L 3194 4666 
L 4922 0 
L 3713 0 
L 3419 850 
z
M 1838 1716 
L 3116 1716 
L 2478 3572 
L 1838 1716 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-70" d="M 1656 506 
L 1656 -1331 
L 538 -1331 
L 538 3500 
L 1656 3500 
L 1656 2988 
Q 1888 3294 2169 3439 
Q 2450 3584 2816 3584 
Q 3463 3584 3878 3070 
Q 4294 2556 4294 1747 
Q 4294 938 3878 423 
Q 3463 -91 2816 -91 
Q 2450 -91 2169 54 
Q 1888 200 1656 506 
z
M 2400 2772 
Q 2041 2772 1848 2508 
Q 1656 2244 1656 1747 
Q 1656 1250 1848 986 
Q 2041 722 2400 722 
Q 2759 722 2948 984 
Q 3138 1247 3138 1747 
Q 3138 2247 2948 2509 
Q 2759 2772 2400 2772 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-32"/>
     <use xlink:href="#DejaVuSans-Bold-44" x="69.580078"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="152.587891"/>
     <use xlink:href="#DejaVuSans-Bold-41" x="187.402344"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="264.794922"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="324.072266"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="371.875"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="406.152344"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="471.337891"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="538.818359"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="586.621094"/>
     <use xlink:href="#DejaVuSans-Bold-6f" x="620.898438"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="689.599609"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="760.791016"/>
     <use xlink:href="#DejaVuSans-Bold-4d" x="795.605469"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="895.117188"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="962.597656"/>
    </g>
   </g>
   <g id="PathCollection_1">
    <defs>
     <path id="m808ce9ca0d" d="M 0 -5.477226 
L -1.229714 -1.692556 
L -5.209151 -1.692556 
L -1.989719 0.646499 
L -3.219432 4.431169 
L -0 2.092114 
L 3.219432 4.431169 
L 1.989719 0.646499 
L 5.209151 -1.692556 
L 1.229714 -1.692556 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#p7a841bf3ed)">
     <use xlink:href="#m808ce9ca0d" x="177.001906" y="179.73725" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_1">
    <g id="patch_11">
     <path d="M 242.388656 62.99175 
L 307.889906 62.99175 
L 307.889906 48.84925 
L 242.388656 48.84925 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="PathCollection_2">
     <g>
      <use xlink:href="#m808ce9ca0d" x="253.588656" y="56.028" style="fill: #00ffff; stroke: #000000; stroke-width: 2"/>
     </g>
    </g>
    <g id="text_25">
     <!-- Observer -->
     <g transform="translate(267.988656 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-4f" d="M 2522 4238 
Q 1834 4238 1429 3725 
Q 1025 3213 1025 2328 
Q 1025 1447 1429 934 
Q 1834 422 2522 422 
Q 3209 422 3611 934 
Q 4013 1447 4013 2328 
Q 4013 3213 3611 3725 
Q 3209 4238 2522 4238 
z
M 2522 4750 
Q 3503 4750 4090 4092 
Q 4678 3434 4678 2328 
Q 4678 1225 4090 567 
Q 3503 -91 2522 -91 
Q 1538 -91 948 565 
Q 359 1222 359 2328 
Q 359 3434 948 4092 
Q 1538 4750 2522 4750 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-62" d="M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
M 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2969 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-73" d="M 2834 3397 
L 2834 2853 
Q 2591 2978 2328 3040 
Q 2066 3103 1784 3103 
Q 1356 3103 1142 2972 
Q 928 2841 928 2578 
Q 928 2378 1081 2264 
Q 1234 2150 1697 2047 
L 1894 2003 
Q 2506 1872 2764 1633 
Q 3022 1394 3022 966 
Q 3022 478 2636 193 
Q 2250 -91 1575 -91 
Q 1294 -91 989 -36 
Q 684 19 347 128 
L 347 722 
Q 666 556 975 473 
Q 1284 391 1588 391 
Q 1994 391 2212 530 
Q 2431 669 2431 922 
Q 2431 1156 2273 1281 
Q 2116 1406 1581 1522 
L 1381 1569 
Q 847 1681 609 1914 
Q 372 2147 372 2553 
Q 372 3047 722 3315 
Q 1072 3584 1716 3584 
Q 2034 3584 2315 3537 
Q 2597 3491 2834 3397 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-65" d="M 3597 1894 
L 3597 1613 
L 953 1613 
Q 991 1019 1311 708 
Q 1631 397 2203 397 
Q 2534 397 2845 478 
Q 3156 559 3463 722 
L 3463 178 
Q 3153 47 2828 -22 
Q 2503 -91 2169 -91 
Q 1331 -91 842 396 
Q 353 884 353 1716 
Q 353 2575 817 3079 
Q 1281 3584 2069 3584 
Q 2775 3584 3186 3129 
Q 3597 2675 3597 1894 
z
M 3022 2063 
Q 3016 2534 2758 2815 
Q 2500 3097 2075 3097 
Q 1594 3097 1305 2825 
Q 1016 2553 972 2059 
L 3022 2063 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-72" d="M 2631 2963 
Q 2534 3019 2420 3045 
Q 2306 3072 2169 3072 
Q 1681 3072 1420 2755 
Q 1159 2438 1159 1844 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1341 3275 1631 3429 
Q 1922 3584 2338 3584 
Q 2397 3584 2469 3576 
Q 2541 3569 2628 3553 
L 2631 2963 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-76" d="M 191 3500 
L 800 3500 
L 1894 563 
L 2988 3500 
L 3597 3500 
L 2284 0 
L 1503 0 
L 191 3500 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-4f"/>
      <use xlink:href="#DejaVuSans-62" x="78.710938"/>
      <use xlink:href="#DejaVuSans-73" x="142.1875"/>
      <use xlink:href="#DejaVuSans-65" x="194.287109"/>
      <use xlink:href="#DejaVuSans-72" x="255.810547"/>
      <use xlink:href="#DejaVuSans-76" x="296.923828"/>
      <use xlink:href="#DejaVuSans-65" x="356.103516"/>
      <use xlink:href="#DejaVuSans-72" x="417.626953"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_2">
   <g id="patch_12">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
L 813.937288 44.84925 
L 436.259015 44.84925 
z
" style="fill: #ffffff"/>
   </g>
   <g id="PolyCollection_1">
    <defs>
     <path id="mb00367e096" d="M 440.97851 -37.642188 
L 440.97851 -37.642188 
L 450.4175 -53.529096 
L 459.85649 -51.588382 
L 469.29548 -51.719086 
L 478.73447 -47.913184 
L 488.17346 -52.180988 
L 497.61245 -58.550231 
L 507.051441 -69.540587 
L 516.490431 -99.421686 
L 525.929421 -87.446135 
L 535.368411 -104.736346 
L 544.807401 -100.366518 
L 554.246391 -123.622219 
L 563.685381 -147.46995 
L 573.124371 -127.060999 
L 582.563361 -134.917651 
L 592.002351 -158.927292 
L 601.441341 -156.345317 
L 610.880331 -174.875568 
L 620.319321 -271.795553 
L 629.758311 -287.788846 
L 639.197301 -294.571711 
L 648.636291 -179.730593 
L 658.075281 -189.996318 
L 667.514271 -103.499133 
L 676.953261 -125.37833 
L 686.392251 -163.482496 
L 695.831241 -81.12761 
L 705.270231 -144.606718 
L 714.709221 -111.883323 
L 714.709221 -37.642188 
L 714.709221 -37.642188 
L 705.270231 -37.642188 
L 695.831241 -37.642188 
L 686.392251 -37.642188 
L 676.953261 -37.642188 
L 667.514271 -37.642188 
L 658.075281 -37.642188 
L 648.636291 -37.642188 
L 639.197301 -37.642188 
L 629.758311 -37.642188 
L 620.319321 -37.642188 
L 610.880331 -37.642188 
L 601.441341 -37.642188 
L 592.002351 -37.642188 
L 582.563361 -37.642188 
L 573.124371 -37.642188 
L 563.685381 -37.642188 
L 554.246391 -37.642188 
L 544.807401 -37.642188 
L 535.368411 -37.642188 
L 525.929421 -37.642188 
L 516.490431 -37.642188 
L 507.051441 -37.642188 
L 497.61245 -37.642188 
L 488.17346 -37.642188 
L 478.73447 -37.642188 
L 469.29548 -37.642188 
L 459.85649 -37.642188 
L 450.4175 -37.642188 
L 440.97851 -37.642188 
z
" style="stroke: #1f78b4; stroke-opacity: 0.3"/>
    </defs>
    <g clip-path="url(#pde26437bce)">
     <use xlink:href="#mb00367e096" x="0" y="352.267438" style="fill: #1f78b4; fill-opacity: 0.3; stroke: #1f78b4; stroke-opacity: 0.3"/>
    </g>
   </g>
   <g id="matplotlib.axis_3">
    <g id="xtick_10">
     <g id="line2d_37">
      <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" clip-path="url(#pde26437bce)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_38">
      <g>
       <use xlink:href="#m48f467c95e" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_26">
      <!-- 0 -->
      <g transform="translate(433.39589 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
      </g>
     </g>
    </g>
    <g id="xtick_11">
     <g id="line2d_39">
      <path d="M 483.468799 314.62525 
L 483.468799 44.84925 
" clip-path="url(#pde26437bce)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_40">
      <g>
       <use xlink:href="#m48f467c95e" x="483.468799" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_27">
      <!-- 1 -->
      <g transform="translate(480.605674 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-31" d="M 794 531 
L 1825 531 
L 1825 4091 
L 703 3866 
L 703 4441 
L 1819 4666 
L 2450 4666 
L 2450 531 
L 3481 531 
L 3481 0 
L 794 0 
L 794 531 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-31"/>
      </g>
     </g>
    </g>
    <g id="xtick_12">
     <g id="line2d_41">
      <path d="M 530.678584 314.62525 
L 530.678584 44.84925 
" clip-path="url(#pde26437bce)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_42">
      <g>
       <use xlink:href="#m48f467c95e" x="530.678584" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_28">
      <!-- 2 -->
      <g transform="translate(527.815459 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-32"/>
      </g>
     </g>
    </g>
    <g id="xtick_13">
     <g id="line2d_43">
      <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#pde26437bce)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_44">
      <g>
       <use xlink:href="#m48f467c95e" x="577.888368" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_29">
      <!-- 3 -->
      <g transform="translate(575.025243 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-33" d="M 2597 2516 
Q 3050 2419 3304 2112 
Q 3559 1806 3559 1356 
Q 3559 666 3084 287 
Q 2609 -91 1734 -91 
Q 1441 -91 1130 -33 
Q 819 25 488 141 
L 488 750 
Q 750 597 1062 519 
Q 1375 441 1716 441 
Q 2309 441 2620 675 
Q 2931 909 2931 1356 
Q 2931 1769 2642 2001 
Q 2353 2234 1838 2234 
L 1294 2234 
L 1294 2753 
L 1863 2753 
Q 2328 2753 2575 2939 
Q 2822 3125 2822 3475 
Q 2822 3834 2567 4026 
Q 2313 4219 1838 4219 
Q 1578 4219 1281 4162 
Q 984 4106 628 3988 
L 628 4550 
Q 988 4650 1302 4700 
Q 1616 4750 1894 4750 
Q 2613 4750 3031 4423 
Q 3450 4097 3450 3541 
Q 3450 3153 3228 2886 
Q 3006 2619 2597 2516 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-33"/>
      </g>
     </g>
    </g>
    <g id="xtick_14">
     <g id="line2d_45">
      <path d="M 625.098152 314.62525 
L 625.098152 44.84925 
" clip-path="url(#pde26437bce)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_46">
      <g>
       <use xlink:href="#m48f467c95e" x="625.098152" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_30">
      <!-- 4 -->
      <g transform="translate(622.235027 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-34"/>
      </g>
     </g>
    </g>
    <g id="xtick_15">
     <g id="line2d_47">
      <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#pde26437bce)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_48">
      <g>
       <use xlink:href="#m48f467c95e" x="672.307936" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_31">
      <!-- 5 -->
      <g transform="translate(669.444811 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-35" d="M 691 4666 
L 3169 4666 
L 3169 4134 
L 1269 4134 
L 1269 2991 
Q 1406 3038 1543 3061 
Q 1681 3084 1819 3084 
Q 2600 3084 3056 2656 
Q 3513 2228 3513 1497 
Q 3513 744 3044 326 
Q 2575 -91 1722 -91 
Q 1428 -91 1123 -41 
Q 819 9 494 109 
L 494 744 
Q 775 591 1075 516 
Q 1375 441 1709 441 
Q 2250 441 2565 725 
Q 2881 1009 2881 1497 
Q 2881 1984 2565 2268 
Q 2250 2553 1709 2553 
Q 1456 2553 1204 2497 
Q 953 2441 691 2322 
L 691 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-35"/>
      </g>
     </g>
    </g>
    <g id="xtick_16">
     <g id="line2d_49">
      <path d="M 719.51772 314.62525 
L 719.51772 44.84925 
" clip-path="url(#pde26437bce)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_50">
      <g>
       <use xlink:href="#m48f467c95e" x="719.51772" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_32">
      <!-- 6 -->
      <g transform="translate(716.654595 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-36"/>
      </g>
     </g>
    </g>
    <g id="xtick_17">
     <g id="line2d_51">
      <path d="M 766.727504 314.62525 
L 766.727504 44.84925 
" clip-path="url(#pde26437bce)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_52">
      <g>
       <use xlink:href="#m48f467c95e" x="766.727504" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_33">
      <!-- 7 -->
      <g transform="translate(763.864379 328.463844) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-37" d="M 525 4666 
L 3525 4666 
L 3525 4397 
L 1831 0 
L 1172 0 
L 2766 4134 
L 525 4134 
L 525 4666 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-37"/>
      </g>
     </g>
    </g>
    <g id="xtick_18">
     <g id="line2d_53">
      <path d="M 813.937288 314.62525 
L 813.937288 44.84925 
" clip-path="url(#pde26437bce)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_54">
      <g>
       <use xlink:href="#m48f467c95e" x="813.937288" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_34">
      <!-- 8 -->
      <g transform="translate(811.074163 328.463844) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-38"/>
      </g>
     </g>
    </g>
    <g id="text_35">
     <!-- Inter-Agent Distance (m) -->
     <g transform="translate(547.755261 342.693844) scale(0.11 -0.11)">
      <defs>
       <path id="DejaVuSans-Bold-49" d="M 588 4666 
L 1791 4666 
L 1791 0 
L 588 0 
L 588 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-Bold-67" d="M 2919 594 
Q 2688 288 2409 144 
Q 2131 0 1766 0 
Q 1125 0 706 504 
Q 288 1009 288 1791 
Q 288 2575 706 3076 
Q 1125 3578 1766 3578 
Q 2131 3578 2409 3434 
Q 2688 3291 2919 2981 
L 2919 3500 
L 4044 3500 
L 4044 353 
Q 4044 -491 3511 -936 
Q 2978 -1381 1966 -1381 
Q 1638 -1381 1331 -1331 
Q 1025 -1281 716 -1178 
L 716 -306 
Q 1009 -475 1290 -558 
Q 1572 -641 1856 -641 
Q 2406 -641 2662 -400 
Q 2919 -159 2919 353 
L 2919 594 
z
M 2181 2772 
Q 1834 2772 1640 2515 
Q 1447 2259 1447 1791 
Q 1447 1309 1634 1061 
Q 1822 813 2181 813 
Q 2531 813 2725 1069 
Q 2919 1325 2919 1791 
Q 2919 2259 2725 2515 
Q 2531 2772 2181 2772 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-Bold-49"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="37.207031"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="108.398438"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="156.201172"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="224.023438"/>
      <use xlink:href="#DejaVuSans-Bold-2d" x="273.339844"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="314.84375"/>
      <use xlink:href="#DejaVuSans-Bold-67" x="392.236328"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="463.818359"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="531.640625"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="602.832031"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="650.634766"/>
      <use xlink:href="#DejaVuSans-Bold-44" x="685.449219"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="768.457031"/>
      <use xlink:href="#DejaVuSans-Bold-73" x="802.734375"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="862.255859"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="910.058594"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="977.539062"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="1048.730469"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="1108.007812"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="1175.830078"/>
      <use xlink:href="#DejaVuSans-Bold-28" x="1210.644531"/>
      <use xlink:href="#DejaVuSans-Bold-6d" x="1256.347656"/>
      <use xlink:href="#DejaVuSans-Bold-29" x="1360.546875"/>
     </g>
    </g>
   </g>
   <g id="matplotlib.axis_4">
    <g id="ytick_10">
     <g id="line2d_55">
      <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" clip-path="url(#pde26437bce)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_56">
      <g>
       <use xlink:href="#m3a3388ed00" x="436.259015" y="314.62525" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_36">
      <!-- 0.0 -->
      <g transform="translate(414.946203 318.044547) scale(0.09 -0.09)">
       <defs>
        <path id="DejaVuSans-2e" d="M 684 794 
L 1344 794 
L 1344 0 
L 684 0 
L 684 794 
z
" transform="scale(0.015625)"/>
       </defs>
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_11">
     <g id="line2d_57">
      <path d="M 436.259015 268.347437 
L 813.937288 268.347437 
" clip-path="url(#pde26437bce)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_58">
      <g>
       <use xlink:href="#m3a3388ed00" x="436.259015" y="268.347437" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_37">
      <!-- 0.2 -->
      <g transform="translate(414.946203 271.766734) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_12">
     <g id="line2d_59">
      <path d="M 436.259015 222.069625 
L 813.937288 222.069625 
" clip-path="url(#pde26437bce)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_60">
      <g>
       <use xlink:href="#m3a3388ed00" x="436.259015" y="222.069625" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_38">
      <!-- 0.4 -->
      <g transform="translate(414.946203 225.488922) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_13">
     <g id="line2d_61">
      <path d="M 436.259015 175.791812 
L 813.937288 175.791812 
" clip-path="url(#pde26437bce)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_62">
      <g>
       <use xlink:href="#m3a3388ed00" x="436.259015" y="175.791812" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_39">
      <!-- 0.6 -->
      <g transform="translate(414.946203 179.211109) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_14">
     <g id="line2d_63">
      <path d="M 436.259015 129.514 
L 813.937288 129.514 
" clip-path="url(#pde26437bce)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_64">
      <g>
       <use xlink:href="#m3a3388ed00" x="436.259015" y="129.514" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_40">
      <!-- 0.8 -->
      <g transform="translate(414.946203 132.933297) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-38" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_15">
     <g id="line2d_65">
      <path d="M 436.259015 83.236187 
L 813.937288 83.236187 
" clip-path="url(#pde26437bce)" style="fill: none; stroke-dasharray: 0.8,1.32; stroke-dashoffset: 0; stroke: #b0b0b0; stroke-opacity: 0.5; stroke-width: 0.8"/>
     </g>
     <g id="line2d_66">
      <g>
       <use xlink:href="#m3a3388ed00" x="436.259015" y="83.236187" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_41">
      <!-- 1.0 -->
      <g transform="translate(414.946203 86.655484) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_42">
     <!-- Neural Activation -->
     <g transform="translate(408.658547 233.746391) rotate(-90) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="line2d_67">
    <path d="M 440.97851 314.62525 
L 450.4175 298.738341 
L 459.85649 300.679055 
L 469.29548 300.548352 
L 478.73447 304.354253 
L 488.17346 300.086449 
L 497.61245 293.717207 
L 507.051441 282.72685 
L 516.490431 252.845752 
L 525.929421 264.821302 
L 535.368411 247.531092 
L 544.807401 251.90092 
L 554.246391 228.645218 
L 563.685381 204.797488 
L 573.124371 225.206438 
L 582.563361 217.349786 
L 592.002351 193.340146 
L 601.441341 195.92212 
L 610.880331 177.39187 
L 620.319321 80.471884 
L 629.758311 64.478592 
L 639.197301 57.695726 
L 648.636291 172.536845 
L 658.075281 162.27112 
L 667.514271 248.768305 
L 676.953261 226.889107 
L 686.392251 188.784942 
L 695.831241 271.139827 
L 705.270231 207.66072 
L 714.709221 240.384114 
" clip-path="url(#pde26437bce)" style="fill: none; stroke: #1f78b4; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
   </g>
   <g id="line2d_68">
    <path d="M 639.197301 314.62525 
L 639.197301 44.84925 
" clip-path="url(#pde26437bce)" style="fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #1f78b4; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_69">
    <path d="M 577.888368 314.62525 
L 577.888368 44.84925 
" clip-path="url(#pde26437bce)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="line2d_70">
    <path d="M 672.307936 314.62525 
L 672.307936 44.84925 
" clip-path="url(#pde26437bce)" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
   </g>
   <g id="patch_13">
    <path d="M 436.259015 314.62525 
L 436.259015 44.84925 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="patch_14">
    <path d="M 436.259015 314.62525 
L 813.937288 314.62525 
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
   <g id="text_43">
    <g id="patch_15">
     <path d="M 700.612124 114.0718 
L 795.053374 114.0718 
Q 798.253374 114.0718 798.253374 110.8718 
L 798.253374 58.33805 
Q 798.253374 55.13805 795.053374 55.13805 
L 700.612124 55.13805 
Q 697.412124 55.13805 697.412124 58.33805 
L 697.412124 110.8718 
Q 697.412124 114.0718 700.612124 114.0718 
z
" style="fill: #ffffff; opacity: 0.9; stroke: #808080; stroke-linejoin: miter"/>
    </g>
    <!-- Peak Distance: 4.3m -->
    <g transform="translate(712.787124 64.4168) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-50" d="M 1259 4147 
L 1259 2394 
L 2053 2394 
Q 2494 2394 2734 2622 
Q 2975 2850 2975 3272 
Q 2975 3691 2734 3919 
Q 2494 4147 2053 4147 
L 1259 4147 
z
M 628 4666 
L 2053 4666 
Q 2838 4666 3239 4311 
Q 3641 3956 3641 3272 
Q 3641 2581 3239 2228 
Q 2838 1875 2053 1875 
L 1259 1875 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-61" d="M 2194 1759 
Q 1497 1759 1228 1600 
Q 959 1441 959 1056 
Q 959 750 1161 570 
Q 1363 391 1709 391 
Q 2188 391 2477 730 
Q 2766 1069 2766 1631 
L 2766 1759 
L 2194 1759 
z
M 3341 1997 
L 3341 0 
L 2766 0 
L 2766 531 
Q 2569 213 2275 61 
Q 1981 -91 1556 -91 
Q 1019 -91 701 211 
Q 384 513 384 1019 
Q 384 1609 779 1909 
Q 1175 2209 1959 2209 
L 2766 2209 
L 2766 2266 
Q 2766 2663 2505 2880 
Q 2244 3097 1772 3097 
Q 1472 3097 1187 3025 
Q 903 2953 641 2809 
L 641 3341 
Q 956 3463 1253 3523 
Q 1550 3584 1831 3584 
Q 2591 3584 2966 3190 
Q 3341 2797 3341 1997 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6b" d="M 581 4863 
L 1159 4863 
L 1159 1991 
L 2875 3500 
L 3609 3500 
L 1753 1863 
L 3688 0 
L 2938 0 
L 1159 1709 
L 1159 0 
L 581 0 
L 581 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-20" transform="scale(0.015625)"/>
      <path id="DejaVuSans-44" d="M 1259 4147 
L 1259 519 
L 2022 519 
Q 2988 519 3436 956 
Q 3884 1394 3884 2338 
Q 3884 3275 3436 3711 
Q 2988 4147 2022 4147 
L 1259 4147 
z
M 628 4666 
L 1925 4666 
Q 3281 4666 3915 4102 
Q 4550 3538 4550 2338 
Q 4550 1131 3912 565 
Q 3275 0 1925 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-69" d="M 603 3500 
L 1178 3500 
L 1178 0 
L 603 0 
L 603 3500 
z
M 603 4863 
L 1178 4863 
L 1178 4134 
L 603 4134 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-74" d="M 1172 4494 
L 1172 3500 
L 2356 3500 
L 2356 3053 
L 1172 3053 
L 1172 1153 
Q 1172 725 1289 603 
Q 1406 481 1766 481 
L 2356 481 
L 2356 0 
L 1766 0 
Q 1100 0 847 248 
Q 594 497 594 1153 
L 594 3053 
L 172 3053 
L 172 3500 
L 594 3500 
L 594 4494 
L 1172 4494 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6e" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-63" d="M 3122 3366 
L 3122 2828 
Q 2878 2963 2633 3030 
Q 2388 3097 2138 3097 
Q 1578 3097 1268 2742 
Q 959 2388 959 1747 
Q 959 1106 1268 751 
Q 1578 397 2138 397 
Q 2388 397 2633 464 
Q 2878 531 3122 666 
L 3122 134 
Q 2881 22 2623 -34 
Q 2366 -91 2075 -91 
Q 1284 -91 818 406 
Q 353 903 353 1747 
Q 353 2603 823 3093 
Q 1294 3584 2113 3584 
Q 2378 3584 2631 3529 
Q 2884 3475 3122 3366 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-3a" d="M 750 794 
L 1409 794 
L 1409 0 
L 750 0 
L 750 794 
z
M 750 3309 
L 1409 3309 
L 1409 2516 
L 750 2516 
L 750 3309 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6d" d="M 3328 2828 
Q 3544 3216 3844 3400 
Q 4144 3584 4550 3584 
Q 5097 3584 5394 3201 
Q 5691 2819 5691 2113 
L 5691 0 
L 5113 0 
L 5113 2094 
Q 5113 2597 4934 2840 
Q 4756 3084 4391 3084 
Q 3944 3084 3684 2787 
Q 3425 2491 3425 1978 
L 3425 0 
L 2847 0 
L 2847 2094 
Q 2847 2600 2669 2842 
Q 2491 3084 2119 3084 
Q 1678 3084 1418 2786 
Q 1159 2488 1159 1978 
L 1159 0 
L 581 0 
L 581 3500 
L 1159 3500 
L 1159 2956 
Q 1356 3278 1631 3431 
Q 1906 3584 2284 3584 
Q 2666 3584 2933 3390 
Q 3200 3197 3328 2828 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-44" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="346.179688"/>
     <use xlink:href="#DejaVuSans-73" x="373.962891"/>
     <use xlink:href="#DejaVuSans-74" x="426.0625"/>
     <use xlink:href="#DejaVuSans-61" x="465.271484"/>
     <use xlink:href="#DejaVuSans-6e" x="526.550781"/>
     <use xlink:href="#DejaVuSans-63" x="589.929688"/>
     <use xlink:href="#DejaVuSans-65" x="644.910156"/>
     <use xlink:href="#DejaVuSans-3a" x="706.433594"/>
     <use xlink:href="#DejaVuSans-20" x="740.125"/>
     <use xlink:href="#DejaVuSans-34" x="771.912109"/>
     <use xlink:href="#DejaVuSans-2e" x="835.535156"/>
     <use xlink:href="#DejaVuSans-33" x="867.322266"/>
     <use xlink:href="#DejaVuSans-6d" x="930.945312"/>
    </g>
    <!-- Max Activation: 1.110 -->
    <g transform="translate(707.779624 73.37505) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-4d" d="M 628 4666 
L 1569 4666 
L 2759 1491 
L 3956 4666 
L 4897 4666 
L 4897 0 
L 4281 0 
L 4281 4097 
L 3078 897 
L 2444 897 
L 1241 4097 
L 1241 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-78" d="M 3513 3500 
L 2247 1797 
L 3578 0 
L 2900 0 
L 1881 1375 
L 863 0 
L 184 0 
L 1544 1831 
L 300 3500 
L 978 3500 
L 1906 2253 
L 2834 3500 
L 3513 3500 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-41" d="M 2188 4044 
L 1331 1722 
L 3047 1722 
L 2188 4044 
z
M 1831 4666 
L 2547 4666 
L 4325 0 
L 3669 0 
L 3244 1197 
L 1141 1197 
L 716 0 
L 50 0 
L 1831 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-6f" d="M 1959 3097 
Q 1497 3097 1228 2736 
Q 959 2375 959 1747 
Q 959 1119 1226 758 
Q 1494 397 1959 397 
Q 2419 397 2687 759 
Q 2956 1122 2956 1747 
Q 2956 2369 2687 2733 
Q 2419 3097 1959 3097 
z
M 1959 3584 
Q 2709 3584 3137 3096 
Q 3566 2609 3566 1747 
Q 3566 888 3137 398 
Q 2709 -91 1959 -91 
Q 1206 -91 779 398 
Q 353 888 353 1747 
Q 353 2609 779 3096 
Q 1206 3584 1959 3584 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-4d"/>
     <use xlink:href="#DejaVuSans-61" x="86.279297"/>
     <use xlink:href="#DejaVuSans-78" x="147.558594"/>
     <use xlink:href="#DejaVuSans-20" x="206.738281"/>
     <use xlink:href="#DejaVuSans-41" x="238.525391"/>
     <use xlink:href="#DejaVuSans-63" x="305.183594"/>
     <use xlink:href="#DejaVuSans-74" x="360.164062"/>
     <use xlink:href="#DejaVuSans-69" x="399.373047"/>
     <use xlink:href="#DejaVuSans-76" x="427.15625"/>
     <use xlink:href="#DejaVuSans-61" x="486.335938"/>
     <use xlink:href="#DejaVuSans-74" x="547.615234"/>
     <use xlink:href="#DejaVuSans-69" x="586.824219"/>
     <use xlink:href="#DejaVuSans-6f" x="614.607422"/>
     <use xlink:href="#DejaVuSans-6e" x="675.789062"/>
     <use xlink:href="#DejaVuSans-3a" x="739.167969"/>
     <use xlink:href="#DejaVuSans-20" x="772.859375"/>
     <use xlink:href="#DejaVuSans-31" x="804.646484"/>
     <use xlink:href="#DejaVuSans-2e" x="868.269531"/>
     <use xlink:href="#DejaVuSans-31" x="900.056641"/>
     <use xlink:href="#DejaVuSans-31" x="963.679688"/>
     <use xlink:href="#DejaVuSans-30" x="1027.302734"/>
    </g>
    <!-- Sparsity: 0.383 -->
    <g transform="translate(734.883374 82.3333) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-53" d="M 3425 4513 
L 3425 3897 
Q 3066 4069 2747 4153 
Q 2428 4238 2131 4238 
Q 1616 4238 1336 4038 
Q 1056 3838 1056 3469 
Q 1056 3159 1242 3001 
Q 1428 2844 1947 2747 
L 2328 2669 
Q 3034 2534 3370 2195 
Q 3706 1856 3706 1288 
Q 3706 609 3251 259 
Q 2797 -91 1919 -91 
Q 1588 -91 1214 -16 
Q 841 59 441 206 
L 441 856 
Q 825 641 1194 531 
Q 1563 422 1919 422 
Q 2459 422 2753 634 
Q 3047 847 3047 1241 
Q 3047 1584 2836 1778 
Q 2625 1972 2144 2069 
L 1759 2144 
Q 1053 2284 737 2584 
Q 422 2884 422 3419 
Q 422 4038 858 4394 
Q 1294 4750 2059 4750 
Q 2388 4750 2728 4690 
Q 3069 4631 3425 4513 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-70" d="M 1159 525 
L 1159 -1331 
L 581 -1331 
L 581 3500 
L 1159 3500 
L 1159 2969 
Q 1341 3281 1617 3432 
Q 1894 3584 2278 3584 
Q 2916 3584 3314 3078 
Q 3713 2572 3713 1747 
Q 3713 922 3314 415 
Q 2916 -91 2278 -91 
Q 1894 -91 1617 61 
Q 1341 213 1159 525 
z
M 3116 1747 
Q 3116 2381 2855 2742 
Q 2594 3103 2138 3103 
Q 1681 3103 1420 2742 
Q 1159 2381 1159 1747 
Q 1159 1113 1420 752 
Q 1681 391 2138 391 
Q 2594 391 2855 752 
Q 3116 1113 3116 1747 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-79" d="M 2059 -325 
Q 1816 -950 1584 -1140 
Q 1353 -1331 966 -1331 
L 506 -1331 
L 506 -850 
L 844 -850 
Q 1081 -850 1212 -737 
Q 1344 -625 1503 -206 
L 1606 56 
L 191 3500 
L 800 3500 
L 1894 763 
L 2988 3500 
L 3597 3500 
L 2059 -325 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-70" x="63.476562"/>
     <use xlink:href="#DejaVuSans-61" x="126.953125"/>
     <use xlink:href="#DejaVuSans-72" x="188.232422"/>
     <use xlink:href="#DejaVuSans-73" x="229.345703"/>
     <use xlink:href="#DejaVuSans-69" x="281.445312"/>
     <use xlink:href="#DejaVuSans-74" x="309.228516"/>
     <use xlink:href="#DejaVuSans-79" x="348.4375"/>
     <use xlink:href="#DejaVuSans-3a" x="400.367188"/>
     <use xlink:href="#DejaVuSans-20" x="434.058594"/>
     <use xlink:href="#DejaVuSans-30" x="465.845703"/>
     <use xlink:href="#DejaVuSans-2e" x="529.46875"/>
     <use xlink:href="#DejaVuSans-33" x="561.255859"/>
     <use xlink:href="#DejaVuSans-38" x="624.878906"/>
     <use xlink:href="#DejaVuSans-33" x="688.501953"/>
    </g>
    <!-- Peak Significance: 3.26 -->
    <g transform="translate(702.065874 91.29155) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-67" d="M 2906 1791 
Q 2906 2416 2648 2759 
Q 2391 3103 1925 3103 
Q 1463 3103 1205 2759 
Q 947 2416 947 1791 
Q 947 1169 1205 825 
Q 1463 481 1925 481 
Q 2391 481 2648 825 
Q 2906 1169 2906 1791 
z
M 3481 434 
Q 3481 -459 3084 -895 
Q 2688 -1331 1869 -1331 
Q 1566 -1331 1297 -1286 
Q 1028 -1241 775 -1147 
L 775 -588 
Q 1028 -725 1275 -790 
Q 1522 -856 1778 -856 
Q 2344 -856 2625 -561 
Q 2906 -266 2906 331 
L 2906 616 
Q 2728 306 2450 153 
Q 2172 0 1784 0 
Q 1141 0 747 490 
Q 353 981 353 1791 
Q 353 2603 747 3093 
Q 1141 3584 1784 3584 
Q 2172 3584 2450 3431 
Q 2728 3278 2906 2969 
L 2906 3500 
L 3481 3500 
L 3481 434 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-66" d="M 2375 4863 
L 2375 4384 
L 1825 4384 
Q 1516 4384 1395 4259 
Q 1275 4134 1275 3809 
L 1275 3500 
L 2222 3500 
L 2222 3053 
L 1275 3053 
L 1275 0 
L 697 0 
L 697 3053 
L 147 3053 
L 147 3500 
L 697 3500 
L 697 3744 
Q 697 4328 969 4595 
Q 1241 4863 1831 4863 
L 2375 4863 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-53" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="332.654297"/>
     <use xlink:href="#DejaVuSans-67" x="360.4375"/>
     <use xlink:href="#DejaVuSans-6e" x="423.914062"/>
     <use xlink:href="#DejaVuSans-69" x="487.292969"/>
     <use xlink:href="#DejaVuSans-66" x="515.076172"/>
     <use xlink:href="#DejaVuSans-69" x="550.28125"/>
     <use xlink:href="#DejaVuSans-63" x="578.064453"/>
     <use xlink:href="#DejaVuSans-61" x="633.044922"/>
     <use xlink:href="#DejaVuSans-6e" x="694.324219"/>
     <use xlink:href="#DejaVuSans-63" x="757.703125"/>
     <use xlink:href="#DejaVuSans-65" x="812.683594"/>
     <use xlink:href="#DejaVuSans-3a" x="874.207031"/>
     <use xlink:href="#DejaVuSans-20" x="907.898438"/>
     <use xlink:href="#DejaVuSans-33" x="939.685547"/>
     <use xlink:href="#DejaVuSans-2e" x="1003.308594"/>
     <use xlink:href="#DejaVuSans-32" x="1035.095703"/>
     <use xlink:href="#DejaVuSans-36" x="1098.71875"/>
    </g>
    <!-- Selectivity Index: 0.408 -->
    <g transform="translate(700.612124 100.2498) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-6c" d="M 603 4863 
L 1178 4863 
L 1178 0 
L 603 0 
L 603 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-49" d="M 628 4666 
L 1259 4666 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-64" d="M 2906 2969 
L 2906 4863 
L 3481 4863 
L 3481 0 
L 2906 0 
L 2906 525 
Q 2725 213 2448 61 
Q 2172 -91 1784 -91 
Q 1150 -91 751 415 
Q 353 922 353 1747 
Q 353 2572 751 3078 
Q 1150 3584 1784 3584 
Q 2172 3584 2448 3432 
Q 2725 3281 2906 2969 
z
M 947 1747 
Q 947 1113 1208 752 
Q 1469 391 1925 391 
Q 2381 391 2643 752 
Q 2906 1113 2906 1747 
Q 2906 2381 2643 2742 
Q 2381 3103 1925 3103 
Q 1469 3103 1208 2742 
Q 947 2381 947 1747 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-53"/>
     <use xlink:href="#DejaVuSans-65" x="63.476562"/>
     <use xlink:href="#DejaVuSans-6c" x="125"/>
     <use xlink:href="#DejaVuSans-65" x="152.783203"/>
     <use xlink:href="#DejaVuSans-63" x="214.306641"/>
     <use xlink:href="#DejaVuSans-74" x="269.287109"/>
     <use xlink:href="#DejaVuSans-69" x="308.496094"/>
     <use xlink:href="#DejaVuSans-76" x="336.279297"/>
     <use xlink:href="#DejaVuSans-69" x="395.458984"/>
     <use xlink:href="#DejaVuSans-74" x="423.242188"/>
     <use xlink:href="#DejaVuSans-79" x="462.451172"/>
     <use xlink:href="#DejaVuSans-20" x="521.630859"/>
     <use xlink:href="#DejaVuSans-49" x="553.417969"/>
     <use xlink:href="#DejaVuSans-6e" x="582.910156"/>
     <use xlink:href="#DejaVuSans-64" x="646.289062"/>
     <use xlink:href="#DejaVuSans-65" x="709.765625"/>
     <use xlink:href="#DejaVuSans-78" x="769.539062"/>
     <use xlink:href="#DejaVuSans-3a" x="828.71875"/>
     <use xlink:href="#DejaVuSans-20" x="862.410156"/>
     <use xlink:href="#DejaVuSans-30" x="894.197266"/>
     <use xlink:href="#DejaVuSans-2e" x="957.820312"/>
     <use xlink:href="#DejaVuSans-34" x="989.607422"/>
     <use xlink:href="#DejaVuSans-30" x="1053.230469"/>
     <use xlink:href="#DejaVuSans-38" x="1116.853516"/>
    </g>
    <!-- Peak Width: 1.0m -->
    <g transform="translate(724.529624 109.20805) scale(0.08 -0.08)">
     <defs>
      <path id="DejaVuSans-57" d="M 213 4666 
L 850 4666 
L 1831 722 
L 2809 4666 
L 3519 4666 
L 4500 722 
L 5478 4666 
L 6119 4666 
L 4947 0 
L 4153 0 
L 3169 4050 
L 2175 0 
L 1381 0 
L 213 4666 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-68" d="M 3513 2113 
L 3513 0 
L 2938 0 
L 2938 2094 
Q 2938 2591 2744 2837 
Q 2550 3084 2163 3084 
Q 1697 3084 1428 2787 
Q 1159 2491 1159 1978 
L 1159 0 
L 581 0 
L 581 4863 
L 1159 4863 
L 1159 2956 
Q 1366 3272 1645 3428 
Q 1925 3584 2291 3584 
Q 2894 3584 3203 3211 
Q 3513 2838 3513 2113 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-50"/>
     <use xlink:href="#DejaVuSans-65" x="56.677734"/>
     <use xlink:href="#DejaVuSans-61" x="118.201172"/>
     <use xlink:href="#DejaVuSans-6b" x="179.480469"/>
     <use xlink:href="#DejaVuSans-20" x="237.390625"/>
     <use xlink:href="#DejaVuSans-57" x="269.177734"/>
     <use xlink:href="#DejaVuSans-69" x="365.804688"/>
     <use xlink:href="#DejaVuSans-64" x="393.587891"/>
     <use xlink:href="#DejaVuSans-74" x="457.064453"/>
     <use xlink:href="#DejaVuSans-68" x="496.273438"/>
     <use xlink:href="#DejaVuSans-3a" x="559.652344"/>
     <use xlink:href="#DejaVuSans-20" x="593.34375"/>
     <use xlink:href="#DejaVuSans-31" x="625.130859"/>
     <use xlink:href="#DejaVuSans-2e" x="688.753906"/>
     <use xlink:href="#DejaVuSans-30" x="720.541016"/>
     <use xlink:href="#DejaVuSans-6d" x="784.164062"/>
    </g>
   </g>
   <g id="text_44">
    <!-- Distance Tuning Curve -->
    <g transform="translate(549.284402 16.318125) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-54" d="M 31 4666 
L 4331 4666 
L 4331 3756 
L 2784 3756 
L 2784 0 
L 1581 0 
L 1581 3756 
L 31 3756 
L 31 4666 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-44"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="83.007812"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="117.285156"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="176.806641"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="224.609375"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="292.089844"/>
     <use xlink:href="#DejaVuSans-Bold-63" x="363.28125"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="422.558594"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="490.380859"/>
     <use xlink:href="#DejaVuSans-Bold-54" x="525.195312"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="582.408203"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="653.599609"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="724.791016"/>
     <use xlink:href="#DejaVuSans-Bold-6e" x="759.068359"/>
     <use xlink:href="#DejaVuSans-Bold-67" x="830.259766"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="901.841797"/>
     <use xlink:href="#DejaVuSans-Bold-43" x="936.65625"/>
     <use xlink:href="#DejaVuSans-Bold-75" x="1010.044922"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="1081.236328"/>
     <use xlink:href="#DejaVuSans-Bold-76" x="1130.552734"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="1195.738281"/>
    </g>
    <!-- Peak: 4.3m, Sparsity: 0.383 -->
    <g transform="translate(531.359402 29.84925) scale(0.12 -0.12)">
     <defs>
      <path id="DejaVuSans-Bold-6b" d="M 538 4863 
L 1656 4863 
L 1656 2216 
L 2944 3500 
L 4244 3500 
L 2534 1894 
L 4378 0 
L 3022 0 
L 1656 1459 
L 1656 0 
L 538 0 
L 538 4863 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-3a" d="M 716 3500 
L 1844 3500 
L 1844 2291 
L 716 2291 
L 716 3500 
z
M 716 1209 
L 1844 1209 
L 1844 0 
L 716 0 
L 716 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-2c" d="M 653 1209 
L 1778 1209 
L 1778 256 
L 1006 -909 
L 341 -909 
L 653 256 
L 653 1209 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-53" d="M 3834 4519 
L 3834 3531 
Q 3450 3703 3084 3790 
Q 2719 3878 2394 3878 
Q 1963 3878 1756 3759 
Q 1550 3641 1550 3391 
Q 1550 3203 1689 3098 
Q 1828 2994 2194 2919 
L 2706 2816 
Q 3484 2659 3812 2340 
Q 4141 2022 4141 1434 
Q 4141 663 3683 286 
Q 3225 -91 2284 -91 
Q 1841 -91 1394 -6 
Q 947 78 500 244 
L 500 1259 
Q 947 1022 1364 901 
Q 1781 781 2169 781 
Q 2563 781 2772 912 
Q 2981 1044 2981 1288 
Q 2981 1506 2839 1625 
Q 2697 1744 2272 1838 
L 1806 1941 
Q 1106 2091 782 2419 
Q 459 2747 459 3303 
Q 459 4000 909 4375 
Q 1359 4750 2203 4750 
Q 2588 4750 2994 4692 
Q 3400 4634 3834 4519 
z
" transform="scale(0.015625)"/>
      <path id="DejaVuSans-Bold-79" d="M 78 3500 
L 1197 3500 
L 2138 1125 
L 2938 3500 
L 4056 3500 
L 2584 -331 
Q 2363 -916 2067 -1148 
Q 1772 -1381 1288 -1381 
L 641 -1381 
L 641 -647 
L 991 -647 
Q 1275 -647 1404 -556 
Q 1534 -466 1606 -231 
L 1638 -134 
L 78 3500 
z
" transform="scale(0.015625)"/>
     </defs>
     <use xlink:href="#DejaVuSans-Bold-50"/>
     <use xlink:href="#DejaVuSans-Bold-65" x="73.291016"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="141.113281"/>
     <use xlink:href="#DejaVuSans-Bold-6b" x="208.59375"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="275.097656"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="315.087891"/>
     <use xlink:href="#DejaVuSans-Bold-34" x="349.902344"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="419.482422"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="457.470703"/>
     <use xlink:href="#DejaVuSans-Bold-6d" x="527.050781"/>
     <use xlink:href="#DejaVuSans-Bold-2c" x="631.25"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="669.238281"/>
     <use xlink:href="#DejaVuSans-Bold-53" x="704.052734"/>
     <use xlink:href="#DejaVuSans-Bold-70" x="776.074219"/>
     <use xlink:href="#DejaVuSans-Bold-61" x="847.65625"/>
     <use xlink:href="#DejaVuSans-Bold-72" x="915.136719"/>
     <use xlink:href="#DejaVuSans-Bold-73" x="964.453125"/>
     <use xlink:href="#DejaVuSans-Bold-69" x="1023.974609"/>
     <use xlink:href="#DejaVuSans-Bold-74" x="1058.251953"/>
     <use xlink:href="#DejaVuSans-Bold-79" x="1106.054688"/>
     <use xlink:href="#DejaVuSans-Bold-3a" x="1171.240234"/>
     <use xlink:href="#DejaVuSans-Bold-20" x="1211.230469"/>
     <use xlink:href="#DejaVuSans-Bold-30" x="1246.044922"/>
     <use xlink:href="#DejaVuSans-Bold-2e" x="1315.625"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="1353.613281"/>
     <use xlink:href="#DejaVuSans-Bold-38" x="1423.193359"/>
     <use xlink:href="#DejaVuSans-Bold-33" x="1492.773438"/>
    </g>
   </g>
   <g id="PathCollection_3">
    <defs>
     <path id="m32696eb13c" d="M 0 5 
C 1.326016 5 2.597899 4.473168 3.535534 3.535534 
C 4.473168 2.597899 5 1.326016 5 0 
C 5 -1.326016 4.473168 -2.597899 3.535534 -3.535534 
C 2.597899 -4.473168 1.326016 -5 0 -5 
C -1.326016 -5 -2.597899 -4.473168 -3.535534 -3.535534 
C -4.473168 -2.597899 -5 -1.326016 -5 0 
C -5 1.326016 -4.473168 2.597899 -3.535534 3.535534 
C -2.597899 4.473168 -1.326016 5 0 5 
z
" style="stroke: #000000; stroke-width: 2"/>
    </defs>
    <g clip-path="url(#pde26437bce)">
     <use xlink:href="#m32696eb13c" x="639.197301" y="57.695726" style="fill: #1f78b4; stroke: #000000; stroke-width: 2"/>
    </g>
   </g>
   <g id="legend_2">
    <g id="patch_16">
     <path d="M 691.429788 86.47675 
L 808.337288 86.47675 
Q 809.937288 86.47675 809.937288 84.87675 
L 809.937288 50.44925 
Q 809.937288 48.84925 808.337288 48.84925 
L 691.429788 48.84925 
Q 689.829788 48.84925 689.829788 50.44925 
L 689.829788 84.87675 
Q 689.829788 86.47675 691.429788 86.47675 
z
" style="fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter"/>
    </g>
    <g id="line2d_71">
     <path d="M 693.029788 55.328 
L 701.029788 55.328 
L 709.029788 55.328 
" style="fill: none; stroke: #1f78b4; stroke-opacity: 0.8; stroke-width: 3; stroke-linecap: square"/>
    </g>
    <g id="text_45">
     <!-- Tuning Curve -->
     <g transform="translate(715.429788 58.128) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-54" d="M -19 4666 
L 3928 4666 
L 3928 4134 
L 2272 4134 
L 2272 0 
L 1638 0 
L 1638 4134 
L -19 4134 
L -19 4666 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-75" d="M 544 1381 
L 544 3500 
L 1119 3500 
L 1119 1403 
Q 1119 906 1312 657 
Q 1506 409 1894 409 
Q 2359 409 2629 706 
Q 2900 1003 2900 1516 
L 2900 3500 
L 3475 3500 
L 3475 0 
L 2900 0 
L 2900 538 
Q 2691 219 2414 64 
Q 2138 -91 1772 -91 
Q 1169 -91 856 284 
Q 544 659 544 1381 
z
M 1991 3584 
L 1991 3584 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-43" d="M 4122 4306 
L 4122 3641 
Q 3803 3938 3442 4084 
Q 3081 4231 2675 4231 
Q 1875 4231 1450 3742 
Q 1025 3253 1025 2328 
Q 1025 1406 1450 917 
Q 1875 428 2675 428 
Q 3081 428 3442 575 
Q 3803 722 4122 1019 
L 4122 359 
Q 3791 134 3420 21 
Q 3050 -91 2638 -91 
Q 1578 -91 968 557 
Q 359 1206 359 2328 
Q 359 3453 968 4101 
Q 1578 4750 2638 4750 
Q 3056 4750 3426 4639 
Q 3797 4528 4122 4306 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-54"/>
      <use xlink:href="#DejaVuSans-75" x="45.958984"/>
      <use xlink:href="#DejaVuSans-6e" x="109.337891"/>
      <use xlink:href="#DejaVuSans-69" x="172.716797"/>
      <use xlink:href="#DejaVuSans-6e" x="200.5"/>
      <use xlink:href="#DejaVuSans-67" x="263.878906"/>
      <use xlink:href="#DejaVuSans-20" x="327.355469"/>
      <use xlink:href="#DejaVuSans-43" x="359.142578"/>
      <use xlink:href="#DejaVuSans-75" x="428.966797"/>
      <use xlink:href="#DejaVuSans-72" x="492.345703"/>
      <use xlink:href="#DejaVuSans-76" x="533.458984"/>
      <use xlink:href="#DejaVuSans-65" x="592.638672"/>
     </g>
    </g>
    <g id="line2d_72">
     <path d="M 693.029788 67.0705 
L 701.029788 67.0705 
L 709.029788 67.0705 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #ff0000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_46">
     <!-- Close threshold (3.0m) -->
     <g transform="translate(715.429788 69.8705) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-28" d="M 1984 4856 
Q 1566 4138 1362 3434 
Q 1159 2731 1159 2009 
Q 1159 1288 1364 580 
Q 1569 -128 1984 -844 
L 1484 -844 
Q 1016 -109 783 600 
Q 550 1309 550 2009 
Q 550 2706 781 3412 
Q 1013 4119 1484 4856 
L 1984 4856 
z
" transform="scale(0.015625)"/>
       <path id="DejaVuSans-29" d="M 513 4856 
L 1013 4856 
Q 1481 4119 1714 3412 
Q 1947 2706 1947 2009 
Q 1947 1309 1714 600 
Q 1481 -109 1013 -844 
L 513 -844 
Q 928 -128 1133 580 
Q 1338 1288 1338 2009 
Q 1338 2731 1133 3434 
Q 928 4138 513 4856 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-43"/>
      <use xlink:href="#DejaVuSans-6c" x="69.824219"/>
      <use xlink:href="#DejaVuSans-6f" x="97.607422"/>
      <use xlink:href="#DejaVuSans-73" x="158.789062"/>
      <use xlink:href="#DejaVuSans-65" x="210.888672"/>
      <use xlink:href="#DejaVuSans-20" x="272.412109"/>
      <use xlink:href="#DejaVuSans-74" x="304.199219"/>
      <use xlink:href="#DejaVuSans-68" x="343.408203"/>
      <use xlink:href="#DejaVuSans-72" x="406.787109"/>
      <use xlink:href="#DejaVuSans-65" x="445.650391"/>
      <use xlink:href="#DejaVuSans-73" x="507.173828"/>
      <use xlink:href="#DejaVuSans-68" x="559.273438"/>
      <use xlink:href="#DejaVuSans-6f" x="622.652344"/>
      <use xlink:href="#DejaVuSans-6c" x="683.833984"/>
      <use xlink:href="#DejaVuSans-64" x="711.617188"/>
      <use xlink:href="#DejaVuSans-20" x="775.09375"/>
      <use xlink:href="#DejaVuSans-28" x="806.880859"/>
      <use xlink:href="#DejaVuSans-33" x="845.894531"/>
      <use xlink:href="#DejaVuSans-2e" x="909.517578"/>
      <use xlink:href="#DejaVuSans-30" x="941.304688"/>
      <use xlink:href="#DejaVuSans-6d" x="1004.927734"/>
      <use xlink:href="#DejaVuSans-29" x="1102.339844"/>
     </g>
    </g>
    <g id="line2d_73">
     <path d="M 693.029788 78.813 
L 701.029788 78.813 
L 709.029788 78.813 
" style="fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #008000; stroke-opacity: 0.7; stroke-width: 1.5"/>
    </g>
    <g id="text_47">
     <!-- Far threshold (5.0m) -->
     <g transform="translate(715.429788 81.613) scale(0.08 -0.08)">
      <defs>
       <path id="DejaVuSans-46" d="M 628 4666 
L 3309 4666 
L 3309 4134 
L 1259 4134 
L 1259 2759 
L 3109 2759 
L 3109 2228 
L 1259 2228 
L 1259 0 
L 628 0 
L 628 4666 
z
" transform="scale(0.015625)"/>
      </defs>
      <use xlink:href="#DejaVuSans-46"/>
      <use xlink:href="#DejaVuSans-61" x="48.394531"/>
      <use xlink:href="#DejaVuSans-72" x="109.673828"/>
      <use xlink:href="#DejaVuSans-20" x="150.787109"/>
      <use xlink:href="#DejaVuSans-74" x="182.574219"/>
      <use xlink:href="#DejaVuSans-68" x="221.783203"/>
      <use xlink:href="#DejaVuSans-72" x="285.162109"/>
      <use xlink:href="#DejaVuSans-65" x="324.025391"/>
      <use xlink:href="#DejaVuSans-73" x="385.548828"/>
      <use xlink:href="#DejaVuSans-68" x="437.648438"/>
      <use xlink:href="#DejaVuSans-6f" x="501.027344"/>
      <use xlink:href="#DejaVuSans-6c" x="562.208984"/>
      <use xlink:href="#DejaVuSans-64" x="589.992188"/>
      <use xlink:href="#DejaVuSans-20" x="653.46875"/>
      <use xlink:href="#DejaVuSans-28" x="685.255859"/>
      <use xlink:href="#DejaVuSans-35" x="724.269531"/>
      <use xlink:href="#DejaVuSans-2e" x="787.892578"/>
      <use xlink:href="#DejaVuSans-30" x="819.679688"/>
      <use xlink:href="#DejaVuSans-6d" x="883.302734"/>
      <use xlink:href="#DejaVuSans-29" x="980.714844"/>
     </g>
    </g>
   </g>
  </g>
  <g id="axes_3">
   <g id="patch_17">
    <path d="M 330.77382 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 330.77382 71.82685 
z
" style="fill: #ffffff"/>
   </g>
   <image xlink:href="data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAABcAAAHBCAYAAACCOiAmAAACGUlEQVR4nO2b0W0bURDEnqRz3+k7VpwCjvkcAQzIAhbEzuzpLMiPc379nBHXOddq9nnOJp9zrnO+ZsPX5u0ch7eWO1URqYqIuope811bWgtSFZGqiFRFpJ0jvVog6ip6zXue3/Ee0eOcn9mXOVPzhv9nw6/z2A0fm7+Ww3cPxfVadg/FzJmqiJgD9ZpXRcAcqNe8KgLmQL3mVREwB+o1r4qAOVCveVUEzIF6zYdVHJsPe24ONHPAe0Q9z5HMkal5a0EyR3oqIuZAM//0cO9aMkd6KiLmQDPn4bNfWs3X8p4Nzxy5nq/v3fDry7qWlzZQs/myit61PLXmJ/M7a/PlEYnXkvmdqogUKJL5v4bver5dS0dEeM2rIuIN1GteFRFvoF5z9Vo6ojtVEamKSFX8/HDvWjoiZHpE6kC95lURhpsDzfxOR4R0RIjavCrCcHOgXvOqCMPNgWZ+x3tEfSWCZI5URUQdaOY4vPeWO95Ax+bvZRW/tVV8/5kNHwe6W7nY/Hrsar7+rxxtoJnz8KoIZI5M22JeS+aAt4renRco4g3Ua16gSOZIVUQKFPF+EpnX4jXv/D893LuWqohURaRAEa+5eS1VEfCaFyiSOdKfLUjmiLeK3p0XKOIN1GteoEjmSFVErt/aQIe/hVzvfPmWK16L1nwovt65dXiBIu2ch7cWoioiVRGpiojXvECRjgipikhVRLw7967lLyPgue9DbEF4AAAAAElFTkSuQmCC" id="image1b4d46e905" transform="scale(1 -1) translate(0 -215.52)" x="330.72" y="-71.52" width="11.04" height="215.52"/>
   <g id="matplotlib.axis_5"/>
   <g id="matplotlib.axis_6">
    <g id="ytick_16">
     <g id="line2d_74">
      <defs>
       <path id="mc78000fc79" d="M 0 0 
L 3.5 0 
" style="stroke: #000000"/>
      </defs>
      <g>
       <use xlink:href="#mc78000fc79" x="341.56486" y="287.64765" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_48">
      <!-- 0.0 -->
      <g transform="translate(348.56486 291.066947) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_17">
     <g id="line2d_75">
      <g>
       <use xlink:href="#mc78000fc79" x="341.56486" y="244.48349" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_49">
      <!-- 0.2 -->
      <g transform="translate(348.56486 247.902787) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-32" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_18">
     <g id="line2d_76">
      <g>
       <use xlink:href="#mc78000fc79" x="341.56486" y="201.31933" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_50">
      <!-- 0.4 -->
      <g transform="translate(348.56486 204.738627) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-34" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_19">
     <g id="line2d_77">
      <g>
       <use xlink:href="#mc78000fc79" x="341.56486" y="158.15517" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_51">
      <!-- 0.6 -->
      <g transform="translate(348.56486 161.574467) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-36" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_20">
     <g id="line2d_78">
      <g>
       <use xlink:href="#mc78000fc79" x="341.56486" y="114.99101" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_52">
      <!-- 0.8 -->
      <g transform="translate(348.56486 118.410307) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-30"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-38" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="ytick_21">
     <g id="line2d_79">
      <g>
       <use xlink:href="#mc78000fc79" x="341.56486" y="71.82685" style="stroke: #000000"/>
      </g>
     </g>
     <g id="text_53">
      <!-- 1.0 -->
      <g transform="translate(348.56486 75.246147) scale(0.09 -0.09)">
       <use xlink:href="#DejaVuSans-31"/>
       <use xlink:href="#DejaVuSans-2e" x="63.623047"/>
       <use xlink:href="#DejaVuSans-30" x="95.410156"/>
      </g>
     </g>
    </g>
    <g id="text_54">
     <!-- Neural Activation -->
     <g transform="translate(369.519391 125.728109) rotate(-270) scale(0.11 -0.11)">
      <use xlink:href="#DejaVuSans-Bold-4e"/>
      <use xlink:href="#DejaVuSans-Bold-65" x="83.691406"/>
      <use xlink:href="#DejaVuSans-Bold-75" x="151.513672"/>
      <use xlink:href="#DejaVuSans-Bold-72" x="222.705078"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="272.021484"/>
      <use xlink:href="#DejaVuSans-Bold-6c" x="339.501953"/>
      <use xlink:href="#DejaVuSans-Bold-20" x="373.779297"/>
      <use xlink:href="#DejaVuSans-Bold-41" x="408.59375"/>
      <use xlink:href="#DejaVuSans-Bold-63" x="485.986328"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="545.263672"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="593.066406"/>
      <use xlink:href="#DejaVuSans-Bold-76" x="627.34375"/>
      <use xlink:href="#DejaVuSans-Bold-61" x="692.529297"/>
      <use xlink:href="#DejaVuSans-Bold-74" x="760.009766"/>
      <use xlink:href="#DejaVuSans-Bold-69" x="807.8125"/>
      <use xlink:href="#DejaVuSans-Bold-6f" x="842.089844"/>
      <use xlink:href="#DejaVuSans-Bold-6e" x="910.791016"/>
     </g>
    </g>
   </g>
   <g id="LineCollection_1"/>
   <g id="patch_18">
    <path d="M 330.77382 287.64765 
L 336.16934 287.64765 
L 341.56486 287.64765 
L 341.56486 71.82685 
L 336.16934 71.82685 
L 330.77382 71.82685 
L 330.77382 287.64765 
z
" style="fill: none; stroke: #000000; stroke-width: 1.2; stroke-linejoin: miter; stroke-linecap: square"/>
   </g>
  </g>
 </g>
 <defs>
  <clipPath id="p7a841bf3ed">
   <rect x="42.113906" y="44.84925" width="269.776" height="269.776"/>
  </clipPath>
  <clipPath id="pde26437bce">
   <rect x="436.259015" y="44.84925" width="377.678273" height="269.776"/>
  </clipPath>
 </defs>
</svg>
