# config.py

class Config:
    # 保持原有配置不变
    # DATA_ROOT = "/data1/fangzr/Research/Memory_Maze_3D/Dataset/gen-like-agent"
    DATA_ROOT =  "/data1/fangzr/Research/Memory_Maze_3D/Dataset/self-navigation-maze-frame-only"
    TRAIN_VAL_TEST_SPLIT = [0.7, 0.15, 0.15]
    SEQUENCE_LENGTH = 100
    SEQUENCE_STRIDE = 1

    @property
    def DATASET_DIRS(self):
        """动态获取所有符合模式的数据集目录"""
        import glob
        import os

        dirs = glob.glob(os.path.join(self.DATA_ROOT, self.DATASET_PATTERN))
        dirs = [d for d in dirs if os.path.isdir(d)]
        dirs.sort()
        return [os.path.basename(d) for d in dirs]

    # Grid Cell Network配置
    ENV_SIZE = 15  # 环境大小(米)
    PLACE_CELLS_N = 256  # Place cells数量
    PLACE_CELLS_SCALE = 0.5  # Place field尺度(米)
    HD_CELLS_N = 12  # Head direction cells数量
    HD_CELLS_CONCENTRATION = 20  # HD tuning的集中度

    HIDDEN_SIZE = 128  # LSTM隐藏状态维度
    DROPOUT_RATE = 0.5  # Dropout比率

    # 其他
    SEED = 42  # 随机种子
    RESUME = None  # 恢复训练的检查点路径

    # 训练参数
    BATCH_SIZE = 32
    NUM_EPOCHS = 150
    # LEARNING_RATE = 5e-6

    # Nature 超参数设置（用于优化器、学习率、权重衰减与梯度裁剪）
    NATURE_LEARNING_RATE = 1e-3
    NATURE_WEIGHT_DECAY = 1e-4
    GRAD_CLIP = 1

    SAVE_FREQUENCY = 5

    # 模型架构参数
    LATENT_DIM = 256
    MAX_OBJECTS = 6
    HIDDEN_DIM = 256
    MAZE_SIZE = 15

    # 添加记忆相关参数
    MEMORY_SIZE = 10000  # 记忆库大小
    MEMORY_TEMPERATURE = 0.1  # 记忆匹配的softmax温度
    MEMORY_SPARSITY_WEIGHT = 0.01  # 记忆激活的稀疏性损失权重
    MEMORY_ENTROPY_WEIGHT = 0.01  # 记忆匹配的熵损失权重
    MEMORY_UPDATE_FREQ = 10  # 每隔多少batch更新一次记忆库
    MEMORY_TOP_K = 3  # 检索时返回的最佳匹配数量

    # 物体检测参数
    TARGET_RADIUS = 0.6

    # 更新损失函数权重，添加记忆相关权重
    LOSS_WEIGHTS = {
        'alpha_weight': 1.0,
        'rgb_weight': 0.5,
        'smoothness_weight': 0.1,
        'visibility_weight': 1.0,
        'position_weight': 1.0,
    }

    NAV_LOSS_WEIGHTS = {
        'position': 1.0,  # Keep position loss dominant
        'rotation': 10,  # Increase rotation weight slightly
        'consistency': 0.05,  # Keep low to encourage smoothness
        'memory_sparsity': 0.005,  # Reduce sparsity loss weight
        'memory_entropy': 0.005,  # Reduce entropy loss weight slightly
        'grid_periodicity': 0.0,
        'grid_sparsity': 0.0,
        'grid_consistency': 0.0
    }

    # 训练设置
    USE_MIXED_PRECISION = True
    NUM_WORKERS = 4
    PIN_MEMORY = True

    # 评估设置
    EVAL_FREQUENCY = 1
    EVAL_SAMPLES = 2
    VISIBILITY_THRESHOLD = 0.5

    # 可视化设置
    SAVE_VISUALIZATIONS = False
    VIZ_OUTPUT_SIZE = 250
    VIZ_INTERVAL = 20
    VIZ_MAX_SAMPLES = 8
    VIZ_TYPES = [
        'predicted_positions',
        'place_memory_matches',  # 新增：显示记忆匹配结果
        'bev_output'
    ]

    # 路径设置
    CHECKPOINT_DIR = None
    INFERENCE_DIR = None

    # 预训练模型路径
    BEV_PRETRAINED_PATH = "/data1/fangzr/Research/Memory_Maze_3D/memory-maze-cp/bev_predictor/log_train/20250202_000204/checkpoints/best_model.pth"
    TARGET_PRETRAINED_PATH = "/data1/fangzr/Research/Memory_Maze_3D/memory-maze-cp/bev_predictor/log_train_target/20250202_151348/checkpoints/latest_checkpoint.pth"

    # ===== 新增：大数据集内存管理配置 =====
    # 数据分块设置
    DATA_CHUNKING_ENABLED = True  # 启用数据分块
    DATA_CHUNK_SIZE = 1000  # 每块包含的序列数
    ROTATE_CHUNKS_WITHIN_EPOCH = True  # 在一个epoch内轮换所有数据块
    SHUFFLE_CHUNKS = True  # 随机化数据块处理顺序

    # 惰性加载设置
    LAZY_LOADING_ENABLED = True  # 启用惰性加载（按需加载数据）
    FRAME_INFO_CACHE_SIZE = 100  # 缓存的帧信息数量
    PRELOAD_METADATA_ONLY = True  # 仅预加载元数据，完整数据按需加载

    # 内存管理设置
    MEMORY_MANAGEMENT_ENABLED = True  # 启用内存管理
    MEMORY_THRESHOLD_MB = 10000  # 内存使用阈值（MB），超过则执行优化
    FORCE_GC_BETWEEN_CHUNKS = True  # 在数据块间强制垃圾回收
    CACHE_CLEARING_FREQUENCY = 10  # 每隔多少步清理一次缓存

    # 数据加载器优化
    WORKER_MEMORY_LIMIT_MB = 2048  # 每个工作进程的内存限制（MB）
    WORKER_TIMEOUT = 60  # 工作进程超时（秒）
    PREFETCH_FACTOR = 1  # 预取因子（减少预取可节省内存）
    MAX_ACTIVE_WORKERS = 2  # 最大活跃工作进程数

    # 梯度累积（模拟更大批次）
    GRADIENT_ACCUMULATION_STEPS = 1  # 梯度累积步数
    ADAPTIVE_BATCH_SIZE = False  # 自适应调整批次大小
    MIN_BATCH_SIZE = 1  # 最小批次大小

    # 监控和调试
    MEMORY_PROFILING_ENABLED = True  # 启用内存使用监控
    LOG_MEMORY_USAGE = True  # 记录内存使用情况
    MEMORY_LOG_FREQUENCY = 10  # 内存日志记录频率（步数）

    # 恢复训练增强
    CHECKPOINT_CHUNK_STATE = True  # 检查点保存数据块状态
    RESUME_CHUNK_TRAINING = True  # 从上次处理的数据块恢复

    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
            else:
                raise ValueError(f"Unknown configuration key: {key}")

    @staticmethod
    def from_dict(config_dict):
        return Config(**config_dict)

    def to_dict(self):
        return {k: v for k, v in self.__dict__.items()
                if not k.startswith('__') and not callable(v)}

    def save(self, filepath):
        import json
        with open(filepath, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)

    @classmethod
    def load(cls, filepath):
        import json
        with open(filepath, 'r') as f:
            config_dict = json.load(f)
        return cls.from_dict(config_dict)


# 预定义配置类保持不变，但添加新的内存优化设置
class TrainConfig(Config):
    def __init__(self):
        super().__init__()
        self.BATCH_SIZE = 32
        self.USE_MIXED_PRECISION = True
        # 添加针对训练优化的内存管理设置
        self.DATA_CHUNK_SIZE = 1000
        self.WORKER_MEMORY_LIMIT_MB = 4096  # 训练时可能使用更多内存
        self.GRADIENT_ACCUMULATION_STEPS = 2  # 使用梯度累积来减少内存使用


class TestConfig(Config):
    def __init__(self):
        super().__init__()
        self.BATCH_SIZE = 1
        self.USE_MIXED_PRECISION = False
        # 针对测试优化的内存管理设置
        self.DATA_CHUNK_SIZE = 250  # 测试时使用更小的块
        self.LAZY_LOADING_ENABLED = True
        self.NUM_WORKERS = 1  # 减少测试时的worker数量


class DebugConfig(Config):
    def __init__(self):
        super().__init__()
        self.BATCH_SIZE = 2
        self.NUM_EPOCHS = 2
        self.SAVE_FREQUENCY = 1
        self.NUM_WORKERS = 0
        # 禁用一些内存优化以便更容易调试
        self.MEMORY_PROFILING_ENABLED = True
        self.LOG_MEMORY_USAGE = True
        self.MEMORY_LOG_FREQUENCY = 1  # 每步都记录内存使用
        self.DATA_CHUNK_SIZE = 100  # 调试时使用更小的数据块


# 新增：针对大数据集训练的配置
class LargeDatasetConfig(Config):
    def __init__(self):
        super().__init__()
        # 更激进的内存优化设置
        self.BATCH_SIZE = 32
        self.DATA_CHUNK_SIZE = 100
        self.LAZY_LOADING_ENABLED = True
        self.PRELOAD_METADATA_ONLY = True
        self.MEMORY_MANAGEMENT_ENABLED = True
        self.FORCE_GC_BETWEEN_CHUNKS = True
        self.WORKER_MEMORY_LIMIT_MB = 1024
        self.NUM_WORKERS = 10
        self.PREFETCH_FACTOR = 1
        self.GRADIENT_ACCUMULATION_STEPS = 4
        self.PIN_MEMORY = False
        self.LOG_MEMORY_USAGE = True


if __name__ == "__main__":
    # 测试配置
    config = Config()

    # 测试配置保存和加载
    import tempfile
    import os

    with tempfile.TemporaryDirectory() as tmp_dir:
        # 保存配置
        save_path = os.path.join(tmp_dir, 'config.json')
        config.save(save_path)

        # 加载配置
        loaded_config = Config.load(save_path)

        # 检查加载的配置
        assert loaded_config.BATCH_SIZE == config.BATCH_SIZE
        assert loaded_config.MAX_OBJECTS == config.MAX_OBJECTS
        # 检查新增的内存管理配置
        assert loaded_config.DATA_CHUNKING_ENABLED == config.DATA_CHUNKING_ENABLED
        assert loaded_config.MEMORY_THRESHOLD_MB == config.MEMORY_THRESHOLD_MB
        print("配置测试通过！")

    # 测试预定义配置
    train_config = TrainConfig()
    test_config = TestConfig()
    debug_config = DebugConfig()
    large_dataset_config = LargeDatasetConfig()

    print(f"训练批大小: {train_config.BATCH_SIZE}")
    print(f"测试批大小: {test_config.BATCH_SIZE}")
    print(f"调试批大小: {debug_config.BATCH_SIZE}")
    print(f"大数据集批大小: {large_dataset_config.BATCH_SIZE}")

    # 打印内存管理配置
    print(f"数据分块大小: {large_dataset_config.DATA_CHUNK_SIZE}")
    print(f"梯度累积步数: {large_dataset_config.GRADIENT_ACCUMULATION_STEPS}")
    print(f"内存阈值(MB): {large_dataset_config.MEMORY_THRESHOLD_MB}")