<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="1204.725625" height="320.968026" viewBox="0 0 1204.725625 320.968026">
<defs>
<g>
<g id="glyph-0-0">
<path d="M 3.179688 -6.640625 C 2.671875 -6.640625 2.289062 -6.390625 2.03125 -5.890625 C 1.769531 -5.390625 1.648438 -4.640625 1.648438 -3.640625 C 1.648438 -2.640625 1.769531 -1.890625 2.03125 -1.390625 C 2.289062 -0.890625 2.671875 -0.640625 3.179688 -0.640625 C 3.691406 -0.640625 4.070312 -0.890625 4.328125 -1.390625 C 4.578125 -1.890625 4.710938 -2.640625 4.710938 -3.640625 C 4.710938 -4.640625 4.578125 -5.390625 4.328125 -5.890625 C 4.070312 -6.390625 3.691406 -6.640625 3.179688 -6.640625 Z M 3.179688 -7.421875 C 3.988281 -7.421875 4.609375 -7.089844 5.050781 -6.449219 C 5.480469 -5.800781 5.699219 -4.859375 5.699219 -3.640625 C 5.699219 -2.410156 5.480469 -1.46875 5.050781 -0.828125 C 4.609375 -0.191406 3.988281 0.128906 3.171875 0.128906 C 2.351562 0.128906 1.730469 -0.191406 1.300781 -0.828125 C 0.871094 -1.46875 0.660156 -2.410156 0.660156 -3.640625 C 0.660156 -4.859375 0.871094 -5.800781 1.300781 -6.449219 C 1.730469 -7.089844 2.359375 -7.421875 3.179688 -7.421875 Z M 3.179688 -7.421875 "/>
</g>
<g id="glyph-0-1">
<path d="M 4.058594 -3.929688 C 4.53125 -3.828125 4.898438 -3.621094 5.160156 -3.300781 C 5.421875 -2.980469 5.558594 -2.578125 5.558594 -2.121094 C 5.558594 -1.398438 5.308594 -0.839844 4.820312 -0.449219 C 4.320312 -0.0585938 3.621094 0.128906 2.710938 0.128906 C 2.398438 0.128906 2.078125 0.101562 1.761719 0.0390625 C 1.441406 -0.0117188 1.101562 -0.101562 0.761719 -0.21875 L 0.761719 -1.171875 C 1.03125 -1.011719 1.328125 -0.890625 1.660156 -0.808594 C 1.980469 -0.730469 2.320312 -0.691406 2.679688 -0.691406 C 3.300781 -0.691406 3.769531 -0.808594 4.089844 -1.050781 C 4.410156 -1.289062 4.578125 -1.648438 4.578125 -2.121094 C 4.578125 -2.539062 4.429688 -2.878906 4.128906 -3.121094 C 3.828125 -3.359375 3.410156 -3.488281 2.871094 -3.488281 L 2.019531 -3.488281 L 2.019531 -4.300781 L 2.910156 -4.300781 C 3.390625 -4.300781 3.761719 -4.390625 4.019531 -4.589844 C 4.28125 -4.78125 4.410156 -5.058594 4.410156 -5.429688 C 4.410156 -5.800781 4.269531 -6.089844 4.011719 -6.289062 C 3.738281 -6.488281 3.359375 -6.589844 2.871094 -6.589844 C 2.601562 -6.589844 2.308594 -6.558594 2 -6.5 C 1.691406 -6.441406 1.351562 -6.351562 0.980469 -6.230469 L 0.980469 -7.109375 C 1.351562 -7.210938 1.699219 -7.289062 2.03125 -7.339844 C 2.351562 -7.390625 2.660156 -7.421875 2.960938 -7.421875 C 3.699219 -7.421875 4.289062 -7.25 4.730469 -6.910156 C 5.171875 -6.570312 5.390625 -6.109375 5.390625 -5.53125 C 5.390625 -5.128906 5.269531 -4.789062 5.039062 -4.511719 C 4.808594 -4.230469 4.480469 -4.03125 4.058594 -3.929688 Z M 4.058594 -3.929688 "/>
</g>
<g id="glyph-0-2">
<path d="M 3.300781 -4.039062 C 2.859375 -4.039062 2.511719 -3.878906 2.25 -3.578125 C 1.988281 -3.28125 1.859375 -2.859375 1.859375 -2.339844 C 1.859375 -1.808594 1.988281 -1.390625 2.25 -1.089844 C 2.511719 -0.789062 2.859375 -0.640625 3.300781 -0.640625 C 3.738281 -0.640625 4.089844 -0.789062 4.351562 -1.089844 C 4.609375 -1.390625 4.738281 -1.808594 4.738281 -2.339844 C 4.738281 -2.859375 4.609375 -3.28125 4.351562 -3.578125 C 4.089844 -3.878906 3.738281 -4.039062 3.300781 -4.039062 Z M 5.261719 -7.128906 L 5.261719 -6.230469 C 5.011719 -6.351562 4.761719 -6.441406 4.511719 -6.5 C 4.25 -6.558594 4 -6.589844 3.761719 -6.589844 C 3.101562 -6.589844 2.601562 -6.371094 2.261719 -5.929688 C 1.921875 -5.488281 1.71875 -4.820312 1.679688 -3.941406 C 1.871094 -4.21875 2.109375 -4.441406 2.398438 -4.589844 C 2.691406 -4.738281 3.011719 -4.820312 3.359375 -4.820312 C 4.089844 -4.820312 4.671875 -4.589844 5.089844 -4.148438 C 5.511719 -3.710938 5.730469 -3.101562 5.730469 -2.339844 C 5.730469 -1.589844 5.5 -0.988281 5.058594 -0.539062 C 4.621094 -0.0898438 4.03125 0.128906 3.300781 0.128906 C 2.460938 0.128906 1.808594 -0.191406 1.371094 -0.828125 C 0.921875 -1.46875 0.699219 -2.410156 0.699219 -3.640625 C 0.699219 -4.789062 0.96875 -5.710938 1.519531 -6.390625 C 2.058594 -7.070312 2.800781 -7.421875 3.71875 -7.421875 C 3.960938 -7.421875 4.210938 -7.390625 4.46875 -7.351562 C 4.71875 -7.300781 4.980469 -7.230469 5.261719 -7.128906 Z M 5.261719 -7.128906 "/>
</g>
<g id="glyph-0-3">
<path d="M 1.101562 -0.148438 L 1.101562 -1.050781 C 1.339844 -0.929688 1.589844 -0.839844 1.851562 -0.78125 C 2.101562 -0.71875 2.351562 -0.691406 2.601562 -0.691406 C 3.238281 -0.691406 3.738281 -0.898438 4.078125 -1.339844 C 4.421875 -1.78125 4.621094 -2.441406 4.679688 -3.339844 C 4.480469 -3.058594 4.238281 -2.839844 3.960938 -2.691406 C 3.671875 -2.539062 3.351562 -2.46875 3 -2.46875 C 2.261719 -2.46875 1.679688 -2.691406 1.261719 -3.128906 C 0.839844 -3.570312 0.628906 -4.171875 0.628906 -4.941406 C 0.628906 -5.679688 0.851562 -6.28125 1.289062 -6.738281 C 1.730469 -7.191406 2.320312 -7.421875 3.058594 -7.421875 C 3.898438 -7.421875 4.550781 -7.089844 4.988281 -6.449219 C 5.429688 -5.800781 5.660156 -4.859375 5.660156 -3.640625 C 5.660156 -2.480469 5.378906 -1.570312 4.839844 -0.890625 C 4.289062 -0.210938 3.558594 0.128906 2.640625 0.128906 C 2.390625 0.128906 2.140625 0.101562 1.890625 0.0585938 C 1.628906 0.0195312 1.371094 -0.0507812 1.101562 -0.148438 Z M 3.058594 -3.238281 C 3.5 -3.238281 3.851562 -3.390625 4.109375 -3.691406 C 4.371094 -3.988281 4.5 -4.410156 4.5 -4.941406 C 4.5 -5.460938 4.371094 -5.878906 4.109375 -6.179688 C 3.851562 -6.480469 3.5 -6.640625 3.058594 -6.640625 C 2.621094 -6.640625 2.269531 -6.480469 2.011719 -6.179688 C 1.75 -5.878906 1.621094 -5.460938 1.621094 -4.941406 C 1.621094 -4.410156 1.75 -3.988281 2.011719 -3.691406 C 2.269531 -3.390625 2.621094 -3.238281 3.058594 -3.238281 Z M 3.058594 -3.238281 "/>
</g>
<g id="glyph-0-4">
<path d="M 1.238281 -0.828125 L 2.851562 -0.828125 L 2.851562 -6.390625 L 1.101562 -6.039062 L 1.101562 -6.941406 L 2.839844 -7.289062 L 3.828125 -7.289062 L 3.828125 -0.828125 L 5.441406 -0.828125 L 5.441406 0 L 1.238281 0 Z M 1.238281 -0.828125 "/>
</g>
<g id="glyph-0-5">
<path d="M 1.921875 -0.828125 L 5.359375 -0.828125 L 5.359375 0 L 0.730469 0 L 0.730469 -0.828125 C 1.101562 -1.210938 1.609375 -1.730469 2.261719 -2.390625 C 2.898438 -3.039062 3.308594 -3.460938 3.480469 -3.648438 C 3.800781 -4 4.019531 -4.300781 4.140625 -4.550781 C 4.261719 -4.789062 4.328125 -5.039062 4.328125 -5.28125 C 4.328125 -5.660156 4.191406 -5.980469 3.921875 -6.21875 C 3.648438 -6.460938 3.300781 -6.589844 2.859375 -6.589844 C 2.550781 -6.589844 2.21875 -6.53125 1.878906 -6.429688 C 1.539062 -6.320312 1.171875 -6.160156 0.78125 -5.941406 L 0.78125 -6.941406 C 1.179688 -7.101562 1.550781 -7.21875 1.890625 -7.300781 C 2.230469 -7.378906 2.550781 -7.421875 2.839844 -7.421875 C 3.589844 -7.421875 4.191406 -7.230469 4.640625 -6.851562 C 5.089844 -6.46875 5.320312 -5.96875 5.320312 -5.339844 C 5.320312 -5.039062 5.261719 -4.75 5.148438 -4.488281 C 5.039062 -4.21875 4.839844 -3.898438 4.539062 -3.539062 C 4.460938 -3.441406 4.199219 -3.171875 3.761719 -2.71875 C 3.320312 -2.269531 2.710938 -1.640625 1.921875 -0.828125 Z M 1.921875 -0.828125 "/>
</g>
<g id="glyph-0-6">
<path d="M 1.078125 -7.289062 L 4.949219 -7.289062 L 4.949219 -6.460938 L 1.980469 -6.460938 L 1.980469 -4.671875 C 2.121094 -4.71875 2.269531 -4.761719 2.410156 -4.78125 C 2.550781 -4.800781 2.699219 -4.820312 2.839844 -4.820312 C 3.648438 -4.820312 4.289062 -4.589844 4.769531 -4.148438 C 5.25 -3.699219 5.488281 -3.101562 5.488281 -2.339844 C 5.488281 -1.550781 5.238281 -0.941406 4.75 -0.511719 C 4.261719 -0.078125 3.570312 0.128906 2.691406 0.128906 C 2.378906 0.128906 2.070312 0.101562 1.75 0.0585938 C 1.429688 0.0117188 1.109375 -0.0585938 0.769531 -0.171875 L 0.769531 -1.160156 C 1.058594 -1 1.359375 -0.878906 1.679688 -0.800781 C 1.988281 -0.71875 2.320312 -0.691406 2.671875 -0.691406 C 3.230469 -0.691406 3.679688 -0.828125 4.011719 -1.128906 C 4.328125 -1.429688 4.5 -1.828125 4.5 -2.339844 C 4.5 -2.839844 4.328125 -3.238281 4.011719 -3.539062 C 3.679688 -3.839844 3.230469 -3.988281 2.671875 -3.988281 C 2.410156 -3.988281 2.140625 -3.960938 1.878906 -3.898438 C 1.621094 -3.839844 1.351562 -3.75 1.078125 -3.628906 Z M 1.078125 -7.289062 "/>
</g>
<g id="glyph-0-7">
<path d="M 1.070312 -1.238281 L 2.101562 -1.238281 L 2.101562 0 L 1.070312 0 Z M 1.070312 -1.238281 "/>
</g>
<g id="glyph-0-8">
<path d="M 3.78125 -6.429688 L 1.289062 -2.539062 L 3.78125 -2.539062 Z M 3.519531 -7.289062 L 4.761719 -7.289062 L 4.761719 -2.539062 L 5.800781 -2.539062 L 5.800781 -1.71875 L 4.761719 -1.71875 L 4.761719 0 L 3.78125 0 L 3.78125 -1.71875 L 0.488281 -1.71875 L 0.488281 -2.671875 Z M 3.519531 -7.289062 "/>
</g>
<g id="glyph-0-9">
<path d="M 3.179688 -3.460938 C 2.710938 -3.460938 2.339844 -3.328125 2.070312 -3.078125 C 1.800781 -2.828125 1.671875 -2.488281 1.671875 -2.050781 C 1.671875 -1.609375 1.800781 -1.261719 2.070312 -1.011719 C 2.339844 -0.761719 2.710938 -0.640625 3.179688 -0.640625 C 3.640625 -0.640625 4.011719 -0.761719 4.28125 -1.019531 C 4.550781 -1.269531 4.691406 -1.609375 4.691406 -2.050781 C 4.691406 -2.488281 4.550781 -2.828125 4.289062 -3.078125 C 4.019531 -3.328125 3.648438 -3.460938 3.179688 -3.460938 Z M 2.191406 -3.878906 C 1.769531 -3.980469 1.441406 -4.179688 1.199219 -4.46875 C 0.960938 -4.761719 0.851562 -5.109375 0.851562 -5.53125 C 0.851562 -6.109375 1.050781 -6.570312 1.46875 -6.910156 C 1.878906 -7.25 2.449219 -7.421875 3.179688 -7.421875 C 3.898438 -7.421875 4.46875 -7.25 4.890625 -6.910156 C 5.300781 -6.570312 5.511719 -6.109375 5.511719 -5.53125 C 5.511719 -5.109375 5.390625 -4.761719 5.148438 -4.46875 C 4.910156 -4.179688 4.589844 -3.980469 4.171875 -3.878906 C 4.640625 -3.769531 5.011719 -3.550781 5.28125 -3.230469 C 5.539062 -2.910156 5.679688 -2.511719 5.679688 -2.050781 C 5.679688 -1.339844 5.460938 -0.800781 5.03125 -0.429688 C 4.589844 -0.0507812 3.96875 0.128906 3.171875 0.128906 C 2.371094 0.128906 1.75 -0.0507812 1.320312 -0.429688 C 0.890625 -0.800781 0.679688 -1.339844 0.679688 -2.050781 C 0.679688 -2.511719 0.808594 -2.910156 1.078125 -3.230469 C 1.339844 -3.550781 1.710938 -3.769531 2.191406 -3.878906 Z M 1.828125 -5.441406 C 1.828125 -5.058594 1.941406 -4.761719 2.179688 -4.550781 C 2.421875 -4.339844 2.75 -4.238281 3.179688 -4.238281 C 3.601562 -4.238281 3.929688 -4.339844 4.171875 -4.550781 C 4.410156 -4.761719 4.53125 -5.058594 4.53125 -5.441406 C 4.53125 -5.820312 4.410156 -6.109375 4.171875 -6.320312 C 3.929688 -6.53125 3.601562 -6.640625 3.179688 -6.640625 C 2.75 -6.640625 2.421875 -6.53125 2.179688 -6.320312 C 1.941406 -6.109375 1.828125 -5.820312 1.828125 -5.441406 Z M 1.828125 -5.441406 "/>
</g>
<g id="glyph-1-0">
<path d="M 0.691406 -8.019531 L 1.859375 -8.019531 L 3.851562 -5.039062 L 5.851562 -8.019531 L 7.019531 -8.019531 L 4.445312 -4.167969 L 7.195312 0 L 6.027344 0 L 3.773438 -3.410156 L 1.496094 0 L 0.328125 0 L 3.191406 -4.277344 Z M 0.691406 -8.019531 "/>
</g>
<g id="glyph-1-1">
</g>
<g id="glyph-1-2">
<path d="M 2.167969 -7.128906 L 2.167969 -4.113281 L 3.53125 -4.113281 C 4.035156 -4.113281 4.421875 -4.234375 4.695312 -4.5 C 4.972656 -4.761719 5.113281 -5.136719 5.113281 -5.621094 C 5.113281 -6.105469 4.972656 -6.46875 4.695312 -6.730469 C 4.421875 -6.996094 4.035156 -7.128906 3.53125 -7.128906 Z M 1.078125 -8.019531 L 3.53125 -8.019531 C 4.421875 -8.019531 5.105469 -7.808594 5.566406 -7.402344 C 6.027344 -6.996094 6.257812 -6.402344 6.257812 -5.621094 C 6.257812 -4.828125 6.027344 -4.222656 5.566406 -3.828125 C 5.105469 -3.421875 4.421875 -3.222656 3.53125 -3.222656 L 2.167969 -3.222656 L 2.167969 0 L 1.078125 0 Z M 1.078125 -8.019531 "/>
</g>
<g id="glyph-1-3">
<path d="M 3.367188 -5.324219 C 2.839844 -5.324219 2.421875 -5.113281 2.113281 -4.695312 C 1.804688 -4.277344 1.648438 -3.71875 1.648438 -3.003906 C 1.648438 -2.277344 1.792969 -1.714844 2.101562 -1.296875 C 2.410156 -0.878906 2.828125 -0.683594 3.367188 -0.683594 C 3.894531 -0.683594 4.3125 -0.878906 4.621094 -1.296875 C 4.929688 -1.714844 5.082031 -2.277344 5.082031 -3.003906 C 5.082031 -3.707031 4.929688 -4.277344 4.621094 -4.695312 C 4.3125 -5.113281 3.894531 -5.324219 3.367188 -5.324219 Z M 3.367188 -6.160156 C 4.222656 -6.160156 4.894531 -5.875 5.390625 -5.324219 C 5.875 -4.761719 6.128906 -3.992188 6.128906 -3.003906 C 6.128906 -2.011719 5.875 -1.242188 5.390625 -0.691406 C 4.894531 -0.132812 4.222656 0.144531 3.367188 0.144531 C 2.496094 0.144531 1.816406 -0.132812 1.332031 -0.691406 C 0.847656 -1.242188 0.605469 -2.011719 0.605469 -3.003906 C 0.605469 -3.992188 0.847656 -4.761719 1.332031 -5.324219 C 1.816406 -5.875 2.496094 -6.160156 3.367188 -6.160156 Z M 3.367188 -6.160156 "/>
</g>
<g id="glyph-1-4">
<path d="M 4.871094 -5.839844 L 4.871094 -4.90625 C 4.585938 -5.039062 4.300781 -5.148438 4.003906 -5.226562 C 3.695312 -5.289062 3.386719 -5.335938 3.070312 -5.335938 C 2.574219 -5.335938 2.199219 -5.257812 1.957031 -5.105469 C 1.714844 -4.949219 1.59375 -4.730469 1.59375 -4.433594 C 1.59375 -4.203125 1.683594 -4.027344 1.859375 -3.894531 C 2.035156 -3.761719 2.386719 -3.628906 2.914062 -3.519531 L 3.257812 -3.441406 C 3.960938 -3.289062 4.453125 -3.070312 4.753906 -2.804688 C 5.039062 -2.53125 5.191406 -2.144531 5.191406 -1.660156 C 5.191406 -1.101562 4.972656 -0.660156 4.53125 -0.339844 C 4.09375 -0.0117188 3.476562 0.144531 2.707031 0.144531 C 2.375 0.144531 2.046875 0.109375 1.695312 0.0546875 C 1.34375 0 0.980469 -0.0898438 0.59375 -0.21875 L 0.59375 -1.242188 C 0.957031 -1.046875 1.320312 -0.902344 1.671875 -0.8125 C 2.023438 -0.714844 2.375 -0.671875 2.726562 -0.671875 C 3.191406 -0.671875 3.554688 -0.746094 3.804688 -0.902344 C 4.046875 -1.054688 4.179688 -1.285156 4.179688 -1.585938 C 4.179688 -1.847656 4.082031 -2.058594 3.90625 -2.199219 C 3.730469 -2.34375 3.332031 -2.484375 2.71875 -2.617188 L 2.375 -2.695312 C 1.761719 -2.828125 1.308594 -3.023438 1.046875 -3.289062 C 0.769531 -3.554688 0.636719 -3.914062 0.636719 -4.390625 C 0.636719 -4.949219 0.835938 -5.390625 1.230469 -5.699219 C 1.628906 -6.007812 2.199219 -6.160156 2.949219 -6.160156 C 3.3125 -6.160156 3.652344 -6.128906 3.980469 -6.070312 C 4.300781 -6.015625 4.597656 -5.941406 4.871094 -5.839844 Z M 4.871094 -5.839844 "/>
</g>
<g id="glyph-1-5">
<path d="M 1.035156 -6.015625 L 2.023438 -6.015625 L 2.023438 0 L 1.035156 0 Z M 1.035156 -8.359375 L 2.023438 -8.359375 L 2.023438 -7.105469 L 1.035156 -7.105469 Z M 1.035156 -8.359375 "/>
</g>
<g id="glyph-1-6">
<path d="M 2.011719 -7.722656 L 2.011719 -6.015625 L 4.046875 -6.015625 L 4.046875 -5.246094 L 2.011719 -5.246094 L 2.011719 -1.980469 C 2.011719 -1.484375 2.078125 -1.164062 2.210938 -1.035156 C 2.34375 -0.890625 2.617188 -0.824219 3.035156 -0.824219 L 4.046875 -0.824219 L 4.046875 0 L 3.035156 0 C 2.265625 0 1.738281 -0.144531 1.453125 -0.429688 C 1.164062 -0.714844 1.023438 -1.230469 1.023438 -1.980469 L 1.023438 -5.246094 L 0.296875 -5.246094 L 0.296875 -6.015625 L 1.023438 -6.015625 L 1.023438 -7.722656 Z M 2.011719 -7.722656 "/>
</g>
<g id="glyph-1-7">
<path d="M 6.039062 -3.628906 L 6.039062 0 L 5.050781 0 L 5.050781 -3.597656 C 5.050781 -4.167969 4.929688 -4.585938 4.707031 -4.871094 C 4.488281 -5.160156 4.15625 -5.300781 3.71875 -5.300781 C 3.179688 -5.300781 2.761719 -5.125 2.453125 -4.785156 C 2.144531 -4.445312 1.992188 -3.980469 1.992188 -3.398438 L 1.992188 0 L 1 0 L 1 -6.015625 L 1.992188 -6.015625 L 1.992188 -5.082031 C 2.222656 -5.433594 2.496094 -5.710938 2.828125 -5.886719 C 3.144531 -6.0625 3.519531 -6.160156 3.9375 -6.160156 C 4.621094 -6.160156 5.148438 -5.941406 5.5 -5.511719 C 5.851562 -5.082031 6.039062 -4.453125 6.039062 -3.628906 Z M 6.039062 -3.628906 "/>
</g>
<g id="glyph-1-8">
<path d="M 3.410156 -8.347656 C 2.925781 -7.511719 2.574219 -6.699219 2.34375 -5.894531 C 2.101562 -5.09375 1.992188 -4.277344 1.992188 -3.453125 C 1.992188 -2.617188 2.101562 -1.804688 2.34375 -1 C 2.574219 -0.1875 2.925781 0.617188 3.410156 1.441406 L 2.550781 1.441406 C 2.011719 0.59375 1.605469 -0.21875 1.34375 -1.035156 C 1.078125 -1.847656 0.945312 -2.652344 0.945312 -3.453125 C 0.945312 -4.246094 1.078125 -5.050781 1.34375 -5.863281 C 1.605469 -6.675781 2.003906 -7.503906 2.550781 -8.347656 Z M 3.410156 -8.347656 "/>
</g>
<g id="glyph-1-9">
<path d="M 5.71875 -4.863281 C 5.960938 -5.300781 6.257812 -5.621094 6.601562 -5.839844 C 6.941406 -6.050781 7.347656 -6.160156 7.820312 -6.160156 C 8.4375 -6.160156 8.921875 -5.941406 9.261719 -5.5 C 9.601562 -5.058594 9.777344 -4.433594 9.777344 -3.628906 L 9.777344 0 L 8.789062 0 L 8.789062 -3.597656 C 8.789062 -4.167969 8.679688 -4.597656 8.480469 -4.882812 C 8.273438 -5.160156 7.964844 -5.300781 7.546875 -5.300781 C 7.027344 -5.300781 6.621094 -5.125 6.324219 -4.785156 C 6.027344 -4.445312 5.886719 -3.980469 5.886719 -3.398438 L 5.886719 0 L 4.894531 0 L 4.894531 -3.597656 C 4.894531 -4.167969 4.785156 -4.597656 4.585938 -4.882812 C 4.378906 -5.160156 4.058594 -5.300781 3.640625 -5.300781 C 3.136719 -5.300781 2.726562 -5.125 2.429688 -4.785156 C 2.132812 -4.445312 1.992188 -3.980469 1.992188 -3.398438 L 1.992188 0 L 1 0 L 1 -6.015625 L 1.992188 -6.015625 L 1.992188 -5.082031 C 2.210938 -5.445312 2.484375 -5.71875 2.804688 -5.894531 C 3.113281 -6.070312 3.488281 -6.160156 3.925781 -6.160156 C 4.367188 -6.160156 4.730469 -6.050781 5.039062 -5.828125 C 5.347656 -5.609375 5.566406 -5.28125 5.71875 -4.863281 Z M 5.71875 -4.863281 "/>
</g>
<g id="glyph-1-10">
<path d="M 0.878906 -8.347656 L 1.738281 -8.347656 C 2.265625 -7.503906 2.671875 -6.675781 2.9375 -5.863281 C 3.199219 -5.050781 3.34375 -4.246094 3.34375 -3.453125 C 3.34375 -2.652344 3.199219 -1.847656 2.9375 -1.035156 C 2.671875 -0.21875 2.265625 0.59375 1.738281 1.441406 L 0.878906 1.441406 C 1.351562 0.617188 1.703125 -0.1875 1.945312 -1 C 2.179688 -1.804688 2.300781 -2.617188 2.300781 -3.453125 C 2.300781 -4.277344 2.179688 -5.09375 1.945312 -5.894531 C 1.703125 -6.699219 1.351562 -7.511719 0.878906 -8.347656 Z M 0.878906 -8.347656 "/>
</g>
<g id="glyph-2-0">
<path d="M -8.019531 0.0117188 L -8.019531 -1.144531 L -4.71875 -3.367188 L -8.019531 -5.578125 L -8.019531 -6.742188 L -3.816406 -3.90625 L 0 -3.90625 L 0 -2.816406 L -3.816406 -2.816406 Z M -8.019531 0.0117188 "/>
</g>
<g id="glyph-2-1">
</g>
<g id="glyph-2-2">
<path d="M -7.128906 -2.167969 L -4.113281 -2.167969 L -4.113281 -3.53125 C -4.113281 -4.035156 -4.234375 -4.421875 -4.5 -4.695312 C -4.761719 -4.972656 -5.136719 -5.113281 -5.621094 -5.113281 C -6.105469 -5.113281 -6.46875 -4.972656 -6.730469 -4.695312 C -6.996094 -4.421875 -7.128906 -4.035156 -7.128906 -3.53125 Z M -8.019531 -1.078125 L -8.019531 -3.53125 C -8.019531 -4.421875 -7.808594 -5.105469 -7.402344 -5.566406 C -6.996094 -6.027344 -6.402344 -6.257812 -5.621094 -6.257812 C -4.828125 -6.257812 -4.222656 -6.027344 -3.828125 -5.566406 C -3.421875 -5.105469 -3.222656 -4.421875 -3.222656 -3.53125 L -3.222656 -2.167969 L 0 -2.167969 L 0 -1.078125 Z M -8.019531 -1.078125 "/>
</g>
<g id="glyph-2-3">
<path d="M -5.324219 -3.367188 C -5.324219 -2.839844 -5.113281 -2.421875 -4.695312 -2.113281 C -4.277344 -1.804688 -3.71875 -1.648438 -3.003906 -1.648438 C -2.277344 -1.648438 -1.714844 -1.792969 -1.296875 -2.101562 C -0.878906 -2.410156 -0.683594 -2.828125 -0.683594 -3.367188 C -0.683594 -3.894531 -0.878906 -4.3125 -1.296875 -4.621094 C -1.714844 -4.929688 -2.277344 -5.082031 -3.003906 -5.082031 C -3.707031 -5.082031 -4.277344 -4.929688 -4.695312 -4.621094 C -5.113281 -4.3125 -5.324219 -3.894531 -5.324219 -3.367188 Z M -6.160156 -3.367188 C -6.160156 -4.222656 -5.875 -4.894531 -5.324219 -5.390625 C -4.761719 -5.875 -3.992188 -6.128906 -3.003906 -6.128906 C -2.011719 -6.128906 -1.242188 -5.875 -0.691406 -5.390625 C -0.132812 -4.894531 0.144531 -4.222656 0.144531 -3.367188 C 0.144531 -2.496094 -0.132812 -1.816406 -0.691406 -1.332031 C -1.242188 -0.847656 -2.011719 -0.605469 -3.003906 -0.605469 C -3.992188 -0.605469 -4.761719 -0.847656 -5.324219 -1.332031 C -5.875 -1.816406 -6.160156 -2.496094 -6.160156 -3.367188 Z M -6.160156 -3.367188 "/>
</g>
<g id="glyph-2-4">
<path d="M -5.839844 -4.871094 L -4.90625 -4.871094 C -5.039062 -4.585938 -5.148438 -4.300781 -5.226562 -4.003906 C -5.289062 -3.695312 -5.335938 -3.386719 -5.335938 -3.070312 C -5.335938 -2.574219 -5.257812 -2.199219 -5.105469 -1.957031 C -4.949219 -1.714844 -4.730469 -1.59375 -4.433594 -1.59375 C -4.203125 -1.59375 -4.027344 -1.683594 -3.894531 -1.859375 C -3.761719 -2.035156 -3.628906 -2.386719 -3.519531 -2.914062 L -3.441406 -3.257812 C -3.289062 -3.960938 -3.070312 -4.453125 -2.804688 -4.753906 C -2.53125 -5.039062 -2.144531 -5.191406 -1.660156 -5.191406 C -1.101562 -5.191406 -0.660156 -4.972656 -0.339844 -4.53125 C -0.0117188 -4.09375 0.144531 -3.476562 0.144531 -2.707031 C 0.144531 -2.375 0.109375 -2.046875 0.0546875 -1.695312 C 0 -1.34375 -0.0898438 -0.980469 -0.21875 -0.59375 L -1.242188 -0.59375 C -1.046875 -0.957031 -0.902344 -1.320312 -0.8125 -1.671875 C -0.714844 -2.023438 -0.671875 -2.375 -0.671875 -2.726562 C -0.671875 -3.191406 -0.746094 -3.554688 -0.902344 -3.804688 C -1.054688 -4.046875 -1.285156 -4.179688 -1.585938 -4.179688 C -1.847656 -4.179688 -2.058594 -4.082031 -2.199219 -3.90625 C -2.34375 -3.730469 -2.484375 -3.332031 -2.617188 -2.71875 L -2.695312 -2.375 C -2.828125 -1.761719 -3.023438 -1.308594 -3.289062 -1.046875 C -3.554688 -0.769531 -3.914062 -0.636719 -4.390625 -0.636719 C -4.949219 -0.636719 -5.390625 -0.835938 -5.699219 -1.230469 C -6.007812 -1.628906 -6.160156 -2.199219 -6.160156 -2.949219 C -6.160156 -3.3125 -6.128906 -3.652344 -6.070312 -3.980469 C -6.015625 -4.300781 -5.941406 -4.597656 -5.839844 -4.871094 Z M -5.839844 -4.871094 "/>
</g>
<g id="glyph-2-5">
<path d="M -6.015625 -1.035156 L -6.015625 -2.023438 L 0 -2.023438 L 0 -1.035156 Z M -8.359375 -1.035156 L -8.359375 -2.023438 L -7.105469 -2.023438 L -7.105469 -1.035156 Z M -8.359375 -1.035156 "/>
</g>
<g id="glyph-2-6">
<path d="M -7.722656 -2.011719 L -6.015625 -2.011719 L -6.015625 -4.046875 L -5.246094 -4.046875 L -5.246094 -2.011719 L -1.980469 -2.011719 C -1.484375 -2.011719 -1.164062 -2.078125 -1.035156 -2.210938 C -0.890625 -2.34375 -0.824219 -2.617188 -0.824219 -3.035156 L -0.824219 -4.046875 L 0 -4.046875 L 0 -3.035156 C 0 -2.265625 -0.144531 -1.738281 -0.429688 -1.453125 C -0.714844 -1.164062 -1.230469 -1.023438 -1.980469 -1.023438 L -5.246094 -1.023438 L -5.246094 -0.296875 L -6.015625 -0.296875 L -6.015625 -1.023438 L -7.722656 -1.023438 Z M -7.722656 -2.011719 "/>
</g>
<g id="glyph-2-7">
<path d="M -3.628906 -6.039062 L 0 -6.039062 L 0 -5.050781 L -3.597656 -5.050781 C -4.167969 -5.050781 -4.585938 -4.929688 -4.871094 -4.707031 C -5.160156 -4.488281 -5.300781 -4.15625 -5.300781 -3.71875 C -5.300781 -3.179688 -5.125 -2.761719 -4.785156 -2.453125 C -4.445312 -2.144531 -3.980469 -1.992188 -3.398438 -1.992188 L 0 -1.992188 L 0 -1 L -6.015625 -1 L -6.015625 -1.992188 L -5.082031 -1.992188 C -5.433594 -2.222656 -5.710938 -2.496094 -5.886719 -2.828125 C -6.0625 -3.144531 -6.160156 -3.519531 -6.160156 -3.9375 C -6.160156 -4.621094 -5.941406 -5.148438 -5.511719 -5.5 C -5.082031 -5.851562 -4.453125 -6.039062 -3.628906 -6.039062 Z M -3.628906 -6.039062 "/>
</g>
<g id="glyph-2-8">
<path d="M -8.347656 -3.410156 C -7.511719 -2.925781 -6.699219 -2.574219 -5.894531 -2.34375 C -5.09375 -2.101562 -4.277344 -1.992188 -3.453125 -1.992188 C -2.617188 -1.992188 -1.804688 -2.101562 -1 -2.34375 C -0.1875 -2.574219 0.617188 -2.925781 1.441406 -3.410156 L 1.441406 -2.550781 C 0.59375 -2.011719 -0.21875 -1.605469 -1.035156 -1.34375 C -1.847656 -1.078125 -2.652344 -0.945312 -3.453125 -0.945312 C -4.246094 -0.945312 -5.050781 -1.078125 -5.863281 -1.34375 C -6.675781 -1.605469 -7.503906 -2.003906 -8.347656 -2.550781 Z M -8.347656 -3.410156 "/>
</g>
<g id="glyph-2-9">
<path d="M -4.863281 -5.71875 C -5.300781 -5.960938 -5.621094 -6.257812 -5.839844 -6.601562 C -6.050781 -6.941406 -6.160156 -7.347656 -6.160156 -7.820312 C -6.160156 -8.4375 -5.941406 -8.921875 -5.5 -9.261719 C -5.058594 -9.601562 -4.433594 -9.777344 -3.628906 -9.777344 L 0 -9.777344 L 0 -8.789062 L -3.597656 -8.789062 C -4.167969 -8.789062 -4.597656 -8.679688 -4.882812 -8.480469 C -5.160156 -8.273438 -5.300781 -7.964844 -5.300781 -7.546875 C -5.300781 -7.027344 -5.125 -6.621094 -4.785156 -6.324219 C -4.445312 -6.027344 -3.980469 -5.886719 -3.398438 -5.886719 L 0 -5.886719 L 0 -4.894531 L -3.597656 -4.894531 C -4.167969 -4.894531 -4.597656 -4.785156 -4.882812 -4.585938 C -5.160156 -4.378906 -5.300781 -4.058594 -5.300781 -3.640625 C -5.300781 -3.136719 -5.125 -2.726562 -4.785156 -2.429688 C -4.445312 -2.132812 -3.980469 -1.992188 -3.398438 -1.992188 L 0 -1.992188 L 0 -1 L -6.015625 -1 L -6.015625 -1.992188 L -5.082031 -1.992188 C -5.445312 -2.210938 -5.71875 -2.484375 -5.894531 -2.804688 C -6.070312 -3.113281 -6.160156 -3.488281 -6.160156 -3.925781 C -6.160156 -4.367188 -6.050781 -4.730469 -5.828125 -5.039062 C -5.609375 -5.347656 -5.28125 -5.566406 -4.863281 -5.71875 Z M -4.863281 -5.71875 "/>
</g>
<g id="glyph-2-10">
<path d="M -8.347656 -0.878906 L -8.347656 -1.738281 C -7.503906 -2.265625 -6.675781 -2.671875 -5.863281 -2.9375 C -5.050781 -3.199219 -4.246094 -3.34375 -3.453125 -3.34375 C -2.652344 -3.34375 -1.847656 -3.199219 -1.035156 -2.9375 C -0.21875 -2.671875 0.59375 -2.265625 1.441406 -1.738281 L 1.441406 -0.878906 C 0.617188 -1.351562 -0.1875 -1.703125 -1 -1.945312 C -1.804688 -2.179688 -2.617188 -2.300781 -3.453125 -2.300781 C -4.277344 -2.300781 -5.09375 -2.179688 -5.894531 -1.945312 C -6.699219 -1.703125 -7.511719 -1.351562 -8.347656 -0.878906 Z M -8.347656 -0.878906 "/>
</g>
<g id="glyph-3-0">
<path d="M 6.421875 -8.460938 L 6.421875 -7.308594 C 5.964844 -7.523438 5.542969 -7.679688 5.148438 -7.789062 C 4.738281 -7.882812 4.355469 -7.945312 3.996094 -7.945312 C 3.347656 -7.945312 2.84375 -7.8125 2.496094 -7.570312 C 2.148438 -7.320312 1.980469 -6.960938 1.980469 -6.503906 C 1.980469 -6.121094 2.089844 -5.820312 2.328125 -5.628906 C 2.554688 -5.425781 3 -5.269531 3.648438 -5.148438 L 4.367188 -5.003906 C 5.242188 -4.835938 5.890625 -4.535156 6.3125 -4.117188 C 6.730469 -3.683594 6.949219 -3.121094 6.949219 -2.410156 C 6.949219 -1.558594 6.660156 -0.925781 6.097656 -0.492188 C 5.519531 -0.0585938 4.691406 0.15625 3.601562 0.15625 C 3.179688 0.15625 2.734375 0.109375 2.269531 0.0234375 C 1.800781 -0.0585938 1.320312 -0.191406 0.828125 -0.382812 L 0.828125 -1.609375 C 1.308594 -1.332031 1.777344 -1.128906 2.230469 -0.996094 C 2.6875 -0.851562 3.144531 -0.792969 3.601562 -0.792969 C 4.273438 -0.792969 4.789062 -0.925781 5.160156 -1.1875 C 5.519531 -1.453125 5.710938 -1.824219 5.710938 -2.328125 C 5.710938 -2.761719 5.578125 -3.097656 5.316406 -3.335938 C 5.050781 -3.574219 4.621094 -3.757812 4.019531 -3.875 L 3.300781 -4.019531 C 2.410156 -4.1875 1.777344 -4.464844 1.378906 -4.847656 C 0.984375 -5.21875 0.792969 -5.734375 0.792969 -6.40625 C 0.792969 -7.175781 1.054688 -7.789062 1.609375 -8.230469 C 2.148438 -8.675781 2.902344 -8.902344 3.863281 -8.902344 C 4.273438 -8.902344 4.679688 -8.867188 5.113281 -8.796875 C 5.53125 -8.722656 5.964844 -8.605469 6.421875 -8.460938 Z M 6.421875 -8.460938 "/>
</g>
<g id="glyph-3-1">
<path d="M 6.742188 -3.550781 L 6.742188 -3.023438 L 1.789062 -3.023438 C 1.835938 -2.28125 2.050781 -1.703125 2.460938 -1.320312 C 2.855469 -0.9375 3.40625 -0.742188 4.128906 -0.742188 C 4.535156 -0.742188 4.945312 -0.792969 5.328125 -0.886719 C 5.710938 -0.984375 6.109375 -1.140625 6.492188 -1.355469 L 6.492188 -0.335938 C 6.109375 -0.167969 5.710938 -0.0351562 5.304688 0.0351562 C 4.894531 0.109375 4.476562 0.15625 4.066406 0.15625 C 3.011719 0.15625 2.183594 -0.144531 1.570312 -0.742188 C 0.960938 -1.34375 0.660156 -2.171875 0.660156 -3.214844 C 0.660156 -4.285156 0.949219 -5.136719 1.523438 -5.773438 C 2.101562 -6.394531 2.890625 -6.71875 3.875 -6.71875 C 4.765625 -6.71875 5.460938 -6.433594 5.976562 -5.867188 C 6.480469 -5.292969 6.742188 -4.523438 6.742188 -3.550781 Z M 5.664062 -3.863281 C 5.652344 -4.453125 5.484375 -4.921875 5.171875 -5.28125 C 4.847656 -5.628906 4.414062 -5.808594 3.886719 -5.808594 C 3.289062 -5.808594 2.808594 -5.628906 2.449219 -5.292969 C 2.089844 -4.957031 1.871094 -4.476562 1.824219 -3.863281 Z M 5.664062 -3.863281 "/>
</g>
<g id="glyph-3-2">
<path d="M 1.128906 -9.121094 L 2.207031 -9.121094 L 2.207031 0 L 1.128906 0 Z M 1.128906 -9.121094 "/>
</g>
<g id="glyph-3-3">
<path d="M 4.453125 -9.121094 L 4.453125 -8.21875 L 3.421875 -8.21875 C 3.035156 -8.21875 2.761719 -8.136719 2.617188 -7.980469 C 2.460938 -7.824219 2.386719 -7.546875 2.386719 -7.140625 L 2.386719 -6.5625 L 4.164062 -6.5625 L 4.164062 -5.722656 L 2.386719 -5.722656 L 2.386719 0 L 1.308594 0 L 1.308594 -5.722656 L 0.277344 -5.722656 L 0.277344 -6.5625 L 1.308594 -6.5625 L 1.308594 -7.019531 C 1.308594 -7.738281 1.476562 -8.28125 1.8125 -8.617188 C 2.148438 -8.953125 2.6875 -9.121094 3.433594 -9.121094 Z M 4.453125 -9.121094 "/>
</g>
<g id="glyph-3-4">
</g>
<g id="glyph-3-5">
<path d="M 1.175781 -8.746094 L 2.941406 -8.746094 L 5.171875 -2.796875 L 7.414062 -8.746094 L 9.179688 -8.746094 L 9.179688 0 L 8.027344 0 L 8.027344 -7.679688 L 5.773438 -1.679688 L 4.585938 -1.679688 L 2.328125 -7.679688 L 2.328125 0 L 1.175781 0 Z M 1.175781 -8.746094 "/>
</g>
<g id="glyph-3-6">
<path d="M 3.671875 -5.808594 C 3.097656 -5.808594 2.640625 -5.578125 2.304688 -5.125 C 1.96875 -4.667969 1.800781 -4.054688 1.800781 -3.277344 C 1.800781 -2.484375 1.957031 -1.871094 2.292969 -1.414062 C 2.628906 -0.960938 3.085938 -0.742188 3.671875 -0.742188 C 4.246094 -0.742188 4.703125 -0.960938 5.039062 -1.414062 C 5.375 -1.871094 5.542969 -2.484375 5.542969 -3.277344 C 5.542969 -4.042969 5.375 -4.667969 5.039062 -5.125 C 4.703125 -5.578125 4.246094 -5.808594 3.671875 -5.808594 Z M 3.671875 -6.71875 C 4.609375 -6.71875 5.339844 -6.40625 5.878906 -5.808594 C 6.40625 -5.195312 6.683594 -4.355469 6.683594 -3.277344 C 6.683594 -2.195312 6.40625 -1.355469 5.878906 -0.757812 C 5.339844 -0.144531 4.609375 0.15625 3.671875 0.15625 C 2.722656 0.15625 1.980469 -0.144531 1.453125 -0.757812 C 0.925781 -1.355469 0.660156 -2.195312 0.660156 -3.277344 C 0.660156 -4.355469 0.925781 -5.195312 1.453125 -5.808594 C 1.980469 -6.40625 2.722656 -6.71875 3.671875 -6.71875 Z M 3.671875 -6.71875 "/>
</g>
<g id="glyph-3-7">
<path d="M 0.359375 -6.5625 L 1.5 -6.5625 L 3.550781 -1.054688 L 5.605469 -6.5625 L 6.742188 -6.5625 L 4.285156 0 L 2.820312 0 Z M 0.359375 -6.5625 "/>
</g>
<g id="glyph-3-8">
<path d="M 1.128906 -6.5625 L 2.207031 -6.5625 L 2.207031 0 L 1.128906 0 Z M 1.128906 -9.121094 L 2.207031 -9.121094 L 2.207031 -7.753906 L 1.128906 -7.753906 Z M 1.128906 -9.121094 "/>
</g>
<g id="glyph-3-9">
<path d="M 6.589844 -3.960938 L 6.589844 0 L 5.507812 0 L 5.507812 -3.925781 C 5.507812 -4.546875 5.375 -5.003906 5.136719 -5.316406 C 4.894531 -5.628906 4.535156 -5.785156 4.054688 -5.785156 C 3.46875 -5.785156 3.011719 -5.59375 2.675781 -5.21875 C 2.339844 -4.847656 2.171875 -4.34375 2.171875 -3.707031 L 2.171875 0 L 1.09375 0 L 1.09375 -6.5625 L 2.171875 -6.5625 L 2.171875 -5.542969 C 2.425781 -5.929688 2.722656 -6.226562 3.085938 -6.421875 C 3.433594 -6.613281 3.839844 -6.71875 4.296875 -6.71875 C 5.039062 -6.71875 5.617188 -6.480469 6 -6.011719 C 6.382812 -5.542969 6.589844 -4.859375 6.589844 -3.960938 Z M 6.589844 -3.960938 "/>
</g>
<g id="glyph-3-10">
<path d="M 5.449219 -3.359375 C 5.449219 -4.128906 5.28125 -4.738281 4.96875 -5.171875 C 4.644531 -5.605469 4.1875 -5.820312 3.613281 -5.820312 C 3.035156 -5.820312 2.578125 -5.605469 2.257812 -5.171875 C 1.933594 -4.738281 1.777344 -4.128906 1.777344 -3.359375 C 1.777344 -2.578125 1.933594 -1.980469 2.257812 -1.546875 C 2.578125 -1.117188 3.035156 -0.898438 3.613281 -0.898438 C 4.1875 -0.898438 4.644531 -1.117188 4.96875 -1.546875 C 5.28125 -1.980469 5.449219 -2.578125 5.449219 -3.359375 Z M 6.527344 -0.816406 C 6.527344 0.289062 6.277344 1.117188 5.785156 1.667969 C 5.28125 2.207031 4.523438 2.484375 3.503906 2.484375 C 3.121094 2.484375 2.773438 2.449219 2.4375 2.398438 C 2.101562 2.339844 1.765625 2.257812 1.453125 2.136719 L 1.453125 1.09375 C 1.765625 1.261719 2.074219 1.378906 2.386719 1.464844 C 2.699219 1.546875 3.011719 1.597656 3.335938 1.597656 C 4.03125 1.597656 4.558594 1.402344 4.921875 1.042969 C 5.269531 0.671875 5.449219 0.121094 5.449219 -0.625 L 5.449219 -1.152344 C 5.21875 -0.769531 4.933594 -0.480469 4.585938 -0.289062 C 4.234375 -0.0976562 3.828125 0 3.347656 0 C 2.53125 0 1.882812 -0.300781 1.390625 -0.910156 C 0.898438 -1.523438 0.660156 -2.339844 0.660156 -3.359375 C 0.660156 -4.367188 0.898438 -5.183594 1.390625 -5.796875 C 1.882812 -6.40625 2.53125 -6.71875 3.347656 -6.71875 C 3.828125 -6.71875 4.234375 -6.625 4.585938 -6.433594 C 4.933594 -6.238281 5.21875 -5.953125 5.449219 -5.566406 L 5.449219 -6.5625 L 6.527344 -6.5625 Z M 6.527344 -0.816406 "/>
</g>
<g id="glyph-3-11">
<path d="M 2.363281 -7.777344 L 2.363281 -4.488281 L 3.851562 -4.488281 C 4.402344 -4.488281 4.824219 -4.621094 5.125 -4.90625 C 5.425781 -5.195312 5.578125 -5.605469 5.578125 -6.132812 C 5.578125 -6.660156 5.425781 -7.054688 5.125 -7.34375 C 4.824219 -7.632812 4.402344 -7.777344 3.851562 -7.777344 Z M 1.175781 -8.746094 L 3.851562 -8.746094 C 4.824219 -8.746094 5.566406 -8.519531 6.070312 -8.074219 C 6.574219 -7.632812 6.828125 -6.984375 6.828125 -6.132812 C 6.828125 -5.269531 6.574219 -4.609375 6.070312 -4.175781 C 5.566406 -3.730469 4.824219 -3.515625 3.851562 -3.515625 L 2.363281 -3.515625 L 2.363281 0 L 1.175781 0 Z M 1.175781 -8.746094 "/>
</g>
<g id="glyph-3-12">
<path d="M 4.933594 -5.554688 C 4.8125 -5.628906 4.679688 -5.675781 4.535156 -5.710938 C 4.390625 -5.734375 4.234375 -5.761719 4.066406 -5.761719 C 3.457031 -5.761719 2.988281 -5.554688 2.664062 -5.160156 C 2.328125 -4.765625 2.171875 -4.199219 2.171875 -3.457031 L 2.171875 0 L 1.09375 0 L 1.09375 -6.5625 L 2.171875 -6.5625 L 2.171875 -5.542969 C 2.386719 -5.941406 2.6875 -6.238281 3.046875 -6.433594 C 3.40625 -6.625 3.851562 -6.71875 4.378906 -6.71875 C 4.453125 -6.71875 4.535156 -6.707031 4.632812 -6.707031 C 4.714844 -6.695312 4.8125 -6.683594 4.933594 -6.660156 Z M 4.933594 -5.554688 "/>
</g>
<g id="glyph-3-13">
<path d="M 2.195312 -8.425781 L 2.195312 -6.5625 L 4.414062 -6.5625 L 4.414062 -5.722656 L 2.195312 -5.722656 L 2.195312 -2.160156 C 2.195312 -1.621094 2.269531 -1.273438 2.410156 -1.128906 C 2.554688 -0.972656 2.855469 -0.898438 3.3125 -0.898438 L 4.414062 -0.898438 L 4.414062 0 L 3.3125 0 C 2.472656 0 1.894531 -0.15625 1.585938 -0.46875 C 1.273438 -0.78125 1.117188 -1.34375 1.117188 -2.160156 L 1.117188 -5.722656 L 0.324219 -5.722656 L 0.324219 -6.5625 L 1.117188 -6.5625 L 1.117188 -8.425781 Z M 2.195312 -8.425781 "/>
</g>
<g id="glyph-3-14">
<path d="M 4.117188 -3.300781 C 3.238281 -3.300781 2.640625 -3.191406 2.304688 -3 C 1.96875 -2.796875 1.800781 -2.460938 1.800781 -1.980469 C 1.800781 -1.597656 1.921875 -1.285156 2.171875 -1.066406 C 2.425781 -0.839844 2.773438 -0.730469 3.203125 -0.730469 C 3.804688 -0.730469 4.285156 -0.9375 4.644531 -1.367188 C 5.003906 -1.789062 5.183594 -2.351562 5.183594 -3.058594 L 5.183594 -3.300781 Z M 6.265625 -3.742188 L 6.265625 0 L 5.183594 0 L 5.183594 -0.996094 C 4.933594 -0.589844 4.621094 -0.300781 4.261719 -0.121094 C 3.898438 0.0585938 3.445312 0.15625 2.914062 0.15625 C 2.242188 0.15625 1.703125 -0.0234375 1.308594 -0.394531 C 0.910156 -0.769531 0.71875 -1.273438 0.71875 -1.90625 C 0.71875 -2.640625 0.960938 -3.191406 1.464844 -3.574219 C 1.957031 -3.949219 2.6875 -4.140625 3.671875 -4.140625 L 5.183594 -4.140625 L 5.183594 -4.246094 C 5.183594 -4.738281 5.015625 -5.125 4.691406 -5.398438 C 4.367188 -5.664062 3.910156 -5.808594 3.324219 -5.808594 C 2.941406 -5.808594 2.578125 -5.761719 2.21875 -5.664062 C 1.859375 -5.566406 1.523438 -5.4375 1.199219 -5.269531 L 1.199219 -6.265625 C 1.585938 -6.40625 1.96875 -6.527344 2.339844 -6.601562 C 2.710938 -6.671875 3.070312 -6.71875 3.433594 -6.71875 C 4.378906 -6.71875 5.089844 -6.46875 5.554688 -5.976562 C 6.023438 -5.484375 6.265625 -4.738281 6.265625 -3.742188 Z M 6.265625 -3.742188 "/>
</g>
<g id="glyph-3-15">
<path d="M 5.855469 -6.3125 L 5.855469 -5.304688 C 5.542969 -5.472656 5.242188 -5.59375 4.933594 -5.675781 C 4.621094 -5.761719 4.320312 -5.808594 4.007812 -5.808594 C 3.3125 -5.808594 2.761719 -5.578125 2.375 -5.136719 C 1.992188 -4.691406 1.800781 -4.066406 1.800781 -3.277344 C 1.800781 -2.472656 1.992188 -1.847656 2.375 -1.402344 C 2.761719 -0.960938 3.3125 -0.742188 4.007812 -0.742188 C 4.320312 -0.742188 4.621094 -0.78125 4.933594 -0.863281 C 5.242188 -0.949219 5.542969 -1.078125 5.855469 -1.246094 L 5.855469 -0.253906 C 5.542969 -0.109375 5.230469 0 4.921875 0.0585938 C 4.597656 0.121094 4.246094 0.15625 3.886719 0.15625 C 2.902344 0.15625 2.113281 -0.144531 1.535156 -0.769531 C 0.949219 -1.378906 0.660156 -2.21875 0.660156 -3.277344 C 0.660156 -4.34375 0.949219 -5.183594 1.535156 -5.796875 C 2.125 -6.40625 2.929688 -6.71875 3.960938 -6.71875 C 4.296875 -6.71875 4.621094 -6.683594 4.933594 -6.613281 C 5.242188 -6.539062 5.554688 -6.445312 5.855469 -6.3125 Z M 5.855469 -6.3125 "/>
</g>
<g id="glyph-3-16">
<path d="M 1.09375 -9.121094 L 2.171875 -9.121094 L 2.171875 -3.730469 L 5.386719 -6.5625 L 6.769531 -6.5625 L 3.289062 -3.492188 L 6.910156 0 L 5.507812 0 L 2.171875 -3.203125 L 2.171875 0 L 1.09375 0 Z M 1.09375 -9.121094 "/>
</g>
<g id="glyph-3-17">
<path d="M 1.402344 -1.488281 L 2.640625 -1.488281 L 2.640625 0 L 1.402344 0 Z M 1.402344 -6.203125 L 2.640625 -6.203125 L 2.640625 -4.714844 L 1.402344 -4.714844 Z M 1.402344 -6.203125 "/>
</g>
<g id="glyph-3-18">
<path d="M 3.816406 -7.96875 C 3.203125 -7.96875 2.746094 -7.667969 2.4375 -7.066406 C 2.125 -6.46875 1.980469 -5.566406 1.980469 -4.367188 C 1.980469 -3.167969 2.125 -2.269531 2.4375 -1.667969 C 2.746094 -1.066406 3.203125 -0.769531 3.816406 -0.769531 C 4.429688 -0.769531 4.882812 -1.066406 5.195312 -1.667969 C 5.496094 -2.269531 5.652344 -3.167969 5.652344 -4.367188 C 5.652344 -5.566406 5.496094 -6.46875 5.195312 -7.066406 C 4.882812 -7.667969 4.429688 -7.96875 3.816406 -7.96875 Z M 3.816406 -8.902344 C 4.789062 -8.902344 5.53125 -8.507812 6.058594 -7.738281 C 6.574219 -6.960938 6.839844 -5.832031 6.839844 -4.367188 C 6.839844 -2.890625 6.574219 -1.765625 6.058594 -0.996094 C 5.53125 -0.226562 4.789062 0.15625 3.804688 0.15625 C 2.820312 0.15625 2.074219 -0.226562 1.558594 -0.996094 C 1.042969 -1.765625 0.792969 -2.890625 0.792969 -4.367188 C 0.792969 -5.832031 1.042969 -6.960938 1.558594 -7.738281 C 2.074219 -8.507812 2.832031 -8.902344 3.816406 -8.902344 Z M 3.816406 -8.902344 "/>
</g>
<g id="glyph-3-19">
<path d="M 1.285156 -1.488281 L 2.519531 -1.488281 L 2.519531 0 L 1.285156 0 Z M 1.285156 -1.488281 "/>
</g>
<g id="glyph-3-20">
<path d="M 2.304688 -0.996094 L 6.433594 -0.996094 L 6.433594 0 L 0.875 0 L 0.875 -0.996094 C 1.320312 -1.453125 1.933594 -2.074219 2.710938 -2.867188 C 3.480469 -3.648438 3.972656 -4.152344 4.175781 -4.378906 C 4.558594 -4.800781 4.824219 -5.160156 4.96875 -5.460938 C 5.113281 -5.746094 5.195312 -6.046875 5.195312 -6.335938 C 5.195312 -6.792969 5.027344 -7.175781 4.703125 -7.464844 C 4.378906 -7.753906 3.960938 -7.90625 3.433594 -7.90625 C 3.058594 -7.90625 2.664062 -7.835938 2.257812 -7.714844 C 1.847656 -7.585938 1.402344 -7.390625 0.9375 -7.128906 L 0.9375 -8.328125 C 1.414062 -8.519531 1.859375 -8.664062 2.269531 -8.761719 C 2.675781 -8.855469 3.058594 -8.902344 3.40625 -8.902344 C 4.308594 -8.902344 5.027344 -8.675781 5.566406 -8.21875 C 6.109375 -7.765625 6.382812 -7.164062 6.382812 -6.40625 C 6.382812 -6.046875 6.3125 -5.699219 6.179688 -5.386719 C 6.046875 -5.0625 5.808594 -4.679688 5.449219 -4.246094 C 5.351562 -4.128906 5.039062 -3.804688 4.511719 -3.265625 C 3.984375 -2.722656 3.253906 -1.96875 2.304688 -0.996094 Z M 2.304688 -0.996094 "/>
</g>
<g id="glyph-3-21">
<path d="M 3.960938 -4.847656 C 3.433594 -4.847656 3.011719 -4.65625 2.699219 -4.296875 C 2.386719 -3.9375 2.230469 -3.433594 2.230469 -2.808594 C 2.230469 -2.171875 2.386719 -1.667969 2.699219 -1.308594 C 3.011719 -0.949219 3.433594 -0.769531 3.960938 -0.769531 C 4.488281 -0.769531 4.90625 -0.949219 5.21875 -1.308594 C 5.53125 -1.667969 5.6875 -2.171875 5.6875 -2.808594 C 5.6875 -3.433594 5.53125 -3.9375 5.21875 -4.296875 C 4.90625 -4.65625 4.488281 -4.847656 3.960938 -4.847656 Z M 6.3125 -8.554688 L 6.3125 -7.476562 C 6.011719 -7.621094 5.710938 -7.726562 5.410156 -7.800781 C 5.101562 -7.871094 4.800781 -7.90625 4.511719 -7.90625 C 3.71875 -7.90625 3.121094 -7.644531 2.710938 -7.117188 C 2.304688 -6.589844 2.0625 -5.785156 2.015625 -4.726562 C 2.242188 -5.0625 2.53125 -5.328125 2.878906 -5.507812 C 3.226562 -5.6875 3.613281 -5.785156 4.03125 -5.785156 C 4.90625 -5.785156 5.605469 -5.507812 6.109375 -4.980469 C 6.613281 -4.453125 6.875 -3.71875 6.875 -2.808594 C 6.875 -1.90625 6.601562 -1.1875 6.070312 -0.648438 C 5.542969 -0.109375 4.835938 0.15625 3.960938 0.15625 C 2.953125 0.15625 2.171875 -0.226562 1.644531 -0.996094 C 1.105469 -1.765625 0.839844 -2.890625 0.839844 -4.367188 C 0.839844 -5.746094 1.164062 -6.851562 1.824219 -7.667969 C 2.472656 -8.484375 3.359375 -8.902344 4.464844 -8.902344 C 4.753906 -8.902344 5.050781 -8.867188 5.363281 -8.820312 C 5.664062 -8.761719 5.976562 -8.675781 6.3125 -8.554688 Z M 6.3125 -8.554688 "/>
</g>
<g id="glyph-3-22">
<path d="M 1.488281 -0.996094 L 3.421875 -0.996094 L 3.421875 -7.667969 L 1.320312 -7.246094 L 1.320312 -8.328125 L 3.40625 -8.746094 L 4.597656 -8.746094 L 4.597656 -0.996094 L 6.527344 -0.996094 L 6.527344 0 L 1.488281 0 Z M 1.488281 -0.996094 "/>
</g>
<g id="glyph-3-23">
<path d="M 3.71875 -9.109375 C 3.191406 -8.195312 2.808594 -7.308594 2.554688 -6.433594 C 2.292969 -5.554688 2.171875 -4.667969 2.171875 -3.769531 C 2.171875 -2.855469 2.292969 -1.96875 2.554688 -1.09375 C 2.808594 -0.203125 3.191406 0.671875 3.71875 1.570312 L 2.785156 1.570312 C 2.195312 0.648438 1.753906 -0.238281 1.464844 -1.128906 C 1.175781 -2.015625 1.03125 -2.890625 1.03125 -3.769531 C 1.03125 -4.632812 1.175781 -5.507812 1.464844 -6.394531 C 1.753906 -7.285156 2.183594 -8.183594 2.785156 -9.109375 Z M 3.71875 -9.109375 "/>
</g>
<g id="glyph-3-24">
<path d="M 0.503906 -6.5625 L 1.585938 -6.5625 L 2.929688 -1.441406 L 4.273438 -6.5625 L 5.542969 -6.5625 L 6.886719 -1.441406 L 8.230469 -6.5625 L 9.3125 -6.5625 L 7.597656 0 L 6.324219 0 L 4.90625 -5.375 L 3.492188 0 L 2.21875 0 Z M 0.503906 -6.5625 "/>
</g>
<g id="glyph-3-25">
<path d="M 4.871094 -4.714844 C 5.4375 -4.597656 5.878906 -4.34375 6.191406 -3.960938 C 6.503906 -3.574219 6.671875 -3.097656 6.671875 -2.542969 C 6.671875 -1.679688 6.371094 -1.007812 5.785156 -0.539062 C 5.183594 -0.0703125 4.34375 0.15625 3.253906 0.15625 C 2.878906 0.15625 2.496094 0.121094 2.113281 0.046875 C 1.726562 -0.0117188 1.320312 -0.121094 0.910156 -0.265625 L 0.910156 -1.402344 C 1.234375 -1.210938 1.597656 -1.066406 1.992188 -0.972656 C 2.375 -0.875 2.785156 -0.828125 3.214844 -0.828125 C 3.960938 -0.828125 4.523438 -0.972656 4.90625 -1.261719 C 5.292969 -1.546875 5.496094 -1.980469 5.496094 -2.542969 C 5.496094 -3.046875 5.316406 -3.457031 4.957031 -3.742188 C 4.597656 -4.03125 4.09375 -4.1875 3.445312 -4.1875 L 2.425781 -4.1875 L 2.425781 -5.160156 L 3.492188 -5.160156 C 4.066406 -5.160156 4.511719 -5.269531 4.824219 -5.507812 C 5.136719 -5.734375 5.292969 -6.070312 5.292969 -6.515625 C 5.292969 -6.960938 5.125 -7.308594 4.8125 -7.546875 C 4.488281 -7.789062 4.03125 -7.90625 3.445312 -7.90625 C 3.121094 -7.90625 2.773438 -7.871094 2.398438 -7.800781 C 2.027344 -7.726562 1.621094 -7.621094 1.175781 -7.476562 L 1.175781 -8.53125 C 1.621094 -8.652344 2.039062 -8.746094 2.4375 -8.808594 C 2.820312 -8.867188 3.191406 -8.902344 3.550781 -8.902344 C 4.441406 -8.902344 5.148438 -8.699219 5.675781 -8.292969 C 6.203125 -7.882812 6.46875 -7.332031 6.46875 -6.636719 C 6.46875 -6.15625 6.324219 -5.746094 6.046875 -5.410156 C 5.773438 -5.074219 5.375 -4.835938 4.871094 -4.714844 Z M 4.871094 -4.714844 "/>
</g>
<g id="glyph-3-26">
<path d="M 4.535156 -7.714844 L 1.546875 -3.046875 L 4.535156 -3.046875 Z M 4.222656 -8.746094 L 5.710938 -8.746094 L 5.710938 -3.046875 L 6.960938 -3.046875 L 6.960938 -2.0625 L 5.710938 -2.0625 L 5.710938 0 L 4.535156 0 L 4.535156 -2.0625 L 0.589844 -2.0625 L 0.589844 -3.203125 Z M 4.222656 -8.746094 "/>
</g>
<g id="glyph-3-27">
<path d="M 0.960938 -9.109375 L 1.894531 -9.109375 C 2.472656 -8.183594 2.914062 -7.285156 3.203125 -6.394531 C 3.492188 -5.507812 3.648438 -4.632812 3.648438 -3.769531 C 3.648438 -2.890625 3.492188 -2.015625 3.203125 -1.128906 C 2.914062 -0.238281 2.472656 0.648438 1.894531 1.570312 L 0.960938 1.570312 C 1.476562 0.671875 1.859375 -0.203125 2.125 -1.09375 C 2.375 -1.96875 2.507812 -2.855469 2.507812 -3.769531 C 2.507812 -4.667969 2.375 -5.554688 2.125 -6.433594 C 1.859375 -7.308594 1.476562 -8.195312 0.960938 -9.109375 Z M 0.960938 -9.109375 "/>
</g>
<g id="glyph-3-28">
<path d="M 2.363281 -4.175781 L 2.363281 -0.972656 L 4.261719 -0.972656 C 4.894531 -0.972656 5.363281 -1.105469 5.675781 -1.367188 C 5.976562 -1.632812 6.132812 -2.027344 6.132812 -2.578125 C 6.132812 -3.121094 5.976562 -3.515625 5.675781 -3.78125 C 5.363281 -4.042969 4.894531 -4.175781 4.261719 -4.175781 Z M 2.363281 -7.777344 L 2.363281 -5.136719 L 4.117188 -5.136719 C 4.691406 -5.136719 5.113281 -5.242188 5.398438 -5.460938 C 5.6875 -5.675781 5.832031 -6 5.832031 -6.457031 C 5.832031 -6.886719 5.6875 -7.222656 5.398438 -7.441406 C 5.113281 -7.65625 4.691406 -7.777344 4.117188 -7.777344 Z M 1.175781 -8.746094 L 4.199219 -8.746094 C 5.101562 -8.746094 5.796875 -8.554688 6.289062 -8.183594 C 6.769531 -7.800781 7.019531 -7.273438 7.019531 -6.589844 C 7.019531 -6.046875 6.886719 -5.617188 6.636719 -5.304688 C 6.382812 -4.992188 6.023438 -4.789062 5.542969 -4.714844 C 6.121094 -4.585938 6.574219 -4.320312 6.898438 -3.925781 C 7.210938 -3.527344 7.378906 -3.035156 7.378906 -2.449219 C 7.378906 -1.65625 7.105469 -1.054688 6.574219 -0.636719 C 6.046875 -0.203125 5.292969 0 4.320312 0 L 1.175781 0 Z M 1.175781 -8.746094 "/>
</g>
<g id="glyph-3-29">
<path d="M 6.589844 -3.960938 L 6.589844 0 L 5.507812 0 L 5.507812 -3.925781 C 5.507812 -4.546875 5.375 -5.003906 5.136719 -5.316406 C 4.894531 -5.628906 4.535156 -5.785156 4.054688 -5.785156 C 3.46875 -5.785156 3.011719 -5.59375 2.675781 -5.21875 C 2.339844 -4.847656 2.171875 -4.34375 2.171875 -3.707031 L 2.171875 0 L 1.09375 0 L 1.09375 -9.121094 L 2.171875 -9.121094 L 2.171875 -5.542969 C 2.425781 -5.929688 2.722656 -6.226562 3.085938 -6.421875 C 3.433594 -6.613281 3.839844 -6.71875 4.296875 -6.71875 C 5.039062 -6.71875 5.617188 -6.480469 6 -6.011719 C 6.382812 -5.542969 6.589844 -4.859375 6.589844 -3.960938 Z M 6.589844 -3.960938 "/>
</g>
<g id="glyph-3-30">
<path d="M 1.296875 -8.746094 L 5.941406 -8.746094 L 5.941406 -7.753906 L 2.375 -7.753906 L 2.375 -5.605469 C 2.542969 -5.664062 2.722656 -5.710938 2.890625 -5.734375 C 3.058594 -5.761719 3.238281 -5.785156 3.40625 -5.785156 C 4.378906 -5.785156 5.148438 -5.507812 5.722656 -4.980469 C 6.300781 -4.441406 6.589844 -3.71875 6.589844 -2.808594 C 6.589844 -1.859375 6.289062 -1.128906 5.699219 -0.613281 C 5.113281 -0.0976562 4.285156 0.15625 3.226562 0.15625 C 2.855469 0.15625 2.484375 0.121094 2.101562 0.0703125 C 1.714844 0.0117188 1.332031 -0.0703125 0.925781 -0.203125 L 0.925781 -1.390625 C 1.273438 -1.199219 1.632812 -1.054688 2.015625 -0.960938 C 2.386719 -0.863281 2.785156 -0.828125 3.203125 -0.828125 C 3.875 -0.828125 4.414062 -0.996094 4.8125 -1.355469 C 5.195312 -1.714844 5.398438 -2.195312 5.398438 -2.808594 C 5.398438 -3.40625 5.195312 -3.886719 4.8125 -4.246094 C 4.414062 -4.609375 3.875 -4.789062 3.203125 -4.789062 C 2.890625 -4.789062 2.566406 -4.753906 2.257812 -4.679688 C 1.945312 -4.609375 1.621094 -4.5 1.296875 -4.355469 Z M 1.296875 -8.746094 "/>
</g>
<g id="glyph-3-31">
<path d="M 1.320312 -0.179688 L 1.320312 -1.261719 C 1.609375 -1.117188 1.90625 -1.007812 2.21875 -0.9375 C 2.519531 -0.863281 2.820312 -0.828125 3.121094 -0.828125 C 3.886719 -0.828125 4.488281 -1.078125 4.894531 -1.609375 C 5.304688 -2.136719 5.542969 -2.929688 5.617188 -4.007812 C 5.375 -3.671875 5.089844 -3.40625 4.753906 -3.226562 C 4.402344 -3.046875 4.019531 -2.964844 3.601562 -2.964844 C 2.710938 -2.964844 2.015625 -3.226562 1.511719 -3.757812 C 1.007812 -4.285156 0.757812 -5.003906 0.757812 -5.929688 C 0.757812 -6.816406 1.019531 -7.535156 1.546875 -8.089844 C 2.074219 -8.628906 2.785156 -8.902344 3.671875 -8.902344 C 4.679688 -8.902344 5.460938 -8.507812 5.988281 -7.738281 C 6.515625 -6.960938 6.792969 -5.832031 6.792969 -4.367188 C 6.792969 -2.976562 6.457031 -1.882812 5.808594 -1.066406 C 5.148438 -0.253906 4.273438 0.15625 3.167969 0.15625 C 2.867188 0.15625 2.566406 0.121094 2.269531 0.0703125 C 1.957031 0.0234375 1.644531 -0.0585938 1.320312 -0.179688 Z M 3.671875 -3.886719 C 4.199219 -3.886719 4.621094 -4.066406 4.933594 -4.429688 C 5.242188 -4.789062 5.398438 -5.292969 5.398438 -5.929688 C 5.398438 -6.550781 5.242188 -7.054688 4.933594 -7.414062 C 4.621094 -7.777344 4.199219 -7.96875 3.671875 -7.96875 C 3.144531 -7.96875 2.722656 -7.777344 2.410156 -7.414062 C 2.101562 -7.054688 1.945312 -6.550781 1.945312 -5.929688 C 1.945312 -5.292969 2.101562 -4.789062 2.410156 -4.429688 C 2.722656 -4.066406 3.144531 -3.886719 3.671875 -3.886719 Z M 3.671875 -3.886719 "/>
</g>
<g id="glyph-4-0">
<path d="M 4.28125 -5.640625 L 4.28125 -4.871094 C 3.976562 -5.015625 3.695312 -5.121094 3.433594 -5.191406 C 3.160156 -5.257812 2.902344 -5.296875 2.664062 -5.296875 C 2.230469 -5.296875 1.894531 -5.207031 1.664062 -5.046875 C 1.433594 -4.878906 1.320312 -4.640625 1.320312 -4.335938 C 1.320312 -4.078125 1.390625 -3.878906 1.550781 -3.753906 C 1.703125 -3.617188 2 -3.511719 2.433594 -3.433594 L 2.910156 -3.335938 C 3.496094 -3.222656 3.929688 -3.023438 4.207031 -2.742188 C 4.488281 -2.457031 4.632812 -2.078125 4.632812 -1.609375 C 4.632812 -1.039062 4.441406 -0.617188 4.0625 -0.328125 C 3.679688 -0.0390625 3.128906 0.105469 2.398438 0.105469 C 2.121094 0.105469 1.824219 0.0703125 1.511719 0.015625 C 1.199219 -0.0390625 0.878906 -0.128906 0.550781 -0.257812 L 0.550781 -1.070312 C 0.871094 -0.886719 1.183594 -0.753906 1.488281 -0.664062 C 1.792969 -0.566406 2.097656 -0.527344 2.398438 -0.527344 C 2.847656 -0.527344 3.191406 -0.617188 3.441406 -0.792969 C 3.679688 -0.96875 3.808594 -1.214844 3.808594 -1.550781 C 3.808594 -1.839844 3.71875 -2.0625 3.542969 -2.222656 C 3.367188 -2.382812 3.078125 -2.503906 2.679688 -2.585938 L 2.199219 -2.679688 C 1.609375 -2.792969 1.183594 -2.976562 0.921875 -3.230469 C 0.65625 -3.480469 0.527344 -3.824219 0.527344 -4.273438 C 0.527344 -4.785156 0.703125 -5.191406 1.070312 -5.488281 C 1.433594 -5.785156 1.9375 -5.9375 2.574219 -5.9375 C 2.847656 -5.9375 3.121094 -5.910156 3.40625 -5.863281 C 3.6875 -5.816406 3.976562 -5.734375 4.28125 -5.640625 Z M 4.28125 -5.640625 "/>
</g>
<g id="glyph-4-1">
<path d="M 1.464844 -5.617188 L 1.464844 -4.375 L 2.945312 -4.375 L 2.945312 -3.816406 L 1.464844 -3.816406 L 1.464844 -1.441406 C 1.464844 -1.078125 1.511719 -0.847656 1.609375 -0.753906 C 1.703125 -0.648438 1.902344 -0.601562 2.207031 -0.601562 L 2.945312 -0.601562 L 2.945312 0 L 2.207031 0 C 1.648438 0 1.265625 -0.105469 1.054688 -0.3125 C 0.847656 -0.519531 0.742188 -0.894531 0.742188 -1.441406 L 0.742188 -3.816406 L 0.214844 -3.816406 L 0.214844 -4.375 L 0.742188 -4.375 L 0.742188 -5.617188 Z M 1.464844 -5.617188 "/>
</g>
<g id="glyph-4-2">
<path d="M 2.742188 -2.199219 C 2.160156 -2.199219 1.761719 -2.128906 1.535156 -2 C 1.3125 -1.863281 1.199219 -1.640625 1.199219 -1.320312 C 1.199219 -1.0625 1.28125 -0.855469 1.449219 -0.710938 C 1.617188 -0.558594 1.847656 -0.488281 2.136719 -0.488281 C 2.535156 -0.488281 2.855469 -0.625 3.097656 -0.910156 C 3.335938 -1.191406 3.457031 -1.566406 3.457031 -2.039062 L 3.457031 -2.199219 Z M 4.175781 -2.496094 L 4.175781 0 L 3.457031 0 L 3.457031 -0.664062 C 3.289062 -0.390625 3.078125 -0.199219 2.839844 -0.078125 C 2.601562 0.0390625 2.296875 0.105469 1.945312 0.105469 C 1.496094 0.105469 1.136719 -0.015625 0.871094 -0.265625 C 0.609375 -0.511719 0.480469 -0.847656 0.480469 -1.273438 C 0.480469 -1.761719 0.640625 -2.128906 0.976562 -2.382812 C 1.304688 -2.632812 1.792969 -2.761719 2.449219 -2.761719 L 3.457031 -2.761719 L 3.457031 -2.832031 C 3.457031 -3.160156 3.34375 -3.414062 3.128906 -3.601562 C 2.910156 -3.777344 2.609375 -3.871094 2.214844 -3.871094 C 1.960938 -3.871094 1.71875 -3.839844 1.480469 -3.777344 C 1.238281 -3.710938 1.015625 -3.625 0.800781 -3.511719 L 0.800781 -4.175781 C 1.054688 -4.273438 1.3125 -4.351562 1.558594 -4.398438 C 1.808594 -4.449219 2.046875 -4.480469 2.289062 -4.480469 C 2.921875 -4.480469 3.390625 -4.3125 3.703125 -3.984375 C 4.015625 -3.65625 4.175781 -3.160156 4.175781 -2.496094 Z M 4.175781 -2.496094 "/>
</g>
<g id="glyph-4-3">
<path d="M 3.289062 -3.703125 C 3.207031 -3.753906 3.121094 -3.785156 3.023438 -3.808594 C 2.929688 -3.824219 2.824219 -3.839844 2.710938 -3.839844 C 2.304688 -3.839844 1.992188 -3.703125 1.777344 -3.441406 C 1.550781 -3.175781 1.449219 -2.800781 1.449219 -2.304688 L 1.449219 0 L 0.726562 0 L 0.726562 -4.375 L 1.449219 -4.375 L 1.449219 -3.695312 C 1.59375 -3.960938 1.792969 -4.160156 2.03125 -4.289062 C 2.273438 -4.414062 2.566406 -4.480469 2.921875 -4.480469 C 2.96875 -4.480469 3.023438 -4.472656 3.089844 -4.472656 C 3.144531 -4.464844 3.207031 -4.457031 3.289062 -4.441406 Z M 3.289062 -3.703125 "/>
</g>
<g id="glyph-4-4">
<path d="M 0.785156 -5.832031 L 4.472656 -5.832031 L 4.472656 -5.167969 L 1.574219 -5.167969 L 1.574219 -3.441406 L 4.351562 -3.441406 L 4.351562 -2.777344 L 1.574219 -2.777344 L 1.574219 -0.664062 L 4.542969 -0.664062 L 4.542969 0 L 0.785156 0 Z M 0.785156 -5.832031 "/>
</g>
<g id="glyph-4-5">
<path d="M 4.390625 -2.640625 L 4.390625 0 L 3.671875 0 L 3.671875 -2.617188 C 3.671875 -3.03125 3.585938 -3.335938 3.425781 -3.542969 C 3.265625 -3.753906 3.023438 -3.855469 2.703125 -3.855469 C 2.3125 -3.855469 2.007812 -3.726562 1.785156 -3.480469 C 1.558594 -3.230469 1.449219 -2.894531 1.449219 -2.472656 L 1.449219 0 L 0.726562 0 L 0.726562 -4.375 L 1.449219 -4.375 L 1.449219 -3.695312 C 1.617188 -3.953125 1.816406 -4.152344 2.054688 -4.28125 C 2.289062 -4.40625 2.558594 -4.480469 2.863281 -4.480469 C 3.359375 -4.480469 3.742188 -4.320312 4 -4.007812 C 4.257812 -3.695312 4.390625 -3.238281 4.390625 -2.640625 Z M 4.390625 -2.640625 "/>
</g>
<g id="glyph-4-6">
<path d="M 3.632812 -3.710938 L 3.632812 -6.078125 L 4.351562 -6.078125 L 4.351562 0 L 3.632812 0 L 3.632812 -0.65625 C 3.480469 -0.390625 3.289062 -0.199219 3.054688 -0.078125 C 2.824219 0.0390625 2.550781 0.105469 2.230469 0.105469 C 1.703125 0.105469 1.273438 -0.105469 0.9375 -0.519531 C 0.601562 -0.9375 0.441406 -1.496094 0.441406 -2.183594 C 0.441406 -2.871094 0.601562 -3.425781 0.9375 -3.847656 C 1.273438 -4.265625 1.703125 -4.480469 2.230469 -4.480469 C 2.550781 -4.480469 2.824219 -4.414062 3.054688 -4.289062 C 3.289062 -4.160156 3.480469 -3.96875 3.632812 -3.710938 Z M 1.183594 -2.183594 C 1.183594 -1.65625 1.289062 -1.238281 1.503906 -0.9375 C 1.71875 -0.632812 2.015625 -0.488281 2.398438 -0.488281 C 2.785156 -0.488281 3.078125 -0.632812 3.304688 -0.9375 C 3.519531 -1.238281 3.632812 -1.65625 3.632812 -2.183594 C 3.632812 -2.710938 3.519531 -3.121094 3.304688 -3.425781 C 3.078125 -3.726562 2.785156 -3.878906 2.398438 -3.878906 C 2.015625 -3.878906 1.71875 -3.726562 1.503906 -3.425781 C 1.289062 -3.121094 1.183594 -2.710938 1.183594 -2.183594 Z M 1.183594 -2.183594 "/>
</g>
<g id="glyph-5-0">
<path d="M 8.746094 1.175781 L 8.746094 2.773438 L 1.429688 6.648438 L 8.746094 6.648438 L 8.746094 7.800781 L 0 7.800781 L 0 6.203125 L 7.320312 2.328125 L 0 2.328125 L 0 1.175781 Z M 8.746094 1.175781 "/>
</g>
<g id="glyph-5-1">
<path d="M 3.550781 6.742188 L 3.023438 6.742188 L 3.023438 1.789062 C 2.28125 1.835938 1.703125 2.050781 1.320312 2.460938 C 0.9375 2.855469 0.742188 3.40625 0.742188 4.128906 C 0.742188 4.535156 0.792969 4.945312 0.886719 5.328125 C 0.984375 5.710938 1.140625 6.109375 1.355469 6.492188 L 0.335938 6.492188 C 0.167969 6.109375 0.0351562 5.710938 -0.0351562 5.304688 C -0.109375 4.894531 -0.15625 4.476562 -0.15625 4.066406 C -0.15625 3.011719 0.144531 2.183594 0.742188 1.570312 C 1.34375 0.960938 2.171875 0.660156 3.214844 0.660156 C 4.285156 0.660156 5.136719 0.949219 5.773438 1.523438 C 6.394531 2.101562 6.71875 2.890625 6.71875 3.875 C 6.71875 4.765625 6.433594 5.460938 5.867188 5.976562 C 5.292969 6.480469 4.523438 6.742188 3.550781 6.742188 Z M 3.863281 5.664062 C 4.453125 5.652344 4.921875 5.484375 5.28125 5.171875 C 5.628906 4.847656 5.808594 4.414062 5.808594 3.886719 C 5.808594 3.289062 5.628906 2.808594 5.292969 2.449219 C 4.957031 2.089844 4.476562 1.871094 3.863281 1.824219 Z M 3.863281 5.664062 "/>
</g>
<g id="glyph-5-2">
<path d="M 2.59375 1.019531 L 6.5625 1.019531 L 6.5625 2.101562 L 2.628906 2.101562 C 2.003906 2.101562 1.546875 2.21875 1.234375 2.460938 C 0.925781 2.699219 0.769531 3.058594 0.769531 3.550781 C 0.769531 4.128906 0.949219 4.597656 1.320312 4.933594 C 1.691406 5.269531 2.195312 5.4375 2.84375 5.4375 L 6.5625 5.4375 L 6.5625 6.515625 L 0 6.515625 L 0 5.4375 L 1.007812 5.4375 C 0.601562 5.171875 0.3125 4.859375 0.121094 4.523438 C -0.0585938 4.175781 -0.15625 3.78125 -0.15625 3.324219 C -0.15625 2.566406 0.0703125 1.992188 0.539062 1.609375 C 0.996094 1.210938 1.679688 1.019531 2.59375 1.019531 Z M 6.71875 3.730469 Z M 6.71875 3.730469 "/>
</g>
<g id="glyph-5-3">
<path d="M 5.554688 4.933594 C 5.628906 4.8125 5.675781 4.679688 5.710938 4.535156 C 5.734375 4.390625 5.761719 4.234375 5.761719 4.066406 C 5.761719 3.457031 5.554688 2.988281 5.160156 2.664062 C 4.765625 2.328125 4.199219 2.171875 3.457031 2.171875 L 0 2.171875 L 0 1.09375 L 6.5625 1.09375 L 6.5625 2.171875 L 5.542969 2.171875 C 5.941406 2.386719 6.238281 2.6875 6.433594 3.046875 C 6.625 3.40625 6.71875 3.851562 6.71875 4.378906 C 6.71875 4.453125 6.707031 4.535156 6.707031 4.632812 C 6.695312 4.714844 6.683594 4.8125 6.660156 4.933594 Z M 5.554688 4.933594 "/>
</g>
<g id="glyph-5-4">
<path d="M 5.808594 3.671875 C 5.808594 3.097656 5.578125 2.640625 5.125 2.304688 C 4.667969 1.96875 4.054688 1.800781 3.277344 1.800781 C 2.484375 1.800781 1.871094 1.957031 1.414062 2.292969 C 0.960938 2.628906 0.742188 3.085938 0.742188 3.671875 C 0.742188 4.246094 0.960938 4.703125 1.414062 5.039062 C 1.871094 5.375 2.484375 5.542969 3.277344 5.542969 C 4.042969 5.542969 4.667969 5.375 5.125 5.039062 C 5.578125 4.703125 5.808594 4.246094 5.808594 3.671875 Z M 6.71875 3.671875 C 6.71875 4.609375 6.40625 5.339844 5.808594 5.878906 C 5.195312 6.40625 4.355469 6.683594 3.277344 6.683594 C 2.195312 6.683594 1.355469 6.40625 0.757812 5.878906 C 0.144531 5.339844 -0.15625 4.609375 -0.15625 3.671875 C -0.15625 2.722656 0.144531 1.980469 0.757812 1.453125 C 1.355469 0.925781 2.195312 0.660156 3.277344 0.660156 C 4.355469 0.660156 5.195312 0.925781 5.808594 1.453125 C 6.40625 1.980469 6.71875 2.722656 6.71875 3.671875 Z M 6.71875 3.671875 "/>
</g>
<g id="glyph-5-5">
<path d="M 3.960938 6.589844 L 0 6.589844 L 0 5.507812 L 3.925781 5.507812 C 4.546875 5.507812 5.003906 5.375 5.316406 5.136719 C 5.628906 4.894531 5.785156 4.535156 5.785156 4.054688 C 5.785156 3.46875 5.59375 3.011719 5.21875 2.675781 C 4.847656 2.339844 4.34375 2.171875 3.707031 2.171875 L 0 2.171875 L 0 1.09375 L 6.5625 1.09375 L 6.5625 2.171875 L 5.542969 2.171875 C 5.929688 2.425781 6.226562 2.722656 6.421875 3.085938 C 6.613281 3.433594 6.71875 3.839844 6.71875 4.296875 C 6.71875 5.039062 6.480469 5.617188 6.011719 6 C 5.542969 6.382812 4.859375 6.589844 3.960938 6.589844 Z M 3.960938 6.589844 "/>
</g>
<g id="glyph-5-6">
<path d="M 3.300781 4.117188 C 3.300781 3.238281 3.191406 2.640625 3 2.304688 C 2.796875 1.96875 2.460938 1.800781 1.980469 1.800781 C 1.597656 1.800781 1.285156 1.921875 1.066406 2.171875 C 0.839844 2.425781 0.730469 2.773438 0.730469 3.203125 C 0.730469 3.804688 0.9375 4.285156 1.367188 4.644531 C 1.789062 5.003906 2.351562 5.183594 3.058594 5.183594 L 3.300781 5.183594 Z M 3.742188 6.265625 L 0 6.265625 L 0 5.183594 L 0.996094 5.183594 C 0.589844 4.933594 0.300781 4.621094 0.121094 4.261719 C -0.0585938 3.898438 -0.15625 3.445312 -0.15625 2.914062 C -0.15625 2.242188 0.0234375 1.703125 0.394531 1.308594 C 0.769531 0.910156 1.273438 0.71875 1.90625 0.71875 C 2.640625 0.71875 3.191406 0.960938 3.574219 1.464844 C 3.949219 1.957031 4.140625 2.6875 4.140625 3.671875 L 4.140625 5.183594 L 4.246094 5.183594 C 4.738281 5.183594 5.125 5.015625 5.398438 4.691406 C 5.664062 4.367188 5.808594 3.910156 5.808594 3.324219 C 5.808594 2.941406 5.761719 2.578125 5.664062 2.21875 C 5.566406 1.859375 5.4375 1.523438 5.269531 1.199219 L 6.265625 1.199219 C 6.40625 1.585938 6.527344 1.96875 6.601562 2.339844 C 6.671875 2.710938 6.71875 3.070312 6.71875 3.433594 C 6.71875 4.378906 6.46875 5.089844 5.976562 5.554688 C 5.484375 6.023438 4.738281 6.265625 3.742188 6.265625 Z M 3.742188 6.265625 "/>
</g>
<g id="glyph-5-7">
<path d="M 9.121094 1.128906 L 9.121094 2.207031 L 0 2.207031 L 0 1.128906 Z M 9.121094 1.128906 "/>
</g>
<g id="glyph-5-8">
</g>
<g id="glyph-5-9">
<path d="M 7.585938 4.105469 L 3.226562 2.496094 L 3.226562 5.710938 Z M 8.746094 3.433594 L 8.746094 4.777344 L 0 8.113281 L 0 6.875 L 2.242188 6.085938 L 2.242188 2.136719 L 0 1.34375 L 0 0.0976562 Z M 8.746094 3.433594 "/>
</g>
<g id="glyph-5-10">
<path d="M 6.3125 5.855469 L 5.304688 5.855469 C 5.472656 5.542969 5.59375 5.242188 5.675781 4.933594 C 5.761719 4.621094 5.808594 4.320312 5.808594 4.007812 C 5.808594 3.3125 5.578125 2.761719 5.136719 2.375 C 4.691406 1.992188 4.066406 1.800781 3.277344 1.800781 C 2.472656 1.800781 1.847656 1.992188 1.402344 2.375 C 0.960938 2.761719 0.742188 3.3125 0.742188 4.007812 C 0.742188 4.320312 0.78125 4.621094 0.863281 4.933594 C 0.949219 5.242188 1.078125 5.542969 1.246094 5.855469 L 0.253906 5.855469 C 0.109375 5.542969 0 5.230469 -0.0585938 4.921875 C -0.121094 4.597656 -0.15625 4.246094 -0.15625 3.886719 C -0.15625 2.902344 0.144531 2.113281 0.769531 1.535156 C 1.378906 0.949219 2.21875 0.660156 3.277344 0.660156 C 4.34375 0.660156 5.183594 0.949219 5.796875 1.535156 C 6.40625 2.125 6.71875 2.929688 6.71875 3.960938 C 6.71875 4.296875 6.683594 4.621094 6.613281 4.933594 C 6.539062 5.242188 6.445312 5.554688 6.3125 5.855469 Z M 6.3125 5.855469 "/>
</g>
<g id="glyph-5-11">
<path d="M 8.425781 2.195312 L 6.5625 2.195312 L 6.5625 4.414062 L 5.722656 4.414062 L 5.722656 2.195312 L 2.160156 2.195312 C 1.621094 2.195312 1.273438 2.269531 1.128906 2.410156 C 0.972656 2.554688 0.898438 2.855469 0.898438 3.3125 L 0.898438 4.414062 L 0 4.414062 L 0 3.3125 C 0 2.472656 0.15625 1.894531 0.46875 1.585938 C 0.78125 1.273438 1.34375 1.117188 2.160156 1.117188 L 5.722656 1.117188 L 5.722656 0.324219 L 6.5625 0.324219 L 6.5625 1.117188 L 8.425781 1.117188 Z M 8.425781 2.195312 "/>
</g>
<g id="glyph-5-12">
<path d="M 6.5625 1.128906 L 6.5625 2.207031 L 0 2.207031 L 0 1.128906 Z M 9.121094 1.128906 L 9.121094 2.207031 L 7.753906 2.207031 L 7.753906 1.128906 Z M 9.121094 1.128906 "/>
</g>
<g id="glyph-5-13">
<path d="M 6.5625 0.359375 L 6.5625 1.5 L 1.054688 3.550781 L 6.5625 5.605469 L 6.5625 6.742188 L 0 4.285156 L 0 2.820312 Z M 6.5625 0.359375 "/>
</g>
<g id="glyph-6-0">
<path d="M 1.472656 -11.664062 L 4.832031 -11.664062 L 9.070312 -3.664062 L 9.070312 -11.664062 L 11.921875 -11.664062 L 11.921875 0 L 8.558594 0 L 4.320312 -8 L 4.320312 0 L 1.472656 0 Z M 1.472656 -11.664062 "/>
</g>
<g id="glyph-6-1">
<path d="M 10.078125 -4.398438 L 10.078125 -3.601562 L 3.535156 -3.601562 C 3.601562 -2.945312 3.839844 -2.449219 4.257812 -2.128906 C 4.65625 -1.792969 5.214844 -1.632812 5.953125 -1.632812 C 6.527344 -1.632812 7.136719 -1.710938 7.761719 -1.886719 C 8.367188 -2.0625 9.007812 -2.335938 9.664062 -2.6875 L 9.664062 -0.527344 C 8.992188 -0.273438 8.335938 -0.0976562 7.679688 0.015625 C 7.007812 0.144531 6.351562 0.207031 5.695312 0.207031 C 4.113281 0.207031 2.878906 -0.191406 2 -0.992188 C 1.121094 -1.792969 0.6875 -2.910156 0.6875 -4.367188 C 0.6875 -5.792969 1.121094 -6.910156 1.984375 -7.726562 C 2.847656 -8.542969 4.03125 -8.960938 5.535156 -8.960938 C 6.910156 -8.960938 8 -8.542969 8.832031 -7.710938 C 9.664062 -6.878906 10.078125 -5.777344 10.078125 -4.398438 Z M 7.199219 -5.328125 C 7.199219 -5.855469 7.039062 -6.289062 6.734375 -6.609375 C 6.414062 -6.929688 6.015625 -7.105469 5.519531 -7.105469 C 4.976562 -7.105469 4.527344 -6.945312 4.191406 -6.640625 C 3.855469 -6.335938 3.648438 -5.902344 3.566406 -5.328125 Z M 7.199219 -5.328125 "/>
</g>
<g id="glyph-6-2">
<path d="M 1.246094 -3.40625 L 1.246094 -8.753906 L 4.0625 -8.753906 L 4.0625 -7.871094 C 4.0625 -7.390625 4.046875 -6.800781 4.046875 -6.078125 C 4.046875 -5.359375 4.046875 -4.878906 4.046875 -4.65625 C 4.046875 -3.953125 4.0625 -3.441406 4.097656 -3.136719 C 4.128906 -2.816406 4.191406 -2.59375 4.289062 -2.464844 C 4.398438 -2.273438 4.542969 -2.128906 4.734375 -2.03125 C 4.910156 -1.9375 5.121094 -1.886719 5.375 -1.886719 C 5.953125 -1.886719 6.414062 -2.113281 6.753906 -2.558594 C 7.070312 -3.007812 7.246094 -3.632812 7.246094 -4.433594 L 7.246094 -8.753906 L 10.046875 -8.753906 L 10.046875 0 L 7.246094 0 L 7.246094 -1.265625 C 6.832031 -0.753906 6.382812 -0.367188 5.902344 -0.144531 C 5.425781 0.078125 4.910156 0.207031 4.351562 0.207031 C 3.328125 0.207031 2.558594 -0.0976562 2.03125 -0.703125 C 1.503906 -1.3125 1.246094 -2.222656 1.246094 -3.40625 Z M 1.246094 -3.40625 "/>
</g>
<g id="glyph-6-3">
<path d="M 7.839844 -6.367188 C 7.585938 -6.480469 7.34375 -6.558594 7.105469 -6.625 C 6.863281 -6.671875 6.625 -6.703125 6.382812 -6.703125 C 5.664062 -6.703125 5.105469 -6.464844 4.71875 -6.015625 C 4.335938 -5.550781 4.144531 -4.894531 4.144531 -4.03125 L 4.144531 0 L 1.34375 0 L 1.34375 -8.753906 L 4.144531 -8.753906 L 4.144531 -7.3125 C 4.496094 -7.886719 4.894531 -8.304688 5.375 -8.558594 C 5.839844 -8.816406 6.398438 -8.960938 7.054688 -8.960938 C 7.152344 -8.960938 7.246094 -8.945312 7.359375 -8.945312 C 7.457031 -8.945312 7.617188 -8.929688 7.839844 -8.894531 Z M 7.839844 -6.367188 "/>
</g>
<g id="glyph-6-4">
<path d="M 5.503906 -6.960938 C 4.878906 -6.960938 4.398438 -6.734375 4.078125 -6.289062 C 3.761719 -5.839844 3.601562 -5.199219 3.601562 -4.367188 C 3.601562 -3.519531 3.761719 -2.878906 4.078125 -2.433594 C 4.398438 -1.984375 4.878906 -1.777344 5.503906 -1.777344 C 6.113281 -1.777344 6.574219 -1.984375 6.894531 -2.433594 C 7.214844 -2.878906 7.390625 -3.519531 7.390625 -4.367188 C 7.390625 -5.199219 7.214844 -5.839844 6.894531 -6.289062 C 6.574219 -6.734375 6.113281 -6.960938 5.503906 -6.960938 Z M 5.503906 -8.960938 C 7.007812 -8.960938 8.175781 -8.542969 9.023438 -7.742188 C 9.871094 -6.929688 10.304688 -5.808594 10.304688 -4.367188 C 10.304688 -2.929688 9.871094 -1.792969 9.023438 -0.992188 C 8.175781 -0.191406 7.007812 0.207031 5.503906 0.207031 C 4 0.207031 2.816406 -0.191406 1.96875 -0.992188 C 1.105469 -1.792969 0.6875 -2.929688 0.6875 -4.367188 C 0.6875 -5.808594 1.105469 -6.929688 1.96875 -7.742188 C 2.816406 -8.542969 4 -8.960938 5.503906 -8.960938 Z M 5.503906 -8.960938 "/>
</g>
<g id="glyph-6-5">
<path d="M 10.144531 -5.328125 L 10.144531 0 L 7.328125 0 L 7.328125 -4.078125 C 7.328125 -4.832031 7.3125 -5.34375 7.28125 -5.632812 C 7.246094 -5.921875 7.183594 -6.128906 7.105469 -6.273438 C 6.992188 -6.449219 6.847656 -6.59375 6.65625 -6.6875 C 6.464844 -6.785156 6.238281 -6.847656 6.015625 -6.847656 C 5.425781 -6.847656 4.960938 -6.625 4.640625 -6.175781 C 4.304688 -5.726562 4.144531 -5.105469 4.144531 -4.304688 L 4.144531 0 L 1.34375 0 L 1.34375 -8.753906 L 4.144531 -8.753906 L 4.144531 -7.472656 C 4.558594 -7.984375 4.992188 -8.351562 5.472656 -8.59375 C 5.953125 -8.832031 6.480469 -8.960938 7.054688 -8.960938 C 8.0625 -8.960938 8.832031 -8.640625 9.359375 -8.03125 C 9.871094 -7.40625 10.144531 -6.511719 10.144531 -5.328125 Z M 10.144531 -5.328125 "/>
</g>
<g id="glyph-6-6">
</g>
<g id="glyph-6-7">
<path d="M 1.871094 -2.078125 L 4.527344 -2.078125 L 4.527344 -9.617188 L 1.808594 -9.054688 L 1.808594 -11.105469 L 4.511719 -11.664062 L 7.375 -11.664062 L 7.375 -2.078125 L 10.03125 -2.078125 L 10.03125 0 L 1.871094 0 Z M 1.871094 -2.078125 "/>
</g>
<g id="glyph-6-8">
<path d="M 1.601562 -0.257812 L 1.601562 -2.414062 C 2.078125 -2.191406 2.527344 -2.015625 2.976562 -1.902344 C 3.40625 -1.792969 3.839844 -1.742188 4.273438 -1.742188 C 5.167969 -1.742188 5.855469 -1.984375 6.367188 -2.480469 C 6.863281 -2.976562 7.152344 -3.710938 7.246094 -4.703125 C 6.894531 -4.449219 6.511719 -4.238281 6.113281 -4.113281 C 5.710938 -3.984375 5.28125 -3.921875 4.816406 -3.921875 C 3.617188 -3.921875 2.65625 -4.257812 1.921875 -4.960938 C 1.183594 -5.648438 0.832031 -6.558594 0.832031 -7.710938 C 0.832031 -8.960938 1.230469 -9.96875 2.046875 -10.71875 C 2.863281 -11.472656 3.953125 -11.855469 5.328125 -11.855469 C 6.847656 -11.855469 8.03125 -11.34375 8.863281 -10.320312 C 9.695312 -9.296875 10.113281 -7.839844 10.113281 -5.96875 C 10.113281 -4.03125 9.617188 -2.511719 8.640625 -1.425781 C 7.664062 -0.335938 6.335938 0.207031 4.640625 0.207031 C 4.097656 0.207031 3.566406 0.160156 3.070312 0.0976562 C 2.558594 0.015625 2.078125 -0.0976562 1.601562 -0.257812 Z M 5.3125 -5.871094 C 5.824219 -5.871094 6.222656 -6.03125 6.496094 -6.382812 C 6.753906 -6.71875 6.894531 -7.230469 6.894531 -7.921875 C 6.894531 -8.59375 6.753906 -9.105469 6.496094 -9.457031 C 6.222656 -9.792969 5.824219 -9.96875 5.3125 -9.96875 C 4.785156 -9.96875 4.382812 -9.792969 4.128906 -9.457031 C 3.855469 -9.105469 3.726562 -8.59375 3.726562 -7.921875 C 3.726562 -7.230469 3.855469 -6.71875 4.128906 -6.382812 C 4.382812 -6.03125 4.785156 -5.871094 5.3125 -5.871094 Z M 5.3125 -5.871094 "/>
</g>
<g id="glyph-6-9">
<path d="M 5.566406 -5.214844 C 4.992188 -5.214844 4.558594 -5.054688 4.273438 -4.753906 C 3.96875 -4.449219 3.824219 -4.015625 3.824219 -3.441406 C 3.824219 -2.863281 3.96875 -2.414062 4.273438 -2.113281 C 4.558594 -1.808594 4.992188 -1.664062 5.566406 -1.664062 C 6.113281 -1.664062 6.542969 -1.808594 6.847656 -2.113281 C 7.136719 -2.414062 7.296875 -2.863281 7.296875 -3.441406 C 7.296875 -4.015625 7.136719 -4.449219 6.847656 -4.753906 C 6.542969 -5.054688 6.113281 -5.214844 5.566406 -5.214844 Z M 3.375 -6.207031 C 2.671875 -6.414062 2.128906 -6.734375 1.777344 -7.183594 C 1.40625 -7.632812 1.230469 -8.175781 1.230469 -8.847656 C 1.230469 -9.839844 1.601562 -10.59375 2.335938 -11.105469 C 3.070312 -11.617188 4.144531 -11.871094 5.566406 -11.871094 C 6.976562 -11.871094 8.046875 -11.617188 8.785156 -11.105469 C 9.519531 -10.59375 9.886719 -9.839844 9.886719 -8.847656 C 9.886719 -8.175781 9.695312 -7.632812 9.34375 -7.183594 C 8.976562 -6.734375 8.449219 -6.414062 7.742188 -6.207031 C 8.527344 -5.984375 9.121094 -5.632812 9.535156 -5.136719 C 9.9375 -4.640625 10.144531 -4.015625 10.144531 -3.28125 C 10.144531 -2.128906 9.761719 -1.246094 8.992188 -0.671875 C 8.222656 -0.078125 7.070312 0.207031 5.566406 0.207031 C 4.046875 0.207031 2.894531 -0.078125 2.128906 -0.671875 C 1.359375 -1.246094 0.976562 -2.128906 0.976562 -3.28125 C 0.976562 -4.015625 1.167969 -4.640625 1.585938 -5.136719 C 1.984375 -5.632812 2.574219 -5.984375 3.375 -6.207031 Z M 4.078125 -8.542969 C 4.078125 -8.078125 4.207031 -7.726562 4.464844 -7.472656 C 4.71875 -7.214844 5.089844 -7.105469 5.566406 -7.105469 C 6.03125 -7.105469 6.398438 -7.214844 6.65625 -7.472656 C 6.910156 -7.726562 7.039062 -8.078125 7.039062 -8.542969 C 7.039062 -9.007812 6.910156 -9.359375 6.65625 -9.617188 C 6.398438 -9.855469 6.03125 -9.984375 5.566406 -9.984375 C 5.089844 -9.984375 4.71875 -9.855469 4.464844 -9.601562 C 4.207031 -9.34375 4.078125 -8.992188 4.078125 -8.542969 Z M 4.078125 -8.542969 "/>
</g>
<g id="glyph-6-10">
<path d="M 0.863281 -5.742188 L 5.777344 -5.742188 L 5.777344 -3.472656 L 0.863281 -3.472656 Z M 0.863281 -5.742188 "/>
</g>
<g id="glyph-6-11">
<path d="M 1.472656 -11.664062 L 6.464844 -11.664062 C 7.9375 -11.664062 9.070312 -11.328125 9.871094 -10.671875 C 10.671875 -10 11.070312 -9.070312 11.070312 -7.855469 C 11.070312 -6.625 10.671875 -5.679688 9.871094 -5.023438 C 9.070312 -4.367188 7.9375 -4.046875 6.464844 -4.046875 L 4.480469 -4.046875 L 4.480469 0 L 1.472656 0 Z M 4.480469 -9.488281 L 4.480469 -6.222656 L 6.144531 -6.222656 C 6.71875 -6.222656 7.167969 -6.351562 7.488281 -6.640625 C 7.808594 -6.929688 7.96875 -7.328125 7.96875 -7.855469 C 7.96875 -8.367188 7.808594 -8.769531 7.488281 -9.054688 C 7.167969 -9.34375 6.71875 -9.488281 6.144531 -9.488281 Z M 4.480469 -9.488281 "/>
</g>
<g id="glyph-6-12">
<path d="M 9.585938 -11.296875 L 9.585938 -8.832031 C 8.945312 -9.121094 8.320312 -9.328125 7.710938 -9.472656 C 7.105469 -9.617188 6.527344 -9.695312 5.984375 -9.695312 C 5.265625 -9.695312 4.734375 -9.585938 4.382812 -9.390625 C 4.03125 -9.199219 3.871094 -8.894531 3.871094 -8.480469 C 3.871094 -8.160156 3.984375 -7.902344 4.222656 -7.742188 C 4.449219 -7.566406 4.878906 -7.425781 5.488281 -7.296875 L 6.769531 -7.039062 C 8.046875 -6.785156 8.976562 -6.382812 9.519531 -5.855469 C 10.0625 -5.3125 10.351562 -4.558594 10.351562 -3.585938 C 10.351562 -2.304688 9.96875 -1.34375 9.199219 -0.71875 C 8.433594 -0.0976562 7.28125 0.207031 5.710938 0.207031 C 4.976562 0.207031 4.222656 0.128906 3.488281 0 C 2.734375 -0.128906 1.984375 -0.320312 1.246094 -0.609375 L 1.246094 -3.152344 C 1.984375 -2.753906 2.703125 -2.449219 3.40625 -2.257812 C 4.097656 -2.046875 4.769531 -1.953125 5.425781 -1.953125 C 6.0625 -1.953125 6.574219 -2.046875 6.929688 -2.273438 C 7.28125 -2.496094 7.457031 -2.800781 7.457031 -3.214844 C 7.457031 -3.566406 7.328125 -3.855469 7.089844 -4.046875 C 6.847656 -4.238281 6.382812 -4.433594 5.679688 -4.59375 L 4.511719 -4.847656 C 3.34375 -5.105469 2.496094 -5.503906 1.953125 -6.046875 C 1.40625 -6.59375 1.152344 -7.328125 1.152344 -8.257812 C 1.152344 -9.40625 1.519531 -10.304688 2.273438 -10.929688 C 3.007812 -11.550781 4.097656 -11.871094 5.503906 -11.871094 C 6.144531 -11.871094 6.800781 -11.824219 7.472656 -11.726562 C 8.144531 -11.632812 8.847656 -11.488281 9.585938 -11.296875 Z M 9.585938 -11.296875 "/>
</g>
<g id="glyph-6-13">
<path d="M 10.71875 -0.640625 C 10.160156 -0.351562 9.585938 -0.128906 8.992188 0 C 8.382812 0.128906 7.761719 0.207031 7.121094 0.207031 C 5.167969 0.207031 3.632812 -0.320312 2.496094 -1.40625 C 1.359375 -2.480469 0.800781 -3.953125 0.800781 -5.824219 C 0.800781 -7.679688 1.359375 -9.152344 2.496094 -10.238281 C 3.632812 -11.328125 5.167969 -11.871094 7.121094 -11.871094 C 7.761719 -11.871094 8.382812 -11.792969 8.992188 -11.648438 C 9.585938 -11.503906 10.160156 -11.296875 10.71875 -11.007812 L 10.71875 -8.59375 C 10.160156 -8.976562 9.617188 -9.246094 9.070312 -9.425781 C 8.527344 -9.601562 7.953125 -9.695312 7.359375 -9.695312 C 6.273438 -9.695312 5.425781 -9.34375 4.816406 -8.65625 C 4.207031 -7.96875 3.902344 -7.023438 3.902344 -5.824219 C 3.902344 -4.609375 4.207031 -3.664062 4.816406 -2.976562 C 5.425781 -2.289062 6.273438 -1.953125 7.359375 -1.953125 C 7.953125 -1.953125 8.527344 -2.03125 9.070312 -2.207031 C 9.617188 -2.382812 10.160156 -2.671875 10.71875 -3.054688 Z M 10.71875 -0.640625 "/>
</g>
</g>
<clipPath id="clip-0">
<path clip-rule="nonzero" d="M 42.058594 58.226562 L 265.789062 58.226562 L 265.789062 281.957031 L 42.058594 281.957031 Z M 42.058594 58.226562 "/>
</clipPath>
<image id="source-5" x="0" y="0" width="933" height="933" xlink:href="data:image/png;base64,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"/>
<clipPath id="clip-1">
<path clip-rule="nonzero" d="M 42.058594 58.226562 L 43 58.226562 L 43 281.957031 L 42.058594 281.957031 Z M 42.058594 58.226562 "/>
</clipPath>
<clipPath id="clip-2">
<path clip-rule="nonzero" d="M 86 58.226562 L 88 58.226562 L 88 281.957031 L 86 281.957031 Z M 86 58.226562 "/>
</clipPath>
<clipPath id="clip-3">
<path clip-rule="nonzero" d="M 131 58.226562 L 132 58.226562 L 132 281.957031 L 131 281.957031 Z M 131 58.226562 "/>
</clipPath>
<clipPath id="clip-4">
<path clip-rule="nonzero" d="M 175 58.226562 L 177 58.226562 L 177 281.957031 L 175 281.957031 Z M 175 58.226562 "/>
</clipPath>
<clipPath id="clip-5">
<path clip-rule="nonzero" d="M 220 58.226562 L 222 58.226562 L 222 281.957031 L 220 281.957031 Z M 220 58.226562 "/>
</clipPath>
<clipPath id="clip-6">
<path clip-rule="nonzero" d="M 265 58.226562 L 265.789062 58.226562 L 265.789062 281.957031 L 265 281.957031 Z M 265 58.226562 "/>
</clipPath>
<clipPath id="clip-7">
<path clip-rule="nonzero" d="M 42.058594 281 L 265.789062 281 L 265.789062 281.957031 L 42.058594 281.957031 Z M 42.058594 281 "/>
</clipPath>
<clipPath id="clip-8">
<path clip-rule="nonzero" d="M 42.058594 236 L 265.789062 236 L 265.789062 238 L 42.058594 238 Z M 42.058594 236 "/>
</clipPath>
<clipPath id="clip-9">
<path clip-rule="nonzero" d="M 42.058594 192 L 265.789062 192 L 265.789062 193 L 42.058594 193 Z M 42.058594 192 "/>
</clipPath>
<clipPath id="clip-10">
<path clip-rule="nonzero" d="M 42.058594 147 L 265.789062 147 L 265.789062 149 L 42.058594 149 Z M 42.058594 147 "/>
</clipPath>
<clipPath id="clip-11">
<path clip-rule="nonzero" d="M 42.058594 102 L 265.789062 102 L 265.789062 104 L 42.058594 104 Z M 42.058594 102 "/>
</clipPath>
<clipPath id="clip-12">
<path clip-rule="nonzero" d="M 42.058594 58.226562 L 265.789062 58.226562 L 265.789062 59 L 42.058594 59 Z M 42.058594 58.226562 "/>
</clipPath>
<clipPath id="clip-13">
<path clip-rule="nonzero" d="M 322 58.226562 L 545.453125 58.226562 L 545.453125 281.957031 L 322 281.957031 Z M 322 58.226562 "/>
</clipPath>
<image id="source-77" x="0" y="0" width="933" height="933" xlink:href="data:image/png;base64,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"/>
<clipPath id="clip-14">
<path clip-rule="nonzero" d="M 321.722656 58.226562 L 323 58.226562 L 323 281.957031 L 321.722656 281.957031 Z M 321.722656 58.226562 "/>
</clipPath>
<clipPath id="clip-15">
<path clip-rule="nonzero" d="M 366 58.226562 L 367 58.226562 L 367 281.957031 L 366 281.957031 Z M 366 58.226562 "/>
</clipPath>
<clipPath id="clip-16">
<path clip-rule="nonzero" d="M 410 58.226562 L 412 58.226562 L 412 281.957031 L 410 281.957031 Z M 410 58.226562 "/>
</clipPath>
<clipPath id="clip-17">
<path clip-rule="nonzero" d="M 455 58.226562 L 457 58.226562 L 457 281.957031 L 455 281.957031 Z M 455 58.226562 "/>
</clipPath>
<clipPath id="clip-18">
<path clip-rule="nonzero" d="M 500 58.226562 L 502 58.226562 L 502 281.957031 L 500 281.957031 Z M 500 58.226562 "/>
</clipPath>
<clipPath id="clip-19">
<path clip-rule="nonzero" d="M 545 58.226562 L 545.453125 58.226562 L 545.453125 281.957031 L 545 281.957031 Z M 545 58.226562 "/>
</clipPath>
<clipPath id="clip-20">
<path clip-rule="nonzero" d="M 321.722656 281 L 545.453125 281 L 545.453125 281.957031 L 321.722656 281.957031 Z M 321.722656 281 "/>
</clipPath>
<clipPath id="clip-21">
<path clip-rule="nonzero" d="M 321.722656 236 L 545.453125 236 L 545.453125 238 L 321.722656 238 Z M 321.722656 236 "/>
</clipPath>
<clipPath id="clip-22">
<path clip-rule="nonzero" d="M 321.722656 192 L 545.453125 192 L 545.453125 193 L 321.722656 193 Z M 321.722656 192 "/>
</clipPath>
<clipPath id="clip-23">
<path clip-rule="nonzero" d="M 321.722656 147 L 545.453125 147 L 545.453125 149 L 321.722656 149 Z M 321.722656 147 "/>
</clipPath>
<clipPath id="clip-24">
<path clip-rule="nonzero" d="M 321.722656 102 L 545.453125 102 L 545.453125 104 L 321.722656 104 Z M 321.722656 102 "/>
</clipPath>
<clipPath id="clip-25">
<path clip-rule="nonzero" d="M 321.722656 58.226562 L 545.453125 58.226562 L 545.453125 59 L 321.722656 59 Z M 321.722656 58.226562 "/>
</clipPath>
<clipPath id="clip-26">
<path clip-rule="nonzero" d="M 601.386719 58.226562 L 825 58.226562 L 825 281.957031 L 601.386719 281.957031 Z M 601.386719 58.226562 "/>
</clipPath>
<image id="source-86" x="0" y="0" width="933" height="933" xlink:href="data:image/png;base64,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"/>
<clipPath id="clip-27">
<path clip-rule="nonzero" d="M 601.386719 58.226562 L 602 58.226562 L 602 281.957031 L 601.386719 281.957031 Z M 601.386719 58.226562 "/>
</clipPath>
<clipPath id="clip-28">
<path clip-rule="nonzero" d="M 645 58.226562 L 647 58.226562 L 647 281.957031 L 645 281.957031 Z M 645 58.226562 "/>
</clipPath>
<clipPath id="clip-29">
<path clip-rule="nonzero" d="M 690 58.226562 L 692 58.226562 L 692 281.957031 L 690 281.957031 Z M 690 58.226562 "/>
</clipPath>
<clipPath id="clip-30">
<path clip-rule="nonzero" d="M 735 58.226562 L 737 58.226562 L 737 281.957031 L 735 281.957031 Z M 735 58.226562 "/>
</clipPath>
<clipPath id="clip-31">
<path clip-rule="nonzero" d="M 779 58.226562 L 781 58.226562 L 781 281.957031 L 779 281.957031 Z M 779 58.226562 "/>
</clipPath>
<clipPath id="clip-32">
<path clip-rule="nonzero" d="M 824 58.226562 L 825.117188 58.226562 L 825.117188 281.957031 L 824 281.957031 Z M 824 58.226562 "/>
</clipPath>
<clipPath id="clip-33">
<path clip-rule="nonzero" d="M 601.386719 281 L 825.117188 281 L 825.117188 281.957031 L 601.386719 281.957031 Z M 601.386719 281 "/>
</clipPath>
<clipPath id="clip-34">
<path clip-rule="nonzero" d="M 601.386719 236 L 825.117188 236 L 825.117188 238 L 601.386719 238 Z M 601.386719 236 "/>
</clipPath>
<clipPath id="clip-35">
<path clip-rule="nonzero" d="M 601.386719 192 L 825.117188 192 L 825.117188 193 L 601.386719 193 Z M 601.386719 192 "/>
</clipPath>
<clipPath id="clip-36">
<path clip-rule="nonzero" d="M 601.386719 147 L 825.117188 147 L 825.117188 149 L 601.386719 149 Z M 601.386719 147 "/>
</clipPath>
<clipPath id="clip-37">
<path clip-rule="nonzero" d="M 601.386719 102 L 825.117188 102 L 825.117188 104 L 601.386719 104 Z M 601.386719 102 "/>
</clipPath>
<clipPath id="clip-38">
<path clip-rule="nonzero" d="M 601.386719 58.226562 L 825.117188 58.226562 L 825.117188 59 L 601.386719 59 Z M 601.386719 58.226562 "/>
</clipPath>
<clipPath id="clip-39">
<path clip-rule="nonzero" d="M 881.050781 58.226562 L 1104.78125 58.226562 L 1104.78125 281.957031 L 881.050781 281.957031 Z M 881.050781 58.226562 "/>
</clipPath>
<image id="source-93" x="0" y="0" width="933" height="933" xlink:href="data:image/png;base64,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"/>
<clipPath id="clip-40">
<path clip-rule="nonzero" d="M 881.050781 58.226562 L 882 58.226562 L 882 281.957031 L 881.050781 281.957031 Z M 881.050781 58.226562 "/>
</clipPath>
<clipPath id="clip-41">
<path clip-rule="nonzero" d="M 925 58.226562 L 927 58.226562 L 927 281.957031 L 925 281.957031 Z M 925 58.226562 "/>
</clipPath>
<clipPath id="clip-42">
<path clip-rule="nonzero" d="M 970 58.226562 L 971 58.226562 L 971 281.957031 L 970 281.957031 Z M 970 58.226562 "/>
</clipPath>
<clipPath id="clip-43">
<path clip-rule="nonzero" d="M 1014 58.226562 L 1016 58.226562 L 1016 281.957031 L 1014 281.957031 Z M 1014 58.226562 "/>
</clipPath>
<clipPath id="clip-44">
<path clip-rule="nonzero" d="M 1059 58.226562 L 1061 58.226562 L 1061 281.957031 L 1059 281.957031 Z M 1059 58.226562 "/>
</clipPath>
<clipPath id="clip-45">
<path clip-rule="nonzero" d="M 1104 58.226562 L 1104.78125 58.226562 L 1104.78125 281.957031 L 1104 281.957031 Z M 1104 58.226562 "/>
</clipPath>
<clipPath id="clip-46">
<path clip-rule="nonzero" d="M 881.050781 281 L 1104.78125 281 L 1104.78125 281.957031 L 881.050781 281.957031 Z M 881.050781 281 "/>
</clipPath>
<clipPath id="clip-47">
<path clip-rule="nonzero" d="M 881.050781 236 L 1104.78125 236 L 1104.78125 238 L 881.050781 238 Z M 881.050781 236 "/>
</clipPath>
<clipPath id="clip-48">
<path clip-rule="nonzero" d="M 881.050781 192 L 1104.78125 192 L 1104.78125 193 L 881.050781 193 Z M 881.050781 192 "/>
</clipPath>
<clipPath id="clip-49">
<path clip-rule="nonzero" d="M 881.050781 147 L 1104.78125 147 L 1104.78125 149 L 881.050781 149 Z M 881.050781 147 "/>
</clipPath>
<clipPath id="clip-50">
<path clip-rule="nonzero" d="M 881.050781 102 L 1104.78125 102 L 1104.78125 104 L 881.050781 104 Z M 881.050781 102 "/>
</clipPath>
<clipPath id="clip-51">
<path clip-rule="nonzero" d="M 881.050781 58.226562 L 1104.78125 58.226562 L 1104.78125 59 L 881.050781 59 Z M 881.050781 58.226562 "/>
</clipPath>
<image id="source-96" x="0" y="0" width="108" height="1050" xlink:href="data:image/png;base64,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"/>
</defs>
<path fill-rule="nonzero" fill="rgb(100%, 100%, 100%)" fill-opacity="1" d="M 0 320.96875 L 1204.726562 320.96875 L 1204.726562 0 L 0 0 Z M 0 320.96875 "/>
<path fill-rule="nonzero" fill="rgb(100%, 100%, 100%)" fill-opacity="1" d="M 42.058594 281.957031 L 265.789062 281.957031 L 265.789062 58.226562 L 42.058594 58.226562 Z M 42.058594 281.957031 "/>
<g clip-path="url(#clip-0)">
<use xlink:href="#source-5" transform="matrix(0.24, 0, 0, 0.24, 42.059375, 58.035526)"/>
</g>
<path fill="none" stroke-width="1" stroke-linecap="square" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="0.8" stroke-miterlimit="10" d="M 144.730469 79.163339 L 145.011719 80.999276 L 145.445312 82.909433 L 146.011719 84.885995 L 146.683594 86.917245 L 147.4375 88.991464 L 148.25 91.093026 L 149.09375 93.206308 L 149.9375 95.323495 L 150.757812 97.428964 L 151.519531 99.507089 L 152.203125 101.542245 L 152.78125 103.52662 L 153.226562 105.444589 L 153.527344 107.288339 L 153.65625 109.050058 L 153.605469 110.718026 L 153.359375 112.292245 L 152.914062 113.760995 L 152.257812 115.128183 L 151.398438 116.393808 L 150.332031 117.55787 L 149.070312 118.62037 L 147.625 119.593026 L 146.007812 120.479745 L 144.238281 121.288339 L 142.332031 122.030526 L 140.3125 122.718026 L 138.210938 123.358651 L 136.046875 123.971933 L 133.851562 124.569589 L 131.648438 125.163339 L 129.46875 125.768808 L 127.34375 126.40162 L 125.292969 127.069589 L 123.347656 127.792245 L 121.527344 128.577401 L 119.851562 129.436776 L 118.34375 130.374276 L 117.019531 131.409433 L 115.886719 132.534433 L 114.953125 133.764901 L 114.226562 135.096933 L 113.707031 136.530526 L 113.390625 138.069589 L 113.273438 139.706308 L 113.34375 141.43287 L 113.585938 143.249276 L 113.984375 145.143808 L 114.519531 147.108651 L 115.167969 149.128183 L 115.90625 151.190683 L 116.707031 153.288339 L 117.546875 155.40162 L 118.394531 157.518808 L 119.21875 159.628183 L 120 161.710214 L 120.703125 163.757089 L 121.308594 165.757089 L 121.789062 167.690683 L 122.125 169.553964 L 122.296875 171.335214 L 122.289062 173.02662 L 122.089844 174.62037 L 121.691406 176.116464 L 121.085938 177.510995 L 120.273438 178.796151 L 119.253906 179.983651 L 118.039062 181.073495 L 116.636719 182.065683 L 115.058594 182.971933 L 113.320312 183.796151 L 111.445312 184.553964 L 109.453125 185.253183 L 107.367188 185.905526 L 105.214844 186.522714 L 103.023438 187.124276 L 100.820312 187.718026 L 98.636719 188.319589 L 96.492188 188.940683 L 94.421875 189.600839 L 92.449219 190.311776 L 90.597656 191.077401 L 88.886719 191.917245 L 87.339844 192.83912 L 85.96875 193.850839 L 84.789062 194.952401 L 83.808594 196.159433 L 83.03125 197.468026 L 82.464844 198.878183 L 82.101562 200.389901 L 81.9375 202.003183 L 81.964844 203.710214 L 82.167969 205.507089 L 82.53125 207.385995 L 83.035156 209.331308 L 83.660156 211.33912 L 84.378906 213.393808 L 85.167969 215.483651 L 86 217.593026 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill="none" stroke-width="1" stroke-linecap="square" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="0.8" stroke-miterlimit="10" d="M 144.296875 88.573495 L 143.214844 90.80787 L 142.261719 92.979745 L 141.457031 95.077401 L 140.816406 97.093026 L 140.359375 99.014901 L 140.089844 100.846933 L 140.027344 102.573495 L 140.175781 104.194589 L 140.539062 105.706308 L 141.128906 107.104745 L 141.9375 108.397714 L 142.96875 109.577401 L 144.214844 110.647714 L 145.675781 111.612558 L 147.335938 112.475839 L 149.183594 113.24537 L 151.210938 113.928964 L 153.402344 114.52662 L 155.738281 115.05787 L 158.195312 115.522714 L 160.761719 115.93287 L 163.410156 116.303964 L 166.125 116.643808 L 168.875 116.96412 L 171.636719 117.27662 L 174.394531 117.593026 L 177.117188 117.925058 L 179.789062 118.284433 L 182.378906 118.686776 L 184.867188 119.135995 L 187.238281 119.643808 L 189.46875 120.225839 L 191.539062 120.882089 L 193.441406 121.628183 L 195.152344 122.468026 L 196.664062 123.405526 L 197.96875 124.448495 L 199.058594 125.596933 L 199.929688 126.858651 L 200.574219 128.229745 L 201 129.71412 L 201.207031 131.303964 L 201.199219 133.003183 L 200.984375 134.803964 L 200.574219 136.706308 L 199.980469 138.698495 L 199.21875 140.772714 L 198.304688 142.925058 L 197.253906 145.143808 L 196.089844 147.421151 L 194.832031 149.74537 L 193.5 152.108651 L 192.121094 154.491464 L 189.308594 159.288339 L 187.921875 161.675058 L 186.582031 164.042245 L 185.308594 166.37037 L 184.128906 168.659433 L 183.058594 170.889901 L 182.117188 173.053964 L 181.328125 175.143808 L 180.703125 177.15162 L 180.261719 179.065683 L 180.011719 180.885995 L 179.96875 182.600839 L 180.136719 184.21412 L 180.523438 185.71412 L 181.128906 187.104745 L 181.960938 188.385995 L 183.011719 189.553964 L 184.28125 190.616464 L 185.757812 191.569589 L 187.4375 192.425058 L 189.304688 193.186776 L 191.347656 193.862558 L 193.554688 194.452401 L 195.898438 194.975839 L 198.371094 195.436776 L 200.945312 195.843026 L 203.601562 196.210214 L 206.316406 196.546151 L 209.070312 196.866464 L 211.835938 197.178964 L 214.589844 197.49537 L 217.308594 197.831308 L 219.972656 198.194589 L 222.554688 198.596933 L 225.035156 199.053964 L 227.390625 199.569589 L 229.605469 200.155526 L 231.664062 200.823495 L 233.546875 201.577401 L 235.238281 202.425058 L 236.734375 203.37037 L 238.019531 204.425058 L 239.085938 205.585214 L 239.9375 206.854745 L 240.5625 208.237558 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill="none" stroke-width="1" stroke-linecap="square" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="0.8" stroke-miterlimit="10" d="M 148.160156 80.878183 L 145.429688 81.206308 L 142.800781 81.585214 L 140.296875 82.030526 L 137.945312 82.550058 L 135.765625 83.15162 L 133.777344 83.854745 L 131.996094 84.659433 L 130.441406 85.577401 L 129.121094 86.612558 L 128.042969 87.764901 L 127.210938 89.046151 L 126.632812 90.452401 L 126.300781 91.979745 L 126.214844 93.632089 L 126.363281 95.40162 L 126.742188 97.284433 L 127.328125 99.272714 L 128.113281 101.362558 L 129.074219 103.538339 L 130.191406 105.788339 L 131.4375 108.108651 L 132.792969 110.483651 L 134.230469 112.893808 L 135.722656 115.335214 L 137.234375 117.788339 L 138.746094 120.237558 L 140.226562 122.671151 L 141.648438 125.077401 L 142.980469 127.440683 L 144.199219 129.74537 L 145.28125 131.979745 L 146.203125 134.135995 L 146.941406 136.198495 L 147.480469 138.163339 L 147.804688 140.018808 L 147.898438 141.764901 L 147.753906 143.385995 L 147.363281 144.885995 L 146.726562 146.260995 L 145.835938 147.510995 L 144.699219 148.639901 L 143.320312 149.643808 L 141.710938 150.534433 L 139.882812 151.315683 L 137.847656 151.991464 L 135.625 152.573495 L 133.234375 153.073495 L 130.699219 153.503183 L 128.042969 153.866464 L 125.289062 154.186776 L 122.46875 154.471933 L 119.609375 154.737558 L 116.734375 154.99537 L 113.878906 155.260995 L 111.066406 155.550058 L 108.328125 155.874276 L 105.6875 156.249276 L 103.171875 156.686776 L 100.804688 157.198495 L 98.605469 157.796151 L 96.601562 158.487558 L 94.800781 159.284433 L 93.222656 160.190683 L 91.882812 161.21412 L 90.78125 162.354745 L 89.925781 163.624276 L 89.324219 165.018808 L 88.96875 166.538339 L 88.859375 168.175058 L 88.988281 169.936776 L 89.34375 171.80787 L 89.914062 173.788339 L 90.679688 175.866464 L 91.628906 178.034433 L 92.730469 180.280526 L 93.96875 182.593026 L 95.316406 184.96412 L 96.746094 187.374276 L 98.230469 189.811776 L 99.746094 192.260995 L 101.257812 194.71412 L 102.742188 197.15162 L 104.167969 199.55787 L 105.511719 201.925058 L 106.742188 204.237558 L 107.835938 206.479745 L 108.773438 208.639901 L 109.53125 210.71412 L 110.089844 212.690683 L 110.433594 214.55787 L 110.546875 216.30787 L 110.425781 217.944589 L 110.058594 219.456308 L 109.445312 220.843026 L 108.578125 222.104745 L 107.464844 223.241464 L 106.109375 224.257089 L 104.519531 225.159433 L 102.707031 225.948495 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g clip-path="url(#clip-1)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 42.058594 39.010995 L 42.058594 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 42.058594 39.010995 L 42.058594 35.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-0" x="38.879687" y="297.049276"/>
</g>
<g clip-path="url(#clip-2)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 86.804688 39.010995 L 86.804688 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 86.804688 39.010995 L 86.804688 35.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-1" x="83.625793" y="297.049276"/>
</g>
<g clip-path="url(#clip-3)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 131.550781 39.010995 L 131.550781 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 131.550781 39.010995 L 131.550781 35.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-2" x="128.371898" y="297.049276"/>
</g>
<g clip-path="url(#clip-4)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 176.296875 39.010995 L 176.296875 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 176.296875 39.010995 L 176.296875 35.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-3" x="173.118003" y="297.049276"/>
</g>
<g clip-path="url(#clip-5)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 221.042969 39.010995 L 221.042969 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 221.042969 39.010995 L 221.042969 35.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-4" x="214.684421" y="297.049276"/>
<use xlink:href="#glyph-0-5" x="221.044421" y="297.049276"/>
</g>
<g clip-path="url(#clip-6)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 265.789062 39.010995 L 265.789062 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 265.789062 39.010995 L 265.789062 35.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-4" x="259.430526" y="297.049276"/>
<use xlink:href="#glyph-0-6" x="265.790526" y="297.049276"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-1-0" x="115.565263" y="311.486776"/>
<use xlink:href="#glyph-1-1" x="123.100263" y="311.486776"/>
<use xlink:href="#glyph-1-2" x="126.598263" y="311.486776"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-1-3" x="132.840638" y="311.486776"/>
<use xlink:href="#glyph-1-4" x="139.572638" y="311.486776"/>
<use xlink:href="#glyph-1-5" x="145.303638" y="311.486776"/>
<use xlink:href="#glyph-1-6" x="148.361638" y="311.486776"/>
<use xlink:href="#glyph-1-5" x="152.673638" y="311.486776"/>
<use xlink:href="#glyph-1-3" x="155.731638" y="311.486776"/>
<use xlink:href="#glyph-1-7" x="162.463638" y="311.486776"/>
<use xlink:href="#glyph-1-1" x="169.437638" y="311.486776"/>
<use xlink:href="#glyph-1-8" x="172.935638" y="311.486776"/>
<use xlink:href="#glyph-1-9" x="177.225638" y="311.486776"/>
<use xlink:href="#glyph-1-10" x="187.939638" y="311.486776"/>
</g>
<g clip-path="url(#clip-7)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 42.058594 39.010995 L 265.789062 39.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 42.058594 39.010995 L 38.058594 39.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-0" x="28.2" y="285.752401"/>
</g>
<g clip-path="url(#clip-8)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 42.058594 83.757089 L 265.789062 83.757089 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 42.058594 83.757089 L 38.058594 83.757089 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-1" x="28.2" y="241.006296"/>
</g>
<g clip-path="url(#clip-9)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 42.058594 128.503183 L 265.789062 128.503183 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 42.058594 128.503183 L 38.058594 128.503183 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-2" x="28.2" y="196.260191"/>
</g>
<g clip-path="url(#clip-10)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 42.058594 173.249276 L 265.789062 173.249276 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 42.058594 173.249276 L 38.058594 173.249276 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-3" x="28.2" y="151.514086"/>
</g>
<g clip-path="url(#clip-11)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 42.058594 217.99537 L 265.789062 217.99537 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 42.058594 217.99537 L 38.058594 217.99537 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-4" x="21.840625" y="106.76798"/>
<use xlink:href="#glyph-0-5" x="28.200625" y="106.76798"/>
</g>
<g clip-path="url(#clip-12)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 42.058594 262.741464 L 265.789062 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 42.058594 262.741464 L 38.058594 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-4" x="21.840625" y="62.021875"/>
<use xlink:href="#glyph-0-6" x="28.200625" y="62.021875"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-2-0" x="15.559375" y="208.043388"/>
<use xlink:href="#glyph-2-1" x="15.559375" y="201.322388"/>
<use xlink:href="#glyph-2-2" x="15.559375" y="197.824388"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-2-3" x="15.559375" y="191.582013"/>
<use xlink:href="#glyph-2-4" x="15.559375" y="184.850013"/>
<use xlink:href="#glyph-2-5" x="15.559375" y="179.119013"/>
<use xlink:href="#glyph-2-6" x="15.559375" y="176.061013"/>
<use xlink:href="#glyph-2-5" x="15.559375" y="171.749013"/>
<use xlink:href="#glyph-2-3" x="15.559375" y="168.691013"/>
<use xlink:href="#glyph-2-7" x="15.559375" y="161.959013"/>
<use xlink:href="#glyph-2-1" x="15.559375" y="154.985013"/>
<use xlink:href="#glyph-2-8" x="15.559375" y="151.487013"/>
<use xlink:href="#glyph-2-9" x="15.559375" y="147.197013"/>
<use xlink:href="#glyph-2-10" x="15.559375" y="136.483013"/>
</g>
<path fill="none" stroke-width="2.5" stroke-linecap="square" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.9" stroke-miterlimit="10" d="M 144.730469 79.163339 L 145.011719 80.999276 L 145.445312 82.909433 L 146.011719 84.885995 L 146.683594 86.917245 L 147.4375 88.991464 L 148.25 91.093026 L 149.09375 93.206308 L 149.9375 95.323495 L 150.757812 97.428964 L 151.519531 99.507089 L 152.203125 101.542245 L 152.78125 103.52662 L 153.226562 105.444589 L 153.527344 107.288339 L 153.65625 109.050058 L 153.605469 110.718026 L 153.359375 112.292245 L 152.914062 113.760995 L 152.257812 115.128183 L 151.398438 116.393808 L 150.332031 117.55787 L 149.070312 118.62037 L 147.625 119.593026 L 146.007812 120.479745 L 144.238281 121.288339 L 142.332031 122.030526 L 140.3125 122.718026 L 138.210938 123.358651 L 136.046875 123.971933 L 133.851562 124.569589 L 131.648438 125.163339 L 129.46875 125.768808 L 127.34375 126.40162 L 125.292969 127.069589 L 123.347656 127.792245 L 121.527344 128.577401 L 119.851562 129.436776 L 118.34375 130.374276 L 117.019531 131.409433 L 115.886719 132.534433 L 114.953125 133.764901 L 114.226562 135.096933 L 113.707031 136.530526 L 113.390625 138.069589 L 113.273438 139.706308 L 113.34375 141.43287 L 113.585938 143.249276 L 113.984375 145.143808 L 114.519531 147.108651 L 115.167969 149.128183 L 115.90625 151.190683 L 116.707031 153.288339 L 117.546875 155.40162 L 118.394531 157.518808 L 119.21875 159.628183 L 120 161.710214 L 120.703125 163.757089 L 121.308594 165.757089 L 121.789062 167.690683 L 122.125 169.553964 L 122.296875 171.335214 L 122.289062 173.02662 L 122.089844 174.62037 L 121.691406 176.116464 L 121.085938 177.510995 L 120.273438 178.796151 L 119.253906 179.983651 L 118.039062 181.073495 L 116.636719 182.065683 L 115.058594 182.971933 L 113.320312 183.796151 L 111.445312 184.553964 L 109.453125 185.253183 L 107.367188 185.905526 L 105.214844 186.522714 L 103.023438 187.124276 L 100.820312 187.718026 L 98.636719 188.319589 L 96.492188 188.940683 L 94.421875 189.600839 L 92.449219 190.311776 L 90.597656 191.077401 L 88.886719 191.917245 L 87.339844 192.83912 L 85.96875 193.850839 L 84.789062 194.952401 L 83.808594 196.159433 L 83.03125 197.468026 L 82.464844 198.878183 L 82.101562 200.389901 L 81.9375 202.003183 L 81.964844 203.710214 L 82.167969 205.507089 L 82.53125 207.385995 L 83.035156 209.331308 L 83.660156 211.33912 L 84.378906 213.393808 L 85.167969 215.483651 L 86 217.593026 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill="none" stroke-width="2.5" stroke-linecap="square" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.9" stroke-miterlimit="10" d="M 144.296875 88.573495 L 143.214844 90.80787 L 142.261719 92.979745 L 141.457031 95.077401 L 140.816406 97.093026 L 140.359375 99.014901 L 140.089844 100.846933 L 140.027344 102.573495 L 140.175781 104.194589 L 140.539062 105.706308 L 141.128906 107.104745 L 141.9375 108.397714 L 142.96875 109.577401 L 144.214844 110.647714 L 145.675781 111.612558 L 147.335938 112.475839 L 149.183594 113.24537 L 151.210938 113.928964 L 153.402344 114.52662 L 155.738281 115.05787 L 158.195312 115.522714 L 160.761719 115.93287 L 163.410156 116.303964 L 166.125 116.643808 L 168.875 116.96412 L 171.636719 117.27662 L 174.394531 117.593026 L 177.117188 117.925058 L 179.789062 118.284433 L 182.378906 118.686776 L 184.867188 119.135995 L 187.238281 119.643808 L 189.46875 120.225839 L 191.539062 120.882089 L 193.441406 121.628183 L 195.152344 122.468026 L 196.664062 123.405526 L 197.96875 124.448495 L 199.058594 125.596933 L 199.929688 126.858651 L 200.574219 128.229745 L 201 129.71412 L 201.207031 131.303964 L 201.199219 133.003183 L 200.984375 134.803964 L 200.574219 136.706308 L 199.980469 138.698495 L 199.21875 140.772714 L 198.304688 142.925058 L 197.253906 145.143808 L 196.089844 147.421151 L 194.832031 149.74537 L 193.5 152.108651 L 192.121094 154.491464 L 189.308594 159.288339 L 187.921875 161.675058 L 186.582031 164.042245 L 185.308594 166.37037 L 184.128906 168.659433 L 183.058594 170.889901 L 182.117188 173.053964 L 181.328125 175.143808 L 180.703125 177.15162 L 180.261719 179.065683 L 180.011719 180.885995 L 179.96875 182.600839 L 180.136719 184.21412 L 180.523438 185.71412 L 181.128906 187.104745 L 181.960938 188.385995 L 183.011719 189.553964 L 184.28125 190.616464 L 185.757812 191.569589 L 187.4375 192.425058 L 189.304688 193.186776 L 191.347656 193.862558 L 193.554688 194.452401 L 195.898438 194.975839 L 198.371094 195.436776 L 200.945312 195.843026 L 203.601562 196.210214 L 206.316406 196.546151 L 209.070312 196.866464 L 211.835938 197.178964 L 214.589844 197.49537 L 217.308594 197.831308 L 219.972656 198.194589 L 222.554688 198.596933 L 225.035156 199.053964 L 227.390625 199.569589 L 229.605469 200.155526 L 231.664062 200.823495 L 233.546875 201.577401 L 235.238281 202.425058 L 236.734375 203.37037 L 238.019531 204.425058 L 239.085938 205.585214 L 239.9375 206.854745 L 240.5625 208.237558 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill="none" stroke-width="2.5" stroke-linecap="square" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.9" stroke-miterlimit="10" d="M 148.160156 80.878183 L 145.429688 81.206308 L 142.800781 81.585214 L 140.296875 82.030526 L 137.945312 82.550058 L 135.765625 83.15162 L 133.777344 83.854745 L 131.996094 84.659433 L 130.441406 85.577401 L 129.121094 86.612558 L 128.042969 87.764901 L 127.210938 89.046151 L 126.632812 90.452401 L 126.300781 91.979745 L 126.214844 93.632089 L 126.363281 95.40162 L 126.742188 97.284433 L 127.328125 99.272714 L 128.113281 101.362558 L 129.074219 103.538339 L 130.191406 105.788339 L 131.4375 108.108651 L 132.792969 110.483651 L 134.230469 112.893808 L 135.722656 115.335214 L 137.234375 117.788339 L 138.746094 120.237558 L 140.226562 122.671151 L 141.648438 125.077401 L 142.980469 127.440683 L 144.199219 129.74537 L 145.28125 131.979745 L 146.203125 134.135995 L 146.941406 136.198495 L 147.480469 138.163339 L 147.804688 140.018808 L 147.898438 141.764901 L 147.753906 143.385995 L 147.363281 144.885995 L 146.726562 146.260995 L 145.835938 147.510995 L 144.699219 148.639901 L 143.320312 149.643808 L 141.710938 150.534433 L 139.882812 151.315683 L 137.847656 151.991464 L 135.625 152.573495 L 133.234375 153.073495 L 130.699219 153.503183 L 128.042969 153.866464 L 125.289062 154.186776 L 122.46875 154.471933 L 119.609375 154.737558 L 116.734375 154.99537 L 113.878906 155.260995 L 111.066406 155.550058 L 108.328125 155.874276 L 105.6875 156.249276 L 103.171875 156.686776 L 100.804688 157.198495 L 98.605469 157.796151 L 96.601562 158.487558 L 94.800781 159.284433 L 93.222656 160.190683 L 91.882812 161.21412 L 90.78125 162.354745 L 89.925781 163.624276 L 89.324219 165.018808 L 88.96875 166.538339 L 88.859375 168.175058 L 88.988281 169.936776 L 89.34375 171.80787 L 89.914062 173.788339 L 90.679688 175.866464 L 91.628906 178.034433 L 92.730469 180.280526 L 93.96875 182.593026 L 95.316406 184.96412 L 96.746094 187.374276 L 98.230469 189.811776 L 99.746094 192.260995 L 101.257812 194.71412 L 102.742188 197.15162 L 104.167969 199.55787 L 105.511719 201.925058 L 106.742188 204.237558 L 107.835938 206.479745 L 108.773438 208.639901 L 109.53125 210.71412 L 110.089844 212.690683 L 110.433594 214.55787 L 110.546875 216.30787 L 110.425781 217.944589 L 110.058594 219.456308 L 109.445312 220.843026 L 108.578125 222.104745 L 107.464844 223.241464 L 106.109375 224.257089 L 104.519531 225.159433 L 102.707031 225.948495 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill="none" stroke-width="0.8" stroke-linecap="square" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 42.058594 39.010995 L 42.058594 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill="none" stroke-width="0.8" stroke-linecap="square" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 42.058594 39.010995 L 265.789062 39.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill-rule="nonzero" fill="rgb(0%, 100%, 0%)" fill-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 144.730469 76.206308 C 145.515625 76.206308 146.269531 76.514901 146.824219 77.069589 C 147.378906 77.624276 147.691406 78.378183 147.691406 79.163339 C 147.691406 79.948495 147.378906 80.698495 146.824219 81.253183 C 146.269531 81.80787 145.515625 82.12037 144.730469 82.12037 C 143.949219 82.12037 143.195312 81.80787 142.640625 81.253183 C 142.085938 80.698495 141.773438 79.948495 141.773438 79.163339 C 141.773438 78.378183 142.085938 77.624276 142.640625 77.069589 C 143.195312 76.514901 143.949219 76.206308 144.730469 76.206308 Z M 144.730469 76.206308 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill-rule="nonzero" fill="rgb(100%, 0%, 0%)" fill-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 83.042969 214.635995 L 88.957031 214.635995 L 88.957031 220.553964 L 83.042969 220.553964 Z M 83.042969 214.635995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-3-0" x="119.049638" y="16.325"/>
<use xlink:href="#glyph-3-1" x="126.669638" y="16.325"/>
<use xlink:href="#glyph-3-2" x="134.049638" y="16.325"/>
<use xlink:href="#glyph-3-3" x="137.385638" y="16.325"/>
<use xlink:href="#glyph-3-4" x="141.609638" y="16.325"/>
<use xlink:href="#glyph-3-5" x="145.425638" y="16.325"/>
<use xlink:href="#glyph-3-6" x="155.781638" y="16.325"/>
<use xlink:href="#glyph-3-7" x="163.125638" y="16.325"/>
<use xlink:href="#glyph-3-8" x="170.229638" y="16.325"/>
<use xlink:href="#glyph-3-9" x="173.565638" y="16.325"/>
<use xlink:href="#glyph-3-10" x="181.173638" y="16.325"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-3-11" x="121.573076" y="29.775"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-3-1" x="128.387201" y="29.775"/>
<use xlink:href="#glyph-3-1" x="135.767201" y="29.775"/>
<use xlink:href="#glyph-3-12" x="143.147201" y="29.775"/>
<use xlink:href="#glyph-3-4" x="148.079201" y="29.775"/>
<use xlink:href="#glyph-3-0" x="151.895201" y="29.775"/>
<use xlink:href="#glyph-3-13" x="159.515201" y="29.775"/>
<use xlink:href="#glyph-3-14" x="164.219201" y="29.775"/>
<use xlink:href="#glyph-3-13" x="171.575201" y="29.775"/>
<use xlink:href="#glyph-3-8" x="176.279201" y="29.775"/>
<use xlink:href="#glyph-3-15" x="179.615201" y="29.775"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-3-11" x="118.526201" y="43.225"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-3-1" x="125.340326" y="43.225"/>
<use xlink:href="#glyph-3-14" x="132.720326" y="43.225"/>
<use xlink:href="#glyph-3-16" x="140.076326" y="43.225"/>
<use xlink:href="#glyph-3-17" x="147.024326" y="43.225"/>
<use xlink:href="#glyph-3-4" x="151.068326" y="43.225"/>
<use xlink:href="#glyph-3-18" x="154.884326" y="43.225"/>
<use xlink:href="#glyph-3-19" x="162.516326" y="43.225"/>
<use xlink:href="#glyph-3-20" x="166.332326" y="43.225"/>
<use xlink:href="#glyph-3-18" x="173.964326" y="43.225"/>
<use xlink:href="#glyph-3-21" x="181.596326" y="43.225"/>
</g>
<path fill-rule="nonzero" fill="rgb(100%, 100%, 100%)" fill-opacity="0.9" stroke-width="1" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(79.998779%, 79.998779%, 79.998779%)" stroke-opacity="0.9" stroke-miterlimit="10" d="M 213.425781 232.843026 L 261.789062 232.843026 L 261.789062 258.741464 L 213.425781 258.741464 Z M 213.425781 232.843026 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill-rule="nonzero" fill="rgb(0%, 100%, 0%)" fill-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 224.628906 248.608651 C 225.410156 248.608651 226.164062 248.917245 226.71875 249.471933 C 227.273438 250.02662 227.585938 250.780526 227.585938 251.565683 C 227.585938 252.350839 227.273438 253.100839 226.71875 253.655526 C 226.164062 254.210214 225.410156 254.522714 224.628906 254.522714 C 223.84375 254.522714 223.089844 254.210214 222.535156 253.655526 C 221.980469 253.100839 221.667969 252.350839 221.667969 251.565683 C 221.667969 250.780526 221.980469 250.02662 222.535156 249.471933 C 223.089844 248.917245 223.84375 248.608651 224.628906 248.608651 Z M 224.628906 248.608651 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-4-0" x="239.027401" y="71.503125"/>
<use xlink:href="#glyph-4-1" x="244.107401" y="71.503125"/>
<use xlink:href="#glyph-4-2" x="247.243401" y="71.503125"/>
<use xlink:href="#glyph-4-3" x="252.147401" y="71.503125"/>
<use xlink:href="#glyph-4-1" x="255.435401" y="71.503125"/>
</g>
<path fill-rule="nonzero" fill="rgb(100%, 0%, 0%)" fill-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 221.667969 236.858651 L 227.585938 236.858651 L 227.585938 242.772714 L 221.667969 242.772714 Z M 221.667969 236.858651 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-4-4" x="239.027401" y="83.253125"/>
<use xlink:href="#glyph-4-5" x="244.083401" y="83.253125"/>
<use xlink:href="#glyph-4-6" x="249.155401" y="83.253125"/>
</g>
<path fill-rule="nonzero" fill="rgb(100%, 100%, 100%)" fill-opacity="1" d="M 321.722656 281.957031 L 545.453125 281.957031 L 545.453125 58.226562 L 321.722656 58.226562 Z M 321.722656 281.957031 "/>
<g clip-path="url(#clip-13)">
<use xlink:href="#source-77" transform="matrix(0.24, 0, 0, 0.24, 321.722533, 58.035526)"/>
</g>
<path fill="none" stroke-width="1" stroke-linecap="square" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="0.8" stroke-miterlimit="10" d="M 427.292969 80.612558 L 425.777344 81.546151 L 424.425781 82.569589 L 423.257812 83.678964 L 422.285156 84.885995 L 421.511719 86.194589 L 420.945312 87.604745 L 420.574219 89.116464 L 420.398438 90.721933 L 420.40625 92.421151 L 420.578125 94.202401 L 420.894531 96.05787 L 421.335938 97.971933 L 421.871094 99.93287 L 422.476562 101.93287 L 423.117188 103.948495 L 423.765625 105.968026 L 424.390625 107.975839 L 424.960938 109.956308 L 425.449219 111.893808 L 425.824219 113.77662 L 426.066406 115.58912 L 426.148438 117.327401 L 426.058594 118.975839 L 425.777344 120.530526 L 425.300781 121.987558 L 424.617188 123.343026 L 423.734375 124.596933 L 422.65625 125.749276 L 421.386719 126.811776 L 419.941406 127.784433 L 418.34375 128.678964 L 416.609375 129.507089 L 414.761719 130.280526 L 412.835938 131.010995 L 410.851562 131.71412 L 408.847656 132.405526 L 406.847656 133.100839 L 404.890625 133.815683 L 403 134.565683 L 401.207031 135.366464 L 399.539062 136.225839 L 398.011719 137.159433 L 396.65625 138.175058 L 395.480469 139.280526 L 394.496094 140.483651 L 393.714844 141.788339 L 393.136719 143.194589 L 392.761719 144.702401 L 392.574219 146.303964 L 392.574219 147.999276 L 392.738281 149.77662 L 393.050781 151.628183 L 393.484375 153.538339 L 394.019531 155.499276 L 394.621094 157.49537 L 395.261719 159.510995 L 395.910156 161.530526 L 396.539062 163.538339 L 397.109375 165.522714 L 397.601562 167.460214 L 397.984375 169.346933 L 398.230469 171.167245 L 398.320312 172.905526 L 398.238281 174.55787 L 397.96875 176.12037 L 397.5 177.577401 L 396.828125 178.936776 L 395.953125 180.194589 L 394.878906 181.354745 L 393.621094 182.421151 L 392.183594 183.397714 L 390.589844 184.296151 L 388.859375 185.124276 L 387.019531 185.897714 L 385.09375 186.632089 L 383.113281 187.335214 L 381.109375 188.030526 L 379.109375 188.725839 L 377.148438 189.440683 L 375.257812 190.186776 L 373.457031 190.983651 L 371.78125 191.83912 L 370.25 192.768808 L 368.886719 193.780526 L 367.699219 194.885995 L 366.710938 196.085214 L 365.917969 197.382089 L 365.332031 198.784433 L 364.945312 200.284433 L 364.753906 201.885995 L 364.742188 203.573495 L 364.902344 205.350839 L 365.207031 207.194589 L 365.636719 209.104745 L 366.164062 211.065683 L 366.765625 213.061776 L 367.40625 215.073495 L 368.054688 217.096933 L 368.683594 219.104745 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill="none" stroke-width="1" stroke-linecap="square" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="0.8" stroke-miterlimit="10" d="M 425.082031 88.010995 L 424.609375 89.944589 L 424.296875 91.792245 L 424.164062 93.553964 L 424.21875 95.221933 L 424.476562 96.788339 L 424.941406 98.249276 L 425.621094 99.608651 L 426.507812 100.858651 L 427.605469 102.003183 L 428.902344 103.050058 L 430.394531 103.999276 L 432.058594 104.862558 L 433.886719 105.643808 L 435.855469 106.354745 L 437.945312 107.003183 L 440.128906 107.608651 L 442.386719 108.175058 L 444.6875 108.718026 L 447.011719 109.249276 L 449.320312 109.788339 L 451.597656 110.346933 L 453.8125 110.936776 L 455.9375 111.569589 L 457.949219 112.257089 L 459.824219 113.010995 L 461.546875 113.846933 L 463.101562 114.764901 L 464.464844 115.77662 L 465.632812 116.889901 L 466.59375 118.104745 L 467.339844 119.425058 L 467.878906 120.850839 L 468.207031 122.382089 L 468.332031 124.014901 L 468.261719 125.74537 L 468.011719 127.565683 L 467.589844 129.468026 L 467.023438 131.448495 L 466.328125 133.491464 L 465.527344 135.585214 L 464.648438 137.721933 L 463.714844 139.882089 L 462.75 142.05787 L 461.789062 144.233651 L 460.855469 146.397714 L 459.972656 148.530526 L 459.171875 150.628183 L 458.476562 152.671151 L 457.90625 154.65162 L 457.484375 156.55787 L 457.230469 158.378183 L 457.15625 160.108651 L 457.28125 161.74537 L 457.605469 163.27662 L 458.140625 164.702401 L 458.886719 166.02662 L 459.84375 167.241464 L 461.007812 168.354745 L 462.371094 169.366464 L 463.917969 170.288339 L 465.640625 171.124276 L 467.515625 171.878183 L 469.527344 172.569589 L 471.648438 173.202401 L 473.863281 173.792245 L 476.136719 174.350839 L 478.449219 174.889901 L 480.769531 175.421151 L 483.074219 175.968026 L 485.332031 176.530526 L 487.519531 177.132089 L 489.609375 177.784433 L 491.578125 178.491464 L 493.40625 179.272714 L 495.074219 180.135995 L 496.566406 181.085214 L 497.867188 182.128183 L 498.96875 183.272714 L 499.859375 184.522714 L 500.539062 185.878183 L 501.007812 187.33912 L 501.269531 188.90162 L 501.328125 190.565683 L 501.195312 192.327401 L 500.886719 194.178964 L 500.417969 196.108651 L 499.804688 198.108651 L 499.074219 200.171151 L 498.246094 202.280526 L 497.34375 204.425058 L 496.398438 206.593026 L 495.433594 208.768808 L 494.476562 210.944589 L 493.554688 213.096933 L 492.699219 215.221933 L 491.929688 217.300058 L 491.269531 219.323495 L 490.75 221.280526 L 490.378906 223.159433 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill="none" stroke-width="1" stroke-linecap="square" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="0.8" stroke-miterlimit="10" d="M 432.046875 82.987558 L 429.742188 83.530526 L 427.480469 84.093026 L 425.285156 84.690683 L 423.179688 85.335214 L 421.1875 86.034433 L 419.324219 86.796151 L 417.609375 87.632089 L 416.054688 88.550058 L 414.675781 89.55787 L 413.480469 90.65162 L 412.476562 91.846933 L 411.667969 93.135995 L 411.058594 94.52662 L 410.648438 96.018808 L 410.429688 97.604745 L 410.398438 99.284433 L 410.546875 101.050058 L 410.859375 102.90162 L 411.324219 104.831308 L 411.921875 106.823495 L 412.636719 108.878183 L 413.449219 110.979745 L 414.339844 113.116464 L 415.277344 115.280526 L 416.25 117.46412 L 417.222656 119.643808 L 418.179688 121.819589 L 419.097656 123.971933 L 419.949219 126.093026 L 420.710938 128.167245 L 421.367188 130.190683 L 421.898438 132.15162 L 422.285156 134.038339 L 422.511719 135.846933 L 422.566406 137.569589 L 422.4375 139.198495 L 422.117188 140.733651 L 421.601562 142.171151 L 420.886719 143.510995 L 419.976562 144.749276 L 418.871094 145.889901 L 417.574219 146.940683 L 416.101562 147.897714 L 414.460938 148.772714 L 412.664062 149.569589 L 410.730469 150.296151 L 408.675781 150.96412 L 406.523438 151.581308 L 404.289062 152.159433 L 402 152.710214 L 399.679688 153.24537 L 397.347656 153.772714 L 395.027344 154.311776 L 392.746094 154.866464 L 390.527344 155.448495 L 388.394531 156.077401 L 386.359375 156.757089 L 384.453125 157.49537 L 382.683594 158.30787 L 381.074219 159.198495 L 379.636719 160.171151 L 378.375 161.237558 L 377.308594 162.397714 L 376.433594 163.655526 L 375.757812 165.014901 L 375.28125 166.471933 L 375 168.02662 L 374.90625 169.675058 L 374.996094 171.413339 L 375.253906 173.237558 L 375.671875 175.139901 L 376.226562 177.112558 L 376.90625 179.147714 L 377.6875 181.233651 L 378.554688 183.362558 L 379.476562 185.518808 L 380.441406 187.694589 L 381.417969 189.878183 L 382.382812 192.053964 L 383.316406 194.218026 L 384.191406 196.350839 L 384.984375 198.440683 L 385.679688 200.483651 L 386.253906 202.468026 L 386.691406 204.378183 L 386.972656 206.21412 L 387.085938 207.968026 L 387.019531 209.628183 L 386.765625 211.194589 L 386.316406 212.667245 L 385.667969 214.038339 L 384.820312 215.30787 L 383.78125 216.483651 L 382.546875 217.561776 L 381.132812 218.550058 L 379.546875 219.452401 L 377.800781 220.272714 L 375.910156 221.022714 L 373.894531 221.710214 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g clip-path="url(#clip-14)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 321.722656 39.010995 L 321.722656 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 321.722656 39.010995 L 321.722656 35.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-0" x="318.542845" y="297.049276"/>
</g>
<g clip-path="url(#clip-15)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 366.46875 39.010995 L 366.46875 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 366.46875 39.010995 L 366.46875 35.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-1" x="363.288951" y="297.049276"/>
</g>
<g clip-path="url(#clip-16)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 411.214844 39.010995 L 411.214844 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 411.214844 39.010995 L 411.214844 35.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-2" x="408.035056" y="297.049276"/>
</g>
<g clip-path="url(#clip-17)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 455.960938 39.010995 L 455.960938 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 455.960938 39.010995 L 455.960938 35.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-3" x="452.781161" y="297.049276"/>
</g>
<g clip-path="url(#clip-18)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 500.707031 39.010995 L 500.707031 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 500.707031 39.010995 L 500.707031 35.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-4" x="494.347579" y="297.049276"/>
<use xlink:href="#glyph-0-5" x="500.707579" y="297.049276"/>
</g>
<g clip-path="url(#clip-19)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 545.453125 39.010995 L 545.453125 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 545.453125 39.010995 L 545.453125 35.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-4" x="539.093684" y="297.049276"/>
<use xlink:href="#glyph-0-6" x="545.453684" y="297.049276"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-1-0" x="395.228421" y="311.486776"/>
<use xlink:href="#glyph-1-1" x="402.763421" y="311.486776"/>
<use xlink:href="#glyph-1-2" x="406.261421" y="311.486776"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-1-3" x="412.503796" y="311.486776"/>
<use xlink:href="#glyph-1-4" x="419.235796" y="311.486776"/>
<use xlink:href="#glyph-1-5" x="424.966796" y="311.486776"/>
<use xlink:href="#glyph-1-6" x="428.024796" y="311.486776"/>
<use xlink:href="#glyph-1-5" x="432.336796" y="311.486776"/>
<use xlink:href="#glyph-1-3" x="435.394796" y="311.486776"/>
<use xlink:href="#glyph-1-7" x="442.126796" y="311.486776"/>
<use xlink:href="#glyph-1-1" x="449.100796" y="311.486776"/>
<use xlink:href="#glyph-1-8" x="452.598796" y="311.486776"/>
<use xlink:href="#glyph-1-9" x="456.888796" y="311.486776"/>
<use xlink:href="#glyph-1-10" x="467.602796" y="311.486776"/>
</g>
<g clip-path="url(#clip-20)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 321.722656 39.010995 L 545.453125 39.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 321.722656 39.010995 L 317.722656 39.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-0" x="307.863158" y="285.752401"/>
</g>
<g clip-path="url(#clip-21)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 321.722656 83.757089 L 545.453125 83.757089 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 321.722656 83.757089 L 317.722656 83.757089 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-1" x="307.863158" y="241.006296"/>
</g>
<g clip-path="url(#clip-22)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 321.722656 128.503183 L 545.453125 128.503183 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 321.722656 128.503183 L 317.722656 128.503183 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-2" x="307.863158" y="196.260191"/>
</g>
<g clip-path="url(#clip-23)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 321.722656 173.249276 L 545.453125 173.249276 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 321.722656 173.249276 L 317.722656 173.249276 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-3" x="307.863158" y="151.514086"/>
</g>
<g clip-path="url(#clip-24)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 321.722656 217.99537 L 545.453125 217.99537 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 321.722656 217.99537 L 317.722656 217.99537 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-4" x="301.503783" y="106.76798"/>
<use xlink:href="#glyph-0-5" x="307.863783" y="106.76798"/>
</g>
<g clip-path="url(#clip-25)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 321.722656 262.741464 L 545.453125 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 321.722656 262.741464 L 317.722656 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-4" x="301.503783" y="62.021875"/>
<use xlink:href="#glyph-0-6" x="307.863783" y="62.021875"/>
</g>
<path fill="none" stroke-width="2.5" stroke-linecap="square" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.9" stroke-miterlimit="10" d="M 427.292969 80.612558 L 425.777344 81.546151 L 424.425781 82.569589 L 423.257812 83.678964 L 422.285156 84.885995 L 421.511719 86.194589 L 420.945312 87.604745 L 420.574219 89.116464 L 420.398438 90.721933 L 420.40625 92.421151 L 420.578125 94.202401 L 420.894531 96.05787 L 421.335938 97.971933 L 421.871094 99.93287 L 422.476562 101.93287 L 423.117188 103.948495 L 423.765625 105.968026 L 424.390625 107.975839 L 424.960938 109.956308 L 425.449219 111.893808 L 425.824219 113.77662 L 426.066406 115.58912 L 426.148438 117.327401 L 426.058594 118.975839 L 425.777344 120.530526 L 425.300781 121.987558 L 424.617188 123.343026 L 423.734375 124.596933 L 422.65625 125.749276 L 421.386719 126.811776 L 419.941406 127.784433 L 418.34375 128.678964 L 416.609375 129.507089 L 414.761719 130.280526 L 412.835938 131.010995 L 410.851562 131.71412 L 408.847656 132.405526 L 406.847656 133.100839 L 404.890625 133.815683 L 403 134.565683 L 401.207031 135.366464 L 399.539062 136.225839 L 398.011719 137.159433 L 396.65625 138.175058 L 395.480469 139.280526 L 394.496094 140.483651 L 393.714844 141.788339 L 393.136719 143.194589 L 392.761719 144.702401 L 392.574219 146.303964 L 392.574219 147.999276 L 392.738281 149.77662 L 393.050781 151.628183 L 393.484375 153.538339 L 394.019531 155.499276 L 394.621094 157.49537 L 395.261719 159.510995 L 395.910156 161.530526 L 396.539062 163.538339 L 397.109375 165.522714 L 397.601562 167.460214 L 397.984375 169.346933 L 398.230469 171.167245 L 398.320312 172.905526 L 398.238281 174.55787 L 397.96875 176.12037 L 397.5 177.577401 L 396.828125 178.936776 L 395.953125 180.194589 L 394.878906 181.354745 L 393.621094 182.421151 L 392.183594 183.397714 L 390.589844 184.296151 L 388.859375 185.124276 L 387.019531 185.897714 L 385.09375 186.632089 L 383.113281 187.335214 L 381.109375 188.030526 L 379.109375 188.725839 L 377.148438 189.440683 L 375.257812 190.186776 L 373.457031 190.983651 L 371.78125 191.83912 L 370.25 192.768808 L 368.886719 193.780526 L 367.699219 194.885995 L 366.710938 196.085214 L 365.917969 197.382089 L 365.332031 198.784433 L 364.945312 200.284433 L 364.753906 201.885995 L 364.742188 203.573495 L 364.902344 205.350839 L 365.207031 207.194589 L 365.636719 209.104745 L 366.164062 211.065683 L 366.765625 213.061776 L 367.40625 215.073495 L 368.054688 217.096933 L 368.683594 219.104745 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill="none" stroke-width="2.5" stroke-linecap="square" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.9" stroke-miterlimit="10" d="M 425.082031 88.010995 L 424.609375 89.944589 L 424.296875 91.792245 L 424.164062 93.553964 L 424.21875 95.221933 L 424.476562 96.788339 L 424.941406 98.249276 L 425.621094 99.608651 L 426.507812 100.858651 L 427.605469 102.003183 L 428.902344 103.050058 L 430.394531 103.999276 L 432.058594 104.862558 L 433.886719 105.643808 L 435.855469 106.354745 L 437.945312 107.003183 L 440.128906 107.608651 L 442.386719 108.175058 L 444.6875 108.718026 L 447.011719 109.249276 L 449.320312 109.788339 L 451.597656 110.346933 L 453.8125 110.936776 L 455.9375 111.569589 L 457.949219 112.257089 L 459.824219 113.010995 L 461.546875 113.846933 L 463.101562 114.764901 L 464.464844 115.77662 L 465.632812 116.889901 L 466.59375 118.104745 L 467.339844 119.425058 L 467.878906 120.850839 L 468.207031 122.382089 L 468.332031 124.014901 L 468.261719 125.74537 L 468.011719 127.565683 L 467.589844 129.468026 L 467.023438 131.448495 L 466.328125 133.491464 L 465.527344 135.585214 L 464.648438 137.721933 L 463.714844 139.882089 L 462.75 142.05787 L 461.789062 144.233651 L 460.855469 146.397714 L 459.972656 148.530526 L 459.171875 150.628183 L 458.476562 152.671151 L 457.90625 154.65162 L 457.484375 156.55787 L 457.230469 158.378183 L 457.15625 160.108651 L 457.28125 161.74537 L 457.605469 163.27662 L 458.140625 164.702401 L 458.886719 166.02662 L 459.84375 167.241464 L 461.007812 168.354745 L 462.371094 169.366464 L 463.917969 170.288339 L 465.640625 171.124276 L 467.515625 171.878183 L 469.527344 172.569589 L 471.648438 173.202401 L 473.863281 173.792245 L 476.136719 174.350839 L 478.449219 174.889901 L 480.769531 175.421151 L 483.074219 175.968026 L 485.332031 176.530526 L 487.519531 177.132089 L 489.609375 177.784433 L 491.578125 178.491464 L 493.40625 179.272714 L 495.074219 180.135995 L 496.566406 181.085214 L 497.867188 182.128183 L 498.96875 183.272714 L 499.859375 184.522714 L 500.539062 185.878183 L 501.007812 187.33912 L 501.269531 188.90162 L 501.328125 190.565683 L 501.195312 192.327401 L 500.886719 194.178964 L 500.417969 196.108651 L 499.804688 198.108651 L 499.074219 200.171151 L 498.246094 202.280526 L 497.34375 204.425058 L 496.398438 206.593026 L 495.433594 208.768808 L 494.476562 210.944589 L 493.554688 213.096933 L 492.699219 215.221933 L 491.929688 217.300058 L 491.269531 219.323495 L 490.75 221.280526 L 490.378906 223.159433 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill="none" stroke-width="2.5" stroke-linecap="square" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.9" stroke-miterlimit="10" d="M 432.046875 82.987558 L 429.742188 83.530526 L 427.480469 84.093026 L 425.285156 84.690683 L 423.179688 85.335214 L 421.1875 86.034433 L 419.324219 86.796151 L 417.609375 87.632089 L 416.054688 88.550058 L 414.675781 89.55787 L 413.480469 90.65162 L 412.476562 91.846933 L 411.667969 93.135995 L 411.058594 94.52662 L 410.648438 96.018808 L 410.429688 97.604745 L 410.398438 99.284433 L 410.546875 101.050058 L 410.859375 102.90162 L 411.324219 104.831308 L 411.921875 106.823495 L 412.636719 108.878183 L 413.449219 110.979745 L 414.339844 113.116464 L 415.277344 115.280526 L 416.25 117.46412 L 417.222656 119.643808 L 418.179688 121.819589 L 419.097656 123.971933 L 419.949219 126.093026 L 420.710938 128.167245 L 421.367188 130.190683 L 421.898438 132.15162 L 422.285156 134.038339 L 422.511719 135.846933 L 422.566406 137.569589 L 422.4375 139.198495 L 422.117188 140.733651 L 421.601562 142.171151 L 420.886719 143.510995 L 419.976562 144.749276 L 418.871094 145.889901 L 417.574219 146.940683 L 416.101562 147.897714 L 414.460938 148.772714 L 412.664062 149.569589 L 410.730469 150.296151 L 408.675781 150.96412 L 406.523438 151.581308 L 404.289062 152.159433 L 402 152.710214 L 399.679688 153.24537 L 397.347656 153.772714 L 395.027344 154.311776 L 392.746094 154.866464 L 390.527344 155.448495 L 388.394531 156.077401 L 386.359375 156.757089 L 384.453125 157.49537 L 382.683594 158.30787 L 381.074219 159.198495 L 379.636719 160.171151 L 378.375 161.237558 L 377.308594 162.397714 L 376.433594 163.655526 L 375.757812 165.014901 L 375.28125 166.471933 L 375 168.02662 L 374.90625 169.675058 L 374.996094 171.413339 L 375.253906 173.237558 L 375.671875 175.139901 L 376.226562 177.112558 L 376.90625 179.147714 L 377.6875 181.233651 L 378.554688 183.362558 L 379.476562 185.518808 L 380.441406 187.694589 L 381.417969 189.878183 L 382.382812 192.053964 L 383.316406 194.218026 L 384.191406 196.350839 L 384.984375 198.440683 L 385.679688 200.483651 L 386.253906 202.468026 L 386.691406 204.378183 L 386.972656 206.21412 L 387.085938 207.968026 L 387.019531 209.628183 L 386.765625 211.194589 L 386.316406 212.667245 L 385.667969 214.038339 L 384.820312 215.30787 L 383.78125 216.483651 L 382.546875 217.561776 L 381.132812 218.550058 L 379.546875 219.452401 L 377.800781 220.272714 L 375.910156 221.022714 L 373.894531 221.710214 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill="none" stroke-width="0.8" stroke-linecap="square" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 321.722656 39.010995 L 321.722656 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill="none" stroke-width="0.8" stroke-linecap="square" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 321.722656 39.010995 L 545.453125 39.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill-rule="nonzero" fill="rgb(0%, 100%, 0%)" fill-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 427.292969 77.65162 C 428.078125 77.65162 428.832031 77.96412 429.386719 78.518808 C 429.941406 79.073495 430.25 79.827401 430.25 80.612558 C 430.25 81.397714 429.941406 82.147714 429.386719 82.702401 C 428.832031 83.257089 428.078125 83.569589 427.292969 83.569589 C 426.507812 83.569589 425.757812 83.257089 425.203125 82.702401 C 424.648438 82.147714 424.335938 81.397714 424.335938 80.612558 C 424.335938 79.827401 424.648438 79.073495 425.203125 78.518808 C 425.757812 77.96412 426.507812 77.65162 427.292969 77.65162 Z M 427.292969 77.65162 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill-rule="nonzero" fill="rgb(100%, 0%, 0%)" fill-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 365.726562 216.147714 L 371.640625 216.147714 L 371.640625 222.061776 L 365.726562 222.061776 Z M 365.726562 216.147714 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-3-11" x="396.704984" y="16.325"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-3-1" x="403.519109" y="16.325"/>
<use xlink:href="#glyph-3-1" x="410.899109" y="16.325"/>
<use xlink:href="#glyph-3-12" x="418.279109" y="16.325"/>
<use xlink:href="#glyph-3-4" x="423.211109" y="16.325"/>
<use xlink:href="#glyph-3-5" x="427.027109" y="16.325"/>
<use xlink:href="#glyph-3-6" x="437.383109" y="16.325"/>
<use xlink:href="#glyph-3-7" x="444.727109" y="16.325"/>
<use xlink:href="#glyph-3-8" x="451.831109" y="16.325"/>
<use xlink:href="#glyph-3-9" x="455.167109" y="16.325"/>
<use xlink:href="#glyph-3-10" x="462.775109" y="16.325"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-3-0" x="403.244046" y="29.775"/>
<use xlink:href="#glyph-3-1" x="410.864046" y="29.775"/>
<use xlink:href="#glyph-3-2" x="418.244046" y="29.775"/>
<use xlink:href="#glyph-3-3" x="421.580046" y="29.775"/>
<use xlink:href="#glyph-3-4" x="425.804046" y="29.775"/>
<use xlink:href="#glyph-3-0" x="429.620046" y="29.775"/>
<use xlink:href="#glyph-3-13" x="437.240046" y="29.775"/>
<use xlink:href="#glyph-3-14" x="441.944046" y="29.775"/>
<use xlink:href="#glyph-3-13" x="449.300046" y="29.775"/>
<use xlink:href="#glyph-3-8" x="454.004046" y="29.775"/>
<use xlink:href="#glyph-3-15" x="457.340046" y="29.775"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-3-11" x="359.736234" y="43.225"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-3-1" x="366.550359" y="43.225"/>
<use xlink:href="#glyph-3-14" x="373.930359" y="43.225"/>
<use xlink:href="#glyph-3-16" x="381.286359" y="43.225"/>
<use xlink:href="#glyph-3-17" x="388.234359" y="43.225"/>
<use xlink:href="#glyph-3-4" x="392.278359" y="43.225"/>
<use xlink:href="#glyph-3-22" x="396.094359" y="43.225"/>
<use xlink:href="#glyph-3-19" x="403.726359" y="43.225"/>
<use xlink:href="#glyph-3-18" x="407.542359" y="43.225"/>
<use xlink:href="#glyph-3-18" x="415.174359" y="43.225"/>
<use xlink:href="#glyph-3-18" x="422.806359" y="43.225"/>
<use xlink:href="#glyph-3-4" x="430.438359" y="43.225"/>
<use xlink:href="#glyph-3-23" x="434.254359" y="43.225"/>
<use xlink:href="#glyph-3-12" x="438.934359" y="43.225"/>
<use xlink:href="#glyph-3-14" x="443.866359" y="43.225"/>
<use xlink:href="#glyph-3-24" x="451.222359" y="43.225"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-3-17" x="460.382109" y="43.225"/>
<use xlink:href="#glyph-3-4" x="464.426109" y="43.225"/>
<use xlink:href="#glyph-3-22" x="468.242109" y="43.225"/>
<use xlink:href="#glyph-3-19" x="475.874109" y="43.225"/>
<use xlink:href="#glyph-3-25" x="479.690109" y="43.225"/>
<use xlink:href="#glyph-3-26" x="487.322109" y="43.225"/>
<use xlink:href="#glyph-3-22" x="494.954109" y="43.225"/>
<use xlink:href="#glyph-3-27" x="502.586109" y="43.225"/>
</g>
<path fill-rule="nonzero" fill="rgb(100%, 100%, 100%)" fill-opacity="1" d="M 601.386719 281.957031 L 825.117188 281.957031 L 825.117188 58.226562 L 601.386719 58.226562 Z M 601.386719 281.957031 "/>
<g clip-path="url(#clip-26)">
<use xlink:href="#source-86" transform="matrix(0.24, 0, 0, 0.24, 601.385691, 58.035526)"/>
</g>
<path fill="none" stroke-width="1" stroke-linecap="square" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="0.8" stroke-miterlimit="10" d="M 698.105469 76.186776 L 697.933594 77.792245 L 697.902344 79.475839 L 698.015625 81.225839 L 698.257812 83.042245 L 698.617188 84.917245 L 699.09375 86.850839 L 699.667969 88.831308 L 700.328125 90.854745 L 701.0625 92.917245 L 701.859375 95.010995 L 702.699219 97.128183 L 703.570312 99.257089 L 704.457031 101.397714 L 705.34375 103.534433 L 706.214844 105.663339 L 707.054688 107.780526 L 707.851562 109.87037 L 708.585938 111.93287 L 709.242188 113.960214 L 709.816406 115.940683 L 710.285156 117.87037 L 710.644531 119.74537 L 710.882812 121.55787 L 710.988281 123.30787 L 710.957031 124.983651 L 710.78125 126.593026 L 710.453125 128.124276 L 709.976562 129.577401 L 709.339844 130.956308 L 708.546875 132.257089 L 707.601562 133.475839 L 706.503906 134.624276 L 705.257812 135.694589 L 703.871094 136.694589 L 702.347656 137.628183 L 700.699219 138.499276 L 698.929688 139.311776 L 697.054688 140.069589 L 695.085938 140.780526 L 693.039062 141.448495 L 690.917969 142.085214 L 688.746094 142.694589 L 686.535156 143.280526 L 684.296875 143.858651 L 682.050781 144.43287 L 679.816406 145.007089 L 677.597656 145.593026 L 675.421875 146.198495 L 673.292969 146.831308 L 671.234375 147.49537 L 669.257812 148.202401 L 667.371094 148.956308 L 665.589844 149.760995 L 663.925781 150.62037 L 662.386719 151.546151 L 660.980469 152.538339 L 659.71875 153.604745 L 658.601562 154.741464 L 657.636719 155.952401 L 656.828125 157.241464 L 656.175781 158.612558 L 655.675781 160.05787 L 655.332031 161.577401 L 655.136719 163.175058 L 655.085938 164.846933 L 655.179688 166.58912 L 655.402344 168.393808 L 655.746094 170.260995 L 656.203125 172.186776 L 656.765625 174.159433 L 657.414062 176.178964 L 658.140625 178.237558 L 658.925781 180.327401 L 659.761719 182.440683 L 660.628906 184.569589 L 662.402344 188.843026 L 663.277344 190.975839 L 664.125 193.093026 L 664.925781 195.190683 L 665.667969 197.257089 L 666.339844 199.288339 L 666.925781 201.27662 L 667.410156 203.21412 L 667.789062 205.096933 L 668.042969 206.917245 L 668.171875 208.678964 L 668.160156 210.366464 L 668.003906 211.983651 L 667.699219 213.52662 L 667.242188 214.991464 L 666.632812 216.382089 L 665.863281 217.694589 L 664.9375 218.925058 L 663.863281 220.081308 L 662.640625 221.167245 L 661.269531 222.175058 L 659.765625 223.12037 L 658.136719 223.999276 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill="none" stroke-width="1" stroke-linecap="square" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="0.8" stroke-miterlimit="10" d="M 710.730469 85.018808 L 709.933594 87.112558 L 709.179688 89.18287 L 708.496094 91.221933 L 707.898438 93.21412 L 707.394531 95.159433 L 707.007812 97.050058 L 706.746094 98.874276 L 706.625 100.632089 L 706.652344 102.311776 L 706.835938 103.917245 L 707.179688 105.440683 L 707.691406 106.878183 L 708.371094 108.233651 L 709.21875 109.503183 L 710.234375 110.690683 L 711.410156 111.796151 L 712.742188 112.827401 L 714.222656 113.780526 L 715.84375 114.667245 L 717.589844 115.487558 L 719.449219 116.253183 L 721.40625 116.968026 L 723.449219 117.643808 L 725.558594 118.284433 L 727.714844 118.90162 L 729.902344 119.503183 L 732.101562 120.096933 L 734.292969 120.694589 L 736.460938 121.30787 L 738.582031 121.940683 L 740.644531 122.604745 L 742.628906 123.30787 L 744.519531 124.05787 L 746.296875 124.862558 L 747.957031 125.729745 L 749.476562 126.663339 L 750.851562 127.671151 L 752.074219 128.753183 L 753.136719 129.917245 L 754.03125 131.163339 L 754.761719 132.49537 L 755.320312 133.909433 L 755.710938 135.409433 L 755.941406 136.991464 L 756.011719 138.65162 L 755.929688 140.385995 L 755.707031 142.190683 L 755.355469 144.061776 L 754.882812 145.991464 L 754.3125 147.975839 L 753.648438 149.999276 L 752.914062 152.061776 L 752.128906 154.15162 L 751.304688 156.257089 L 750.460938 158.374276 L 749.621094 160.487558 L 748.800781 162.593026 L 748.015625 164.68287 L 747.289062 166.741464 L 746.636719 168.760995 L 746.074219 170.737558 L 745.613281 172.663339 L 745.273438 174.52662 L 745.066406 176.327401 L 745 178.053964 L 745.085938 179.706308 L 745.332031 181.280526 L 745.738281 182.768808 L 746.316406 184.175058 L 747.058594 185.499276 L 747.972656 186.737558 L 749.050781 187.893808 L 750.289062 188.968026 L 751.679688 189.968026 L 753.214844 190.893808 L 754.886719 191.757089 L 756.679688 192.553964 L 758.578125 193.300058 L 760.570312 193.999276 L 762.640625 194.659433 L 764.769531 195.288339 L 766.9375 195.897714 L 769.132812 196.49537 L 771.332031 197.093026 L 773.515625 197.694589 L 775.667969 198.311776 L 777.769531 198.956308 L 779.804688 199.635995 L 781.753906 200.354745 L 783.601562 201.124276 L 785.335938 201.952401 L 786.941406 202.846933 L 788.410156 203.80787 L 789.726562 204.843026 L 790.886719 205.960214 L 791.886719 207.155526 L 792.714844 208.43287 L 793.378906 209.796151 L 793.875 211.24537 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill="none" stroke-width="1" stroke-linecap="square" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="0.8" stroke-miterlimit="10" d="M 706.625 80.444589 L 705.3125 81.483651 L 704.097656 82.569589 L 702.980469 83.710214 L 701.972656 84.90162 L 701.078125 86.147714 L 700.292969 87.448495 L 699.625 88.811776 L 699.074219 90.229745 L 698.636719 91.706308 L 698.308594 93.237558 L 698.085938 94.819589 L 697.960938 96.452401 L 697.933594 98.132089 L 697.988281 99.854745 L 698.117188 101.616464 L 698.3125 103.409433 L 698.558594 105.225839 L 698.847656 107.065683 L 699.164062 108.917245 L 699.496094 110.780526 L 699.832031 112.643808 L 700.15625 114.499276 L 700.457031 116.346933 L 700.722656 118.171151 L 700.9375 119.975839 L 701.09375 121.749276 L 701.179688 123.483651 L 701.183594 125.18287 L 701.097656 126.835214 L 700.914062 128.436776 L 700.625 129.987558 L 700.230469 131.487558 L 699.722656 132.925058 L 699.097656 134.311776 L 698.359375 135.635995 L 697.507812 136.905526 L 696.539062 138.116464 L 695.464844 139.272714 L 694.289062 140.378183 L 693.011719 141.436776 L 691.648438 142.448495 L 690.203125 143.421151 L 688.6875 144.358651 L 687.113281 145.268808 L 685.492188 146.15162 L 683.832031 147.014901 L 682.152344 147.87037 L 680.460938 148.721933 L 678.773438 149.569589 L 677.101562 150.428964 L 675.457031 151.303964 L 673.859375 152.198495 L 672.3125 153.12037 L 670.828125 154.073495 L 669.421875 155.065683 L 668.101562 156.100839 L 666.871094 157.178964 L 665.742188 158.311776 L 664.722656 159.49537 L 663.808594 160.733651 L 663.011719 162.030526 L 662.328125 163.382089 L 661.761719 164.796151 L 661.308594 166.264901 L 660.96875 167.788339 L 660.730469 169.362558 L 660.597656 170.991464 L 660.554688 172.667245 L 660.601562 174.382089 L 660.722656 176.139901 L 660.910156 177.928964 L 661.148438 179.741464 L 661.433594 181.581308 L 661.746094 183.43287 L 662.078125 185.292245 L 662.414062 187.155526 L 662.742188 189.014901 L 663.046875 190.858651 L 663.316406 192.690683 L 663.539062 194.49537 L 663.703125 196.272714 L 663.796875 198.014901 L 663.8125 199.718026 L 663.738281 201.374276 L 663.566406 202.987558 L 663.292969 204.542245 L 662.914062 206.046151 L 662.417969 207.49537 L 661.808594 208.885995 L 661.085938 210.218026 L 660.246094 211.49537 L 659.296875 212.71412 L 658.234375 213.878183 L 657.070312 214.991464 L 655.804688 216.053964 L 654.453125 217.073495 L 653.019531 218.050058 L 651.511719 218.991464 L 649.945312 219.90162 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g clip-path="url(#clip-27)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 601.386719 39.010995 L 601.386719 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 601.386719 39.010995 L 601.386719 35.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-0" x="598.206003" y="297.049276"/>
</g>
<g clip-path="url(#clip-28)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 646.132812 39.010995 L 646.132812 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 646.132812 39.010995 L 646.132812 35.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-1" x="642.952109" y="297.049276"/>
</g>
<g clip-path="url(#clip-29)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 690.878906 39.010995 L 690.878906 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 690.878906 39.010995 L 690.878906 35.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-2" x="687.698214" y="297.049276"/>
</g>
<g clip-path="url(#clip-30)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 735.625 39.010995 L 735.625 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 735.625 39.010995 L 735.625 35.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-3" x="732.444319" y="297.049276"/>
</g>
<g clip-path="url(#clip-31)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 780.371094 39.010995 L 780.371094 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 780.371094 39.010995 L 780.371094 35.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-4" x="774.010737" y="297.049276"/>
<use xlink:href="#glyph-0-5" x="780.370737" y="297.049276"/>
</g>
<g clip-path="url(#clip-32)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 825.117188 39.010995 L 825.117188 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 825.117188 39.010995 L 825.117188 35.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-4" x="818.756842" y="297.049276"/>
<use xlink:href="#glyph-0-6" x="825.116842" y="297.049276"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-1-0" x="674.891579" y="311.486776"/>
<use xlink:href="#glyph-1-1" x="682.426579" y="311.486776"/>
<use xlink:href="#glyph-1-2" x="685.924579" y="311.486776"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-1-3" x="692.166954" y="311.486776"/>
<use xlink:href="#glyph-1-4" x="698.898954" y="311.486776"/>
<use xlink:href="#glyph-1-5" x="704.629954" y="311.486776"/>
<use xlink:href="#glyph-1-6" x="707.687954" y="311.486776"/>
<use xlink:href="#glyph-1-5" x="711.999954" y="311.486776"/>
<use xlink:href="#glyph-1-3" x="715.057954" y="311.486776"/>
<use xlink:href="#glyph-1-7" x="721.789954" y="311.486776"/>
<use xlink:href="#glyph-1-1" x="728.763954" y="311.486776"/>
<use xlink:href="#glyph-1-8" x="732.261954" y="311.486776"/>
<use xlink:href="#glyph-1-9" x="736.551954" y="311.486776"/>
<use xlink:href="#glyph-1-10" x="747.265954" y="311.486776"/>
</g>
<g clip-path="url(#clip-33)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 601.386719 39.010995 L 825.117188 39.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 601.386719 39.010995 L 597.386719 39.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-0" x="587.526316" y="285.752401"/>
</g>
<g clip-path="url(#clip-34)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 601.386719 83.757089 L 825.117188 83.757089 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 601.386719 83.757089 L 597.386719 83.757089 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-1" x="587.526316" y="241.006296"/>
</g>
<g clip-path="url(#clip-35)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 601.386719 128.503183 L 825.117188 128.503183 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 601.386719 128.503183 L 597.386719 128.503183 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-2" x="587.526316" y="196.260191"/>
</g>
<g clip-path="url(#clip-36)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 601.386719 173.249276 L 825.117188 173.249276 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 601.386719 173.249276 L 597.386719 173.249276 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-3" x="587.526316" y="151.514086"/>
</g>
<g clip-path="url(#clip-37)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 601.386719 217.99537 L 825.117188 217.99537 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 601.386719 217.99537 L 597.386719 217.99537 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-4" x="581.166941" y="106.76798"/>
<use xlink:href="#glyph-0-5" x="587.526941" y="106.76798"/>
</g>
<g clip-path="url(#clip-38)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 601.386719 262.741464 L 825.117188 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 601.386719 262.741464 L 597.386719 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-4" x="581.166941" y="62.021875"/>
<use xlink:href="#glyph-0-6" x="587.526941" y="62.021875"/>
</g>
<path fill="none" stroke-width="2.5" stroke-linecap="square" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.9" stroke-miterlimit="10" d="M 698.105469 76.186776 L 697.933594 77.792245 L 697.902344 79.475839 L 698.015625 81.225839 L 698.257812 83.042245 L 698.617188 84.917245 L 699.09375 86.850839 L 699.667969 88.831308 L 700.328125 90.854745 L 701.0625 92.917245 L 701.859375 95.010995 L 702.699219 97.128183 L 703.570312 99.257089 L 704.457031 101.397714 L 705.34375 103.534433 L 706.214844 105.663339 L 707.054688 107.780526 L 707.851562 109.87037 L 708.585938 111.93287 L 709.242188 113.960214 L 709.816406 115.940683 L 710.285156 117.87037 L 710.644531 119.74537 L 710.882812 121.55787 L 710.988281 123.30787 L 710.957031 124.983651 L 710.78125 126.593026 L 710.453125 128.124276 L 709.976562 129.577401 L 709.339844 130.956308 L 708.546875 132.257089 L 707.601562 133.475839 L 706.503906 134.624276 L 705.257812 135.694589 L 703.871094 136.694589 L 702.347656 137.628183 L 700.699219 138.499276 L 698.929688 139.311776 L 697.054688 140.069589 L 695.085938 140.780526 L 693.039062 141.448495 L 690.917969 142.085214 L 688.746094 142.694589 L 686.535156 143.280526 L 684.296875 143.858651 L 682.050781 144.43287 L 679.816406 145.007089 L 677.597656 145.593026 L 675.421875 146.198495 L 673.292969 146.831308 L 671.234375 147.49537 L 669.257812 148.202401 L 667.371094 148.956308 L 665.589844 149.760995 L 663.925781 150.62037 L 662.386719 151.546151 L 660.980469 152.538339 L 659.71875 153.604745 L 658.601562 154.741464 L 657.636719 155.952401 L 656.828125 157.241464 L 656.175781 158.612558 L 655.675781 160.05787 L 655.332031 161.577401 L 655.136719 163.175058 L 655.085938 164.846933 L 655.179688 166.58912 L 655.402344 168.393808 L 655.746094 170.260995 L 656.203125 172.186776 L 656.765625 174.159433 L 657.414062 176.178964 L 658.140625 178.237558 L 658.925781 180.327401 L 659.761719 182.440683 L 660.628906 184.569589 L 662.402344 188.843026 L 663.277344 190.975839 L 664.125 193.093026 L 664.925781 195.190683 L 665.667969 197.257089 L 666.339844 199.288339 L 666.925781 201.27662 L 667.410156 203.21412 L 667.789062 205.096933 L 668.042969 206.917245 L 668.171875 208.678964 L 668.160156 210.366464 L 668.003906 211.983651 L 667.699219 213.52662 L 667.242188 214.991464 L 666.632812 216.382089 L 665.863281 217.694589 L 664.9375 218.925058 L 663.863281 220.081308 L 662.640625 221.167245 L 661.269531 222.175058 L 659.765625 223.12037 L 658.136719 223.999276 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill="none" stroke-width="2.5" stroke-linecap="square" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.9" stroke-miterlimit="10" d="M 710.730469 85.018808 L 709.933594 87.112558 L 709.179688 89.18287 L 708.496094 91.221933 L 707.898438 93.21412 L 707.394531 95.159433 L 707.007812 97.050058 L 706.746094 98.874276 L 706.625 100.632089 L 706.652344 102.311776 L 706.835938 103.917245 L 707.179688 105.440683 L 707.691406 106.878183 L 708.371094 108.233651 L 709.21875 109.503183 L 710.234375 110.690683 L 711.410156 111.796151 L 712.742188 112.827401 L 714.222656 113.780526 L 715.84375 114.667245 L 717.589844 115.487558 L 719.449219 116.253183 L 721.40625 116.968026 L 723.449219 117.643808 L 725.558594 118.284433 L 727.714844 118.90162 L 729.902344 119.503183 L 732.101562 120.096933 L 734.292969 120.694589 L 736.460938 121.30787 L 738.582031 121.940683 L 740.644531 122.604745 L 742.628906 123.30787 L 744.519531 124.05787 L 746.296875 124.862558 L 747.957031 125.729745 L 749.476562 126.663339 L 750.851562 127.671151 L 752.074219 128.753183 L 753.136719 129.917245 L 754.03125 131.163339 L 754.761719 132.49537 L 755.320312 133.909433 L 755.710938 135.409433 L 755.941406 136.991464 L 756.011719 138.65162 L 755.929688 140.385995 L 755.707031 142.190683 L 755.355469 144.061776 L 754.882812 145.991464 L 754.3125 147.975839 L 753.648438 149.999276 L 752.914062 152.061776 L 752.128906 154.15162 L 751.304688 156.257089 L 750.460938 158.374276 L 749.621094 160.487558 L 748.800781 162.593026 L 748.015625 164.68287 L 747.289062 166.741464 L 746.636719 168.760995 L 746.074219 170.737558 L 745.613281 172.663339 L 745.273438 174.52662 L 745.066406 176.327401 L 745 178.053964 L 745.085938 179.706308 L 745.332031 181.280526 L 745.738281 182.768808 L 746.316406 184.175058 L 747.058594 185.499276 L 747.972656 186.737558 L 749.050781 187.893808 L 750.289062 188.968026 L 751.679688 189.968026 L 753.214844 190.893808 L 754.886719 191.757089 L 756.679688 192.553964 L 758.578125 193.300058 L 760.570312 193.999276 L 762.640625 194.659433 L 764.769531 195.288339 L 766.9375 195.897714 L 769.132812 196.49537 L 771.332031 197.093026 L 773.515625 197.694589 L 775.667969 198.311776 L 777.769531 198.956308 L 779.804688 199.635995 L 781.753906 200.354745 L 783.601562 201.124276 L 785.335938 201.952401 L 786.941406 202.846933 L 788.410156 203.80787 L 789.726562 204.843026 L 790.886719 205.960214 L 791.886719 207.155526 L 792.714844 208.43287 L 793.378906 209.796151 L 793.875 211.24537 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill="none" stroke-width="2.5" stroke-linecap="square" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.9" stroke-miterlimit="10" d="M 706.625 80.444589 L 705.3125 81.483651 L 704.097656 82.569589 L 702.980469 83.710214 L 701.972656 84.90162 L 701.078125 86.147714 L 700.292969 87.448495 L 699.625 88.811776 L 699.074219 90.229745 L 698.636719 91.706308 L 698.308594 93.237558 L 698.085938 94.819589 L 697.960938 96.452401 L 697.933594 98.132089 L 697.988281 99.854745 L 698.117188 101.616464 L 698.3125 103.409433 L 698.558594 105.225839 L 698.847656 107.065683 L 699.164062 108.917245 L 699.496094 110.780526 L 699.832031 112.643808 L 700.15625 114.499276 L 700.457031 116.346933 L 700.722656 118.171151 L 700.9375 119.975839 L 701.09375 121.749276 L 701.179688 123.483651 L 701.183594 125.18287 L 701.097656 126.835214 L 700.914062 128.436776 L 700.625 129.987558 L 700.230469 131.487558 L 699.722656 132.925058 L 699.097656 134.311776 L 698.359375 135.635995 L 697.507812 136.905526 L 696.539062 138.116464 L 695.464844 139.272714 L 694.289062 140.378183 L 693.011719 141.436776 L 691.648438 142.448495 L 690.203125 143.421151 L 688.6875 144.358651 L 687.113281 145.268808 L 685.492188 146.15162 L 683.832031 147.014901 L 682.152344 147.87037 L 680.460938 148.721933 L 678.773438 149.569589 L 677.101562 150.428964 L 675.457031 151.303964 L 673.859375 152.198495 L 672.3125 153.12037 L 670.828125 154.073495 L 669.421875 155.065683 L 668.101562 156.100839 L 666.871094 157.178964 L 665.742188 158.311776 L 664.722656 159.49537 L 663.808594 160.733651 L 663.011719 162.030526 L 662.328125 163.382089 L 661.761719 164.796151 L 661.308594 166.264901 L 660.96875 167.788339 L 660.730469 169.362558 L 660.597656 170.991464 L 660.554688 172.667245 L 660.601562 174.382089 L 660.722656 176.139901 L 660.910156 177.928964 L 661.148438 179.741464 L 661.433594 181.581308 L 661.746094 183.43287 L 662.078125 185.292245 L 662.414062 187.155526 L 662.742188 189.014901 L 663.046875 190.858651 L 663.316406 192.690683 L 663.539062 194.49537 L 663.703125 196.272714 L 663.796875 198.014901 L 663.8125 199.718026 L 663.738281 201.374276 L 663.566406 202.987558 L 663.292969 204.542245 L 662.914062 206.046151 L 662.417969 207.49537 L 661.808594 208.885995 L 661.085938 210.218026 L 660.246094 211.49537 L 659.296875 212.71412 L 658.234375 213.878183 L 657.070312 214.991464 L 655.804688 216.053964 L 654.453125 217.073495 L 653.019531 218.050058 L 651.511719 218.991464 L 649.945312 219.90162 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill="none" stroke-width="0.8" stroke-linecap="square" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 601.386719 39.010995 L 601.386719 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill="none" stroke-width="0.8" stroke-linecap="square" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 601.386719 39.010995 L 825.117188 39.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill-rule="nonzero" fill="rgb(0%, 100%, 0%)" fill-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 698.105469 73.225839 C 698.890625 73.225839 699.640625 73.538339 700.195312 74.093026 C 700.75 74.647714 701.0625 75.40162 701.0625 76.186776 C 701.0625 76.968026 700.75 77.721933 700.195312 78.27662 C 699.640625 78.831308 698.890625 79.143808 698.105469 79.143808 C 697.320312 79.143808 696.566406 78.831308 696.011719 78.27662 C 695.457031 77.721933 695.148438 76.968026 695.148438 76.186776 C 695.148438 75.40162 695.457031 74.647714 696.011719 74.093026 C 696.566406 73.538339 697.320312 73.225839 698.105469 73.225839 Z M 698.105469 73.225839 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill-rule="nonzero" fill="rgb(100%, 0%, 0%)" fill-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 655.175781 221.042245 L 661.09375 221.042245 L 661.09375 226.956308 L 655.175781 226.956308 Z M 655.175781 221.042245 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-3-28" x="675.711891" y="29.775"/>
<use xlink:href="#glyph-3-6" x="683.943891" y="29.775"/>
<use xlink:href="#glyph-3-13" x="691.287891" y="29.775"/>
<use xlink:href="#glyph-3-29" x="695.991891" y="29.775"/>
<use xlink:href="#glyph-3-4" x="703.599891" y="29.775"/>
<use xlink:href="#glyph-3-5" x="707.415891" y="29.775"/>
<use xlink:href="#glyph-3-6" x="717.771891" y="29.775"/>
<use xlink:href="#glyph-3-7" x="725.115891" y="29.775"/>
<use xlink:href="#glyph-3-8" x="732.219891" y="29.775"/>
<use xlink:href="#glyph-3-9" x="735.555891" y="29.775"/>
<use xlink:href="#glyph-3-10" x="743.163891" y="29.775"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-3-11" x="639.399391" y="43.225"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-3-1" x="646.213516" y="43.225"/>
<use xlink:href="#glyph-3-14" x="653.593516" y="43.225"/>
<use xlink:href="#glyph-3-16" x="660.949516" y="43.225"/>
<use xlink:href="#glyph-3-17" x="667.897516" y="43.225"/>
<use xlink:href="#glyph-3-4" x="671.941516" y="43.225"/>
<use xlink:href="#glyph-3-22" x="675.757516" y="43.225"/>
<use xlink:href="#glyph-3-19" x="683.389516" y="43.225"/>
<use xlink:href="#glyph-3-18" x="687.205516" y="43.225"/>
<use xlink:href="#glyph-3-18" x="694.837516" y="43.225"/>
<use xlink:href="#glyph-3-18" x="702.469516" y="43.225"/>
<use xlink:href="#glyph-3-4" x="710.101516" y="43.225"/>
<use xlink:href="#glyph-3-23" x="713.917516" y="43.225"/>
<use xlink:href="#glyph-3-12" x="718.597516" y="43.225"/>
<use xlink:href="#glyph-3-14" x="723.529516" y="43.225"/>
<use xlink:href="#glyph-3-24" x="730.885516" y="43.225"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-3-17" x="740.045266" y="43.225"/>
<use xlink:href="#glyph-3-4" x="744.089266" y="43.225"/>
<use xlink:href="#glyph-3-22" x="747.905266" y="43.225"/>
<use xlink:href="#glyph-3-19" x="755.537266" y="43.225"/>
<use xlink:href="#glyph-3-25" x="759.353266" y="43.225"/>
<use xlink:href="#glyph-3-30" x="766.985266" y="43.225"/>
<use xlink:href="#glyph-3-31" x="774.617266" y="43.225"/>
<use xlink:href="#glyph-3-27" x="782.249266" y="43.225"/>
</g>
<path fill-rule="nonzero" fill="rgb(100%, 100%, 100%)" fill-opacity="1" d="M 881.050781 281.957031 L 1104.78125 281.957031 L 1104.78125 58.226562 L 881.050781 58.226562 Z M 881.050781 281.957031 "/>
<g clip-path="url(#clip-39)">
<use xlink:href="#source-93" transform="matrix(0.24, 0, 0, 0.24, 881.048849, 58.035526)"/>
</g>
<path fill="none" stroke-width="1" stroke-linecap="square" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="0.8" stroke-miterlimit="10" d="M 992.914062 83.757089 L 992.914062 83.757089 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill="none" stroke-width="1" stroke-linecap="square" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="0.8" stroke-miterlimit="10" d="M 992.914062 83.757089 L 992.914062 83.757089 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill="none" stroke-width="1" stroke-linecap="square" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="0.8" stroke-miterlimit="10" d="M 992.914062 83.757089 L 992.914062 83.757089 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g clip-path="url(#clip-40)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 881.050781 39.010995 L 881.050781 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 881.050781 39.010995 L 881.050781 35.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-0" x="877.869161" y="297.049276"/>
</g>
<g clip-path="url(#clip-41)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 925.796875 39.010995 L 925.796875 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 925.796875 39.010995 L 925.796875 35.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-1" x="922.615266" y="297.049276"/>
</g>
<g clip-path="url(#clip-42)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 970.542969 39.010995 L 970.542969 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 970.542969 39.010995 L 970.542969 35.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-2" x="967.361372" y="297.049276"/>
</g>
<g clip-path="url(#clip-43)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 1015.289062 39.010995 L 1015.289062 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 1015.289062 39.010995 L 1015.289062 35.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-3" x="1012.107477" y="297.049276"/>
</g>
<g clip-path="url(#clip-44)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 1060.035156 39.010995 L 1060.035156 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 1060.035156 39.010995 L 1060.035156 35.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-4" x="1053.673895" y="297.049276"/>
<use xlink:href="#glyph-0-5" x="1060.033895" y="297.049276"/>
</g>
<g clip-path="url(#clip-45)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 1104.78125 39.010995 L 1104.78125 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 1104.78125 39.010995 L 1104.78125 35.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-4" x="1098.42" y="297.049276"/>
<use xlink:href="#glyph-0-6" x="1104.78" y="297.049276"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-1-0" x="954.554737" y="311.486776"/>
<use xlink:href="#glyph-1-1" x="962.089737" y="311.486776"/>
<use xlink:href="#glyph-1-2" x="965.587737" y="311.486776"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-1-3" x="971.830112" y="311.486776"/>
<use xlink:href="#glyph-1-4" x="978.562112" y="311.486776"/>
<use xlink:href="#glyph-1-5" x="984.293112" y="311.486776"/>
<use xlink:href="#glyph-1-6" x="987.351112" y="311.486776"/>
<use xlink:href="#glyph-1-5" x="991.663112" y="311.486776"/>
<use xlink:href="#glyph-1-3" x="994.721112" y="311.486776"/>
<use xlink:href="#glyph-1-7" x="1001.453112" y="311.486776"/>
<use xlink:href="#glyph-1-1" x="1008.427112" y="311.486776"/>
<use xlink:href="#glyph-1-8" x="1011.925112" y="311.486776"/>
<use xlink:href="#glyph-1-9" x="1016.215112" y="311.486776"/>
<use xlink:href="#glyph-1-10" x="1026.929112" y="311.486776"/>
</g>
<g clip-path="url(#clip-46)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 881.050781 39.010995 L 1104.78125 39.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 881.050781 39.010995 L 877.050781 39.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-0" x="867.189474" y="285.752401"/>
</g>
<g clip-path="url(#clip-47)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 881.050781 83.757089 L 1104.78125 83.757089 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 881.050781 83.757089 L 877.050781 83.757089 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-1" x="867.189474" y="241.006296"/>
</g>
<g clip-path="url(#clip-48)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 881.050781 128.503183 L 1104.78125 128.503183 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 881.050781 128.503183 L 877.050781 128.503183 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-2" x="867.189474" y="196.260191"/>
</g>
<g clip-path="url(#clip-49)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 881.050781 173.249276 L 1104.78125 173.249276 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 881.050781 173.249276 L 877.050781 173.249276 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-3" x="867.189474" y="151.514086"/>
</g>
<g clip-path="url(#clip-50)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 881.050781 217.99537 L 1104.78125 217.99537 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 881.050781 217.99537 L 877.050781 217.99537 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-4" x="860.830099" y="106.76798"/>
<use xlink:href="#glyph-0-5" x="867.190099" y="106.76798"/>
</g>
<g clip-path="url(#clip-51)">
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.6" stroke-dasharray="0.8 1.32" stroke-miterlimit="10" d="M 881.050781 262.741464 L 1104.78125 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 881.050781 262.741464 L 877.050781 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-4" x="860.830099" y="62.021875"/>
<use xlink:href="#glyph-0-6" x="867.190099" y="62.021875"/>
</g>
<path fill="none" stroke-width="2.5" stroke-linecap="square" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.9" stroke-miterlimit="10" d="M 992.914062 83.757089 L 992.914062 83.757089 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill="none" stroke-width="2.5" stroke-linecap="square" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.9" stroke-miterlimit="10" d="M 992.914062 83.757089 L 992.914062 83.757089 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill="none" stroke-width="2.5" stroke-linecap="square" stroke-linejoin="round" stroke="rgb(100%, 100%, 100%)" stroke-opacity="0.9" stroke-miterlimit="10" d="M 992.914062 83.757089 L 992.914062 83.757089 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill="none" stroke-width="0.8" stroke-linecap="square" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 881.050781 39.010995 L 881.050781 262.741464 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill="none" stroke-width="0.8" stroke-linecap="square" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 881.050781 39.010995 L 1104.78125 39.010995 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill-rule="nonzero" fill="rgb(0%, 100%, 0%)" fill-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 992.914062 80.800058 C 993.699219 80.800058 994.449219 81.112558 995.003906 81.667245 C 995.558594 82.221933 995.871094 82.975839 995.871094 83.757089 C 995.871094 84.542245 995.558594 85.296151 995.003906 85.850839 C 994.449219 86.405526 993.699219 86.718026 992.914062 86.718026 C 992.128906 86.718026 991.378906 86.405526 990.824219 85.850839 C 990.269531 85.296151 989.957031 84.542245 989.957031 83.757089 C 989.957031 82.975839 990.269531 82.221933 990.824219 81.667245 C 991.378906 81.112558 992.128906 80.800058 992.914062 80.800058 Z M 992.914062 80.800058 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<path fill-rule="nonzero" fill="rgb(100%, 0%, 0%)" fill-opacity="1" stroke-width="1" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 989.957031 80.800058 L 995.871094 80.800058 L 995.871094 86.718026 L 989.957031 86.718026 Z M 989.957031 80.800058 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-3-28" x="959.906299" y="29.775"/>
<use xlink:href="#glyph-3-6" x="968.138299" y="29.775"/>
<use xlink:href="#glyph-3-13" x="975.482299" y="29.775"/>
<use xlink:href="#glyph-3-29" x="980.186299" y="29.775"/>
<use xlink:href="#glyph-3-4" x="987.794299" y="29.775"/>
<use xlink:href="#glyph-3-0" x="991.610299" y="29.775"/>
<use xlink:href="#glyph-3-13" x="999.230299" y="29.775"/>
<use xlink:href="#glyph-3-14" x="1003.934299" y="29.775"/>
<use xlink:href="#glyph-3-13" x="1011.290299" y="29.775"/>
<use xlink:href="#glyph-3-8" x="1015.994299" y="29.775"/>
<use xlink:href="#glyph-3-15" x="1019.330299" y="29.775"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-3-11" x="957.515674" y="43.225"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-3-1" x="964.329799" y="43.225"/>
<use xlink:href="#glyph-3-14" x="971.709799" y="43.225"/>
<use xlink:href="#glyph-3-16" x="979.065799" y="43.225"/>
<use xlink:href="#glyph-3-17" x="986.013799" y="43.225"/>
<use xlink:href="#glyph-3-4" x="990.057799" y="43.225"/>
<use xlink:href="#glyph-3-18" x="993.873799" y="43.225"/>
<use xlink:href="#glyph-3-19" x="1001.505799" y="43.225"/>
<use xlink:href="#glyph-3-22" x="1005.321799" y="43.225"/>
<use xlink:href="#glyph-3-20" x="1012.953799" y="43.225"/>
<use xlink:href="#glyph-3-31" x="1020.585799" y="43.225"/>
</g>
<path fill-rule="nonzero" fill="rgb(100%, 100%, 100%)" fill-opacity="1" d="M 1130.699219 296.089844 L 1156.621094 296.089844 L 1156.621094 44.089844 L 1130.699219 44.089844 Z M 1130.699219 296.089844 "/>
<use xlink:href="#source-96" transform="matrix(0.24, 0, 0, 0.24, 1130.64, 43.92)"/>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 1156.621094 24.878183 L 1160.121094 24.878183 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-0" x="1163.619375" y="299.887138"/>
<use xlink:href="#glyph-0-7" x="1169.979375" y="299.887138"/>
<use xlink:href="#glyph-0-0" x="1173.159375" y="299.887138"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 1156.621094 75.27662 L 1160.121094 75.27662 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-0" x="1163.619375" y="249.487138"/>
<use xlink:href="#glyph-0-7" x="1169.979375" y="249.487138"/>
<use xlink:href="#glyph-0-5" x="1173.159375" y="249.487138"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 1156.621094 125.678964 L 1160.121094 125.678964 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-0" x="1163.619375" y="199.087138"/>
<use xlink:href="#glyph-0-7" x="1169.979375" y="199.087138"/>
<use xlink:href="#glyph-0-8" x="1173.159375" y="199.087138"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 1156.621094 176.077401 L 1160.121094 176.077401 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-0" x="1163.619375" y="148.687138"/>
<use xlink:href="#glyph-0-7" x="1169.979375" y="148.687138"/>
<use xlink:href="#glyph-0-2" x="1173.159375" y="148.687138"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 1156.621094 226.475839 L 1160.121094 226.475839 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-0" x="1163.619375" y="98.287138"/>
<use xlink:href="#glyph-0-7" x="1169.979375" y="98.287138"/>
<use xlink:href="#glyph-0-9" x="1173.159375" y="98.287138"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="butt" stroke-linejoin="round" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 1156.621094 276.878183 L 1160.121094 276.878183 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-4" x="1163.619375" y="47.887138"/>
<use xlink:href="#glyph-0-7" x="1169.979375" y="47.887138"/>
<use xlink:href="#glyph-0-0" x="1173.159375" y="47.887138"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-5-0" x="1188.400625" y="111.012138"/>
<use xlink:href="#glyph-5-1" x="1188.400625" y="119.988138"/>
<use xlink:href="#glyph-5-2" x="1188.400625" y="127.368138"/>
<use xlink:href="#glyph-5-3" x="1188.400625" y="134.976138"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-5-4" x="1188.400625" y="139.658138"/>
<use xlink:href="#glyph-5-5" x="1188.400625" y="147.002138"/>
<use xlink:href="#glyph-5-6" x="1188.400625" y="154.610138"/>
<use xlink:href="#glyph-5-7" x="1188.400625" y="161.966138"/>
<use xlink:href="#glyph-5-8" x="1188.400625" y="165.302138"/>
<use xlink:href="#glyph-5-9" x="1188.400625" y="169.118138"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-5-10" x="1188.400625" y="177.123013"/>
<use xlink:href="#glyph-5-11" x="1188.400625" y="183.723013"/>
<use xlink:href="#glyph-5-12" x="1188.400625" y="188.427013"/>
<use xlink:href="#glyph-5-13" x="1188.400625" y="191.763013"/>
<use xlink:href="#glyph-5-6" x="1188.400625" y="198.867013"/>
<use xlink:href="#glyph-5-11" x="1188.400625" y="206.223013"/>
<use xlink:href="#glyph-5-12" x="1188.400625" y="210.927013"/>
<use xlink:href="#glyph-5-4" x="1188.400625" y="214.263013"/>
<use xlink:href="#glyph-5-5" x="1188.400625" y="221.607013"/>
</g>
<path fill="none" stroke-width="0.8" stroke-linecap="square" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 1130.699219 24.878183 L 1156.621094 24.878183 L 1156.621094 276.878183 L 1130.699219 276.878183 Z M 1130.699219 24.878183 " transform="matrix(1, 0, 0, -1, 0, 320.968026)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-6-0" x="509.705625" y="20.246513"/>
<use xlink:href="#glyph-6-1" x="523.097625" y="20.246513"/>
<use xlink:href="#glyph-6-2" x="533.945625" y="20.246513"/>
<use xlink:href="#glyph-6-3" x="545.337625" y="20.246513"/>
<use xlink:href="#glyph-6-4" x="553.225625" y="20.246513"/>
<use xlink:href="#glyph-6-5" x="564.217625" y="20.246513"/>
<use xlink:href="#glyph-6-6" x="575.609625" y="20.246513"/>
<use xlink:href="#glyph-6-7" x="581.177625" y="20.246513"/>
<use xlink:href="#glyph-6-8" x="592.313625" y="20.246513"/>
<use xlink:href="#glyph-6-9" x="603.449625" y="20.246513"/>
<use xlink:href="#glyph-6-6" x="614.585625" y="20.246513"/>
<use xlink:href="#glyph-6-10" x="620.153625" y="20.246513"/>
<use xlink:href="#glyph-6-6" x="626.793625" y="20.246513"/>
<use xlink:href="#glyph-6-11" x="632.361625" y="20.246513"/>
<use xlink:href="#glyph-6-2" x="644.089625" y="20.246513"/>
<use xlink:href="#glyph-6-3" x="655.481625" y="20.246513"/>
<use xlink:href="#glyph-6-1" x="663.369625" y="20.246513"/>
<use xlink:href="#glyph-6-6" x="674.217625" y="20.246513"/>
<use xlink:href="#glyph-6-12" x="679.785625" y="20.246513"/>
<use xlink:href="#glyph-6-11" x="691.305625" y="20.246513"/>
<use xlink:href="#glyph-6-13" x="703.033625" y="20.246513"/>
</g>
</svg>
