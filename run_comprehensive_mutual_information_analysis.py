#!/usr/bin/env python3
"""
全面的互信息分析脚本 - 分析所有神经元层
包括：LSTM隐层、Bottleneck层、Relational head隐层

使用方法:
python run_comprehensive_mutual_information_analysis.py --model_path logs_social_grid_cells_ddp/20250801_163726/checkpoints/best_model.pth --output_dir comprehensive_mi_analysis --num_reps 200
"""

import os
import sys
import argparse
import torch
import numpy as np
from collections import defaultdict
from tqdm import tqdm

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from config import Config
from models.social_grid_cell import SocialGridCellNetwork
from models.place_hd_cells import PlaceCellEnsemble, HeadDirectionCellEnsemble
from utils.mutual_information_analysis import MutualInformationAnalyzer


# 复制细胞分类器
class CellTypeDetector:
    @staticmethod
    def classify(peak_results: dict) -> str:
        c1, c2, c3, c4 = peak_results[1], peak_results[2], peak_results[3], peak_results[4]
        high_thresh, low_thresh = 0.5, 0.2
        is_responsive_to_self = c1 > high_thresh
        is_responsive_to_peer = c2 > high_thresh
        is_silent_when_static = c4 < low_thresh

        if not is_silent_when_static:
            return "Other (Active when static)"
        if is_responsive_to_self and is_responsive_to_peer:
            return "Special SPC"
        elif is_responsive_to_self and not is_responsive_to_peer:
            return "Pure Place Cell" if c3 > high_thresh else "Mixed Response"
        elif not is_responsive_to_self and is_responsive_to_peer:
            return "Pure SPC" if c3 > high_thresh else "Mixed Response"
        else:
            return "Other"


class ComprehensiveAnalysisResult:
    def __init__(self, neuron_idx: int, layer_name: str):
        self.neuron_idx = neuron_idx
        self.layer_name = layer_name
        self.condition_data = {}
        self.cell_type = None


class ConditionData:
    def __init__(self):
        self.activations = None
        self.peak_activation = 0.0
        self.positions_to_plot = {}


def collect_comprehensive_activations(model, config, device, num_reps: int = 100):
    """
    收集所有神经元层的激活数据
    """
    print("Collecting comprehensive activation data from all neural layers...")
    
    # 创建轨迹生成器 - 内联定义
    class TrajectoryGenerator:
        def __init__(self, config):
            self.maze_size = config.ENV_SIZE
            self.seq_len = config.SEQUENCE_LENGTH
            self.start_S = np.array([self.maze_size * 0.5, self.maze_size * 0.2])
            self.end_A = np.array([self.maze_size * 0.2, self.maze_size * 0.8])
            self.end_B = np.array([self.maze_size * 0.8, self.maze_size * 0.8])

        def _generate_path(self, start, end, moving):
            if not moving:
                positions = np.tile(start, (self.seq_len, 1))
                velocities = np.zeros((self.seq_len, 2))
                angular_velocities = np.zeros(self.seq_len)
                return positions, velocities, angular_velocities

            base_positions = np.array([start + (t / max(1, self.seq_len - 1)) * (end - start) for t in range(self.seq_len)])
            direction = (end - start) / (np.linalg.norm(end - start) + 1e-6)
            perpendicular = np.array([-direction[1], direction[0]])

            amplitude = np.random.uniform(0.5, 1.5)
            frequency = np.random.uniform(1.5, 2.5)
            phase = np.random.uniform(0, np.pi)

            t = np.linspace(0, 1, self.seq_len)
            sine_offset = amplitude * np.sin(2 * np.pi * frequency * t + phase)

            positions = base_positions + sine_offset[:, np.newaxis] * perpendicular[np.newaxis, :]
            positions = np.clip(positions, 0.5, self.maze_size - 0.5)

            velocities = np.diff(positions, axis=0, prepend=positions[0:1])
            angles = np.arctan2(velocities[:, 1], velocities[:, 0])
            angular_velocities = np.diff(np.unwrap(angles), prepend=angles[0:1])
            return positions, velocities, angular_velocities

        def get_trajectories_for_condition(self, condition, num_reps=20):
            all_self_pos, all_self_vel, all_self_ang_vel = [], [], []
            all_peer_pos, all_peer_vel, all_peer_ang_vel = [], [], []
            movement_patterns = {1: (True, False), 2: (False, True), 3: (True, True), 4: (False, False)}
            self_moving, peer_moving = movement_patterns[condition]

            for i in range(num_reps):
                target = self.end_A if i % 2 == 0 else self.end_B
                static = self.start_S

                self_start, self_end = (self.start_S, target) if self_moving else (static, static)
                peer_start, peer_end = (self.start_S, target) if peer_moving else (static, static)

                self_pos, self_vel, self_ang_vel = self._generate_path(self_start, self_end, self_moving)
                peer_pos, peer_vel, peer_ang_vel = self._generate_path(peer_start, peer_end, peer_moving)

                all_self_pos.append(self_pos)
                all_self_vel.append(self_vel)
                all_self_ang_vel.append(self_ang_vel)
                all_peer_pos.append(peer_pos)
                all_peer_vel.append(peer_vel)
                all_peer_ang_vel.append(peer_ang_vel)

            return {
                'self_pos': np.array(all_self_pos),
                'self_vel': np.array(all_self_vel),
                'self_ang_vel': np.array(all_self_ang_vel),
                'peer_pos': np.array(all_peer_pos),
                'peer_vel': np.array(all_peer_vel),
                'peer_ang_vel': np.array(all_peer_ang_vel)
            }

    trajectory_gen = TrajectoryGenerator(config)
    
    # 存储所有层的结果
    all_layer_results = {
        'lstm_self': {},      # LSTM self流输出
        'lstm_peer': {},      # LSTM peer流输出  
        'lstm_joint': {},     # LSTM联合表示
        'bottleneck': {},     # Bottleneck层
        'relational': {}      # Relational head隐层
    }
    
    # 获取各层的神经元数量
    layer_sizes = {
        'lstm_self': config.HIDDEN_SIZE,    # 128
        'lstm_peer': config.HIDDEN_SIZE,    # 128
        'lstm_joint': config.HIDDEN_SIZE,   # 128
        'bottleneck': config.LATENT_DIM,    # 通常64或128
        'relational': 64                    # Relational head隐层
    }
    
    # 初始化结果结构
    for layer_name, layer_size in layer_sizes.items():
        all_layer_results[layer_name] = {
            i: ComprehensiveAnalysisResult(i, layer_name) 
            for i in range(layer_size)
        }
    
    model.eval()
    with torch.no_grad():
        for condition in tqdm(range(1, 5), desc="Processing Conditions"):
            trajs = trajectory_gen.get_trajectories_for_condition(condition, num_reps=num_reps)
            
            # 准备输入数据
            self_vel_input = torch.cat([
                torch.from_numpy(trajs['self_vel']).float(), 
                torch.from_numpy(trajs['self_ang_vel']).float().unsqueeze(-1)
            ], dim=-1).to(device)
            
            self_init_pos = torch.from_numpy(trajs['self_pos'][:, 0, :]).float().to(device)
            self_init_hd = torch.from_numpy(np.arctan2(trajs['self_vel'][:, 0, 1], trajs['self_vel'][:, 0, 0])).float().to(device)
            
            peer_vel_input = torch.cat([
                torch.from_numpy(trajs['peer_vel']).float(), 
                torch.from_numpy(trajs['peer_ang_vel']).float().unsqueeze(-1)
            ], dim=-1).to(device)
            
            peer_init_pos = torch.from_numpy(trajs['peer_pos'][:, 0, :]).float().to(device)
            peer_init_hd = torch.from_numpy(np.arctan2(trajs['peer_vel'][:, 0, 1], trajs['peer_vel'][:, 0, 0])).float().to(device)
            
            # 修改前向传播以获取中间激活
            batch_size, seq_len = self_vel_input.shape[:2]
            
            # 获取ego token
            ego_token_expanded = model.ego_token.expand(batch_size, seq_len, -1)
            peer_token_expanded = torch.zeros_like(ego_token_expanded)
            self_vel_tagged = torch.cat([self_vel_input, ego_token_expanded], dim=-1)
            peer_vel_tagged = torch.cat([peer_vel_input, peer_token_expanded], dim=-1)
            
            # 获取初始状态
            h0_self, c0_self = model._get_initial_state(self_init_pos, self_init_hd)
            h0_peer, c0_peer = model._get_initial_state(peer_init_pos, peer_init_hd)
            
            # LSTM前向传播
            lstm_out_self, _ = model.path_integrator_lstm(self_vel_tagged, (h0_self, c0_self))
            lstm_out_peer, _ = model.path_integrator_lstm(peer_vel_tagged, (h0_peer, c0_peer))
            joint_representation = lstm_out_self + lstm_out_peer
            
            # Bottleneck层
            joint_representation_flat = joint_representation.reshape(batch_size * seq_len, model.hidden_size)
            joint_bottleneck = model.joint_bottleneck_layer(joint_representation_flat)
            joint_bottleneck_reshaped = joint_bottleneck.reshape(batch_size, seq_len, -1)
            
            # Relational head隐层
            relational_hidden_activations = model.relational_head[1](model.relational_head[0](joint_bottleneck))
            relational_hidden_reshaped = relational_hidden_activations.reshape(batch_size, seq_len, -1)
            
            # 收集所有层的激活
            layer_activations = {
                'lstm_self': lstm_out_self.cpu().numpy(),
                'lstm_peer': lstm_out_peer.cpu().numpy(), 
                'lstm_joint': joint_representation.cpu().numpy(),
                'bottleneck': joint_bottleneck_reshaped.cpu().numpy(),
                'relational': relational_hidden_reshaped.cpu().numpy()
            }
            
            # 处理每一层的每个神经元
            for layer_name, activations in layer_activations.items():
                for neuron_idx in range(activations.shape[-1]):
                    neuron_activations = activations[:, :, neuron_idx]
                    peak_activation = np.max(neuron_activations)
                    
                    condition_data = ConditionData()
                    condition_data.activations = neuron_activations
                    condition_data.peak_activation = peak_activation
                    condition_data.positions_to_plot = {
                        'self': trajs['self_pos'],
                        'peer': trajs['peer_pos']
                    }
                    
                    all_layer_results[layer_name][neuron_idx].condition_data[condition] = condition_data
    
    return all_layer_results, layer_sizes


def classify_neurons_by_layer(all_layer_results, layer_sizes):
    """
    对每一层的神经元进行分类
    """
    print("Classifying neurons in each layer...")
    
    categorized_by_layer = {}
    
    for layer_name, layer_results in all_layer_results.items():
        print(f"\nClassifying {layer_name} layer ({layer_sizes[layer_name]} neurons)...")
        
        categorized_cells = defaultdict(list)
        
        for neuron_idx in range(layer_sizes[layer_name]):
            res = layer_results[neuron_idx]
            peak_results = {cond: data.peak_activation for cond, data in res.condition_data.items()}
            res.cell_type = CellTypeDetector.classify(peak_results)
            categorized_cells[res.cell_type].append(res)
        
        # 打印分类结果
        for cell_type, results in categorized_cells.items():
            print(f"  {cell_type}: {len(results)} neurons")
        
        categorized_by_layer[layer_name] = categorized_cells
    
    return categorized_by_layer


def run_layer_wise_analysis(categorized_by_layer, model_path, output_dir, num_reps):
    """
    对每一层运行互信息分析
    """
    print("\nRunning mutual information analysis for each layer...")
    
    for layer_name, categorized_cells in categorized_by_layer.items():
        print(f"\n{'='*50}")
        print(f"ANALYZING {layer_name.upper()} LAYER")
        print(f"{'='*50}")
        
        layer_output_dir = os.path.join(output_dir, f"{layer_name}_analysis")
        
        try:
            from utils.mutual_information_analysis import run_mutual_information_analysis
            mi_results = run_mutual_information_analysis(
                model_path, layer_output_dir, categorized_cells, num_reps)
            
            print(f"✅ {layer_name} layer analysis completed!")
            print(f"   Results saved to: {layer_output_dir}")
            
        except Exception as e:
            print(f"❌ Error analyzing {layer_name} layer: {e}")
            continue


def main():
    parser = argparse.ArgumentParser(description='Run comprehensive mutual information analysis on all neural layers')
    parser.add_argument('--model_path', type=str, required=True,
                       help='Path to the trained model checkpoint')
    parser.add_argument('--output_dir', type=str, default='comprehensive_mi_analysis',
                       help='Output directory for results')
    parser.add_argument('--num_reps', type=int, default=200,
                       help='Number of trajectory repetitions for data collection')
    
    args = parser.parse_args()
    
    print("="*80)
    print("COMPREHENSIVE MUTUAL INFORMATION ANALYSIS")
    print("Analyzing ALL Neural Layers: LSTM, Bottleneck, Relational Head")
    print("="*80)
    print(f"Model path: {args.model_path}")
    print(f"Output directory: {args.output_dir}")
    print(f"Number of repetitions: {args.num_reps}")
    print("="*80)
    
    # 检查模型文件
    if not os.path.exists(args.model_path):
        print(f"Error: Model file not found: {args.model_path}")
        sys.exit(1)
    
    # 加载配置和模型
    config = Config()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    print(f"LSTM Hidden Size: {config.HIDDEN_SIZE}")
    print(f"Bottleneck Size: {config.LATENT_DIM}")
    print(f"Relational Head Hidden Size: 64")
    
    # 加载模型
    place_cells = PlaceCellEnsemble(
        n_cells=config.PLACE_CELLS_N, 
        scale=config.PLACE_CELLS_SCALE, 
        pos_min=0, pos_max=config.ENV_SIZE, 
        seed=config.SEED
    )
    hd_cells = HeadDirectionCellEnsemble(
        n_cells=config.HD_CELLS_N, 
        concentration=config.HD_CELLS_CONCENTRATION, 
        seed=config.SEED
    )
    
    model_config = {
        'HIDDEN_SIZE': config.HIDDEN_SIZE,
        'LATENT_DIM': config.LATENT_DIM,
        'dropout_rate': config.DROPOUT_RATE,
        'ego_token_size': getattr(config, 'ego_token_size', 4)
    }
    
    model = SocialGridCellNetwork(
        place_cells=place_cells,
        hd_cells=hd_cells,
        config=model_config
    ).to(device)
    
    # 加载权重
    checkpoint = torch.load(args.model_path, map_location=device, weights_only=False)
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)
    
    print("Model loaded successfully!")
    
    # 第一步：收集所有层的激活数据
    print("\n" + "="*50)
    print("STEP 1: Collecting Activations from All Layers")
    print("="*50)
    all_layer_results, layer_sizes = collect_comprehensive_activations(model, config, device, num_reps=100)
    
    # 第二步：对每层神经元进行分类
    print("\n" + "="*50)
    print("STEP 2: Classifying Neurons in Each Layer")
    print("="*50)
    categorized_by_layer = classify_neurons_by_layer(all_layer_results, layer_sizes)
    
    # 第三步：对每层运行互信息分析
    print("\n" + "="*50)
    print("STEP 3: Running Mutual Information Analysis")
    print("="*50)
    run_layer_wise_analysis(categorized_by_layer, args.model_path, args.output_dir, args.num_reps)
    
    print("\n" + "="*80)
    print("COMPREHENSIVE ANALYSIS COMPLETED!")
    print("="*80)
    print(f"All results saved to: {args.output_dir}")
    print("\nGenerated analysis for layers:")
    for layer_name in categorized_by_layer.keys():
        layer_dir = os.path.join(args.output_dir, f"{layer_name}_analysis")
        print(f"- {layer_name}: {layer_dir}")


if __name__ == "__main__":
    main()
