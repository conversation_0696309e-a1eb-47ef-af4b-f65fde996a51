# train_social_grid_cells_ddp.py (Relational Task Fix)
import torch
import torch.optim as optim
import torch.multiprocessing as mp
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP
from torch.utils.data.distributed import DistributedSampler
import os
import time
from collections import defaultdict
import numpy as np
from tqdm import tqdm
import argparse
import json

# 从本地文件导入
from config import Config
from models.social_grid_cell import SocialGridCellNetwork, SocialNavigationLoss
from datasets.social_navigation_dataset import SocialNavigationDataset
from models.place_hd_cells import PlaceCellEnsemble, HeadDirectionCellEnsemble

def setup(rank, world_size):
    """设置分布式训练环境"""
    os.environ['MASTER_ADDR'] = 'localhost'
    os.environ['MASTER_PORT'] = '12355'
    dist.init_process_group("nccl", rank=rank, world_size=world_size)
    torch.cuda.set_device(rank)

def cleanup():
    """清理分布式环境"""
    dist.destroy_process_group()

def get_dataloaders(config, rank, world_size):
    """为训练和验证创建数据加载器和采样器"""
    full_dataset = SocialNavigationDataset(root_dir=config.DATA_ROOT, sequence_length=config.SEQUENCE_LENGTH)
    
    # Use a fixed seed for splitting to ensure consistency across processes
    train_size = int(0.8 * len(full_dataset))
    val_size = len(full_dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(
        full_dataset, [train_size, val_size], generator=torch.Generator().manual_seed(42)
    )

    train_sampler = DistributedSampler(train_dataset, num_replicas=world_size, rank=rank, shuffle=True)
    val_sampler = DistributedSampler(val_dataset, num_replicas=world_size, rank=rank, shuffle=False)

    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config.BATCH_SIZE, shuffle=False, 
        num_workers=config.NUM_WORKERS, pin_memory=True, sampler=train_sampler
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=config.BATCH_SIZE, shuffle=False, 
        num_workers=config.NUM_WORKERS, pin_memory=True, sampler=val_sampler
    )
    
    return train_loader, val_loader

def train_worker(rank, world_size, config):
    """每个GPU上运行的训练工作进程。"""
    print(f"Running DDP training on rank {rank}.")
    setup(rank, world_size)
    
    is_main_process = (rank == 0)
    device = torch.device(f'cuda:{rank}')

    if is_main_process:
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        exp_dir = os.path.join("logs_social_grid_cells_ddp", timestamp)
        config.CHECKPOINT_DIR = os.path.join(exp_dir, "checkpoints")
        os.makedirs(config.CHECKPOINT_DIR, exist_ok=True)
        with open(os.path.join(exp_dir, 'config.json'), 'w') as f:
            config_dict = {k: v for k, v in config.__dict__.items() if not k.startswith('__') and not callable(v)}
            json.dump(config_dict, f, indent=4)
        print(f"Experiment logs/checkpoints will be saved in: {exp_dir}")

    train_loader, val_loader = get_dataloaders(config, rank, world_size)
    
    place_cells = PlaceCellEnsemble(n_cells=config.PLACE_CELLS_N, scale=config.PLACE_CELLS_SCALE, pos_min=0, pos_max=config.ENV_SIZE, seed=config.SEED)
    hd_cells = HeadDirectionCellEnsemble(n_cells=config.HD_CELLS_N, concentration=config.HD_CELLS_CONCENTRATION, seed=config.SEED)
    model_config = {'hidden_size': config.HIDDEN_SIZE, 'bottleneck_size': config.LATENT_DIM, 'dropout_rate': config.DROPOUT_RATE}
    
    model = SocialGridCellNetwork(place_cells, hd_cells, model_config).to(device)
    model = DDP(model, device_ids=[rank], find_unused_parameters=False) # Set find_unused_parameters to False for performance
    
    # Initialize loss with a weight for the new relational task
    criterion = SocialNavigationLoss(relational_loss_weight=0.5)
    optimizer = optim.Adam(model.parameters(), lr=config.NATURE_LEARNING_RATE, weight_decay=config.NATURE_WEIGHT_DECAY)
    
    if is_main_process:
        print(f"Model initialized with {sum(p.numel() for p in model.parameters()):,} parameters.")

    best_val_loss = float('inf')

    for epoch in range(config.NUM_EPOCHS):
        model.train()
        train_loader.sampler.set_epoch(epoch)
        
        # Use a dict to store losses for aggregation across GPUs
        train_losses = defaultdict(float)
        train_progress_bar = tqdm(train_loader, desc=f"Epoch {epoch+1} Train", disable=not is_main_process)
        
        for batch in train_progress_bar:
            self_vel = torch.cat([batch['self_velocities'], batch['self_angular_velocities'].unsqueeze(-1)], dim=-1).to(device)
            peer_vel = torch.cat([batch['peer_velocities'], batch['peer_angular_velocities'].unsqueeze(-1)], dim=-1).to(device)
            self_init_pos = batch['self_positions'][:, 0, :].to(device)
            self_init_hd = batch['self_angles'][:, 0].to(device)
            peer_init_pos = batch['peer_positions'][:, 0, :].to(device)
            peer_init_hd = batch['peer_angles'][:, 0].to(device)

            with torch.no_grad():
                # --- CORE FIX: Add position data to the targets dictionary ---
                targets = {
                    'self_place_targets': place_cells.compute_activation(batch['self_positions'].to(device)),
                    'self_hd_targets': hd_cells.compute_activation(batch['self_angles'].to(device)),
                    'peer_place_targets': place_cells.compute_activation(batch['peer_positions'].to(device)),
                    'peer_hd_targets': hd_cells.compute_activation(batch['peer_angles'].to(device)),
                    'self_positions': batch['self_positions'].to(device),
                    'peer_positions': batch['peer_positions'].to(device),
                }

            outputs = model(self_vel, self_init_pos, self_init_hd, peer_vel, peer_init_pos, peer_init_hd)
            loss_dict = criterion(outputs, targets)
            loss = loss_dict['total']
            
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            for key, value in loss_dict.items():
                train_losses[key] += value if isinstance(value, float) else value.item()
            train_progress_bar.set_postfix(loss=f"{loss.item():.4f}")

        # --- Validation Phase ---
        model.eval()
        val_losses = defaultdict(float)
        val_progress_bar = tqdm(val_loader, desc=f"Epoch {epoch+1} Val", disable=not is_main_process)

        with torch.no_grad():
            for batch in val_progress_bar:
                self_vel = torch.cat([batch['self_velocities'], batch['self_angular_velocities'].unsqueeze(-1)], dim=-1).to(device)
                peer_vel = torch.cat([batch['peer_velocities'], batch['peer_angular_velocities'].unsqueeze(-1)], dim=-1).to(device)
                self_init_pos = batch['self_positions'][:, 0, :].to(device)
                self_init_hd = batch['self_angles'][:, 0].to(device)
                peer_init_pos = batch['peer_positions'][:, 0, :].to(device)
                peer_init_hd = batch['peer_angles'][:, 0].to(device)
                
                # --- CORE FIX: Also add position data to targets for validation ---
                targets = {
                    'self_place_targets': place_cells.compute_activation(batch['self_positions'].to(device)),
                    'self_hd_targets': hd_cells.compute_activation(batch['self_angles'].to(device)),
                    'peer_place_targets': place_cells.compute_activation(batch['peer_positions'].to(device)),
                    'peer_hd_targets': hd_cells.compute_activation(batch['peer_angles'].to(device)),
                    'self_positions': batch['self_positions'].to(device),
                    'peer_positions': batch['peer_positions'].to(device),
                }
                outputs = model(self_vel, self_init_pos, self_init_hd, peer_vel, peer_init_pos, peer_init_hd)
                loss_dict = criterion(outputs, targets)
                
                for key, value in loss_dict.items():
                    val_losses[key] += value if isinstance(value, float) else value.item()

        # --- Aggregate, Log, and Save (Main Process Only) ---
        def aggregate_losses(loss_dict, num_batches):
            # Create tensor for aggregation
            loss_tensor = torch.tensor([v for k, v in sorted(loss_dict.items())]).to(device)
            dist.all_reduce(loss_tensor, op=dist.ReduceOp.SUM)
            avg_losses = (loss_tensor / world_size) / num_batches
            return {k: avg_losses[i].item() for i, (k, v) in enumerate(sorted(loss_dict.items()))}

        avg_train_losses = aggregate_losses(train_losses, len(train_loader))
        avg_val_losses = aggregate_losses(val_losses, len(val_loader))

        if is_main_process:
            print(f"\n--- Epoch {epoch+1} Summary ---")
            print(f"  - Avg Train Losses: " + ", ".join([f"{k}: {v:.4f}" for k, v in avg_train_losses.items()]))
            print(f"  - Avg Val Losses:   " + ", ".join([f"{k}: {v:.4f}" for k, v in avg_val_losses.items()]))
            print("--------------------------")

            avg_val_loss_total = avg_val_losses['total']
            if avg_val_loss_total < best_val_loss:
                best_val_loss = avg_val_loss_total
                save_path = os.path.join(config.CHECKPOINT_DIR, 'best_model.pth')
                torch.save(model.module.state_dict(), save_path)
                print(f"🎉 New best model saved with val_loss: {best_val_loss:.4f} at {save_path}")

            if (epoch + 1) % config.SAVE_FREQUENCY == 0:
                save_path = os.path.join(config.CHECKPOINT_DIR, f'social_model_epoch_{epoch+1}.pth')
                torch.save(model.module.state_dict(), save_path)
                print(f"Checkpoint saved to {save_path}")

    cleanup()

def main():
    parser = argparse.ArgumentParser(description="Multi-GPU training for Social Grid Cell Network.")
    parser.add_argument('--gpus', type=str, default='0,1', help='GPU IDs to use, comma-separated (e.g., 0,1,2,3).')
    args = parser.parse_args()
    
    os.environ['CUDA_VISIBLE_DEVICES'] = args.gpus
    world_size = torch.cuda.device_count()
    
    if world_size < 1:
        print("No GPUs available. Please check CUDA setup.")
        return
        
    print(f"Found {world_size} GPUs. Starting DDP training...")
    
    config = Config()
    
    mp.spawn(train_worker,
             args=(world_size, config),
             nprocs=world_size,
             join=True)

if __name__ == '__main__':
    main()
