gemini绘图指南：https://gemini.google.com/app/2beee9632c0e314d

Fig4b绘图

python social_cells_nature_enhanced.py --model_path logs_social_grid_cells_ddp/20250801_163726/checkpoints/best_model.pth --output_dir social_cell_viz_grid_style-2 --w
orkers 32

这个考虑了rational layer：python visualize_social_cells-rational-head.py  --workers 32 --model_path logs_social_grid_cells_ddp/20250802_230022/checkpoints/best_model.pth

pdf转svg
'/data1/fangzr/Research/Memory_Maze_3D/Multi-com/maze-zero/grid-cell-dual/utils/pdf_batch_converter.sh'

Fig 4c绘图：
cd /data1/fangzr/Research/Memory_Maze_3D/Multi-com/maze-zero/grid-cell-dual && python utils/population_coding_viz.py --model_path /data1/fangzr/Research/Memory_Maze_3D/Multi-com/maze-zero/grid-cell-dual/logs_social_grid_cells_ddp/20250802_230022/checkpoints/best_model.pth --output_dir population_coding_fixed_8m --num_reps 600 --close_threshold 3.0 --far_threshold 5.0 --max_distance 8.0

图片路径：/data1/fangzr/Research/Memory_Maze_3D/Multi-com/maze-zero/grid-cell-dual/population_coding_fixed_8m