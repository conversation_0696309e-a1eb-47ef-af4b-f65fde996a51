#!/usr/bin/env python3
"""
查看功能性门控可视化结果的脚本
"""

import os
import subprocess
import sys

def open_pdf(filepath):
    """尝试打开PDF文件"""
    if not os.path.exists(filepath):
        print(f"文件不存在: {filepath}")
        return False
    
    try:
        # 尝试不同的PDF查看器
        viewers = ['evince', 'okular', 'xpdf', 'acroread', 'firefox']
        
        for viewer in viewers:
            try:
                subprocess.run([viewer, filepath], check=True, 
                             stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                print(f"使用 {viewer} 打开: {filepath}")
                return True
            except (subprocess.CalledProcessError, FileNotFoundError):
                continue
        
        print(f"无法找到合适的PDF查看器来打开: {filepath}")
        return False
        
    except Exception as e:
        print(f"打开PDF时出错: {e}")
        return False

def main():
    """主函数"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 要查看的PDF文件列表
    pdf_files = [
        'functional_gating_proximity.pdf',
        'functional_gating_proximity_neuron_0.pdf', 
        'functional_gating_proximity_neuron_2.pdf'
    ]
    
    print("功能性门控可视化结果查看器")
    print("=" * 40)
    
    # 检查文件是否存在
    existing_files = []
    for pdf_file in pdf_files:
        filepath = os.path.join(current_dir, pdf_file)
        if os.path.exists(filepath):
            existing_files.append((pdf_file, filepath))
            print(f"✓ {pdf_file}")
        else:
            print(f"✗ {pdf_file} (不存在)")
    
    if not existing_files:
        print("\n没有找到任何PDF文件！")
        return
    
    print(f"\n找到 {len(existing_files)} 个PDF文件")
    print("\n选择要查看的文件:")
    print("0. 查看所有文件")
    
    for i, (filename, _) in enumerate(existing_files, 1):
        print(f"{i}. {filename}")
    
    try:
        choice = input("\n请输入选择 (0-{}): ".format(len(existing_files)))
        choice = int(choice)
        
        if choice == 0:
            # 打开所有文件
            print("\n正在打开所有PDF文件...")
            for filename, filepath in existing_files:
                open_pdf(filepath)
        elif 1 <= choice <= len(existing_files):
            # 打开选定的文件
            filename, filepath = existing_files[choice - 1]
            print(f"\n正在打开: {filename}")
            open_pdf(filepath)
        else:
            print("无效的选择！")
            
    except (ValueError, KeyboardInterrupt):
        print("\n已取消")

if __name__ == '__main__':
    main()
