#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Enhanced Grid Cell Visualization Script

基于 Nature 论文 "Vector-based navigation using grid-like representations in artificial agents" 实现的可视化方法
优化版本：专注于产生清晰、高质量的可视化结果
"""

import os
import argparse
import numpy as np
import torch
from tqdm import tqdm
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
from matplotlib.backends.backend_pdf import PdfPages
from scipy.ndimage import rotate
import scipy.signal
import scipy.stats

# 设置matplotlib风格
plt.style.use('default')
plt.rcParams['figure.figsize'] = [12, 12]  # 增大图像尺寸以提高清晰度
plt.rcParams['figure.dpi'] = 150  # 提高DPI
plt.rcParams['font.size'] = 10


def circle_mask(size, radius, in_val=1.0, out_val=0.0):
    """创建圆形掩码，与 Nature 论文实现一致"""
    sz = [int(size[0] / 2), int(size[1] / 2)]
    x = np.linspace(-sz[0], sz[1], size[1])
    x = np.expand_dims(x, 0)
    x = x.repeat(size[0], 0)
    y = np.linspace(-sz[0], sz[1], size[1])
    y = np.expand_dims(y, 1)
    y = y.repeat(size[1], 1)
    z = np.sqrt(x**2 + y**2)
    z = np.less_equal(z, radius)
    vfunc = np.vectorize(lambda b: b and in_val or out_val)
    return vfunc(z)


class GridScorer:
    """栅格细胞评分器，基于 Nature 论文实现"""

    def __init__(self, nbins, coords_range, mask_parameters, min_max=False):
        """初始化评分器

        Args:
            nbins: Rate map 的分辨率
            coords_range: 坐标范围，如 ((-1.1, 1.1), (-1.1, 1.1))
            mask_parameters: 环形掩码参数列表，如 [(0.2, 0.4), (0.2, 0.5), ...]
            min_max: 是否使用 min/max 而不是平均来计算 gridness
        """
        self._nbins = nbins
        self._min_max = min_max
        self._coords_range = coords_range
        self._corr_angles = [30, 45, 60, 90, 120, 135, 150]
        
        # 创建所有掩码
        self._masks = [(self._get_ring_mask(mask_min, mask_max), (mask_min, mask_max))
                      for mask_min, mask_max in mask_parameters]
        
        # 用于绘图的SAC掩码
        self._plotting_sac_mask = circle_mask(
            [self._nbins * 2 - 1, self._nbins * 2 - 1],
            self._nbins,
            in_val=1.0,
            out_val=np.nan)

    def calculate_ratemap(self, xs, ys, activations, statistic='mean'):
        """使用 scipy.stats.binned_statistic_2d 计算 rate map，采用occupancy-weighted方法"""
        ratemap, x_edges, y_edges, binnumber = scipy.stats.binned_statistic_2d(
            xs,
            ys,
            activations,
            bins=self._nbins,
            statistic=statistic,
            range=self._coords_range)
        
        # 计算每个bin的访问次数（occupancy）
        occupancy, _, _ = np.histogram2d(xs, ys, bins=[x_edges, y_edges])
        
        # 将没有数据的区域设为NaN（而不是0）
        ratemap = np.where(occupancy > 0, ratemap, np.nan)
        
        # 转置以匹配图像显示约定 (y轴向上)
        return ratemap.T, occupancy.T

    def _get_ring_mask(self, mask_min, mask_max):
        """创建环形掩码"""
        n_points = [self._nbins * 2 - 1, self._nbins * 2 - 1]
        return (circle_mask(n_points, mask_max * self._nbins) *
                (1 - circle_mask(n_points, mask_min * self._nbins)))

    def grid_score_60(self, corr):
        """计算 60° gridness 分数"""
        if self._min_max:
            return np.minimum(corr[60], corr[120]) - np.maximum(
                corr[30], np.maximum(corr[90], corr[150]))
        else:
            return (corr[60] + corr[120]) / 2 - (corr[30] + corr[90] + corr[150]) / 3

    def grid_score_90(self, corr):
        """计算 90° gridness 分数"""
        return corr[90] - (corr[45] + corr[135]) / 2

    def calculate_sac(self, seq1):
        """计算空间自相关图，与 Nature 论文实现一致"""
        seq2 = seq1

        def filter2(b, x):
            stencil = np.rot90(b, 2)
            return scipy.signal.convolve2d(x, stencil, mode='full')

        seq1 = np.nan_to_num(seq1)
        seq2 = np.nan_to_num(seq2)

        ones_seq1 = np.ones(seq1.shape)
        ones_seq1[np.isnan(seq1)] = 0
        ones_seq2 = np.ones(seq2.shape)
        ones_seq2[np.isnan(seq2)] = 0

        seq1[np.isnan(seq1)] = 0
        seq2[np.isnan(seq2)] = 0

        seq1_sq = np.square(seq1)
        seq2_sq = np.square(seq2)

        seq1_x_seq2 = filter2(seq1, seq2)
        sum_seq1 = filter2(seq1, ones_seq2)
        sum_seq2 = filter2(ones_seq1, seq2)
        sum_seq1_sq = filter2(seq1_sq, ones_seq2)
        sum_seq2_sq = filter2(ones_seq1, seq2_sq)
        n_bins = filter2(ones_seq1, ones_seq2)
        n_bins_sq = np.square(n_bins)

        std_seq1 = np.power(
            np.subtract(
                np.divide(sum_seq1_sq, n_bins),
                (np.divide(np.square(sum_seq1), n_bins_sq))), 0.5)
        std_seq2 = np.power(
            np.subtract(
                np.divide(sum_seq2_sq, n_bins),
                (np.divide(np.square(sum_seq2), n_bins_sq))), 0.5)
        covar = np.subtract(
            np.divide(seq1_x_seq2, n_bins),
            np.divide(np.multiply(sum_seq1, sum_seq2), n_bins_sq))
        x_coef = np.divide(covar, np.multiply(std_seq1, std_seq2))
        x_coef = np.real(x_coef)
        x_coef = np.nan_to_num(x_coef)
        return x_coef

    def rotated_sacs(self, sac, angles):
        """计算旋转后的自相关图"""
        return [
            scipy.ndimage.rotate(sac, angle, reshape=False)
            for angle in angles
        ]

    def get_grid_scores_for_mask(self, sac, rotated_sacs, mask):
        """计算特定掩码下的 gridness 分数"""
        masked_sac = sac * mask
        ring_area = np.sum(mask)
        # 计算环形区域上的 dc
        masked_sac_mean = np.sum(masked_sac) / ring_area
        # 中心化环内的 sac 值
        masked_sac_centered = (masked_sac - masked_sac_mean) * mask
        variance = np.sum(masked_sac_centered**2) / ring_area + 1e-5
        corrs = dict()
        for angle, rotated_sac in zip(self._corr_angles, rotated_sacs):
            masked_rotated_sac = (rotated_sac - masked_sac_mean) * mask
            cross_prod = np.sum(masked_sac_centered * masked_rotated_sac) / ring_area
            corrs[angle] = cross_prod / variance
        return self.grid_score_60(corrs), self.grid_score_90(corrs), variance

    def get_scores(self, rate_map):
        """获取栅格细胞分数综合评估"""
        sac = self.calculate_sac(rate_map)
        rotated_sacs = self.rotated_sacs(sac, self._corr_angles)

        scores = [
            self.get_grid_scores_for_mask(sac, rotated_sacs, mask)
            for mask, mask_params in self._masks
        ]
        scores_60, scores_90, variances = map(np.asarray, zip(*scores))
        max_60_ind = np.argmax(scores_60)
        max_90_ind = np.argmax(scores_90)

        return (scores_60[max_60_ind], scores_90[max_90_ind],
                self._masks[max_60_ind][1], self._masks[max_90_ind][1], sac)
				
				
    def apply_occupancy_weighted_smoothing(self, ratemap, sigma=1.0):
        """神经科学标准的occupancy-weighted平滑
        
        Args:
            ratemap: 输入rate map (可能包含NaN)
            sigma: 高斯滤波标准差
            
        Returns:
            smoothed_ratemap: 平滑后的rate map
        """
        from scipy.ndimage import gaussian_filter
        
        # 创建占用掩码
        valid_mask = ~np.isnan(ratemap)
        
        if not np.any(valid_mask):
            return ratemap
        
        # 将NaN替换为0进行滤波
        ratemap_filled = np.where(valid_mask, ratemap, 0.0)
        
        # 对rate map和掩码都做高斯滤波
        smoothed_ratemap = gaussian_filter(ratemap_filled, sigma=sigma)
        smoothed_mask = gaussian_filter(valid_mask.astype(float), sigma=sigma)
        
        # Occupancy-weighted归一化：smoothed_rate / smoothed_occupancy
        # 避免除零：只在smoothed_mask > 一个小阈值的地方计算
        threshold = 0.1  # 相当于原始occupancy的10%
        result = np.where(
            smoothed_mask > threshold,
            smoothed_ratemap / smoothed_mask,
            np.nan
        )
        
        return result

    def plot_ratemap(self, ratemap, ax=None, title=None, apply_processing=True, 
                    gamma=2.0, outlier_smoothing=True, **kwargs):
        """绘制 rate map - 神经科学标准occupancy-weighted smoothing"""
        if ax is None:
            ax = plt.gca()
            
        # 创建rate map的副本
        ratemap_processed = ratemap.copy()
        
        if apply_processing and not np.all(np.isnan(ratemap_processed)):
            # Step 1: Occupancy-weighted Gaussian smoothing (神经科学标准)
            ratemap_processed = self.apply_occupancy_weighted_smoothing(ratemap_processed, sigma=1.0)
            
            # Step 2: 温和的对比度增强（在平滑后）
            ratemap_processed = self.apply_contrast_enhancement(
                ratemap_processed, 
                gamma=gamma, 
                threshold_ratio=0.02  # 降低阈值，保留更多信息
            )
        
        # 对于显示，将NaN设为0
        ratemap_display = np.nan_to_num(ratemap_processed, nan=0.0)
        
        # 设置绘图参数
        plot_kwargs = {
            'interpolation': 'nearest',
            'cmap': 'jet',               
            'origin': 'lower',
            'aspect': 'equal',
            'vmin': 0,
        }
        plot_kwargs.update(kwargs)
        
        # 绘制
        im = ax.imshow(ratemap_display, **plot_kwargs)
        ax.axis('off')
        
        if title is not None:
            ax.set_title(title, fontsize=9)
            
        return im


    def plot_sac(self, sac, mask_params=None, ax=None, title=None, **kwargs):
        """绘制空间自相关图 - 优化版本"""
        if ax is None:
            ax = plt.gca()
        
        # 绘制 sac
        useful_sac = sac * self._plotting_sac_mask
        
        # 设置默认参数
        plot_kwargs = {
            'interpolation': 'nearest',
            'cmap': self._get_autocorr_cmap(),
            'vmin': -1, 
            'vmax': 1,
            'origin': 'lower',
            'aspect': 'equal'
        }
        plot_kwargs.update(kwargs)
        
        ax.imshow(useful_sac, **plot_kwargs)
        
        # # 为环形掩码画圆
        # if mask_params is not None:
        #     center = self._nbins - 1
        #     ax.add_artist(
        #         plt.Circle(
        #             (center, center),
        #             mask_params[0] * self._nbins,
        #             fill=False,
        #             edgecolor='black',
        #             linewidth=1))
        #     ax.add_artist(
        #         plt.Circle(
        #             (center, center),
        #             mask_params[1] * self._nbins,
        #             fill=False,
        #             edgecolor='black',
        #             linewidth=1))
        ax.axis('off')
        if title is not None:
            ax.set_title(title, fontsize=9)

    def apply_contrast_enhancement(self, ratemap, gamma=2.0, threshold_ratio=0.02):
        """温和的对比度增强，保持NaN区域不变"""
        if np.all(np.isnan(ratemap)):
            return ratemap
            
        # 只处理有效值
        valid_mask = ~np.isnan(ratemap)
        if not np.any(valid_mask):
            return ratemap
            
        enhanced = ratemap.copy()
        valid_values = enhanced[valid_mask]
        
        if len(valid_values) > 0 and np.max(valid_values) > 0:
            # 归一化到[0,1]
            max_val = np.max(valid_values)
            normalized = valid_values / max_val
            
            # 设置低值阈值（更保守）
            threshold = threshold_ratio
            normalized = np.where(normalized < threshold, 0, normalized)
            
            # 对剩余值应用伽马校正
            mask_nonzero = normalized > 0
            if np.any(mask_nonzero):
                normalized[mask_nonzero] = np.power(normalized[mask_nonzero], 1/gamma)
            
            # 恢复原始尺度并写回
            enhanced[valid_mask] = normalized * max_val
        
        return enhanced

    def apply_outlier_smoothing(self, ratemap, window_size=3, outlier_threshold=2.0):
        """在激活区域内平滑异常值（野值）
        
        Args:
            ratemap: 输入的rate map
            window_size: 滑动窗口大小 (3, 5, 7)
                        - 3: 只考虑最近邻
                        - 5: 考虑更大邻域  
                        - 7: 考虑很大邻域
            outlier_threshold: 异常值检测阈值 (标准差倍数)
                            - 1.5: 严格检测，会修正更多点
                            - 2.0: 适中检测 (推荐)
                            - 2.5: 宽松检测，只修正明显异常点
        
        Returns:
            smoothed_ratemap: 平滑后的rate map
        """
        from scipy.ndimage import generic_filter
        
        if np.max(ratemap) <= 0:
            return ratemap
            
        # 创建副本
        smoothed = ratemap.copy()
        
        # 识别激活区域 (有显著激活的区域)
        activation_threshold = np.max(ratemap) * 0.1  # 10%的最大值作为激活阈值
        activation_mask = ratemap > activation_threshold
        
    def outlier_filter(values):
        """检测并替换异常值的滤波器"""
        center_idx = len(values) // 2
        center_val = values[center_idx]
        
        # 只处理激活区域内的点
        if center_val < activation_threshold:
            return center_val
            
        # 计算邻域统计（排除中心点）
        neighbors = np.concatenate([values[:center_idx], values[center_idx+1:]])
        neighbors = neighbors[neighbors > activation_threshold * 0.5]  # 只考虑有意义的邻居
        
        if len(neighbors) < 2:  # 邻居太少，保持原值
            return center_val
            
        neighbor_mean = np.mean(neighbors)
        neighbor_std = np.std(neighbors)
        
        # 检测异常值：如果中心值与邻域均值差异过大
        if neighbor_std > 0:
            z_score = abs(center_val - neighbor_mean) / neighbor_std
            if z_score > outlier_threshold and center_val < neighbor_mean * 0.5:
                # 这是一个异常低值（野值），用邻域均值替换
                return neighbor_mean
                
        return center_val
    
        # 应用异常值滤波，只在激活区域内
        if np.any(activation_mask):
            smoothed = generic_filter(ratemap, outlier_filter, size=window_size, mode='constant', cval=0)

        return smoothed

    def _get_autocorr_cmap(self):
        """获取用于自相关图的colormap"""
        # 定义更清晰的colormap (深蓝-绿-黄-深红)
        cdict = {
            'red':   [(0.0, 0.0, 0.0),   # 深蓝
                    (0.3, 0.0, 0.0),   # 绿
                    (0.5, 0.5, 0.5),   # 绿到黄的过渡
                    (0.7, 1.0, 1.0),   # 黄
                    (1.0, 0.8, 0.8)],  # 深红
                    
            'green': [(0.0, 0.0, 0.0),   # 深蓝
                    (0.3, 0.8, 0.8),   # 绿
                    (0.5, 0.8, 0.8),   # 保持绿色强度
                    (0.7, 1.0, 1.0),   # 黄
                    (1.0, 0.0, 0.0)],  # 深红
                    
            'blue':  [(0.0, 0.8, 0.8),   # 深蓝
                    (0.3, 0.0, 0.0),   # 绿
                    (0.5, 0.0, 0.0),   # 保持无蓝色
                    (0.7, 0.0, 0.0),   # 黄
                    (1.0, 0.0, 0.0)]   # 深红
        }
        return LinearSegmentedColormap('GridCellMap', cdict)


class GridCellVisualizer:
    def __init__(self, save_dir, nbins=50, env_size=15):  # 提高分辨率
        """初始化可视化器

        Args:
            save_dir: 保存可视化结果的目录
            nbins: 分辨率 (提高到50以获得更清晰的图像)
            env_size: 环境大小
        """
        self.save_dir = save_dir
        self.nbins = nbins
        self.env_size = env_size
        os.makedirs(save_dir, exist_ok=True)
        
        # 设置坐标范围 (0-15)
        self.coords_range = ((0, env_size), (0, env_size))
        
        # 设置多个环形掩码参数 - 更精细的参数设置
        starts = [0.1] * 20
        ends = np.linspace(0.2, 0.8, num=20)  # 增加采样点，减小最大范围
        mask_parameters = list(zip(starts, ends.tolist()))
        
        # 创建评分器
        self.scorer = GridScorer(nbins, self.coords_range, mask_parameters)
        
    def collect_grid_cell_data(self, model, dataloader, device, num_batches=100):
        """从多个批次收集grid cell数据 - 增加默认批次数
        
        Args:
            model: 预训练的网络模型
            dataloader: 数据加载器
            device: 计算设备
            num_batches: 要处理的批次数量
            
        Returns:
            positions_list: 所有位置数据
            bottleneck_list: 所有bottleneck激活数据
        """
        print(f"收集{num_batches}个批次的数据用于可视化...")
        
        model.eval()
        positions_list = []
        bottleneck_list = []
        
        with torch.no_grad():
            for i, batch in enumerate(tqdm(dataloader, desc="处理批次")):
                if i >= num_batches:
                    break
                
                positions = batch['positions'].to(device)
                angles = batch['angles'].to(device)
                velocities = batch['velocities'].to(device)
                ang_vels = batch['angular_velocities'].to(device)
                
                # 准备模型输入
                w = ang_vels.unsqueeze(-1)
                velocity_input = torch.cat([velocities, w], dim=-1)
                init_pos = positions[:, 0]
                init_hd = angles[:, 0]
                
                # 获取模型输出
                outputs = model(velocity_input, init_pos, init_hd)
                
                # 收集位置和bottleneck激活
                for b in range(positions.size(0)):
                    # 存储所有时间步的位置和激活
                    pos_sample = positions[b].cpu().numpy()  # [seq_len, 2]
                    bottleneck_sample = outputs['bottleneck'][b].cpu().numpy()  # [seq_len, bottleneck_size]
                    
                    positions_list.append(pos_sample)
                    bottleneck_list.append(bottleneck_sample)
        
        print(f"收集完成！总共收集了{len(positions_list)}个样本序列")
        return positions_list, bottleneck_list
    
    def analyze_data_coverage(self, positions_list):
        """分析数据覆盖情况"""
        # 将所有位置数据合并
        all_positions = np.concatenate([p.reshape(-1, 2) for p in positions_list], axis=0)
        
        print(f"\n=== 数据覆盖分析 ===")
        print(f"总位置点数: {len(all_positions)}")
        print(f"X范围: [{all_positions[:, 0].min():.2f}, {all_positions[:, 0].max():.2f}]")
        print(f"Y范围: [{all_positions[:, 1].min():.2f}, {all_positions[:, 1].max():.2f}]")
        
        # 计算每个bin的访问次数
        hist, xedges, yedges = np.histogram2d(
            all_positions[:, 0], all_positions[:, 1], 
            bins=self.nbins, 
            range=[[0, self.env_size], [0, self.env_size]]
        )
        
        empty_bins = np.sum(hist == 0)
        total_bins = self.nbins * self.nbins
        coverage_ratio = 1 - (empty_bins / total_bins)
        
        print(f"空bins数量: {empty_bins}/{total_bins}")
        print(f"空间覆盖率: {coverage_ratio:.2%}")
        
        # 保存覆盖图
        plt.figure(figsize=(10, 8))
        plt.subplot(1, 2, 1)
        plt.scatter(all_positions[:, 0], all_positions[:, 1], s=0.1, alpha=0.5)
        plt.xlim(0, self.env_size)
        plt.ylim(0, self.env_size)
        plt.title('轨迹分布')
        plt.xlabel('X (m)')
        plt.ylabel('Y (m)')
        
        plt.subplot(1, 2, 2)
        plt.imshow(hist.T, origin='lower', extent=[0, self.env_size, 0, self.env_size], 
                   cmap='viridis', interpolation='nearest')
        plt.colorbar(label='访问次数')
        plt.title('空间访问热图')
        plt.xlabel('X (m)')
        plt.ylabel('Y (m)')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, 'data_coverage.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        return coverage_ratio
    
    def get_scores_and_plot(self, positions, activations, filename, max_neurons_to_plot=64):
        """计算 gridness 分数并绘图，基于 Nature 论文实现 - 修复SAC显示版本"""
        # 获取神经元数量
        n_units = activations.shape[1]
        print(f"开始分析{n_units}个神经元...")
        
        # 为每个神经元计算 rate map
        ratemaps = []
        for i in tqdm(range(n_units), desc="计算Rate Maps"):
            ratemap, counts = self.scorer.calculate_ratemap(
                positions[:, 0], positions[:, 1], activations[:, i])
            ratemaps.append(ratemap)
        
        # 获取得分
        print("计算Grid Scores...")
        results = [self.scorer.get_scores(rate_map) for rate_map in tqdm(ratemaps, desc="计算Grid Scores")]
        scores_60, scores_90, max_60_mask, max_90_mask, sacs = zip(*results)
        
        # 转换为numpy数组
        scores_60 = np.array(scores_60)
        scores_90 = np.array(scores_90)
        
        # 排序（按60度分数）
        ordering = np.argsort(-scores_60)
        
        # 限制绘制的神经元数量
        neurons_to_plot = min(max_neurons_to_plot, n_units)
        plot_ordering = ordering[:neurons_to_plot]
        
        # 绘图参数 - 每页8个神经元，确保有足够空间显示SAC
        cols = 8
        neurons_per_page = 8  # 减少到8个，确保有空间显示SAC
        total_pages = int(np.ceil(neurons_to_plot / neurons_per_page))
        
        # 使用 PdfPages 保存多页PDF
        pdf_path = os.path.join(self.save_dir, filename)
        with PdfPages(pdf_path) as pdf:
            for page in range(total_pages):
                start_idx = page * neurons_per_page
                end_idx = min(start_idx + neurons_per_page, neurons_to_plot)
                page_ordering = plot_ordering[start_idx:end_idx]
                
                n_on_page = len(page_ordering)
                rows = 2  # 固定2行：第1行rate map，第2行SAC
                
                fig = plt.figure(figsize=(cols * 2.5, rows * 2.5))
                
                for i, idx in enumerate(page_ordering):
                    # 第1行：rate map
                    rate_subplot_idx = i + 1  # 1到n_on_page
                    ax_rate = plt.subplot(rows, cols, rate_subplot_idx)
                    title = f"#{idx} G60:{scores_60[idx]:.3f}"
                    
                    # 使用增强的数据处理
                    self.scorer.plot_ratemap(
                        ratemaps[idx], 
                        ax=ax_rate, 
                        title=title, 
                        apply_processing=True,
                        gamma=2.0,
                        outlier_smoothing=True,
                        cmap='jet'
                    )
                    
                    # 第2行：自相关图 (直接对应下方)
                    sac_subplot_idx = i + 1 + cols  # 第2行对应位置
                    print(f"绘制神经元{idx}: rate_subplot={rate_subplot_idx}, sac_subplot={sac_subplot_idx}")
                    
                    ax_sac = plt.subplot(rows, cols, sac_subplot_idx)
                    self.scorer.plot_sac(
                        sacs[idx],
                        mask_params=None,  # 不显示圆圈
                        ax=ax_sac,
                        title=None  # 不重复显示title
                    )
                
                plt.tight_layout()
                pdf.savefig(fig, dpi=150, bbox_inches='tight')
                plt.close(fig)
                
            # 绘制得分分布图
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
            
            # 60度得分分布
            ax1.hist(scores_60, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
            ax1.set_title('60° Gridness Score Distribution')
            ax1.set_xlabel('Score')
            ax1.set_ylabel('Count')
            ax1.axvline(x=0, color='r', linestyle='--', label='Zero line')
            ax1.axvline(x=np.mean(scores_60), color='orange', linestyle='-', label=f'Mean: {np.mean(scores_60):.3f}')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 90度得分分布
            ax2.hist(scores_90, bins=50, alpha=0.7, color='lightcoral', edgecolor='black')
            ax2.set_title('90° Gridness Score Distribution')
            ax2.set_xlabel('Score')
            ax2.set_ylabel('Count')
            ax2.axvline(x=0, color='r', linestyle='--', label='Zero line')
            ax2.axvline(x=np.mean(scores_90), color='orange', linestyle='-', label=f'Mean: {np.mean(scores_90):.3f}')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            # Top neurons分析
            top_10_indices = ordering[:10]
            top_10_scores = scores_60[top_10_indices]
            
            ax3.bar(range(10), top_10_scores, color='green', alpha=0.7)
            ax3.set_title('Top 10 Neurons (60° Gridness)')
            ax3.set_xlabel('Rank')
            ax3.set_ylabel('Score')
            ax3.set_xticks(range(10))
            ax3.set_xticklabels([f'#{i}' for i in top_10_indices], rotation=45)
            ax3.grid(True, alpha=0.3)
            
            # 60度 vs 90度得分散点图
            ax4.scatter(scores_60, scores_90, alpha=0.6, s=20)
            ax4.set_xlabel('60° Gridness Score')
            ax4.set_ylabel('90° Gridness Score')
            ax4.set_title('60° vs 90° Gridness Scores')
            ax4.axhline(y=0, color='r', linestyle='--', alpha=0.5)
            ax4.axvline(x=0, color='r', linestyle='--', alpha=0.5)
            ax4.grid(True, alpha=0.3)
            
            plt.tight_layout()
            pdf.savefig(fig, dpi=150, bbox_inches='tight')
            plt.close(fig)
        
        print(f"可视化已保存到: {pdf_path}")
        
        # 返回得分和掩码参数
        return (scores_60, scores_90,
                np.array([np.mean(m) for m in max_60_mask]),
                np.array([np.mean(m) for m in max_90_mask]))

    def visualize_grid_cells(self, positions_list, bottleneck_list, prefix="grid"):
        """可视化grid cells - 增强版本
        
        Args:
            positions_list: 位置数据列表，每个元素是[seq_len, 2]
            bottleneck_list: bottleneck激活列表，每个元素是[seq_len, bottleneck_size]
            prefix: 文件名前缀
            
        Returns:
            avg_gridness_60: 60度 gridness 平均分数
            coverage_ratio: 空间覆盖率
        """
        print("开始Grid Cell可视化...")
        
        # 分析数据覆盖情况
        coverage_ratio = self.analyze_data_coverage(positions_list)
        
        # 将所有数据拼接到一起
        all_positions = np.concatenate([p.reshape(-1, 2) for p in positions_list], axis=0)
        bottleneck_size = bottleneck_list[0].shape[-1]
        all_bottleneck = np.concatenate([b.reshape(-1, bottleneck_size) for b in bottleneck_list], axis=0)
        
        print(f"可视化数据形状: 位置 {all_positions.shape}, 激活 {all_bottleneck.shape}")
        
        # 数据质量检查
        if coverage_ratio < 0.7:
            print(f"⚠️  警告：空间覆盖率较低 ({coverage_ratio:.1%})，可能影响可视化质量")
            print("   建议增加num_batches参数以获得更好的覆盖")
        
        # 计算所有神经元的栅格细胞分数并绘图
        filename = f"{prefix}_grid_scores.pdf"
        scores_60, scores_90, mask_60, mask_90 = self.get_scores_and_plot(
            all_positions, all_bottleneck, filename)
        
        # 计算平均得分和统计信息
        avg_gridness_60 = np.mean(scores_60)
        avg_gridness_90 = np.mean(scores_90)
        std_gridness_60 = np.std(scores_60)
        
        # 找到得分最高的神经元
        top_indices_60 = np.argsort(-scores_60)[:10]
        top_scores_60 = scores_60[top_indices_60]
        
        # 统计显著的grid cells (60度分数 > 0.3)
        significant_cells = np.sum(scores_60 > 0.3)
        strong_cells = np.sum(scores_60 > 0.5)
        
        print("\n=== Grid Cell分析结果 ===")
        print(f"空间覆盖率: {coverage_ratio:.1%}")
        print(f"60度 Gridness 平均分数: {avg_gridness_60:.4f} ± {std_gridness_60:.4f}")
        print(f"90度 Gridness 平均分数: {avg_gridness_90:.4f}")
        print(f"显著Grid Cells (>0.3): {significant_cells}/{len(scores_60)} ({significant_cells/len(scores_60)*100:.1f}%)")
        print(f"强Grid Cells (>0.5): {strong_cells}/{len(scores_60)} ({strong_cells/len(scores_60)*100:.1f}%)")
        print(f"最高分数: {np.max(scores_60):.4f} (神经元 #{top_indices_60[0]})")
        print(f"Top 10 神经元: {top_indices_60}")
        print(f"对应分数: {top_scores_60}")
        
        # 保存详细结果到文本文件
        results_path = os.path.join(self.save_dir, f"{prefix}_analysis_report.txt")
        with open(results_path, "w", encoding='utf-8') as f:
            f.write("=== Grid Cell 深度分析报告 ===\n")
            f.write(f"分析时间: {import_datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("1. 数据质量评估\n")
            f.write(f"   - 空间覆盖率: {coverage_ratio:.1%}\n")
            f.write(f"   - 总数据点数: {len(all_positions):,}\n")
            f.write(f"   - 分辨率: {self.nbins}x{self.nbins}\n")
            f.write(f"   - 环境大小: {self.env_size}m x {self.env_size}m\n\n")
            
            f.write("2. Grid Cell统计\n")
            f.write(f"   - 总神经元数: {len(scores_60)}\n")
            f.write(f"   - 60度 Gridness 平均: {avg_gridness_60:.4f} ± {std_gridness_60:.4f}\n")
            f.write(f"   - 90度 Gridness 平均: {avg_gridness_90:.4f}\n")
            f.write(f"   - 显著Grid Cells (>0.3): {significant_cells} ({significant_cells/len(scores_60)*100:.1f}%)\n")
            f.write(f"   - 强Grid Cells (>0.5): {strong_cells} ({strong_cells/len(scores_60)*100:.1f}%)\n")
            f.write(f"   - 最高分数: {np.max(scores_60):.4f}\n")
            f.write(f"   - 最低分数: {np.min(scores_60):.4f}\n\n")
            
            f.write("3. Top 20 Grid Cells\n")
            top_20_indices = np.argsort(-scores_60)[:20]
            for rank, idx in enumerate(top_20_indices, 1):
                f.write(f"   {rank:2d}. 神经元 {idx:3d}: 60°={scores_60[idx]:.4f}, 90°={scores_90[idx]:.4f}\n")
            
            f.write("\n4. 所有神经元详细得分\n")
            for i in range(len(scores_60)):
                f.write(f"神经元 {i:3d}: 60°={scores_60[i]:.4f}, 90°={scores_90[i]:.4f}\n")
        
        print(f"详细分析报告已保存到: {results_path}")
        
        # 可视化相位关系（如果有足够高分数的神经元）
        if significant_cells >= 3:
            high_score_indices = np.where(scores_60 > 0.3)[0][:3]
            self.visualize_phase_relationship(high_score_indices, all_positions, all_bottleneck, prefix)
            
        # 如果有特别好的grid cells，创建单独的高质量图像
        if np.max(scores_60) > 0.5:
            self.create_showcase_figures(top_indices_60[:6], all_positions, all_bottleneck, 
                                    scores_60, sacs if 'sacs' in locals() else None, prefix)
        
        return avg_gridness_60, coverage_ratio
    
    def create_showcase_figures(self, top_indices, positions, activations, scores_60, sacs, prefix):
        """创建用于展示的高质量单神经元图像"""
        print("创建showcase图像...")
        
        # 为每个top神经元创建单独的高质量图像
        for idx in top_indices:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 12))
            
            # 计算rate map，处理无数据区域
            ratemap, counts = self.scorer.calculate_ratemap(
                positions[:, 0], positions[:, 1], activations[:, idx])
            
            # 确保没有异常值
            ratemap = np.nan_to_num(ratemap, nan=0.0, posinf=0.0, neginf=0.0)
            
            # 原始rate map
            im1 = ax1.imshow(ratemap, cmap='jet', interpolation='nearest', 
                        origin='lower', aspect='equal')
            ax1.set_title(f'原始 Rate Map (神经元 #{idx})', fontsize=14)
            ax1.set_xlabel('X bins')
            ax1.set_ylabel('Y bins')
            plt.colorbar(im1, ax=ax1, label='平均激活')
            
            # 修复版数据处理，处理无数据区域
            ratemap_thresh = ratemap.copy()
            ratemap_thresh = np.nan_to_num(ratemap_thresh, nan=0.0, posinf=0.0, neginf=0.0)
            
            if np.max(ratemap_thresh) > 0:
                # 轻度处理，避免残缺
                from scipy.ndimage import gaussian_filter
                
                # 只对有数据的区域滤波
                data_mask = ratemap_thresh > 0
                if np.any(data_mask):
                    filtered = gaussian_filter(ratemap_thresh, sigma=0.5)
                    ratemap_thresh = np.where(data_mask, filtered, 0.0)
                    
                    # 温和的阈值
                    valid_pixels = ratemap_thresh[ratemap_thresh > 0]
                    if len(valid_pixels) > 0:
                        threshold = np.mean(valid_pixels) * 0.2
                        ratemap_thresh[ratemap_thresh < threshold] = 0.0
            
            im2 = ax2.imshow(ratemap_thresh, cmap='jet', interpolation='nearest', 
                        origin='lower', aspect='equal')
            ax2.set_title(f'轻度处理 (去噪+温和阈值)', fontsize=14)
            ax2.set_xlabel('X bins')
            ax2.set_ylabel('Y bins')
            plt.colorbar(im2, ax=ax2, label='平均激活')
            
            # 自相关图 (如果提供)
            if sacs is not None:
                sac = sacs[top_indices.tolist().index(idx)]
                useful_sac = sac * self.scorer._plotting_sac_mask
                im3 = ax3.imshow(useful_sac, cmap=self.scorer._get_autocorr_cmap(), 
                            interpolation='nearest', origin='lower', aspect='equal',
                            vmin=-1, vmax=1)
                ax3.set_title('空间自相关图', fontsize=14)
                plt.colorbar(im3, ax=ax3, label='相关系数')
            else:
                ax3.text(0.5, 0.5, '自相关图\n未提供', ha='center', va='center', 
                        transform=ax3.transAxes, fontsize=16)
                ax3.set_title('空间自相关图', fontsize=14)
            
            # 激活分布直方图
            activations_for_neuron = activations[:, idx]
            ax4.hist(activations_for_neuron, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
            ax4.set_title('激活值分布', fontsize=14)
            ax4.set_xlabel('激活值')
            ax4.set_ylabel('频次')
            ax4.axvline(np.mean(activations_for_neuron), color='red', linestyle='--', 
                    label=f'均值: {np.mean(activations_for_neuron):.3f}')
            ax4.legend()
            ax4.grid(True, alpha=0.3)
            
            # 添加总体信息
            fig.suptitle(f'神经元 #{idx} - Gridness Score: {scores_60[idx]:.4f}', 
                        fontsize=16, y=0.95)
            
            plt.tight_layout()
            
            # 保存高质量图像
            save_path = os.path.join(self.save_dir, f'{prefix}_showcase_neuron_{idx}.png')
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
        
        print(f"Showcase图像已保存到 {self.save_dir}")
    
    def visualize_phase_relationship(self, rgb_neurons, positions, activations, prefix="grid"):
        """创建展示相位关系的RGB图 - 增强版本
        
        Args:
            rgb_neurons: 用于RGB通道的3个神经元索引
            positions: 位置数据 [N, 2]
            activations: 激活数据 [N, bottleneck_size]
            prefix: 文件名前缀
        """
        print(f"创建相位关系图，使用神经元: {rgb_neurons}")
        
        # 处理数据，确保无数据区域正确显示
        ratemaps = []
        for neuron_idx in rgb_neurons:
            ratemap, counts = self.scorer.calculate_ratemap(
                positions[:, 0], positions[:, 1], activations[:, neuron_idx])
            
            # 确保无异常值
            ratemap = np.nan_to_num(ratemap, nan=0.0, posinf=0.0, neginf=0.0)
            
            # 温和的处理
            from scipy.ndimage import gaussian_filter
            data_mask = ratemap > 0
            if np.any(data_mask):
                filtered = gaussian_filter(ratemap, sigma=0.5)
                ratemap_processed = np.where(data_mask, filtered, 0.0)
                
                # 温和的阈值
                valid_pixels = ratemap_processed[ratemap_processed > 0]
                if len(valid_pixels) > 0:
                    threshold = np.mean(valid_pixels) * 0.15  # 更低的阈值保留更多信息
                    ratemap_processed[ratemap_processed < threshold] = 0.0
            else:
                ratemap_processed = ratemap
            
            # 简单归一化
            ratemap_max = np.max(ratemap_processed)
            if ratemap_max > 0:
                normalized = ratemap_processed / ratemap_max
            else:
                normalized = ratemap_processed
            ratemaps.append(normalized)
        
        # 创建RGB图
        rgb_map = np.stack([np.nan_to_num(r) for r in ratemaps], axis=-1)
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # RGB合成图
        ax1.imshow(rgb_map, origin='lower', extent=[0, self.env_size, 0, self.env_size],
                interpolation='nearest')
        ax1.set_title(f"相位关系 RGB图\n(R: #{rgb_neurons[0]}, G: #{rgb_neurons[1]}, B: #{rgb_neurons[2]})")
        ax1.set_xlabel("X position (m)")
        ax1.set_ylabel("Y position (m)")
        
        # 单独显示R通道 (红色)
        ax2.imshow(ratemaps[0], cmap='Reds', origin='lower', 
                extent=[0, self.env_size, 0, self.env_size], interpolation='nearest')
        ax2.set_title(f"红色通道 (神经元 #{rgb_neurons[0]})")
        ax2.set_xlabel("X position (m)")
        ax2.set_ylabel("Y position (m)")
        
        # 单独显示G通道 (绿色)
        ax3.imshow(ratemaps[1], cmap='Greens', origin='lower', 
                extent=[0, self.env_size, 0, self.env_size], interpolation='nearest')
        ax3.set_title(f"绿色通道 (神经元 #{rgb_neurons[1]})")
        ax3.set_xlabel("X position (m)")
        ax3.set_ylabel("Y position (m)")
        
        # 单独显示B通道 (蓝色)
        ax4.imshow(ratemaps[2], cmap='Blues', origin='lower', 
                extent=[0, self.env_size, 0, self.env_size], interpolation='nearest')
        ax4.set_title(f"蓝色通道 (神经元 #{rgb_neurons[2]})")
        ax4.set_xlabel("X position (m)")
        ax4.set_ylabel("Y position (m)")
        
        plt.tight_layout()
        
        # 保存图像
        save_path = os.path.join(self.save_dir, f'{prefix}_phase_relationship.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        
        print(f"相位关系图已保存到: {save_path}")


# 导入datetime用于报告生成
import datetime as import_datetime


def load_model(model_path, device):
    """加载预训练模型
    
    Args:
        model_path: 模型文件路径
        device: 计算设备
        
    Returns:
        model: 加载的模型
    """
    try:
        # 加载模型状态字典
        checkpoint = torch.load(model_path, map_location=device)
        print(f"成功加载检查点，训练轮次: {checkpoint.get('epoch', 'unknown')}")
        
        # 导入必要的模块
        from models.toroidal_grid_cell import GridCellNetwork
        from models.place_hd_cells import PlaceCellEnsemble, HeadDirectionCellEnsemble
        from config import Config
        
        # 读取配置
        config = Config()
        
        # 创建place和hd cells
        place_cells = PlaceCellEnsemble(
            n_cells=config.PLACE_CELLS_N,
            scale=config.PLACE_CELLS_SCALE,
            pos_min=0,
            pos_max=config.ENV_SIZE,
            seed=config.SEED
        )
        
        hd_cells = HeadDirectionCellEnsemble(
            n_cells=config.HD_CELLS_N,
            concentration=config.HD_CELLS_CONCENTRATION,
            seed=config.SEED
        )
        
        # 创建模型
        model = GridCellNetwork(
            place_cells=place_cells,
            hd_cells=hd_cells,
            input_size=3,
            hidden_size=config.HIDDEN_SIZE,
            bottleneck_size=256,
            dropout_rate=config.DROPOUT_RATE
        ).to(device)
        
        # 加载状态字典
        model_state_dict = checkpoint['model_state_dict']
        
        # 处理可能的DDP模型状态字典
        if all(k.startswith('module.') for k in model_state_dict.keys()):
            # 创建新的 OrderedDict 去掉 "module." 前缀
            from collections import OrderedDict
            new_state_dict = OrderedDict()
            for k, v in model_state_dict.items():
                name = k[7:]  # 去掉 "module." 前缀
                new_state_dict[name] = v
            model_state_dict = new_state_dict
        
        model.load_state_dict(model_state_dict)
        print("模型参数加载成功")
        
        return model
    
    except Exception as e:
        print(f"加载模型时出错: {e}")
        import traceback
        traceback.print_exc()
        return None


def load_dataloader(split='val', batch_size=16, num_workers=2):
    """创建数据加载器
    
    Args:
        split: 'train' 或 'val'
        batch_size: 批次大小
        num_workers: 工作线程数
        
    Returns:
        dataloader: 数据加载器
    """
    try:
        # 导入必要的模块
        from datasets.navigation_dataset import EnhancedNavigationDataset
        from config import Config
        
        # 读取配置
        config = Config()
        
        # 获取轨迹文件夹
        trajectory_folders = [
            d for d in os.listdir(config.DATA_ROOT)
            if os.path.isdir(os.path.join(config.DATA_ROOT, d))
            and d.startswith('D')
        ]
        trajectory_folders.sort()
        
        # 划分数据集
        n_total = len(trajectory_folders)
        n_train = int(n_total * 0.7)
        n_val = int(n_total * 0.15)
        
        if split == 'train':
            folders = trajectory_folders[:n_train]
        elif split == 'val':
            folders = trajectory_folders[n_train:n_train + n_val]
        else:
            folders = trajectory_folders[n_train + n_val:]
        
        dataset_dirs = []
        for folder in folders:
            dataset_dirs.append(os.path.join(config.DATA_ROOT, folder, '000000'))
        
        # 创建数据集
        dataset = EnhancedNavigationDataset(
            maze_dirs=dataset_dirs,
            sequence_length=config.SEQUENCE_LENGTH,
            stride=config.SEQUENCE_STRIDE,
            split=split
        )
        
        # 创建数据加载器
        dataloader = torch.utils.data.DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=True,
            drop_last=False
        )
        
        print(f"创建{split}数据加载器成功，大小: {len(dataset)}，批次数: {len(dataloader)}")
        return dataloader
    
    except Exception as e:
        print(f"创建数据加载器时出错: {e}")
        import traceback
        traceback.print_exc()
        return None


def main():
    parser = argparse.ArgumentParser(description='Grid Cell可视化工具 - 增强版')
    parser.add_argument('--model_path', type=str, required=True, help='预训练模型的路径')
    parser.add_argument('--save_dir', type=str, default='grid_cell_viz_enhanced', help='保存可视化结果的目录')
    parser.add_argument('--batch_size', type=int, default=32, help='数据批次大小')
    parser.add_argument('--num_batches', type=int, default=500, help='用于可视化的批次数量 (增加默认值)')
    parser.add_argument('--device', type=str, default='cuda:0', help='计算设备')
    parser.add_argument('--prefix', type=str, default='enhanced_grid', help='输出文件名前缀')
    parser.add_argument('--split', type=str, default='val', choices=['train', 'val', 'test'], help='使用的数据集分割')
    parser.add_argument('--nbins', type=int, default=50, help='Rate map分辨率 (提高默认值)')
    parser.add_argument('--env_size', type=float, default=15.0, help='环境大小')
    parser.add_argument('--gamma', type=float, default=2.0, help='伽马校正参数 (1.5-3.0, 越大对比度越强)')
    parser.add_argument('--outlier_smoothing', action='store_true', default=True, help='是否启用异常值平滑')
    parser.add_argument('--outlier_threshold', type=float, default=2.0, help='异常值检测阈值 (1.5-2.5)')
    parser.add_argument('--min_coverage', type=float, default=0.7, help='最小空间覆盖率阈值')
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.save_dir, exist_ok=True)
    
    # 记录参数
    with open(os.path.join(args.save_dir, 'visualization_params.txt'), 'w') as f:
        f.write("=== 可视化参数 ===\n")
        for arg, value in vars(args).items():
            f.write(f"{arg}: {value}\n")
        f.write(f"运行时间: {import_datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载模型
    print("加载模型...")
    model = load_model(args.model_path, device)
    if model is None:
        print("❌ 模型加载失败，退出程序")
        return
    print("✅ 模型加载成功")
    
    # 加载数据
    print("加载数据...")
    dataloader = load_dataloader(split=args.split, batch_size=args.batch_size)
    if dataloader is None:
        print("❌ 数据加载器创建失败，退出程序")
        return
    print("✅ 数据加载成功")
    
    # 创建增强版可视化器
    print("初始化可视化器...")
    visualizer = GridCellVisualizer(args.save_dir, nbins=args.nbins, env_size=args.env_size)
    print("✅ 可视化器初始化完成")
    
    # 收集数据
    print("开始收集数据...")
    positions_list, bottleneck_list = visualizer.collect_grid_cell_data(
        model, dataloader, device, num_batches=args.num_batches
    )
    print("✅ 数据收集完成")
    
    # 进行增强版可视化
    print("开始增强版可视化分析...")
    avg_gridness, coverage_ratio = visualizer.visualize_grid_cells(
        positions_list, bottleneck_list, prefix=args.prefix
    )
    
    # 最终报告
    print("\n" + "="*50)
    print("🎉 可视化完成！")
    print(f"📊 平均Gridness分数: {avg_gridness:.4f}")
    print(f"🗺️  空间覆盖率: {coverage_ratio:.1%}")
    print(f"📁 结果保存在: {args.save_dir}")
    
    if coverage_ratio < args.min_coverage:
        print(f"⚠️  覆盖率低于阈值 ({args.min_coverage:.1%})，建议增加 --num_batches 参数")
    
    if avg_gridness > 0.3:
        print("🌟 发现了显著的Grid Cell活动！")
    elif avg_gridness > 0.1:
        print("✨ 检测到中等程度的Grid Cell活动")
    else:
        print("💡 Grid Cell活动较弱，可能需要：")
        print("   - 检查模型是否充分训练")
        print("   - 增加数据量")
        print("   - 调整可视化参数")
    
    print("="*50)


if __name__ == "__main__":
    main()