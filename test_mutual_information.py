#!/usr/bin/env python3
"""
测试互信息分析功能的简化脚本
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from utils.metrics import compute_mutual_information, compute_mutual_information_2d


def test_mutual_information_functions():
    """测试互信息计算函数"""
    print("Testing mutual information functions...")
    
    # 创建测试数据
    np.random.seed(42)
    n_samples = 1000
    
    # 测试1：完全随机的数据（应该有低互信息）
    random_activations = np.random.randn(n_samples)
    random_positions = np.random.randn(n_samples, 2)
    
    mi_random = compute_mutual_information_2d(random_activations, random_positions)
    print(f"Random data MI: {mi_random:.4f} (should be low)")
    
    # 测试2：相关的数据（应该有高互信息）
    x = np.linspace(0, 10, n_samples)
    y = np.linspace(0, 10, n_samples)
    positions = np.column_stack([x, y])
    
    # 创建与位置相关的激活
    activations = np.sin(x) + np.cos(y) + 0.1 * np.random.randn(n_samples)
    
    mi_correlated = compute_mutual_information_2d(activations, positions)
    print(f"Correlated data MI: {mi_correlated:.4f} (should be higher)")
    
    # 测试3：网格状激活模式（模拟grid cell）
    grid_x, grid_y = np.meshgrid(np.linspace(0, 4*np.pi, 50), np.linspace(0, 4*np.pi, 50))
    grid_positions = np.column_stack([grid_x.flatten(), grid_y.flatten()])
    
    # 创建六边形网格模式
    grid_activations = (np.cos(grid_x.flatten()) + 
                       np.cos(0.5*grid_x.flatten() + np.sqrt(3)/2*grid_y.flatten()) + 
                       np.cos(0.5*grid_x.flatten() - np.sqrt(3)/2*grid_y.flatten()))
    
    mi_grid = compute_mutual_information_2d(grid_activations, grid_positions)
    print(f"Grid pattern MI: {mi_grid:.4f} (should be high)")
    
    return mi_random, mi_correlated, mi_grid


def create_test_visualization():
    """创建测试可视化"""
    print("Creating test visualization...")
    
    # 模拟不同类型细胞的互信息数据
    cell_types = ['Pure Place Cell', 'Pure SPC', 'Special SPC', 'Mixed Response']
    
    # 模拟数据：Place Cell应该对self position有高MI，对partner position有低MI
    # SPC应该相反
    np.random.seed(42)
    
    self_mi_data = {
        'Pure Place Cell': np.random.gamma(2, 0.1, 20),  # 高自身位置MI
        'Pure SPC': np.random.gamma(0.5, 0.05, 15),      # 低自身位置MI
        'Special SPC': np.random.gamma(1.5, 0.08, 10),   # 中等自身位置MI
        'Mixed Response': np.random.gamma(1, 0.06, 25)    # 中等自身位置MI
    }
    
    partner_mi_data = {
        'Pure Place Cell': np.random.gamma(0.5, 0.05, 20),  # 低伙伴位置MI
        'Pure SPC': np.random.gamma(2, 0.1, 15),            # 高伙伴位置MI
        'Special SPC': np.random.gamma(1.5, 0.08, 10),      # 中等伙伴位置MI
        'Mixed Response': np.random.gamma(1, 0.06, 25)       # 中等伙伴位置MI
    }
    
    # 创建双重解离图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
    
    # 准备数据
    cell_type_names = []
    self_means = []
    self_stds = []
    partner_means = []
    partner_stds = []
    counts = []
    
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']
    
    for i, cell_type in enumerate(cell_types):
        cell_type_names.append(cell_type)
        
        self_data = self_mi_data[cell_type]
        partner_data = partner_mi_data[cell_type]
        
        self_means.append(np.mean(self_data))
        self_stds.append(np.std(self_data) / np.sqrt(len(self_data)))
        
        partner_means.append(np.mean(partner_data))
        partner_stds.append(np.std(partner_data) / np.sqrt(len(partner_data)))
        
        counts.append(len(self_data))
    
    x = np.arange(len(cell_types))
    width = 0.35
    
    # 子面板A：自我位置编码
    bars1 = ax1.bar(x, self_means, width, yerr=self_stds, 
                   color=colors, alpha=0.8, capsize=5)
    ax1.set_xlabel('Cell Type', fontsize=12)
    ax1.set_ylabel('Mutual Information with Self Position', fontsize=12)
    ax1.set_title('A. Self-Position Encoding\n(Single-Agent Mode)', fontsize=14, fontweight='bold')
    ax1.set_xticks(x)
    ax1.set_xticklabels([f'{ct}\n(n={count})' for ct, count in zip(cell_type_names, counts)], 
                       rotation=45, ha='right')
    ax1.grid(True, alpha=0.3)
    
    # 子面板B：社交信息编码
    bars2 = ax2.bar(x, partner_means, width, yerr=partner_stds, 
                   color=colors, alpha=0.8, capsize=5)
    ax2.set_xlabel('Cell Type', fontsize=12)
    ax2.set_ylabel('Mutual Information with Partner Position', fontsize=12)
    ax2.set_title('B. Social Information Encoding\n(Two-Agent Mode)', fontsize=14, fontweight='bold')
    ax2.set_xticks(x)
    ax2.set_xticklabels([f'{ct}\n(n={count})' for ct, count in zip(cell_type_names, counts)], 
                       rotation=45, ha='right')
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, (bar1, bar2) in enumerate(zip(bars1, bars2)):
        height1 = bar1.get_height()
        height2 = bar2.get_height()
        ax1.text(bar1.get_x() + bar1.get_width()/2., height1 + self_stds[i],
                f'{height1:.3f}', ha='center', va='bottom', fontsize=10)
        ax2.text(bar2.get_x() + bar2.get_width()/2., height2 + partner_stds[i],
                f'{height2:.3f}', ha='center', va='bottom', fontsize=10)
    
    plt.tight_layout()
    
    # 保存图像
    output_path = 'test_double_dissociation_plot.png'
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"Test visualization saved to: {output_path}")
    
    return output_path


def main():
    print("="*50)
    print("TESTING MUTUAL INFORMATION ANALYSIS")
    print("="*50)
    
    # 测试互信息计算函数
    mi_random, mi_correlated, mi_grid = test_mutual_information_functions()
    
    print("\n" + "="*50)
    print("TESTING VISUALIZATION")
    print("="*50)
    
    # 创建测试可视化
    viz_path = create_test_visualization()
    
    print("\n" + "="*50)
    print("TEST SUMMARY")
    print("="*50)
    print(f"✓ Mutual information functions working")
    print(f"✓ Random data MI: {mi_random:.4f}")
    print(f"✓ Correlated data MI: {mi_correlated:.4f}")
    print(f"✓ Grid pattern MI: {mi_grid:.4f}")
    print(f"✓ Test visualization created: {viz_path}")
    
    # 验证结果合理性
    if mi_correlated > mi_random and mi_grid > mi_random:
        print("✓ MI calculations appear to be working correctly!")
    else:
        print("⚠️ Warning: MI calculations may need adjustment")
    
    print("\n" + "="*50)
    print("READY TO RUN FULL ANALYSIS")
    print("="*50)
    print("You can now run the full analysis with:")
    print("python run_mutual_information_analysis.py --model_path <your_model_path> --output_dir mi_results")


if __name__ == "__main__":
    main()
