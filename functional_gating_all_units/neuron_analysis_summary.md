# 🧠 所有神经元功能性门控分析报告

## 📊 总体统计

- 总神经元数量: 64
- 活跃神经元数量: 44
- 静默神经元数量: 20

## 🏆 Top 10 最佳功能性门控神经元

| 排名 | 神经元 | 评分 | 最大激活 | 平均激活 | 相关系数 | p值 | 门控效应 |
|------|--------|------|----------|----------|----------|-----|----------|
| 1 | 50 | 1.956 | 12.637 | 6.416 | -0.044 | 2.04e-14 | 弱 |
| 2 | 45 | 1.442 | 4.893 | 3.560 | 0.535 | 0.00e+00 | 强 |
| 3 | 40 | 1.437 | 4.552 | 2.906 | 0.807 | 0.00e+00 | 强 |
| 4 | 13 | 1.430 | 5.687 | 3.407 | 0.582 | 0.00e+00 | 强 |
| 5 | 20 | 1.380 | 4.395 | 2.994 | 0.688 | 0.00e+00 | 强 |
| 6 | 33 | 1.370 | 4.946 | 2.960 | 0.689 | 0.00e+00 | 强 |
| 7 | 16 | 1.351 | 4.906 | 2.773 | 0.742 | 0.00e+00 | 强 |
| 8 | 57 | 1.349 | 4.152 | 2.915 | 0.678 | 0.00e+00 | 强 |
| 9 | 61 | 1.321 | 5.619 | 3.012 | 0.597 | 0.00e+00 | 强 |
| 10 | 15 | 1.245 | 4.246 | 2.545 | 0.688 | 0.00e+00 | 强 |

## 🔍 详细分类

### 强门控效应神经元 (|r| > 0.5): 14个
- 神经元 45: r=0.535, 激活=3.560
- 神经元 40: r=0.807, 激活=2.906
- 神经元 13: r=0.582, 激活=3.407
- 神经元 20: r=0.688, 激活=2.994
- 神经元 33: r=0.689, 激活=2.960
- 神经元 16: r=0.742, 激活=2.773
- 神经元 57: r=0.678, 激活=2.915
- 神经元 61: r=0.597, 激活=3.012
- 神经元 15: r=0.688, 激活=2.545
- 神经元 39: r=0.708, 激活=1.966
- 神经元 56: r=0.761, 激活=1.719
- 神经元 53: r=0.746, 激活=1.744
- 神经元 7: r=0.572, 激活=1.870
- 神经元 3: r=0.572, 激活=0.929

### 中等门控效应神经元 (0.2 < |r| ≤ 0.5): 17个
- 神经元 38: r=0.480, 激活=2.878
- 神经元 2: r=0.488, 激活=2.776
- 神经元 0: r=0.400, 激活=2.977
- 神经元 4: r=0.348, 激活=2.477
- 神经元 42: r=0.364, 激活=2.178
- 神经元 10: r=0.363, 激活=1.910
- 神经元 27: r=0.262, 激活=1.728
- 神经元 58: r=0.383, 激活=1.407
- 神经元 55: r=0.388, 激活=0.908
- 神经元 8: r=0.478, 激活=0.385
- 神经元 52: r=0.389, 激活=0.385
- 神经元 60: r=0.369, 激活=0.383
- 神经元 19: r=-0.393, 激活=0.267
- 神经元 51: r=-0.372, 激活=0.082
- 神经元 25: r=0.332, 激活=0.135
- 神经元 5: r=0.330, 激活=0.086
- 神经元 22: r=-0.223, 激活=0.004

### 静默神经元: 20个
神经元索引: 1, 6, 11, 17, 23, 24, 26, 28, 29, 31, 32, 34, 36, 43, 44, 46, 48, 49, 59, 63

## 🎯 推荐可视化

基于分析结果，推荐可视化以下神经元：

1. **神经元 50**: 评分=1.956, 相关性=-0.044
   ```bash
   python utils/functional_gating_viz.py --specific_neuron 50 --num_reps 400
   ```

2. **神经元 45**: 评分=1.442, 相关性=0.535
   ```bash
   python utils/functional_gating_viz.py --specific_neuron 45 --num_reps 400
   ```

3. **神经元 40**: 评分=1.437, 相关性=0.807
   ```bash
   python utils/functional_gating_viz.py --specific_neuron 40 --num_reps 400
   ```

4. **神经元 13**: 评分=1.430, 相关性=0.582
   ```bash
   python utils/functional_gating_viz.py --specific_neuron 13 --num_reps 400
   ```

5. **神经元 20**: 评分=1.380, 相关性=0.688
   ```bash
   python utils/functional_gating_viz.py --specific_neuron 20 --num_reps 400
   ```

