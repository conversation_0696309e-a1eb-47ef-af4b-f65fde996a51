# visualize_social_cells.py (Nature期刊风格版)
import torch
import numpy as np
import matplotlib.pyplot as plt
import os
import argparse
from tqdm import tqdm
from scipy.ndimage import gaussian_filter, gaussian_filter1d
import multiprocessing as mp
from dataclasses import dataclass, field
from typing import Dict, List, Tuple
import time
from collections import defaultdict

# --- 导入你项目中的模块 ---
from config import Config
from models.social_grid_cell import SocialGridCellNetwork
from models.place_hd_cells import PlaceCellEnsemble, HeadDirectionCellEnsemble

# --- 数据结构定义 ---
@dataclass
class ConditionAnalysis:
    peak_activation: float = 0.0
    activations: np.ndarray = field(default_factory=lambda: np.array([]))
    positions_to_plot: np.ndarray = field(default_factory=lambda: np.array([]))
    trajectory_key: str = "self"

@dataclass
class NeuronAnalysisResult:
    neuron_idx: int
    cell_type: str = "Unclassified"
    condition_data: Dict[int, ConditionAnalysis] = field(default_factory=dict)

# --- 细胞分类器 (逻辑不变) ---
class CellTypeDetector:
    @staticmethod
    def classify(peak_results: Dict[int, float]) -> str:
        c1, c2, c3, c4 = peak_results[1], peak_results[2], peak_results[3], peak_results[4]
        high_thresh, low_thresh = 0.1, 0.05
        is_responsive_to_self = c1 > high_thresh
        is_responsive_to_peer = c2 > high_thresh
        is_silent_when_static = c4 < low_thresh

        if not is_silent_when_static:
            return "Other (Active when static)"
        if is_responsive_to_self and is_responsive_to_peer:
            return "Special SPC"
        elif is_responsive_to_self and not is_responsive_to_peer:
            if c3 > high_thresh: return "Pure Place Cell"
            else: return "Mixed Response"
        elif not is_responsive_to_self and is_responsive_to_peer:
            if c3 > high_thresh: return "Pure SPC"
            else: return "Mixed Response"
        else:
            return "Other"

# --- 美化后的轨迹生成器 ---
class TrajectoryGenerator:
    def __init__(self, config: Config):
        self.maze_size = config.ENV_SIZE
        self.seq_len = config.SEQUENCE_LENGTH
        # 新的起点和终点，覆盖更广的区域
        self.start_S = np.array([self.maze_size * 0.5, self.maze_size * 0.2])
        self.end_A = np.array([self.maze_size * 0.2, self.maze_size * 0.8])
        self.end_B = np.array([self.maze_size * 0.8, self.maze_size * 0.8])

    def _generate_path(self, start: np.ndarray, end: np.ndarray, moving: bool) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        if not moving:
            positions = np.tile(start, (self.seq_len, 1))
            velocities = np.zeros((self.seq_len, 2))
            angular_velocities = np.zeros(self.seq_len)
            return positions, velocities, angular_velocities

        # 1. 创建基础直线路径
        base_positions = np.array([start + (t / max(1, self.seq_len - 1)) * (end - start) for t in range(self.seq_len)])
        
        # 2. 创建平滑的随机扰动
        direction = (end - start) / (np.linalg.norm(end - start) + 1e-6)
        perpendicular = np.array([-direction[1], direction[0]])
        
        # 随机正弦波参数
        amplitude = np.random.uniform(0.5, 1.5)
        frequency = np.random.uniform(1.5, 2.5)
        phase = np.random.uniform(0, np.pi)
        
        t = np.linspace(0, 1, self.seq_len)
        sine_offset = amplitude * np.sin(2 * np.pi * frequency * t + phase)
        
        # 将扰动应用到垂直于运动方向的轴上
        positions = base_positions + sine_offset[:, np.newaxis] * perpendicular[np.newaxis, :]
        positions = np.clip(positions, 0.5, self.maze_size - 0.5)

        velocities = np.diff(positions, axis=0, prepend=positions[0:1])
        angles = np.arctan2(velocities[:, 1], velocities[:, 0])
        angular_velocities = np.diff(np.unwrap(angles), prepend=angles[0:1])
        return positions, velocities, angular_velocities

    def get_trajectories_for_condition(self, condition: int, num_reps: int = 20) -> Dict:
        all_self_pos, all_self_vel, all_self_ang_vel = [], [], []
        all_peer_pos, all_peer_vel, all_peer_ang_vel = [], [], []
        movement_patterns = {1: (True, False), 2: (False, True), 3: (True, True), 4: (False, False)}
        self_moving, peer_moving = movement_patterns[condition]
        
        for i in range(num_reps):
            target = self.end_A if i % 2 == 0 else self.end_B
            static_pos = self.start_S
            
            self_start, self_end = (self.start_S, target) if self_moving else (static_pos, static_pos)
            peer_start, peer_end = (self.start_S, target) if peer_moving else (static_pos, static_pos)

            self_pos, self_vel, self_ang_vel = self._generate_path(self_start, self_end, self_moving)
            peer_pos, peer_vel, peer_ang_vel = self._generate_path(peer_start, peer_end, peer_moving)
            
            all_self_pos.append(self_pos); all_self_vel.append(self_vel); all_self_ang_vel.append(self_ang_vel)
            all_peer_pos.append(peer_pos); all_peer_vel.append(peer_vel); all_peer_ang_vel.append(peer_ang_vel)
        
        return {
            'self_pos': np.array(all_self_pos), 'self_vel': np.array(all_self_vel), 'self_ang_vel': np.array(all_self_ang_vel),
            'peer_pos': np.array(all_peer_pos), 'peer_vel': np.array(all_peer_vel), 'peer_ang_vel': np.array(all_peer_ang_vel),
        }

# --- Nature期刊风格的可视化函数 ---
def visualize_neuron(args_tuple):
    """
    生成符合Nature期刊发表标准的可视化图。
    - 白色背景，黑色文字和坐标轴
    - 输出为 PDF 矢量图
    - 修正布局，确保所有元素清晰可见
    - 使用学术期刊标准的颜色和字体
    """
    result, config_dict, output_dir = args_tuple
    config = Config.from_dict(config_dict)
    
    # --- Nature期刊风格设置 ---
    # 重置matplotlib样式，确保使用默认的白色背景
    plt.rcdefaults()
    
    # 设置期刊级别的字体和样式
    plt.rcParams.update({
        'font.family': 'sans-serif',
        'font.sans-serif': ['Arial', 'Helvetica', 'DejaVu Sans'],  # Nature常用字体
        'font.size': 8,           # 期刊标准字体大小
        'axes.labelsize': 9,
        'axes.titlesize': 10,
        'xtick.labelsize': 8,
        'ytick.labelsize': 8,
        'legend.fontsize': 7,
        'figure.titlesize': 12,
        'axes.linewidth': 0.8,    # 细线条
        'xtick.major.width': 0.8,
        'ytick.major.width': 0.8,
        'axes.spines.top': False,     # 去掉上边框
        'axes.spines.right': False,   # 去掉右边框
        'axes.grid': True,
        'grid.alpha': 0.3,
        'grid.linewidth': 0.5,
    })

    neuron_idx = result.neuron_idx
    
    # 创建 1x4 的子图布局，调整大小和间距
    fig, axes = plt.subplots(1, 4, figsize=(16, 4))  # 适中的图片尺寸
    fig.suptitle(f'Neuron {neuron_idx} - {result.cell_type}', 
                fontsize=12, fontweight='bold', y=0.98)  # 调整标题位置
    
    # 计算颜色条的范围
    peak_acts = [d.peak_activation for d in result.condition_data.values() if d.peak_activation > 0]
    vmax = max(0.2, max(peak_acts)) if peak_acts else 0.2
    vmin = 0.0
    
    # 使用对色盲友好且适合期刊的'plasma'色谱（比viridis对比度更好）
    cmap = 'plasma'

    for i, condition in enumerate([1, 2, 3, 4]):
        ax = axes[i]
        data = result.condition_data[condition]
        
        # 生成高分辨率热力图
        grid_size = 50
        heatmap = np.zeros((grid_size, grid_size))
        counts = np.zeros((grid_size, grid_size))

        positions = data.positions_to_plot
        activations = data.activations

        # 累积激活值到网格中
        for traj_idx in range(positions.shape[0]):
            for step_idx in range(positions.shape[1]):
                pos = positions[traj_idx, step_idx]
                act = activations[traj_idx, step_idx]
                
                grid_x = int(np.clip((pos[0] / config.ENV_SIZE) * grid_size, 0, grid_size - 1))
                grid_y = int(np.clip((pos[1] / config.ENV_SIZE) * grid_size, 0, grid_size - 1))
                
                heatmap[grid_y, grid_x] += act
                counts[grid_y, grid_x] += 1
        
        # 计算平均激活值
        with np.errstate(divide='ignore', invalid='ignore'):
            avg_heatmap = np.nan_to_num(heatmap / counts)
        
        # 平滑处理
        smoothed_heatmap = gaussian_filter(avg_heatmap, sigma=1.5)
        
        # 绘制热力图
        im = ax.imshow(smoothed_heatmap, cmap=cmap, origin='lower',
                       extent=[0, config.ENV_SIZE, 0, config.ENV_SIZE], 
                       vmin=vmin, vmax=vmax, aspect='equal')
        
        # 绘制轨迹线（使用深灰色，在白色背景下清晰可见）
        for traj_idx in range(min(3, positions.shape[0])):
            path = positions[traj_idx]
            ax.plot(path[:, 0], path[:, 1], color='#333333', alpha=0.7, linewidth=1.2)
            
            # 标记起点和终点（使用Nature期刊常见的颜色）
            ax.scatter(path[0, 0], path[0, 1], color='#0072B2', s=20, zorder=3, 
                      label='Start' if traj_idx==0 else "", edgecolor='white', linewidth=0.5)
            ax.scatter(path[-1, 0], path[-1, 1], color='#D55E00', s=20, zorder=3, 
                      marker='s', label='End' if traj_idx==0 else "", edgecolor='white', linewidth=0.5)

        # 设置子图标题和标签
        title_map = {
            1: "Self Moving\nPeer Static", 
            2: "Peer Moving\nSelf Static", 
            3: "Both Moving", 
            4: "Both Static"
        }
        ax.set_title(f'{title_map[condition]}\nPeak: {data.peak_activation:.3f}', 
                    fontsize=9, pad=10)
        
        # 设置坐标轴标签
        ax.set_xlabel('X Position (m)', fontsize=9)
        if i == 0:
            ax.set_ylabel('Y Position (m)', fontsize=9)
            ax.legend(loc='upper right', frameon=True, fancybox=False, shadow=False)
        
        # 设置坐标轴范围和刻度
        ax.set_xlim(0, config.ENV_SIZE)
        ax.set_ylim(0, config.ENV_SIZE)
        
        # 设置合理的刻度
        ax.set_xticks(np.linspace(0, config.ENV_SIZE, 5))
        ax.set_yticks(np.linspace(0, config.ENV_SIZE, 5))
        ax.tick_params(direction='out', length=3)
        
        # 网格线
        ax.grid(True, linestyle=':', alpha=0.4, color='#CCCCCC', linewidth=0.5)

    # --- 修正 Colorbar 布局 ---
    # 调整子图布局，为右侧的 colorbar 留出空间
    plt.subplots_adjust(left=0.08, bottom=0.15, right=0.85, top=0.88, wspace=0.3)
    
    # 在图的右侧创建colorbar
    cbar_ax = fig.add_axes([0.87, 0.15, 0.02, 0.73])  # [left, bottom, width, height]
    cbar = fig.colorbar(im, cax=cbar_ax)
    cbar.set_label('Activation', rotation=270, labelpad=15, fontsize=9)
    cbar.ax.tick_params(labelsize=8)

    # --- 保存为 PDF 矢量格式 ---
    # 生成符合期刊要求的文件名
    cell_type_clean = result.cell_type.lower().replace(" ", "_").replace("(", "").replace(")", "")
    filename = f'neuron_{neuron_idx:03d}_{cell_type_clean}.pdf'
    save_path = os.path.join(output_dir, filename)
    
    # 保存为高质量PDF
    plt.savefig(save_path, format='pdf', bbox_inches='tight', 
                dpi=300, facecolor='white', edgecolor='none')
    plt.close(fig)  # 释放内存

def main():
    parser = argparse.ArgumentParser(description="Visualize Social Place Cells for Nature-style publication.")
    parser.add_argument('--model_path', type=str, required=True, help='Path to the trained best_model.pth file.')
    parser.add_argument('--output_dir', type=str, default='social_cell_visualizations_nature', help='Directory to save visualizations.')
    parser.add_argument('--workers', type=int, default=max(1, mp.cpu_count() - 2), help='Number of parallel workers for PLOTTING.')
    args = parser.parse_args()

    start_time = time.time()
    os.makedirs(args.output_dir, exist_ok=True)
    config = Config()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    print("Loading model...")
    place_cells = PlaceCellEnsemble(n_cells=config.PLACE_CELLS_N, scale=config.PLACE_CELLS_SCALE, pos_min=0, pos_max=config.ENV_SIZE, seed=config.SEED)
    hd_cells = HeadDirectionCellEnsemble(n_cells=config.HD_CELLS_N, concentration=config.HD_CELLS_CONCENTRATION, seed=config.SEED)
    model_config = {'hidden_size': config.HIDDEN_SIZE, 'bottleneck_size': config.LATENT_DIM, 'dropout_rate': config.DROPOUT_RATE}
    model = SocialGridCellNetwork(place_cells, hd_cells, model_config)
    
    # 加载模型权重
    state_dict = torch.load(args.model_path, map_location=device, weights_only=False)
    if all(key.startswith('module.') for key in state_dict.keys()):
        state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}
    model.load_state_dict(state_dict)
    model.to(device)
    model.eval()

    num_neurons = config.LATENT_DIM
    trajectory_gen = TrajectoryGenerator(config)
    all_results = {i: NeuronAnalysisResult(neuron_idx=i) for i in range(num_neurons)}

    print(f"Analyzing all {num_neurons} neurons using GPU batching...")
    with torch.no_grad():
        for condition in tqdm(range(1, 5), desc="Processing Conditions"):
            trajs = trajectory_gen.get_trajectories_for_condition(condition, num_reps=20)
            
            # 准备输入数据
            self_vel_input = torch.cat([torch.from_numpy(trajs['self_vel']).float(), torch.from_numpy(trajs['self_ang_vel']).float().unsqueeze(-1)], dim=-1).to(device)
            self_init_pos = torch.from_numpy(trajs['self_pos'][:, 0, :]).float().to(device)
            self_init_hd = torch.from_numpy(np.arctan2(trajs['self_vel'][:, 0, 1], trajs['self_vel'][:, 0, 0])).float().to(device)
            peer_vel_input = torch.cat([torch.from_numpy(trajs['peer_vel']).float(), torch.from_numpy(trajs['peer_ang_vel']).float().unsqueeze(-1)], dim=-1).to(device)
            peer_init_pos = torch.from_numpy(trajs['peer_pos'][:, 0, :]).float().to(device)
            peer_init_hd = torch.from_numpy(np.arctan2(trajs['peer_vel'][:, 0, 1], trajs['peer_vel'][:, 0, 0])).float().to(device)

            # 前向传播
            outputs = model(self_vel_input, self_init_pos, self_init_hd, peer_vel_input, peer_init_pos, peer_init_hd)
            all_activations = outputs['bottleneck_self'].cpu().numpy()

            # 存储每个神经元的激活值
            for i in range(num_neurons):
                neuron_activations = all_activations[:, :, i]
                all_results[i].condition_data[condition] = ConditionAnalysis(
                    peak_activation=np.max(neuron_activations),
                    activations=neuron_activations,
                    positions_to_plot={'self': trajs['self_pos'], 'peer': trajs['peer_pos']}
                )

    print("Classifying neurons...")
    # 轨迹映射表
    trajectory_map = {
        "Pure SPC": {1: "self", 2: "peer", 3: "peer", 4: "peer"},
        "Special SPC": {1: "self", 2: "peer", 3: "self", 4: "peer"},
        "Pure Place Cell": {1: "self", 2: "peer", 3: "self", 4: "peer"},
        "Mixed Response": {1: "self", 2: "peer", 3: "self", 4: "peer"},
        "Other": {1: "self", 2: "peer", 3: "self", 4: "peer"},
        "Other (Active when static)": {1: "self", 2: "peer", 3: "self", 4: "peer"}
    }
    
    categorized_cells = defaultdict(list)
    for i in range(num_neurons):
        res = all_results[i]
        peak_results = {cond: data.peak_activation for cond, data in res.condition_data.items()}
        res.cell_type = CellTypeDetector.classify(peak_results)
        
        # 设置要绘制的轨迹类型
        for cond_num, data in res.condition_data.items():
            plot_key = trajectory_map[res.cell_type][cond_num]
            data.trajectory_key = plot_key
            data.positions_to_plot = data.positions_to_plot[plot_key]

        categorized_cells[res.cell_type].append(res)

    print("\n--- Analysis Complete ---")
    for cell_type, results in categorized_cells.items():
        print(f"Found {len(results)} neurons of type: {cell_type}")

    print("\nGenerating Nature-style visualizations for interesting cells...")
    tasks = []
    for cell_type, results in categorized_cells.items():
        if "Other" not in cell_type:  # 只可视化有趣的细胞类型
            for result in results:
                tasks.append((result, config.to_dict(), args.output_dir))
    
    if tasks:
        print(f"Creating {len(tasks)} publication-quality figures...")
        with mp.Pool(processes=args.workers) as pool:
            list(tqdm(pool.imap_unordered(visualize_neuron, tasks), total=len(tasks), desc="Saving PDFs"))

    total_time = time.time() - start_time
    print(f"\nAll Nature-style visualizations saved in '{args.output_dir}'")
    print(f"Files are in PDF format for publication use.")
    print(f"Total execution time: {total_time:.2f} seconds.")

if __name__ == '__main__':
    try:
        mp.set_start_method('spawn', force=True)
    except RuntimeError:
        pass
    main()