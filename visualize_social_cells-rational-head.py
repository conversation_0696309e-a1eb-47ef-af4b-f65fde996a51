# visualize_social_cells-rational-head.py
import torch
import numpy as np
import matplotlib.pyplot as plt
import os
import argparse
from tqdm import tqdm
from scipy.ndimage import gaussian_filter
import multiprocessing as mp
from dataclasses import dataclass, field
from typing import Dict, Tuple
import time
from collections import defaultdict

# --- 正常从您的项目导入模块 ---
from config import Config
from models.social_grid_cell import SocialGridCellNetwork
from models.place_hd_cells import PlaceCellEnsemble, HeadDirectionCellEnsemble


# --- 数据结构定义 ---
@dataclass
class ConditionAnalysis:
    peak_activation: float = 0.0
    activations: np.ndarray = field(default_factory=lambda: np.array([]))
    positions_to_plot: np.ndarray = field(default_factory=lambda: np.array([]))
    trajectory_key: str = "self"

@dataclass
class NeuronAnalysisResult:
    neuron_idx: int
    cell_type: str = "Unclassified"
    neuron_group: str = "Bottleneck" # 新增字段，用于区分神经元来源
    condition_data: Dict[int, ConditionAnalysis] = field(default_factory=dict)

# --- 细胞分类器 ---
class CellTypeDetector:
    @staticmethod
    def classify(peak_results: Dict[int, float]) -> str:
        c1, c2, c3, c4 = peak_results.get(1,0), peak_results.get(2,0), peak_results.get(3,0), peak_results.get(4,0)
        high_thresh, low_thresh = 0.1, 0.05
        is_responsive_to_self = c1 > high_thresh
        is_responsive_to_peer = c2 > high_thresh
        is_silent_when_static = c4 < low_thresh
        if not is_silent_when_static: return "Other (Active when static)"
        if is_responsive_to_self and is_responsive_to_peer: return "Special SPC"
        elif is_responsive_to_self: return "Pure Place Cell" if c3 > high_thresh else "Mixed Response"
        elif is_responsive_to_peer: return "Pure SPC" if c3 > high_thresh else "Mixed Response"
        else: return "Other"

# --- 轨迹生成器 ---
class TrajectoryGenerator:
    def __init__(self, config: Config):
        self.maze_size = config.ENV_SIZE; self.seq_len = config.SEQUENCE_LENGTH
        self.start_S = np.array([self.maze_size*0.5, self.maze_size*0.2])
        self.end_A = np.array([self.maze_size*0.2, self.maze_size*0.8])
        self.end_B = np.array([self.maze_size*0.8, self.maze_size*0.8])
    def _generate_path(self, start, end, moving):
        if not moving:
            return np.tile(start,(self.seq_len,1)), np.zeros((self.seq_len,2)), np.zeros(self.seq_len)
        base_pos = np.array([start + (t/max(1,self.seq_len-1))*(end-start) for t in range(self.seq_len)])
        direction = (end-start)/(np.linalg.norm(end-start)+1e-6); perp = np.array([-direction[1],direction[0]])
        amp, freq, phase = np.random.uniform(0.5,1.5), np.random.uniform(1.5,2.5), np.random.uniform(0,np.pi)
        t = np.linspace(0,1,self.seq_len); sine_offset = amp*np.sin(2*np.pi*freq*t+phase)
        pos = np.clip(base_pos + sine_offset[:,np.newaxis]*perp[np.newaxis,:], 0.5, self.maze_size-0.5)
        vel = np.diff(pos,axis=0,prepend=pos[0:1]); angles = np.arctan2(vel[:,1],vel[:,0])
        ang_vel = np.diff(np.unwrap(angles),prepend=angles[0:1]); return pos, vel, ang_vel
    def get_trajectories_for_condition(self, cond, num_reps=20):
        s_pos, s_vel, s_ang, p_pos, p_vel, p_ang = [],[],[],[],[],[]
        self_m, peer_m = {1:(True,False),2:(False,True),3:(True,True),4:(False,False)}[cond]
        for i in range(num_reps):
            target = self.end_A if i%2==0 else self.end_B; static = self.start_S
            s_start, s_end = (self.start_S,target) if self_m else (static,static)
            p_start, p_end = (self.start_S,target) if peer_m else (static,static)
            _s_pos, _s_vel, _s_ang = self._generate_path(s_start,s_end,self_m)
            _p_pos, _p_vel, _p_ang = self._generate_path(p_start,p_end,peer_m)
            s_pos.append(_s_pos);s_vel.append(_s_vel);s_ang.append(_s_ang)
            p_pos.append(_p_pos);p_vel.append(_p_vel);p_ang.append(_p_ang)
        return {'self_pos':np.array(s_pos),'self_vel':np.array(s_vel),'self_ang_vel':np.array(s_ang),
                'peer_pos':np.array(p_pos),'peer_vel':np.array(p_vel),'peer_ang_vel':np.array(p_ang)}

# --- 期刊风格可视化函数 ---
def visualize_neuron(args_tuple):
    result, config_dict, output_dir = args_tuple
    config = Config.from_dict(config_dict)
    plt.style.use('default')
    plt.rcParams.update({
        'font.family': 'sans-serif', 'font.sans-serif': 'Arial', 'font.size': 10,
        'axes.labelsize': 12, 'xtick.labelsize': 10, 'ytick.labelsize': 10,
        'legend.fontsize': 8, 'figure.titlesize': 16, 'axes.titlepad': 10,
    })
    fig, axes = plt.subplots(1, 4, figsize=(18, 5))
    fig.suptitle(f'{result.neuron_group} Neuron {result.neuron_idx} - {result.cell_type}', fontweight='bold')
    peak_acts = [d.peak_activation for d in result.condition_data.values() if d.peak_activation > 0]
    vmax = max(0.2, max(peak_acts)) if peak_acts else 0.2
    for i, condition in enumerate([1, 2, 3, 4]):
        ax = axes[i]; data = result.condition_data[condition]
        grid_size = 50; heatmap, counts = np.zeros((grid_size,grid_size)), np.zeros((grid_size,grid_size))
        for traj_idx in range(data.positions_to_plot.shape[0]):
            for step_idx in range(data.positions_to_plot.shape[1]):
                pos, act = data.positions_to_plot[traj_idx,step_idx], data.activations[traj_idx,step_idx]
                gx, gy = int(np.clip((pos[0]/config.ENV_SIZE)*grid_size,0,grid_size-1)), int(np.clip((pos[1]/config.ENV_SIZE)*grid_size,0,grid_size-1))
                heatmap[gy,gx] += act; counts[gy,gx] += 1
        with np.errstate(divide='ignore',invalid='ignore'): avg_heatmap = np.nan_to_num(heatmap/counts)
        smoothed_heatmap = gaussian_filter(avg_heatmap, sigma=1.5)
        im = ax.imshow(smoothed_heatmap, cmap='viridis', origin='lower', extent=[0,config.ENV_SIZE,0,config.ENV_SIZE], vmin=0.0, vmax=vmax)
        for traj_idx in range(min(3, data.positions_to_plot.shape[0])):
            path = data.positions_to_plot[traj_idx]
            ax.plot(path[:,0], path[:,1], color='grey', alpha=0.7, linewidth=1.0)
            ax.scatter(path[0,0], path[0,1], color='#0072B2', s=25, zorder=3, label='Start' if traj_idx==0 else "")
            ax.scatter(path[-1,0], path[-1,1], color='#D55E00', s=25, zorder=3, marker='X', label='End' if traj_idx==0 else "")
        title_map = {1:"Self Moving, Peer Static",2:"Peer Moving, Self Static",3:"Both Moving",4:"Both Static"}
        ax.set_title(f'Cond {condition}: {title_map[condition]}\n(Plotting {data.trajectory_key.title()}) Peak: {data.peak_activation:.3f}', fontsize=10)
        ax.set_xlabel('X Position'); ax.set_xlim(0,config.ENV_SIZE); ax.set_ylim(0,config.ENV_SIZE)
        ax.set_aspect('equal',adjustable='box'); ax.grid(True,linestyle=':',alpha=0.5,color='lightgray')
        if i==0: ax.set_ylabel('Y Position'); ax.legend()
    fig.subplots_adjust(right=0.88, wspace=0.4)
    cbar_ax = fig.add_axes([0.9, 0.15, 0.02, 0.7])
    fig.colorbar(im, cax=cbar_ax).set_label('Neuronal Activation', rotation=270, labelpad=15)
    filename = f'{result.neuron_group.lower()}_neuron_{result.neuron_idx:03d}_{result.cell_type.lower().replace(" ","_")}.pdf'
    plt.savefig(os.path.join(output_dir, filename), format='pdf', bbox_inches='tight', dpi=300)
    plt.close(fig)

def main():
    parser = argparse.ArgumentParser(description="Visualize Bottleneck and Relational neurons from a trained model.")
    parser.add_argument('--model_path', type=str, required=True, help='Path to the trained model file (.pth).')
    parser.add_argument('--output_dir', type=str, default='social_cell_visualizations_full', help='Directory to save visualizations.')
    parser.add_argument('--workers', type=int, default=max(1, mp.cpu_count() - 2), help='Number of parallel workers for plotting.')
    args = parser.parse_args()

    start_time = time.time(); os.makedirs(args.output_dir, exist_ok=True)
    config_obj = Config(); config_dict = config_obj.to_dict()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu'); print(f"Using device: {device}")

    print("Loading model and architecture from your project files...")
    place_cells = PlaceCellEnsemble(n_cells=config_obj.PLACE_CELLS_N, scale=config_obj.PLACE_CELLS_SCALE, pos_min=0, pos_max=config_obj.ENV_SIZE, seed=config_obj.SEED)
    hd_cells = HeadDirectionCellEnsemble(n_cells=config_obj.HD_CELLS_N, concentration=config_obj.HD_CELLS_CONCENTRATION, seed=config_obj.SEED)

    # Create proper model config dictionary with required keys
    model_config = {
        'HIDDEN_SIZE': config_obj.HIDDEN_SIZE,
        'LATENT_DIM': config_obj.LATENT_DIM,
        'dropout_rate': config_obj.DROPOUT_RATE,
        'ego_token_size': getattr(config_obj, 'ego_token_size', 4)
    }
    model = SocialGridCellNetwork(place_cells, hd_cells, model_config)
    
    print(f"Loading weights from: {args.model_path}")
    state_dict = torch.load(args.model_path, map_location=device)
    if all(key.startswith('module.') for key in state_dict.keys()):
        state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}
    
    model.load_state_dict(state_dict, strict=True)
    model.to(device); model.eval()

    num_bottleneck_neurons = model.bottleneck_size
    # The relational head has a hidden layer with 64 neurons (see model definition)
    num_relational_neurons = 64
    print(f"Found {num_bottleneck_neurons} bottleneck neurons and {num_relational_neurons} relational neurons to analyze.")

    bottleneck_results = {i: NeuronAnalysisResult(neuron_idx=i, neuron_group='Bottleneck') for i in range(num_bottleneck_neurons)}
    relational_results = {i: NeuronAnalysisResult(neuron_idx=i, neuron_group='Relational', cell_type='Relational') for i in range(num_relational_neurons)}
    
    trajectory_gen = TrajectoryGenerator(config_obj)
    print("Analyzing neuron activations...")
    with torch.no_grad():
        for condition in tqdm(range(1, 5), desc="Processing Conditions"):
            trajs = trajectory_gen.get_trajectories_for_condition(condition)
            # Prepare 3D velocity input: [vx, vy, angular_velocity]
            self_vel_3d = torch.cat([
                torch.from_numpy(trajs['self_vel']).float(),
                torch.from_numpy(trajs['self_ang_vel']).float().unsqueeze(-1)
            ], dim=-1).to(device)

            peer_vel_3d = torch.cat([
                torch.from_numpy(trajs['peer_vel']).float(),
                torch.from_numpy(trajs['peer_ang_vel']).float().unsqueeze(-1)
            ], dim=-1).to(device)

            inputs = {
                'self_vel': self_vel_3d,
                'self_init_pos': torch.from_numpy(trajs['self_pos'][:,0,:]).float().to(device),
                'self_init_hd': torch.from_numpy(np.arctan2(trajs['self_vel'][:,0,1], trajs['self_vel'][:,0,0])).float().to(device),
                'peer_vel': peer_vel_3d,
                'peer_init_pos': torch.from_numpy(trajs['peer_pos'][:,0,:]).float().to(device),
                'peer_init_hd': torch.from_numpy(np.arctan2(trajs['peer_vel'][:,0,1], trajs['peer_vel'][:,0,0])).float().to(device),
            }
            # 期望模型返回包含 'bottleneck_activations' 和 'relational_activations' 的字典
            outputs = model(**inputs)
            
            groups = {
                'bottleneck': (outputs['bottleneck_self'].cpu().numpy(), bottleneck_results),
                'relational': (outputs['relational_activations'].cpu().numpy(), relational_results)
            }
            for name, (activations, results_dict) in groups.items():
                for i in range(activations.shape[-1]):
                    neuron_acts = activations[:,:,i]
                    results_dict[i].condition_data[condition] = ConditionAnalysis(
                        peak_activation=np.max(neuron_acts),
                        activations=neuron_acts,
                        positions_to_plot={'self': trajs['self_pos'], 'peer': trajs['peer_pos']}
                    )

    print("Classifying bottleneck neurons...")
    trajectory_map = {
        "Pure SPC":{1:"self",2:"peer",3:"peer",4:"peer"}, "Special SPC":{1:"self",2:"peer",3:"self",4:"peer"},
        "Pure Place Cell":{1:"self",2:"peer",3:"self",4:"peer"}, "Mixed Response":{1:"self",2:"peer",3:"self",4:"peer"},
        "Other":{1:"self",2:"peer",3:"self",4:"peer"}, "Other (Active when static)":{1:"self",2:"peer",3:"self",4:"peer"},
        "Relational":{1:"self",2:"peer",3:"self",4:"peer"}
    }
    
    all_results_list = list(bottleneck_results.values()) + list(relational_results.values())
    for res in all_results_list:
        if res.neuron_group == 'Bottleneck':
            peak_res = {cond: data.peak_activation for cond, data in res.condition_data.items()}
            res.cell_type = CellTypeDetector.classify(peak_res)
        
        for cond_num, data in res.condition_data.items():
            plot_key = trajectory_map[res.cell_type][cond_num]
            data.trajectory_key = plot_key
            data.positions_to_plot = data.positions_to_plot[plot_key]

    print("\n--- Analysis Complete ---")
    tasks = []
    for res in all_results_list:
        if "Other" not in res.cell_type:
            tasks.append((res, config_obj.to_dict(), args.output_dir))
    
    if tasks:
        print(f"Generating {len(tasks)} visualizations...")
        with mp.Pool(processes=args.workers) as pool:
            list(tqdm(pool.imap_unordered(visualize_neuron, tasks), total=len(tasks), desc="Saving Plots"))

    print(f"\nAll visualizations saved in '{args.output_dir}'")
    print(f"Total execution time: {time.time() - start_time:.2f} seconds.")

if __name__ == '__main__':
    try: mp.set_start_method('spawn', force=True)
    except RuntimeError: pass
    main()
