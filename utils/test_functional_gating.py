#!/usr/bin/env python3
"""
测试脚本：验证functional_gating_viz.py的功能
"""

import os
import sys
import torch
import numpy as np

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 全局导入，这样所有测试函数都能使用
try:
    from config import Config
    from models.social_grid_cell import SocialGridCellNetwork
    from models.place_hd_cells import PlaceCellEnsemble, HeadDirectionCellEnsemble
    from utils.functional_gating_viz import (
        TrajectoryGenerator,
        ProximityData,
        create_proximity_heatmap,
        analyze_proximity_gating,
        find_best_relational_neurons,
        visualize_functional_gating,
        visualize_individual_neuron_gating
    )
    IMPORTS_SUCCESSFUL = True
except ImportError as e:
    print(f"Import error: {e}")
    IMPORTS_SUCCESSFUL = False

def test_imports():
    """测试所有必要的导入"""
    print("Testing imports...")
    if IMPORTS_SUCCESSFUL:
        print("✓ All imports successful")
        return True
    else:
        print("✗ Import error occurred")
        return False

def test_config():
    """测试配置类"""
    print("Testing Config class...")
    if not IMPORTS_SUCCESSFUL:
        print("✗ Config test failed: imports not successful")
        return False
    try:
        config = Config()
        config_dict = config.to_dict()
        config_restored = Config.from_dict(config_dict)

        # 检查关键属性
        assert hasattr(config, 'ENV_SIZE')
        assert hasattr(config, 'HIDDEN_SIZE')
        assert hasattr(config, 'LATENT_DIM')
        assert hasattr(config, 'SEQUENCE_LENGTH')

        print("✓ Config class working correctly")
        return True
    except Exception as e:
        print(f"✗ Config test failed: {e}")
        return False

def test_trajectory_generator():
    """测试轨迹生成器"""
    print("Testing TrajectoryGenerator...")
    if not IMPORTS_SUCCESSFUL:
        print("✗ TrajectoryGenerator test failed: imports not successful")
        return False
    try:
        config = Config()
        traj_gen = TrajectoryGenerator(config)

        # 测试轨迹生成
        trajs = traj_gen.get_trajectories_for_condition(3, num_reps=5)

        # 检查输出格式
        assert 'self_pos' in trajs
        assert 'peer_pos' in trajs
        assert trajs['self_pos'].shape == (5, config.SEQUENCE_LENGTH, 2)
        assert trajs['peer_pos'].shape == (5, config.SEQUENCE_LENGTH, 2)

        print("✓ TrajectoryGenerator working correctly")
        return True
    except Exception as e:
        print(f"✗ TrajectoryGenerator test failed: {e}")
        return False

def test_model_creation():
    """测试模型创建"""
    print("Testing model creation...")
    if not IMPORTS_SUCCESSFUL:
        print("✗ Model creation test failed: imports not successful")
        return False
    try:
        config = Config()

        place_cells = PlaceCellEnsemble(
            n_cells=config.PLACE_CELLS_N,
            scale=config.PLACE_CELLS_SCALE,
            pos_min=0, pos_max=config.ENV_SIZE,
            seed=config.SEED
        )
        hd_cells = HeadDirectionCellEnsemble(
            n_cells=config.HD_CELLS_N,
            concentration=config.HD_CELLS_CONCENTRATION,
            seed=config.SEED
        )

        model_config = {
            'HIDDEN_SIZE': config.HIDDEN_SIZE,
            'LATENT_DIM': config.LATENT_DIM,
            'dropout_rate': config.DROPOUT_RATE,
            'ego_token_size': getattr(config, 'ego_token_size', 4)
        }
        model = SocialGridCellNetwork(place_cells, hd_cells, model_config)

        # 检查模型结构
        assert hasattr(model, 'relational_head')
        assert len(model.relational_head) == 3  # Linear -> ReLU -> Linear

        print("✓ Model creation successful")
        return True
    except Exception as e:
        print(f"✗ Model creation test failed: {e}")
        return False

def test_proximity_heatmap():
    """测试邻近度热图创建"""
    print("Testing proximity heatmap creation...")
    if not IMPORTS_SUCCESSFUL:
        print("✗ Proximity heatmap test failed: imports not successful")
        return False
    try:
        # 创建测试数据
        np.random.seed(42)
        n_points = 100
        relative_positions = np.random.randn(n_points, 2) * 3  # 相对位置
        activations = np.exp(-np.linalg.norm(relative_positions, axis=1) / 2)  # 距离越近激活越强

        # 创建热图
        heatmap = create_proximity_heatmap(relative_positions, activations,
                                         grid_size=30, max_distance=6.0, sigma=1.0)

        # 检查输出
        assert heatmap.shape == (30, 30)
        assert np.all(heatmap >= 0)
        assert np.any(heatmap > 0)  # 应该有一些激活

        print("✓ Proximity heatmap creation successful")
        return True
    except Exception as e:
        print(f"✗ Proximity heatmap test failed: {e}")
        return False

def test_data_structures():
    """测试数据结构"""
    print("Testing data structures...")
    if not IMPORTS_SUCCESSFUL:
        print("✗ Data structures test failed: imports not successful")
        return False
    try:
        # 测试ProximityData
        data = ProximityData()
        data.distances = np.array([1.0, 2.0, 3.0])
        data.activations = np.array([0.8, 0.5, 0.2])

        assert len(data.distances) == 3
        assert len(data.activations) == 3

        print("✓ Data structures working correctly")
        return True
    except Exception as e:
        print(f"✗ Data structures test failed: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("=" * 50)
    print("Running Functional Gating Visualization Tests")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config,
        test_trajectory_generator,
        test_model_creation,
        test_proximity_heatmap,
        test_data_structures
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The functional gating visualization tool is ready to use.")
        print("\nNext steps:")
        print("1. Train a model or use an existing trained model")
        print("2. Run: python utils/functional_gating_viz.py --model_path your_model.pth --find_best_neurons")
    else:
        print("❌ Some tests failed. Please check the error messages above.")
    
    print("=" * 50)
    
    return passed == total

if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)
