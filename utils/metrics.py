# bio_navigation/utils/metrics.py

import torch
import numpy as np
from sklearn.feature_selection import mutual_info_regression
from sklearn.preprocessing import KBinsDiscretizer
from typing import Dict, List, Tuple, Optional

def compute_navigation_metrics(outputs, targets):
    """计算导航性能指标"""
    metrics = {}
    
    # 确保所有张量都在CPU上
    def to_numpy(tensor):
        return tensor.detach().cpu().numpy()
    
    pred_positions = to_numpy(outputs['position'])  # 已经是在15x15范围内
    true_positions = to_numpy(targets['positions'])
    pred_rotations = to_numpy(outputs['rotation'])
    true_rotations = to_numpy(targets['angles'])
    
    # 计算位置误差（直接计算欧氏距离，因为输入已经是在15x15网格上了）
    position_errors = np.sqrt(np.sum(
        (pred_positions - true_positions) ** 2,
        axis=-1
    ))
    metrics['position_error'] = float(np.mean(position_errors))  # 转换为Python float
    metrics['max_position_error'] = float(np.max(position_errors))
    
    # 计算旋转角度误差（弧度转度数）
    rotation_errors = np.abs(pred_rotations - true_rotations)
    rotation_errors = np.minimum(rotation_errors, 2 * np.pi - rotation_errors)
    metrics['rotation_error_deg'] = float(np.mean(np.degrees(rotation_errors)))
    
    # 记忆激活相关指标
    if 'memory_activation' in outputs:
        memory_activation = to_numpy(outputs['memory_activation'])
        
        # 计算记忆激活的稀疏性（非零激活的比例）
        sparsity = np.mean(np.sum(memory_activation > 0.01, axis=-1) / memory_activation.shape[-1])
        metrics['memory_sparsity'] = float(sparsity)
        
        # 计算top-k记忆激活的平均值
        top_k = 3
        top_k_activations = np.mean(np.sort(memory_activation, axis=-1)[..., -top_k:])
        metrics['top_k_activation'] = float(top_k_activations)
        
        # 输出一些调试信息
        print("\nDebug Info:")
        print(f"Position range - Pred: [{np.min(pred_positions):.3f}, {np.max(pred_positions):.3f}], "
              f"True: [{np.min(true_positions):.3f}, {np.max(true_positions):.3f}]")
        print(f"Rotation range - Pred: [{np.min(pred_rotations):.3f}, {np.max(pred_rotations):.3f}], "
              f"True: [{np.min(true_rotations):.3f}, {np.max(true_rotations):.3f}]")
        print(f"Mean position error: {metrics['position_error']:.3f}")
        print(f"Max position error: {metrics['max_position_error']:.3f}")
        print(f"Mean rotation error: {metrics['rotation_error_deg']:.2f}°")

    # 添加Grid Cell相关指标
    # Grid Cell相关指标
    if 'grid_code' in outputs:
        print("\nProcessing grid cell metrics...")  # 添加调试信息
        grid_code = to_numpy(outputs['grid_code'])
        print("Grid code shape:", grid_code.shape)

        # 计算空间自相关
        def compute_spatial_autocorr(activations):
            B, S, D = activations.shape
            activations = activations.reshape(B * S, D)
            autocorr = np.dot(activations, activations.T) / D
            return autocorr

        autocorr = compute_spatial_autocorr(grid_code)
        print("Autocorrelation shape:", autocorr.shape)

        # 计算网格单元的空间选择性
        spatial_selectivity = np.mean(np.max(grid_code, axis=2) - np.mean(grid_code, axis=2))
        metrics['spatial_selectivity'] = float(spatial_selectivity)
        print(f"Spatial selectivity: {spatial_selectivity:.4f}")

        # 计算网格字段稳定性
        if grid_code.shape[1] > 1:  # 序列长度大于1
            temporal_stability = 1.0 - np.mean(np.abs(grid_code[:, 1:] - grid_code[:, :-1]))
            metrics['grid_stability'] = float(temporal_stability)
            print(f"Grid stability: {temporal_stability:.4f}")

        # 计算grid code的统计特征
        print(f"Grid code stats:")
        print(f"  Mean: {np.mean(grid_code):.4f}")
        print(f"  Std: {np.std(grid_code):.4f}")
        print(f"  Min: {np.min(grid_code):.4f}")
        print(f"  Max: {np.max(grid_code):.4f}")
    else:
        print("\nWarning: 'grid_code' not found in outputs")

    return metrics


def print_metrics_summary(metrics, split='train'):
    """格式化打印指标"""
    print(f"\n{split.capitalize()} Metrics:")
    # 基础指标
    print(f"Position Error (grid units): {metrics['position_error']:.3f}")
    print(f"Max Position Error (grid units): {metrics['max_position_error']:.3f}")
    print(f"Rotation Error: {metrics['rotation_error_deg']:.2f}°")

    # 记忆相关指标
    if 'memory_sparsity' in metrics:
        print(f"Memory Sparsity: {metrics['memory_sparsity']:.4f}")
        print(f"Top-k Activation: {metrics['top_k_activation']:.4f}")

    # Grid Cell指标
    if 'spatial_selectivity' in metrics:
        print("\nGrid Cell Metrics:")
        print(f"Spatial Selectivity: {metrics['spatial_selectivity']:.4f}")
        if 'grid_stability' in metrics:
            print(f"Grid Stability: {metrics['grid_stability']:.4f}")
        # 只在计算了网格间距时打印
        if 'grid_spacing' in metrics:
            print(f"Grid Spacing: {metrics['grid_spacing']:.4f}")

def evaluate_navigation_batch(model, batch, device, memory_bank=None):
    """评估单个批次的导航性能"""
    model.eval()
    with torch.no_grad():
        # 准备数据
        batch = {k: v.to(device) if isinstance(v, torch.Tensor) else v 
                for k, v in batch.items()}
        
        # 模型预测
        outputs = model(batch)
        
        # 计算指标
        metrics = compute_navigation_metrics(outputs, batch)
        
        # 如果有记忆库，查找最佳匹配
        if memory_bank is not None:
            memory_matches = find_best_matches(
                outputs['place_code'],
                memory_bank['features'],
                memory_bank['metadata']
            )
            
            # 计算记忆匹配的准确度（如果有真实标签）
            if 'true_matches' in batch:
                match_accuracy = compute_memory_match_accuracy(
                    memory_matches,
                    batch['true_matches']
                )
                metrics['memory_match_accuracy'] = match_accuracy
        else:
            memory_matches = None
    
    return metrics, outputs, memory_matches

def compute_memory_match_accuracy(pred_matches, true_matches):
    """计算记忆匹配的准确度

    Args:
        pred_matches: 预测的匹配列表
        true_matches: 真实的匹配列表

    Returns:
        float: 匹配准确度
    """
    total_correct = 0
    total_samples = len(true_matches)

    for pred, true in zip(pred_matches, true_matches):
        # 检查预测的最佳匹配是否在真实匹配中
        pred_best = (pred[0]['dataset'], pred[0]['frame_id'])
        if pred_best in true:
            total_correct += 1

    return total_correct / total_samples if total_samples > 0 else 0.0


def compute_mutual_information(activations: np.ndarray, positions: np.ndarray,
                             n_bins: int = 10, random_state: int = 42) -> float:
    """
    计算神经元激活与位置之间的互信息

    Args:
        activations: 神经元激活值，形状为 (n_samples,)
        positions: 位置信息，形状为 (n_samples, 2) 或 (n_samples,) 如果是1D位置
        n_bins: 离散化的bin数量
        random_state: 随机种子

    Returns:
        float: 互信息值
    """
    if len(activations) == 0 or len(positions) == 0:
        return 0.0

    # 确保输入是1D数组
    activations = activations.flatten()

    # 如果positions是2D的，计算到原点的距离或使用第一个维度
    if positions.ndim == 2:
        if positions.shape[1] == 2:
            # 使用位置的欧氏距离作为1D特征
            positions = np.linalg.norm(positions, axis=1)
        else:
            positions = positions[:, 0]

    positions = positions.flatten()

    # 确保长度匹配
    min_len = min(len(activations), len(positions))
    activations = activations[:min_len]
    positions = positions[:min_len]

    if min_len < 2:
        return 0.0

    try:
        # 使用sklearn的互信息计算
        mi = mutual_info_regression(positions.reshape(-1, 1), activations,
                                   discrete_features=False, n_neighbors=3,
                                   random_state=random_state)[0]
        return float(mi)
    except Exception as e:
        print(f"Warning: Failed to compute mutual information: {e}")
        return 0.0


def compute_mutual_information_2d(activations: np.ndarray, positions: np.ndarray,
                                n_bins: int = 10, random_state: int = 42) -> float:
    """
    计算神经元激活与2D位置之间的互信息（使用离散化方法）

    Args:
        activations: 神经元激活值，形状为 (n_samples,)
        positions: 2D位置信息，形状为 (n_samples, 2)
        n_bins: 每个维度的离散化bin数量
        random_state: 随机种子

    Returns:
        float: 互信息值
    """
    if len(activations) == 0 or len(positions) == 0:
        return 0.0

    activations = activations.flatten()

    if positions.ndim != 2 or positions.shape[1] != 2:
        return compute_mutual_information(activations, positions, n_bins, random_state)

    # 确保长度匹配
    min_len = min(len(activations), len(positions))
    activations = activations[:min_len]
    positions = positions[:min_len]

    if min_len < 2:
        return 0.0

    try:
        # 将2D位置离散化为网格
        discretizer = KBinsDiscretizer(n_bins=n_bins, encode='ordinal', strategy='uniform')
        positions_discrete = discretizer.fit_transform(positions)

        # 将2D网格索引转换为1D索引
        position_indices = positions_discrete[:, 0] * n_bins + positions_discrete[:, 1]

        # 计算互信息
        mi = mutual_info_regression(position_indices.reshape(-1, 1), activations,
                                   discrete_features=True, random_state=random_state)[0]
        return float(mi)
    except Exception as e:
        print(f"Warning: Failed to compute 2D mutual information: {e}")
        return 0.0