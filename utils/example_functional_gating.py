#!/usr/bin/env python3
"""
示例脚本：如何使用functional_gating_viz.py来创建功能性门控可视化

使用方法：
1. 基本使用（使用所有relational neurons的平均激活）：
   python utils/functional_gating_viz.py --model_path path/to/best_model.pth

2. 自动找到并可视化最佳的relational neurons：
   python utils/functional_gating_viz.py --model_path path/to/best_model.pth --find_best_neurons

3. 可视化特定的relational neuron：
   python utils/functional_gating_viz.py --model_path path/to/best_model.pth --specific_neuron 15

4. 自定义输出目录和轨迹数量：
   python utils/functional_gating_viz.py --model_path path/to/best_model.pth --output_dir my_viz --num_reps 200
"""

import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from utils.functional_gating_viz import main

if __name__ == '__main__':
    print("Functional Gating Visualization Tool")
    print("====================================")
    print()
    print("This tool creates Figure 4c: Functional Gating Driven by Inter-Agent Proximity")
    print()
    print("The visualization shows:")
    print("- Left panel: Heatmap of relational head activation vs. partner's relative position")
    print("- Right panel: Scatter plot of activation vs. inter-agent distance with trend line")
    print()
    print("Expected pattern: Higher activation when agents are closer together,")
    print("demonstrating proximity-driven functional gating in the relational processing.")
    print()
    
    # 调用主函数
    main()
