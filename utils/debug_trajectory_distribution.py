#!/usr/bin/env python3
"""
调试脚本：检查轨迹分布是否正确
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from config import Config
from utils.functional_gating_viz import TrajectoryGenerator

def plot_trajectory_distribution():
    """绘制轨迹分布图来验证partner位置是否随机分布"""
    config = Config()
    trajectory_gen = TrajectoryGenerator(config)
    
    # 生成轨迹
    print("Generating trajectories...")
    trajs = trajectory_gen.get_proximity_analysis_trajectories(num_reps=500)
    
    # 提取位置数据
    self_pos = trajs['self_pos']  # (500, seq_len, 2)
    peer_pos = trajs['peer_pos']  # (500, seq_len, 2)
    
    # 由于self和peer都是静止的，我们只需要看第一个时间步的位置
    self_positions = self_pos[:, 0, :]  # (500, 2)
    peer_positions = peer_pos[:, 0, :]  # (500, 2)
    
    # 计算相对位置
    relative_positions = peer_positions - self_positions  # (500, 2)
    
    # 创建图形
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 1. Self位置分布（应该都在中心）
    ax1 = axes[0, 0]
    ax1.scatter(self_positions[:, 0], self_positions[:, 1], alpha=0.6, s=10)
    ax1.set_title('Self Agent Positions\n(Should be centered)')
    ax1.set_xlabel('X Position (m)')
    ax1.set_ylabel('Y Position (m)')
    ax1.grid(True, alpha=0.3)
    ax1.set_aspect('equal')
    
    # 2. Peer位置分布（应该围绕self随机分布）
    ax2 = axes[0, 1]
    ax2.scatter(peer_positions[:, 0], peer_positions[:, 1], alpha=0.6, s=10, color='orange')
    ax2.scatter(self_positions[:, 0], self_positions[:, 1], alpha=0.8, s=20, color='blue', marker='*', label='Self')
    ax2.set_title('Peer Agent Positions\n(Should be around self)')
    ax2.set_xlabel('X Position (m)')
    ax2.set_ylabel('Y Position (m)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_aspect('equal')
    
    # 3. 相对位置分布（这是最重要的，应该是圆形均匀分布）
    ax3 = axes[1, 0]
    ax3.scatter(relative_positions[:, 0], relative_positions[:, 1], alpha=0.6, s=10, color='red')
    ax3.scatter(0, 0, s=50, color='blue', marker='*', label='Observer (Self)')
    
    # 添加距离圆圈作为参考
    for radius in [2, 4, 6]:
        circle = plt.Circle((0, 0), radius, fill=False, color='gray', 
                          linestyle='--', alpha=0.5, linewidth=1)
        ax3.add_patch(circle)
        ax3.text(radius*0.7, radius*0.7, f'{radius}m', color='gray', 
                fontsize=8, ha='center', va='center')
    
    ax3.set_title('Relative Positions (Peer relative to Self)\n(Should be circular uniform)')
    ax3.set_xlabel('Relative X Position (m)')
    ax3.set_ylabel('Relative Y Position (m)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    ax3.set_aspect('equal')
    ax3.set_xlim(-8, 8)
    ax3.set_ylim(-8, 8)
    
    # 4. 距离分布直方图
    ax4 = axes[1, 1]
    distances = np.linalg.norm(relative_positions, axis=1)
    ax4.hist(distances, bins=30, alpha=0.7, color='green', edgecolor='black')
    ax4.set_title('Distance Distribution\n(Should increase linearly for uniform circular)')
    ax4.set_xlabel('Distance (m)')
    ax4.set_ylabel('Frequency')
    ax4.grid(True, alpha=0.3)
    
    # 添加理论分布（对于均匀圆形分布，频率应该与半径成正比）
    r_theory = np.linspace(0, 7.5, 100)
    freq_theory = 2 * np.pi * r_theory * (len(distances) / (np.pi * 7.5**2)) * (7.5 / 30)  # 归一化
    ax4.plot(r_theory, freq_theory, 'r--', linewidth=2, label='Theoretical (uniform circular)')
    ax4.legend()
    
    plt.tight_layout()
    
    # 保存图形
    output_path = 'trajectory_distribution_debug.pdf'
    plt.savefig(output_path, format='pdf', bbox_inches='tight', dpi=300)
    print(f"Distribution plot saved to: {output_path}")
    
    # 打印统计信息
    print("\n=== Distribution Statistics ===")
    print(f"Number of trajectories: {len(self_positions)}")
    print(f"Self position std: X={np.std(self_positions[:, 0]):.6f}, Y={np.std(self_positions[:, 1]):.6f}")
    print(f"Relative position range: X=[{np.min(relative_positions[:, 0]):.2f}, {np.max(relative_positions[:, 0]):.2f}]")
    print(f"Relative position range: Y=[{np.min(relative_positions[:, 1]):.2f}, {np.max(relative_positions[:, 1]):.2f}]")
    print(f"Distance range: [{np.min(distances):.2f}, {np.max(distances):.2f}] meters")
    print(f"Mean distance: {np.mean(distances):.2f} meters")
    print(f"Expected mean distance for uniform circle (radius=7.5): {7.5 * 2/3:.2f} meters")
    
    # 检查是否真的是圆形均匀分布
    angles = np.arctan2(relative_positions[:, 1], relative_positions[:, 0])
    angle_uniformity = np.std(np.histogram(angles, bins=8)[0])
    print(f"Angle distribution uniformity (lower is better): {angle_uniformity:.2f}")
    
    plt.show()

if __name__ == '__main__':
    plot_trajectory_distribution()
