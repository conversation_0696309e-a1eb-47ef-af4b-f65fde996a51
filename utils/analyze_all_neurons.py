#!/usr/bin/env python3
"""
分析所有64个神经元的功能性门控效应
"""

import os
import sys
import numpy as np
import torch
from scipy.stats import pearsonr
import matplotlib.pyplot as plt

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from config import Config
from models.social_grid_cell import SocialGridCellNetwork
from models.place_hd_cells import PlaceCellEnsemble, HeadDirectionCellEnsemble
from utils.functional_gating_viz import TrajectoryGenerator

def analyze_all_neurons(model_path: str, num_reps: int = 300):
    """分析所有64个神经元的功能性门控效应"""
    
    # 加载模型
    config = Config()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    print("Loading model...")
    place_cells = PlaceCellEnsemble(
        n_cells=config.PLACE_CELLS_N, 
        scale=config.PLACE_CELLS_SCALE, 
        pos_min=0, pos_max=config.ENV_SIZE, 
        seed=config.SEED
    )
    hd_cells = HeadDirectionCellEnsemble(
        n_cells=config.HD_CELLS_N, 
        concentration=config.HD_CELLS_CONCENTRATION, 
        seed=config.SEED
    )
    
    model_config = {
        'HIDDEN_SIZE': config.HIDDEN_SIZE,
        'LATENT_DIM': config.LATENT_DIM,
        'dropout_rate': config.DROPOUT_RATE,
        'ego_token_size': getattr(config, 'ego_token_size', 4)
    }
    model = SocialGridCellNetwork(place_cells, hd_cells, model_config)
    
    # 加载模型权重
    state_dict = torch.load(model_path, map_location=device, weights_only=False)
    if all(key.startswith('module.') for key in state_dict.keys()):
        state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}
    model.load_state_dict(state_dict)
    model.to(device)
    model.eval()

    print(f"Model loaded. Relational head has {model.relational_head[0].out_features} neurons.")
    
    # 生成轨迹
    trajectory_gen = TrajectoryGenerator(config)
    print(f"Generating {num_reps} trajectories for analysis...")
    
    model.eval()
    with torch.no_grad():
        # 使用专门的邻近度分析轨迹生成方法
        trajs = trajectory_gen.get_proximity_analysis_trajectories(num_reps=num_reps)
        
        # 准备输入数据
        self_vel_input = torch.cat([
            torch.from_numpy(trajs['self_vel']).float(), 
            torch.from_numpy(trajs['self_ang_vel']).float().unsqueeze(-1)
        ], dim=-1).to(device)
        
        self_init_pos = torch.from_numpy(trajs['self_pos'][:, 0, :]).float().to(device)
        self_init_hd = torch.from_numpy(np.arctan2(trajs['self_vel'][:, 0, 1], trajs['self_vel'][:, 0, 0])).float().to(device)
        
        peer_vel_input = torch.cat([
            torch.from_numpy(trajs['peer_vel']).float(), 
            torch.from_numpy(trajs['peer_ang_vel']).float().unsqueeze(-1)
        ], dim=-1).to(device)
        
        peer_init_pos = torch.from_numpy(trajs['peer_pos'][:, 0, :]).float().to(device)
        peer_init_hd = torch.from_numpy(np.arctan2(trajs['peer_vel'][:, 0, 1], trajs['peer_vel'][:, 0, 0])).float().to(device)

        # 前向传播
        print("Running forward pass...")
        outputs = model(self_vel_input, self_init_pos, self_init_hd, peer_vel_input, peer_init_pos, peer_init_hd)
        relational_activations = outputs['relational_activations'].cpu().numpy()  # (batch, seq, 64)
        
        # 计算距离
        self_pos = trajs['self_pos']
        peer_pos = trajs['peer_pos']
        distances = np.linalg.norm(self_pos - peer_pos, axis=-1).flatten()
        
        print("Analyzing all neurons...")
        neuron_results = []
        
        # 评估每个神经元
        for neuron_idx in range(relational_activations.shape[-1]):
            activations = relational_activations[:, :, neuron_idx].flatten()
            
            # 计算统计指标
            max_activation = np.max(activations)
            mean_activation = np.mean(activations)
            std_activation = np.std(activations)
            
            # 计算与距离的相关性
            if std_activation > 1e-6:  # 避免常数激活
                correlation, p_value = pearsonr(distances, activations)
            else:
                correlation, p_value = np.nan, np.nan
            
            # 综合评分：激活强度 + 相关性强度
            if not np.isnan(correlation):
                score = mean_activation * 0.3 + abs(correlation) * 0.7
            else:
                score = np.nan
            
            neuron_results.append({
                'neuron_idx': neuron_idx,
                'max_activation': max_activation,
                'mean_activation': mean_activation,
                'std_activation': std_activation,
                'correlation': correlation,
                'p_value': p_value,
                'score': score,
                'is_active': std_activation > 1e-6
            })
    
    return neuron_results

def create_summary_report(neuron_results, output_dir: str):
    """创建分析总结报告"""
    
    # 按评分排序
    active_neurons = [r for r in neuron_results if r['is_active']]
    active_neurons.sort(key=lambda x: x['score'] if not np.isnan(x['score']) else -1, reverse=True)
    
    # 创建报告
    report_path = os.path.join(output_dir, "neuron_analysis_summary.md")
    
    with open(report_path, 'w') as f:
        f.write("# 🧠 所有神经元功能性门控分析报告\n\n")
        
        f.write("## 📊 总体统计\n\n")
        f.write(f"- 总神经元数量: {len(neuron_results)}\n")
        f.write(f"- 活跃神经元数量: {len(active_neurons)}\n")
        f.write(f"- 静默神经元数量: {len(neuron_results) - len(active_neurons)}\n\n")
        
        f.write("## 🏆 Top 10 最佳功能性门控神经元\n\n")
        f.write("| 排名 | 神经元 | 评分 | 最大激活 | 平均激活 | 相关系数 | p值 | 门控效应 |\n")
        f.write("|------|--------|------|----------|----------|----------|-----|----------|\n")
        
        for i, neuron in enumerate(active_neurons[:10]):
            if not np.isnan(neuron['score']):
                gating_effect = "强" if abs(neuron['correlation']) > 0.5 else "中" if abs(neuron['correlation']) > 0.2 else "弱"
                f.write(f"| {i+1} | {neuron['neuron_idx']} | {neuron['score']:.3f} | {neuron['max_activation']:.3f} | {neuron['mean_activation']:.3f} | {neuron['correlation']:.3f} | {neuron['p_value']:.2e} | {gating_effect} |\n")
        
        f.write("\n## 🔍 详细分类\n\n")
        
        # 强门控效应神经元
        strong_gating = [n for n in active_neurons if not np.isnan(n['correlation']) and abs(n['correlation']) > 0.5]
        f.write(f"### 强门控效应神经元 (|r| > 0.5): {len(strong_gating)}个\n")
        for neuron in strong_gating:
            f.write(f"- 神经元 {neuron['neuron_idx']}: r={neuron['correlation']:.3f}, 激活={neuron['mean_activation']:.3f}\n")
        
        # 中等门控效应神经元
        medium_gating = [n for n in active_neurons if not np.isnan(n['correlation']) and 0.2 < abs(n['correlation']) <= 0.5]
        f.write(f"\n### 中等门控效应神经元 (0.2 < |r| ≤ 0.5): {len(medium_gating)}个\n")
        for neuron in medium_gating:
            f.write(f"- 神经元 {neuron['neuron_idx']}: r={neuron['correlation']:.3f}, 激活={neuron['mean_activation']:.3f}\n")
        
        # 静默神经元
        silent_neurons = [r for r in neuron_results if not r['is_active']]
        f.write(f"\n### 静默神经元: {len(silent_neurons)}个\n")
        silent_list = [str(n['neuron_idx']) for n in silent_neurons]
        f.write(f"神经元索引: {', '.join(silent_list)}\n")
        
        f.write("\n## 🎯 推荐可视化\n\n")
        f.write("基于分析结果，推荐可视化以下神经元：\n\n")
        
        top_5 = active_neurons[:5]
        for i, neuron in enumerate(top_5):
            if not np.isnan(neuron['score']):
                f.write(f"{i+1}. **神经元 {neuron['neuron_idx']}**: 评分={neuron['score']:.3f}, 相关性={neuron['correlation']:.3f}\n")
                f.write(f"   ```bash\n")
                f.write(f"   python utils/functional_gating_viz.py --specific_neuron {neuron['neuron_idx']} --num_reps 400\n")
                f.write(f"   ```\n\n")
    
    print(f"Summary report saved to: {report_path}")
    return report_path

def main():
    model_path = "/data1/fangzr/Research/Memory_Maze_3D/Multi-com/maze-zero/grid-cell-dual/logs_social_grid_cells_ddp/20250802_230022/checkpoints/best_model.pth"
    output_dir = "functional_gating_all_units"
    
    print("🧠 分析所有64个神经元的功能性门控效应")
    print("=" * 60)
    
    # 分析所有神经元
    neuron_results = analyze_all_neurons(model_path, num_reps=300)
    
    # 创建总结报告
    create_summary_report(neuron_results, output_dir)
    
    print("\n✅ 分析完成！")
    print(f"📁 所有可视化文件位于: {output_dir}/all_neurons/")
    print(f"📄 分析报告位于: {output_dir}/neuron_analysis_summary.md")

if __name__ == '__main__':
    main()
