# 功能性门控可视化工具 (Functional Gating Visualization)

## 概述

这个工具实现了**图 4c: 由智能体间邻近度驱动的功能性门控 (Functional Gating Driven by Inter-Agent Proximity)** 的可视化。

## 功能特点

### 主要可视化内容

1. **左侧面板 - 激活热图**：
   - 显示relational_head中间隐藏层的平均激活值
   - X-Y轴代表伙伴智能体相对于观察者智能体的位置
   - 观察者固定在中心点(0,0)
   - 预期结果：当伙伴靠近观察者时出现"热点"区域，随距离增加激活强度减弱

2. **右侧面板 - 定量散点图**：
   - X轴：观察者与伙伴之间的真实物理距离（米）
   - Y轴：relational_head隐藏层的平均激活值
   - 每个点代表测试数据中的一个时间步
   - 包含多项式拟合曲线和相关性统计
   - 预期结果：显著的负相关关系，证明"功能性门控"效应

### 智能体检测功能

- **自动检测最佳神经元**：工具可以自动找到最适合展示的relational neurons
- **Special Social Place Cell检测**：特别关注rational-head中的special social place cell
- **激活强度和相关性评分**：综合考虑激活强度和与距离的相关性

## 使用方法

### 基本命令

```bash
# 1. 基本使用（使用所有relational neurons的平均激活）
python utils/functional_gating_viz.py --model_path path/to/best_model.pth

# 2. 自动找到并可视化最佳的relational neurons
python utils/functional_gating_viz.py --model_path path/to/best_model.pth --find_best_neurons

# 3. 可视化特定的relational neuron
python utils/functional_gating_viz.py --model_path path/to/best_model.pth --specific_neuron 15

# 4. 自定义设置
python utils/functional_gating_viz.py \
    --model_path path/to/best_model.pth \
    --output_dir my_functional_gating_viz \
    --num_reps 200 \
    --find_best_neurons
```

### 参数说明

- `--model_path`: 训练好的模型文件路径（必需）
- `--output_dir`: 输出目录（默认：functional_gating_viz）
- `--num_reps`: 每个条件的轨迹重复次数（默认：100）
- `--specific_neuron`: 指定要可视化的神经元索引
- `--find_best_neurons`: 自动找到并可视化最佳神经元

### 快速开始

```bash
# 使用示例脚本
python utils/example_functional_gating.py --model_path your_model.pth --find_best_neurons
```

## 输出文件

### 生成的可视化文件

1. `functional_gating_proximity.pdf` - 基本的功能性门控可视化
2. `functional_gating_proximity_neuron_X.pdf` - 特定神经元的可视化（X为神经元索引）

### 控制台输出

- 最佳神经元排名和评分
- 各条件下的统计信息（平均距离、平均激活、相关系数）
- 处理进度和执行时间

## 技术细节

### 数据处理

1. **轨迹生成**：使用与原代码一致的TrajectoryGenerator
2. **激活提取**：从model的relational_activations输出中提取64维隐藏层激活
3. **距离计算**：计算智能体间的欧几里得距离
4. **相对位置**：计算伙伴相对于观察者的位置

### 可视化技术

1. **热图创建**：使用occupancy-weighted方法和高斯平滑
2. **趋势拟合**：使用多项式回归捕捉非线性关系
3. **统计分析**：Pearson相关系数和p值计算
4. **期刊级别样式**：遵循Nature期刊的可视化标准

### 神经元评分算法

```python
score = mean_activation * 0.5 + abs(correlation) * 0.5
```

综合考虑：
- 激活强度（mean_activation）
- 与距离的相关性强度（abs(correlation)）

## 预期结果

### 成功的功能性门控应该显示：

1. **热图模式**：
   - 中心区域（观察者位置）周围有明显的激活热点
   - 随着距离增加，激活强度逐渐减弱
   - 形成同心圆状的激活模式

2. **散点图模式**：
   - 明显的负相关趋势（距离越近，激活越强）
   - 相关系数 |r| > 0.3 且 p < 0.05
   - 拟合曲线显示平滑的衰减模式

### 可能的发现

- **Special Social Place Cells**：可能显示更强的邻近度敏感性
- **条件差异**：不同运动条件下的门控效应强度可能不同
- **个体神经元差异**：某些神经元可能比其他神经元更敏感

## 故障排除

### 常见问题

1. **没有明显的邻近度效应**：
   - 尝试增加轨迹数量（--num_reps）
   - 检查模型是否正确训练
   - 尝试不同的神经元

2. **可视化质量差**：
   - 调整高斯平滑参数
   - 增加网格分辨率
   - 检查数据范围设置

3. **内存不足**：
   - 减少num_reps参数
   - 使用GPU加速

## 文件结构

```
utils/
├── functional_gating_viz.py      # 主要可视化工具
├── example_functional_gating.py  # 使用示例
└── README_functional_gating.md   # 本文档
```

## 依赖要求

- torch
- numpy
- matplotlib
- scipy
- sklearn
- tqdm

确保所有依赖都已安装，并且项目的其他模块（config, models等）可以正常导入。
