# 互信息分析工具 (Mutual Information Analysis)

## 概述

这个工具实现了您研究中的**第三步：计算互信息并进行可视化**，用于展示双重解离（Double Dissociation）证据，证明网络中涌现出了功能高度特化的神经元群体。

## 核心功能

### 1. 互信息计算
- `compute_mutual_information()`: 计算1D互信息
- `compute_mutual_information_2d()`: 计算2D位置互信息
- 支持不同的离散化策略和参数调整

### 2. 数据收集
- **单体模式（Single-Agent）**: 收集只有一个智能体运动时的数据
- **社交模式（Two-Agent）**: 收集两个智能体同时运动和交互的数据
- 自动处理不同的轨迹条件和神经元激活

### 3. 细胞分类整合
- 整合已有的细胞分类结果（Pure Place Cell, Pure SPC, Special SPC等）
- 计算每类细胞与自身位置和伙伴位置的互信息

### 4. 可视化生成
- **双重解离图**: 展示不同细胞类型的功能特化
- **详细分析图**: 包括散点图、分布图和比率分析
- **统计报告**: 生成详细的Markdown格式报告

## 使用方法

### 快速开始

```bash
# 使用标准模型
python run_mutual_information_analysis.py \
    --model_path logs_social_grid_cells_ddp/20250801_163726/checkpoints/best_model.pth \
    --output_dir mi_analysis_results \
    --num_reps 200

# 使用rational head模型
python run_mutual_information_analysis.py \
    --model_path logs_social_grid_cells_ddp/20250802_230022/checkpoints/best_model.pth \
    --output_dir mi_analysis_results_rational \
    --num_reps 200
```

### 参数说明

- `--model_path`: 训练好的模型检查点路径
- `--output_dir`: 结果输出目录
- `--num_reps`: 数据收集的轨迹重复次数（默认200）
- `--workers`: 工作进程数（兼容性参数）

### 测试功能

```bash
# 运行测试脚本验证功能
python test_mutual_information.py
```

## 输出文件

运行完成后，会在输出目录中生成以下文件：

1. **`double_dissociation_plot.png`**: 
   - 双重解离可视化图
   - 包含两个子面板：自我位置编码 vs 社交信息编码
   - 展示不同细胞类型的功能特化

2. **`detailed_mi_analysis.png`**:
   - 详细的互信息分析图
   - 包括散点图、分布图和比率分析
   - 提供更深入的统计洞察

3. **`mutual_information_report.md`**:
   - 详细的统计报告
   - 包含数值结果、统计显著性检验
   - 总结关键发现和双重解离证据

## 理论背景

### 双重解离（Double Dissociation）

这个分析旨在证明以下假设：

1. **Place Cell**: 主要编码自身位置信息，对伙伴位置信息编码较弱
2. **Social Cell**: 主要编码伙伴位置信息，对自身位置信息编码较弱
3. **Special SPC**: 同时编码两种信息
4. **其他细胞**: 编码模式不明确或混合

### 互信息（Mutual Information）

互信息衡量两个随机变量之间的相互依赖性：

```
MI(X,Y) = ∑∑ p(x,y) log(p(x,y)/(p(x)p(y)))
```

在我们的分析中：
- X: 神经元激活值
- Y: 位置信息（自身或伙伴）

## 技术细节

### 数据收集策略

1. **单体模式**: 使用条件1（Self运动，Peer静止）模拟单智能体环境
2. **社交模式**: 使用条件3（Both运动）模拟社交交互环境

### 互信息计算

- 使用sklearn的`mutual_info_regression`进行计算
- 支持2D位置的离散化处理
- 包含异常处理和数值稳定性保证

### 统计分析

- 计算均值和标准误差
- 进行配对t检验评估显著性
- 生成比率分析和分布比较

## 故障排除

### 常见问题

1. **模型加载失败**:
   - 检查模型路径是否正确
   - 确认模型文件完整性

2. **内存不足**:
   - 减少`--num_reps`参数
   - 使用更小的批次大小

3. **互信息计算异常**:
   - 检查激活数据是否包含NaN值
   - 调整离散化参数

### 调试模式

```bash
# 运行测试脚本检查基本功能
python test_mutual_information.py

# 检查模型输出格式
python -c "
import torch
from config import Config
# ... 加载模型代码 ...
print('Model outputs:', outputs.keys())
"
```

## 扩展功能

### 自定义分析

可以通过修改`MutualInformationAnalyzer`类来添加：
- 不同的互信息计算方法
- 额外的统计检验
- 自定义可视化样式

### 批量分析

```python
from utils.mutual_information_analysis import run_mutual_information_analysis

# 批量分析多个模型
model_paths = [...]
for model_path in model_paths:
    run_mutual_information_analysis(model_path, f"results_{i}", categorized_cells)
```

## 引用和参考

这个工具基于以下理论和方法：
- Mutual Information理论
- Grid Cell和Place Cell神经科学研究
- 双重解离实验设计原理

---

**作者**: Augment Agent  
**版本**: 1.0  
**更新日期**: 2025-01-05
