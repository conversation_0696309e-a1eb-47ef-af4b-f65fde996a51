#!/bin/bash

# PDF批量转SVG转换脚本
# 使用pdf2svg和inkscape两种工具

TARGET_DIR="/data1/fangzr/Research/Memory_Maze_3D/Multi-com/maze-zero/grid-cell-dual/social_cell_viz_grid_style-2"

echo "=== PDF批量转SVG工具 ==="
echo "目标目录: $TARGET_DIR"

# 进入目标目录
cd "$TARGET_DIR" || {
    echo "错误: 无法进入目录 $TARGET_DIR"
    exit 1
}

echo "当前工作目录: $(pwd)"

# 检查PDF文件
pdf_files=(*.pdf)
if [[ ! -e "${pdf_files[0]}" ]]; then
    echo "错误: 没有找到PDF文件！"
    exit 1
fi

total=${#pdf_files[@]}
echo "找到 $total 个PDF文件"

# 检查可用工具
TOOL=""
if command -v pdf2svg &> /dev/null; then
    TOOL="pdf2svg"
    echo "使用工具: pdf2svg (推荐)"
elif command -v inkscape &> /dev/null; then
    TOOL="inkscape"
    echo "使用工具: inkscape"
else
    echo "错误: 没有找到可用的转换工具！"
    echo "请安装 pdf2svg 或 inkscape"
    exit 1
fi

# 显示前几个文件名
echo ""
echo "PDF文件预览:"
for i in "${!pdf_files[@]}"; do
    if [ $i -lt 5 ]; then
        echo "  $((i+1)). ${pdf_files[i]}"
    fi
done
if [ $total -gt 5 ]; then
    echo "  ... 还有 $((total-5)) 个文件"
fi

echo ""
read -p "确认开始转换？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "转换已取消"
    exit 0
fi

# 开始转换
success=0
failed=0
start_time=$(date +%s)

echo ""
echo "开始转换..."
echo "=========================================="

for pdf_file in "${pdf_files[@]}"; do
    if [[ -f "$pdf_file" ]]; then
        svg_file="${pdf_file%.pdf}.svg"
        
        # 显示进度
        current=$((success + failed + 1))
        printf "[%3d/%3d] %-40s ... " "$current" "$total" "$(basename "$pdf_file")"
        
        # 根据工具执行转换
        case $TOOL in
            "pdf2svg")
                if pdf2svg "$pdf_file" "$svg_file" 2>/dev/null; then
                    echo "✓"
                    ((success++))
                else
                    echo "✗"
                    ((failed++))
                fi
                ;;
            "inkscape")
                if inkscape "$pdf_file" --export-type=svg --export-filename="$svg_file" --export-dpi=96 &>/dev/null; then
                    echo "✓"
                    ((success++))
                else
                    echo "✗"
                    ((failed++))
                fi
                ;;
        esac
    fi
done

end_time=$(date +%s)
duration=$((end_time - start_time))

echo "=========================================="
echo ""
echo "=== 转换完成 ==="
echo "总文件数: $total"
echo "成功转换: $success"
echo "转换失败: $failed"
echo "成功率: $(( success * 100 / total ))%"
echo "用时: ${duration}秒"

if [ $success -gt 0 ]; then
    echo ""
    echo "生成的SVG文件 (前10个):"
    ls -la *.svg 2>/dev/null | head -10
    
    svg_count=$(ls *.svg 2>/dev/null | wc -l)
    if [ $svg_count -gt 10 ]; then
        echo "... 总共生成了 $svg_count 个SVG文件"
    fi
    
    echo ""
    echo "文件大小统计:"
    echo "PDF文件总大小: $(du -sh *.pdf | tail -1 | cut -f1)"
    echo "SVG文件总大小: $(du -sh *.svg 2>/dev/null | tail -1 | cut -f1)"
    
    echo ""
    echo "大小对比 (前5个文件):"
    echo "文件名                    PDF大小    SVG大小"
    echo "----------------------------------------"
    for pdf in $(ls *.pdf | head -5); do
        svg="${pdf%.pdf}.svg"
        if [[ -f "$svg" ]]; then
            pdf_size=$(du -h "$pdf" | cut -f1)
            svg_size=$(du -h "$svg" | cut -f1)
            printf "%-20s %8s -> %8s\n" "$(basename "$pdf" .pdf)" "$pdf_size" "$svg_size"
        fi
    done
fi

if [ $failed -gt 0 ]; then
    echo ""
    echo "转换失败的文件:"
    for pdf_file in "${pdf_files[@]}"; do
        svg_file="${pdf_file%.pdf}.svg"
        if [[ -f "$pdf_file" && ! -f "$svg_file" ]]; then
            echo "  - $pdf_file"
        fi
    done
fi

echo ""
echo "转换完成！所有SVG文件保存在: $TARGET_DIR"