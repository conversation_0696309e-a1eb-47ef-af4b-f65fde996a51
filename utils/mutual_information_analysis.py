#!/usr/bin/env python3
"""
互信息分析工具 - 第三步：计算互信息并进行可视化
实现双重解离（Double Dissociation）证据的可视化
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
from collections import defaultdict
import torch
from tqdm import tqdm

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from utils.metrics import compute_mutual_information, compute_mutual_information_2d
from config import Config


class MutualInformationAnalyzer:
    """互信息分析器 - 实现第三步的核心功能"""

    def __init__(self, config: Config):
        self.config = config
        self.maze_size = config.ENV_SIZE
        self.seq_len = config.SEQUENCE_LENGTH

    def _create_trajectory_generator(self):
        """创建轨迹生成器的内联实现"""
        class TrajectoryGenerator:
            def __init__(self, config):
                self.maze_size = config.ENV_SIZE
                self.seq_len = config.SEQUENCE_LENGTH
                self.start_S = np.array([self.maze_size * 0.5, self.maze_size * 0.2])
                self.end_A = np.array([self.maze_size * 0.2, self.maze_size * 0.8])
                self.end_B = np.array([self.maze_size * 0.8, self.maze_size * 0.8])

            def _generate_path(self, start, end, moving):
                if not moving:
                    positions = np.tile(start, (self.seq_len, 1))
                    velocities = np.zeros((self.seq_len, 2))
                    angular_velocities = np.zeros(self.seq_len)
                    return positions, velocities, angular_velocities

                base_positions = np.array([start + (t / max(1, self.seq_len - 1)) * (end - start) for t in range(self.seq_len)])
                direction = (end - start) / (np.linalg.norm(end - start) + 1e-6)
                perpendicular = np.array([-direction[1], direction[0]])

                amplitude = np.random.uniform(0.5, 1.5)
                frequency = np.random.uniform(1.5, 2.5)
                phase = np.random.uniform(0, np.pi)

                t = np.linspace(0, 1, self.seq_len)
                sine_offset = amplitude * np.sin(2 * np.pi * frequency * t + phase)

                positions = base_positions + sine_offset[:, np.newaxis] * perpendicular[np.newaxis, :]
                positions = np.clip(positions, 0.5, self.maze_size - 0.5)

                velocities = np.diff(positions, axis=0, prepend=positions[0:1])
                angles = np.arctan2(velocities[:, 1], velocities[:, 0])
                angular_velocities = np.diff(np.unwrap(angles), prepend=angles[0:1])
                return positions, velocities, angular_velocities

            def get_trajectories_for_condition(self, condition, num_reps=20):
                all_self_pos, all_self_vel, all_self_ang_vel = [], [], []
                all_peer_pos, all_peer_vel, all_peer_ang_vel = [], [], []
                movement_patterns = {1: (True, False), 2: (False, True), 3: (True, True), 4: (False, False)}
                self_moving, peer_moving = movement_patterns[condition]

                for i in range(num_reps):
                    target = self.end_A if i % 2 == 0 else self.end_B
                    static = self.start_S

                    self_start, self_end = (self.start_S, target) if self_moving else (static, static)
                    peer_start, peer_end = (self.start_S, target) if peer_moving else (static, static)

                    self_pos, self_vel, self_ang_vel = self._generate_path(self_start, self_end, self_moving)
                    peer_pos, peer_vel, peer_ang_vel = self._generate_path(peer_start, peer_end, peer_moving)

                    all_self_pos.append(self_pos)
                    all_self_vel.append(self_vel)
                    all_self_ang_vel.append(self_ang_vel)
                    all_peer_pos.append(peer_pos)
                    all_peer_vel.append(peer_vel)
                    all_peer_ang_vel.append(peer_ang_vel)

                return {
                    'self_pos': np.array(all_self_pos),
                    'self_vel': np.array(all_self_vel),
                    'self_ang_vel': np.array(all_self_ang_vel),
                    'peer_pos': np.array(all_peer_pos),
                    'peer_vel': np.array(all_peer_vel),
                    'peer_ang_vel': np.array(all_peer_ang_vel)
                }

        return TrajectoryGenerator(self.config)
        
    def analyze_cell_mutual_information(self, categorized_cells: Dict, 
                                      single_agent_data: Dict, 
                                      social_agent_data: Dict) -> Dict:
        """
        计算不同类别细胞的互信息
        
        Args:
            categorized_cells: 已分类的细胞字典 {cell_type: [results]}
            single_agent_data: 单体模式数据 {'positions': ..., 'activations': ...}
            social_agent_data: 社交模式数据 {'self_pos': ..., 'peer_pos': ..., 'activations': ...}
            
        Returns:
            Dict: 互信息分析结果
        """
        results = {
            'self_position_mi': {},  # 与自身位置的互信息
            'partner_position_mi': {},  # 与伙伴位置的互信息
            'cell_counts': {},  # 每类细胞的数量
            'individual_results': {}  # 每个神经元的详细结果
        }
        
        print("Computing mutual information for different cell types...")
        
        for cell_type, cell_results in categorized_cells.items():
            if len(cell_results) == 0:
                continue
                
            print(f"\nAnalyzing {len(cell_results)} {cell_type} cells...")
            
            self_mi_values = []
            partner_mi_values = []
            individual_results = []
            
            for cell_result in tqdm(cell_results, desc=f"Processing {cell_type}"):
                neuron_idx = cell_result.neuron_idx
                
                # 计算与自身位置的互信息（使用单体模式数据）
                if neuron_idx < single_agent_data['activations'].shape[-1]:
                    single_activations = single_agent_data['activations'][:, :, neuron_idx].flatten()
                    single_positions = single_agent_data['positions'].reshape(-1, 2)
                    
                    self_mi = compute_mutual_information_2d(single_activations, single_positions)
                    self_mi_values.append(self_mi)
                else:
                    self_mi = 0.0
                    self_mi_values.append(self_mi)
                
                # 计算与伙伴位置的互信息（使用社交模式数据）
                if neuron_idx < social_agent_data['activations'].shape[-1]:
                    social_activations = social_agent_data['activations'][:, :, neuron_idx].flatten()
                    partner_positions = social_agent_data['peer_pos'].reshape(-1, 2)
                    
                    partner_mi = compute_mutual_information_2d(social_activations, partner_positions)
                    partner_mi_values.append(partner_mi)
                else:
                    partner_mi = 0.0
                    partner_mi_values.append(partner_mi)
                
                individual_results.append({
                    'neuron_idx': neuron_idx,
                    'self_mi': self_mi,
                    'partner_mi': partner_mi,
                    'cell_type': cell_type
                })
            
            # 存储结果
            results['self_position_mi'][cell_type] = np.array(self_mi_values)
            results['partner_position_mi'][cell_type] = np.array(partner_mi_values)
            results['cell_counts'][cell_type] = len(cell_results)
            results['individual_results'][cell_type] = individual_results
            
            print(f"{cell_type}: Self MI = {np.mean(self_mi_values):.4f}±{np.std(self_mi_values):.4f}, "
                  f"Partner MI = {np.mean(partner_mi_values):.4f}±{np.std(partner_mi_values):.4f}")
        
        return results
    
    def create_double_dissociation_plot(self, mi_results: Dict, output_path: str):
        """
        创建双重解离（Double Dissociation）可视化图
        
        Args:
            mi_results: 互信息分析结果
            output_path: 输出路径
        """
        # 设置图形样式
        plt.style.use('default')
        sns.set_palette("husl")
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
        
        # 准备数据
        cell_types = []
        self_mi_means = []
        self_mi_stds = []
        partner_mi_means = []
        partner_mi_stds = []
        cell_counts = []
        
        # 定义细胞类型的显示顺序和颜色
        type_order = ['Pure Place Cell', 'Pure SPC', 'Special SPC', 'Mixed Response', 'Other']
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
        
        for cell_type in type_order:
            if cell_type in mi_results['self_position_mi']:
                cell_types.append(cell_type)
                
                self_mi = mi_results['self_position_mi'][cell_type]
                partner_mi = mi_results['partner_position_mi'][cell_type]
                
                self_mi_means.append(np.mean(self_mi))
                self_mi_stds.append(np.std(self_mi) / np.sqrt(len(self_mi)))  # SEM
                
                partner_mi_means.append(np.mean(partner_mi))
                partner_mi_stds.append(np.std(partner_mi) / np.sqrt(len(partner_mi)))  # SEM
                
                cell_counts.append(mi_results['cell_counts'][cell_type])
        
        x = np.arange(len(cell_types))
        width = 0.35
        
        # 子面板A：自我位置编码 (Self-Position Encoding)
        bars1 = ax1.bar(x, self_mi_means, width, yerr=self_mi_stds, 
                       color=colors[:len(cell_types)], alpha=0.8, capsize=5)
        ax1.set_xlabel('Cell Type', fontsize=12)
        ax1.set_ylabel('Mutual Information with Self Position', fontsize=12)
        ax1.set_title('A. Self-Position Encoding\n(Single-Agent Mode)', fontsize=14, fontweight='bold')
        ax1.set_xticks(x)
        ax1.set_xticklabels([f'{ct}\n(n={count})' for ct, count in zip(cell_types, cell_counts)], 
                           rotation=45, ha='right')
        ax1.grid(True, alpha=0.3)
        
        # 子面板B：社交信息编码 (Social Information Encoding)
        bars2 = ax2.bar(x, partner_mi_means, width, yerr=partner_mi_stds, 
                       color=colors[:len(cell_types)], alpha=0.8, capsize=5)
        ax2.set_xlabel('Cell Type', fontsize=12)
        ax2.set_ylabel('Mutual Information with Partner Position', fontsize=12)
        ax2.set_title('B. Social Information Encoding\n(Two-Agent Mode)', fontsize=14, fontweight='bold')
        ax2.set_xticks(x)
        ax2.set_xticklabels([f'{ct}\n(n={count})' for ct, count in zip(cell_types, cell_counts)], 
                           rotation=45, ha='right')
        ax2.grid(True, alpha=0.3)
        
        # 添加数值标签
        for i, (bar1, bar2) in enumerate(zip(bars1, bars2)):
            height1 = bar1.get_height()
            height2 = bar2.get_height()
            ax1.text(bar1.get_x() + bar1.get_width()/2., height1 + self_mi_stds[i],
                    f'{height1:.3f}', ha='center', va='bottom', fontsize=10)
            ax2.text(bar2.get_x() + bar2.get_width()/2., height2 + partner_mi_stds[i],
                    f'{height2:.3f}', ha='center', va='bottom', fontsize=10)
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Double dissociation plot saved to: {output_path}")
        
    def create_detailed_analysis_plot(self, mi_results: Dict, output_path: str):
        """
        创建详细的互信息分析图，包括散点图和分布图
        
        Args:
            mi_results: 互信息分析结果
            output_path: 输出路径
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 准备所有数据
        all_self_mi = []
        all_partner_mi = []
        all_types = []
        all_colors = []
        
        colors = {'Pure Place Cell': '#1f77b4', 'Pure SPC': '#ff7f0e', 
                 'Special SPC': '#2ca02c', 'Mixed Response': '#d62728', 'Other': '#9467bd'}
        
        for cell_type in mi_results['self_position_mi']:
            self_mi = mi_results['self_position_mi'][cell_type]
            partner_mi = mi_results['partner_position_mi'][cell_type]
            
            all_self_mi.extend(self_mi)
            all_partner_mi.extend(partner_mi)
            all_types.extend([cell_type] * len(self_mi))
            all_colors.extend([colors.get(cell_type, '#9467bd')] * len(self_mi))
        
        # 散点图：Self MI vs Partner MI
        ax1 = axes[0, 0]
        for cell_type in colors:
            if cell_type in mi_results['self_position_mi']:
                self_mi = mi_results['self_position_mi'][cell_type]
                partner_mi = mi_results['partner_position_mi'][cell_type]
                ax1.scatter(self_mi, partner_mi, c=colors[cell_type], 
                           label=f'{cell_type} (n={len(self_mi)})', alpha=0.7, s=50)
        
        ax1.set_xlabel('Self Position MI')
        ax1.set_ylabel('Partner Position MI')
        ax1.set_title('MI Scatter Plot: Self vs Partner Position')
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax1.grid(True, alpha=0.3)
        
        # 添加对角线
        max_val = max(max(all_self_mi), max(all_partner_mi))
        ax1.plot([0, max_val], [0, max_val], 'k--', alpha=0.5, label='y=x')
        
        # Self MI 分布
        ax2 = axes[0, 1]
        for cell_type in colors:
            if cell_type in mi_results['self_position_mi']:
                self_mi = mi_results['self_position_mi'][cell_type]
                ax2.hist(self_mi, bins=20, alpha=0.6, label=cell_type, 
                        color=colors[cell_type], density=True)
        ax2.set_xlabel('Self Position MI')
        ax2.set_ylabel('Density')
        ax2.set_title('Distribution of Self Position MI')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # Partner MI 分布
        ax3 = axes[1, 0]
        for cell_type in colors:
            if cell_type in mi_results['partner_position_mi']:
                partner_mi = mi_results['partner_position_mi'][cell_type]
                ax3.hist(partner_mi, bins=20, alpha=0.6, label=cell_type, 
                        color=colors[cell_type], density=True)
        ax3.set_xlabel('Partner Position MI')
        ax3.set_ylabel('Density')
        ax3.set_title('Distribution of Partner Position MI')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 比率分析
        ax4 = axes[1, 1]
        ratios = []
        ratio_types = []
        for cell_type in mi_results['self_position_mi']:
            self_mi = mi_results['self_position_mi'][cell_type]
            partner_mi = mi_results['partner_position_mi'][cell_type]
            
            # 计算比率 (避免除零)
            ratio = np.log2((partner_mi + 1e-6) / (self_mi + 1e-6))
            ratios.extend(ratio)
            ratio_types.extend([cell_type] * len(ratio))
        
        # 箱线图显示比率分布
        unique_types = list(set(ratio_types))
        ratio_data = [np.array(ratios)[np.array(ratio_types) == t] for t in unique_types]
        
        bp = ax4.boxplot(ratio_data, labels=unique_types, patch_artist=True)
        for patch, cell_type in zip(bp['boxes'], unique_types):
            patch.set_facecolor(colors.get(cell_type, '#9467bd'))
            patch.set_alpha(0.7)
        
        ax4.set_ylabel('log2(Partner MI / Self MI)')
        ax4.set_title('MI Ratio Analysis')
        ax4.axhline(y=0, color='k', linestyle='--', alpha=0.5)
        ax4.grid(True, alpha=0.3)
        plt.setp(ax4.get_xticklabels(), rotation=45, ha='right')
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Detailed analysis plot saved to: {output_path}")

    def collect_single_agent_data(self, model, device, num_reps: int = 200) -> Dict:
        """
        收集单体模式（Single-Agent）数据

        Args:
            model: 训练好的模型
            device: 计算设备
            num_reps: 重复次数

        Returns:
            Dict: 包含位置和激活数据
        """
        print("Collecting single-agent mode data...")

        # 创建轨迹生成器 - 使用内联定义避免循环导入
        trajectory_gen = self._create_trajectory_generator()

        model.eval()
        all_positions = []
        all_activations = []

        with torch.no_grad():
            # 使用条件1：Self运动，Peer静止（相当于单体模式）
            trajs = trajectory_gen.get_trajectories_for_condition(1, num_reps=num_reps)

            # 准备输入数据
            self_vel_input = torch.cat([
                torch.from_numpy(trajs['self_vel']).float(),
                torch.from_numpy(trajs['self_ang_vel']).float().unsqueeze(-1)
            ], dim=-1).to(device)

            self_init_pos = torch.from_numpy(trajs['self_pos'][:, 0, :]).float().to(device)
            self_init_hd = torch.from_numpy(np.arctan2(trajs['self_vel'][:, 0, 1], trajs['self_vel'][:, 0, 0])).float().to(device)

            # Peer静止，所以使用零速度
            peer_vel_input = torch.cat([
                torch.from_numpy(trajs['peer_vel']).float(),
                torch.from_numpy(trajs['peer_ang_vel']).float().unsqueeze(-1)
            ], dim=-1).to(device)

            peer_init_pos = torch.from_numpy(trajs['peer_pos'][:, 0, :]).float().to(device)
            peer_init_hd = torch.from_numpy(np.arctan2(trajs['peer_vel'][:, 0, 1], trajs['peer_vel'][:, 0, 0])).float().to(device)

            # 前向传播
            outputs = model(self_vel_input, self_init_pos, self_init_hd,
                          peer_vel_input, peer_init_pos, peer_init_hd)

            # 收集数据
            positions = trajs['self_pos']  # (num_reps, seq_len, 2)

            # 根据模型类型获取激活
            if hasattr(outputs, 'keys') and 'bottleneck_activations' in outputs:
                activations = outputs['bottleneck_activations'].cpu().numpy()
            elif hasattr(outputs, 'keys') and 'relational_activations' in outputs:
                activations = outputs['relational_activations'].cpu().numpy()
            else:
                # 尝试其他可能的键
                for key in ['activations', 'hidden_states', 'features']:
                    if hasattr(outputs, 'keys') and key in outputs:
                        activations = outputs[key].cpu().numpy()
                        break
                else:
                    raise ValueError("Cannot find activation data in model outputs")

            all_positions.append(positions)
            all_activations.append(activations)

        return {
            'positions': np.concatenate(all_positions, axis=0),
            'activations': np.concatenate(all_activations, axis=0)
        }

    def collect_social_agent_data(self, model, device, num_reps: int = 200) -> Dict:
        """
        收集社交模式（Two-Agent）数据

        Args:
            model: 训练好的模型
            device: 计算设备
            num_reps: 重复次数

        Returns:
            Dict: 包含自身位置、伙伴位置和激活数据
        """
        print("Collecting social-agent mode data...")

        # 创建轨迹生成器 - 使用内联定义避免循环导入
        trajectory_gen = self._create_trajectory_generator()

        model.eval()
        all_self_pos = []
        all_peer_pos = []
        all_activations = []

        with torch.no_grad():
            # 使用条件3：Both运动（社交模式）
            trajs = trajectory_gen.get_trajectories_for_condition(3, num_reps=num_reps)

            # 准备输入数据
            self_vel_input = torch.cat([
                torch.from_numpy(trajs['self_vel']).float(),
                torch.from_numpy(trajs['self_ang_vel']).float().unsqueeze(-1)
            ], dim=-1).to(device)

            self_init_pos = torch.from_numpy(trajs['self_pos'][:, 0, :]).float().to(device)
            self_init_hd = torch.from_numpy(np.arctan2(trajs['self_vel'][:, 0, 1], trajs['self_vel'][:, 0, 0])).float().to(device)

            peer_vel_input = torch.cat([
                torch.from_numpy(trajs['peer_vel']).float(),
                torch.from_numpy(trajs['peer_ang_vel']).float().unsqueeze(-1)
            ], dim=-1).to(device)

            peer_init_pos = torch.from_numpy(trajs['peer_pos'][:, 0, :]).float().to(device)
            peer_init_hd = torch.from_numpy(np.arctan2(trajs['peer_vel'][:, 0, 1], trajs['peer_vel'][:, 0, 0])).float().to(device)

            # 前向传播
            outputs = model(self_vel_input, self_init_pos, self_init_hd,
                          peer_vel_input, peer_init_pos, peer_init_hd)

            # 收集数据
            self_positions = trajs['self_pos']  # (num_reps, seq_len, 2)
            peer_positions = trajs['peer_pos']  # (num_reps, seq_len, 2)

            # 根据模型类型获取激活
            if hasattr(outputs, 'keys') and 'bottleneck_activations' in outputs:
                activations = outputs['bottleneck_activations'].cpu().numpy()
            elif hasattr(outputs, 'keys') and 'relational_activations' in outputs:
                activations = outputs['relational_activations'].cpu().numpy()
            else:
                # 尝试其他可能的键
                for key in ['activations', 'hidden_states', 'features']:
                    if hasattr(outputs, 'keys') and key in outputs:
                        activations = outputs[key].cpu().numpy()
                        break
                else:
                    raise ValueError("Cannot find activation data in model outputs")

            all_self_pos.append(self_positions)
            all_peer_pos.append(peer_positions)
            all_activations.append(activations)

        return {
            'self_pos': np.concatenate(all_self_pos, axis=0),
            'peer_pos': np.concatenate(all_peer_pos, axis=0),
            'activations': np.concatenate(all_activations, axis=0)
        }

    def generate_statistical_report(self, mi_results: Dict, output_path: str):
        """
        生成详细的统计报告

        Args:
            mi_results: 互信息分析结果
            output_path: 输出路径
        """
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("# 互信息分析统计报告\n")
            f.write("=" * 50 + "\n\n")

            f.write("## 分析概述\n")
            f.write("本报告展示了不同类型神经元与自身位置和伙伴位置之间的互信息分析结果。\n")
            f.write("这是实现双重解离（Double Dissociation）证据的关键步骤。\n\n")

            f.write("## 细胞类型统计\n")
            total_cells = sum(mi_results['cell_counts'].values())
            f.write(f"总分析细胞数: {total_cells}\n\n")

            for cell_type, count in mi_results['cell_counts'].items():
                percentage = (count / total_cells) * 100
                f.write(f"- {cell_type}: {count} 个 ({percentage:.1f}%)\n")

            f.write("\n## 互信息分析结果\n\n")

            # 创建表格
            f.write("| 细胞类型 | 自身位置MI (均值±标准误) | 伙伴位置MI (均值±标准误) | 比率 (Partner/Self) |\n")
            f.write("|---------|----------------------|----------------------|-------------------|\n")

            for cell_type in mi_results['self_position_mi']:
                self_mi = mi_results['self_position_mi'][cell_type]
                partner_mi = mi_results['partner_position_mi'][cell_type]

                self_mean = np.mean(self_mi)
                self_sem = np.std(self_mi) / np.sqrt(len(self_mi))
                partner_mean = np.mean(partner_mi)
                partner_sem = np.std(partner_mi) / np.sqrt(len(partner_mi))

                ratio = partner_mean / (self_mean + 1e-6)

                f.write(f"| {cell_type} | {self_mean:.4f}±{self_sem:.4f} | "
                       f"{partner_mean:.4f}±{partner_sem:.4f} | {ratio:.2f} |\n")

            f.write("\n## 关键发现\n\n")

            # 找到最高的自身位置MI和伙伴位置MI
            max_self_mi_type = max(mi_results['self_position_mi'].items(),
                                 key=lambda x: np.mean(x[1]))[0]
            max_partner_mi_type = max(mi_results['partner_position_mi'].items(),
                                    key=lambda x: np.mean(x[1]))[0]

            f.write(f"1. **自身位置编码最强**: {max_self_mi_type} "
                   f"(MI = {np.mean(mi_results['self_position_mi'][max_self_mi_type]):.4f})\n")
            f.write(f"2. **伙伴位置编码最强**: {max_partner_mi_type} "
                   f"(MI = {np.mean(mi_results['partner_position_mi'][max_partner_mi_type]):.4f})\n\n")

            # 双重解离证据
            if max_self_mi_type != max_partner_mi_type:
                f.write("🎯 **双重解离证据**: 发现了功能特化的神经元群体！\n")
                f.write(f"- {max_self_mi_type} 主要编码自身位置信息\n")
                f.write(f"- {max_partner_mi_type} 主要编码伙伴位置信息\n")
                f.write("这证明了网络中涌现出了功能高度特化的神经元群体。\n\n")
            else:
                f.write("⚠️ **注意**: 未发现明显的双重解离模式，可能需要调整分析参数。\n\n")

            f.write("## 统计显著性\n\n")

            # 进行简单的统计检验
            try:
                from scipy import stats
            except ImportError:
                print("Warning: scipy not available, skipping statistical tests")
                f.write("*注意: scipy未安装，跳过统计检验*\n\n")
                f.write(f"\n---\n")
                f.write(f"报告生成时间: {np.datetime64('now')}\n")
                return

            for cell_type in mi_results['self_position_mi']:
                self_mi = mi_results['self_position_mi'][cell_type]
                partner_mi = mi_results['partner_position_mi'][cell_type]

                if len(self_mi) > 1 and len(partner_mi) > 1:
                    # 配对t检验
                    t_stat, p_value = stats.ttest_rel(partner_mi, self_mi)

                    f.write(f"**{cell_type}**: ")
                    if p_value < 0.001:
                        f.write(f"伙伴位置MI显著高于自身位置MI (p < 0.001)\n")
                    elif p_value < 0.01:
                        f.write(f"伙伴位置MI显著高于自身位置MI (p < 0.01)\n")
                    elif p_value < 0.05:
                        f.write(f"伙伴位置MI显著高于自身位置MI (p < 0.05)\n")
                    else:
                        f.write(f"两种MI无显著差异 (p = {p_value:.3f})\n")

            f.write(f"\n---\n")
            f.write(f"报告生成时间: {np.datetime64('now')}\n")

        print(f"Statistical report saved to: {output_path}")


def run_mutual_information_analysis(model_path: str, output_dir: str,
                                  categorized_cells: Dict, num_reps: int = 200):
    """
    运行完整的互信息分析流程

    Args:
        model_path: 模型路径
        output_dir: 输出目录
        categorized_cells: 已分类的细胞字典
        num_reps: 数据收集重复次数
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 加载模型和配置
    config = Config()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # 加载模型
    from models.social_grid_cell import SocialGridCellNetwork
    from models.place_hd_cells import PlaceCellEnsemble, HeadDirectionCellEnsemble

    place_cells = PlaceCellEnsemble(
        n_cells=config.PLACE_CELLS_N,
        scale=config.PLACE_CELLS_SCALE,
        pos_min=0, pos_max=config.ENV_SIZE,
        seed=config.SEED
    )
    hd_cells = HeadDirectionCellEnsemble(
        n_cells=config.HD_CELLS_N,
        concentration=config.HD_CELLS_CONCENTRATION,
        seed=config.SEED
    )

    model_config = {
        'HIDDEN_SIZE': config.HIDDEN_SIZE,
        'LATENT_DIM': config.LATENT_DIM,
        'dropout_rate': config.DROPOUT_RATE,
        'ego_token_size': getattr(config, 'ego_token_size', 4)
    }

    model = SocialGridCellNetwork(
        place_cells=place_cells,
        hd_cells=hd_cells,
        config=model_config
    ).to(device)

    # 加载权重
    checkpoint = torch.load(model_path, map_location=device)
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)

    print("Model loaded successfully!")

    # 创建分析器
    analyzer = MutualInformationAnalyzer(config)

    # 收集数据
    print("\n" + "="*50)
    print("STEP 1: Collecting Single-Agent Data")
    print("="*50)
    single_agent_data = analyzer.collect_single_agent_data(model, device, num_reps)

    print("\n" + "="*50)
    print("STEP 2: Collecting Social-Agent Data")
    print("="*50)
    social_agent_data = analyzer.collect_social_agent_data(model, device, num_reps)

    print("\n" + "="*50)
    print("STEP 3: Computing Mutual Information")
    print("="*50)
    mi_results = analyzer.analyze_cell_mutual_information(
        categorized_cells, single_agent_data, social_agent_data)

    print("\n" + "="*50)
    print("STEP 4: Generating Visualizations")
    print("="*50)

    # 生成双重解离图
    double_dissociation_path = os.path.join(output_dir, 'double_dissociation_plot.png')
    analyzer.create_double_dissociation_plot(mi_results, double_dissociation_path)

    # 生成详细分析图
    detailed_analysis_path = os.path.join(output_dir, 'detailed_mi_analysis.png')
    analyzer.create_detailed_analysis_plot(mi_results, detailed_analysis_path)

    # 生成统计报告
    report_path = os.path.join(output_dir, 'mutual_information_report.md')
    analyzer.generate_statistical_report(mi_results, report_path)

    print("\n" + "="*50)
    print("ANALYSIS COMPLETE!")
    print("="*50)
    print(f"Results saved to: {output_dir}")
    print(f"- Double dissociation plot: {double_dissociation_path}")
    print(f"- Detailed analysis: {detailed_analysis_path}")
    print(f"- Statistical report: {report_path}")

    return mi_results
