import os
import numpy as np
import torch
import matplotlib.pyplot as plt
from scipy.ndimage import gaussian_filter, rotate
from scipy.signal import correlate2d
from sklearn.neighbors import KernelDensity
from matplotlib.colors import LinearSegmentedColormap

class NavigationVisualizer:
    def __init__(self, save_dir):
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
        
        # 创建更好的自定义colormap，类似Nature文章中使用的
        # 从蓝色到白色到红色的渐变
        cdict = {
            'red': [(0.0, 0.0, 0.0), (0.5, 1.0, 1.0), (1.0, 1.0, 1.0)],
            'green': [(0.0, 0.0, 0.0), (0.5, 1.0, 1.0), (1.0, 0.0, 0.0)],
            'blue': [(0.0, 1.0, 1.0), (0.5, 1.0, 1.0), (1.0, 0.0, 0.0)]
        }
        self.autocorr_cmap = LinearSegmentedColormap('BlueWhiteRed', cdict)
    
    def compute_spatial_autocorr(self, activation_map):
        """
        计算二维自相关图，使用FFT加速计算
        
        Args:
            activation_map: 2D numpy 数组，表示某个神经元在空间上的激活图
        Returns:
            autocorr: 2D 自相关图（归一化后）
        """
        # 减去平均值以消除直流分量的影响
        activation_map = activation_map - np.mean(activation_map)
        # 使用更精确的2D相关性计算
        autocorr = correlate2d(activation_map, activation_map, mode='same', boundary='wrap')
        
        # 归一化，使中心点为1
        center_y, center_x = autocorr.shape[0] // 2, autocorr.shape[1] // 2
        center_val = autocorr[center_y, center_x]
        if abs(center_val) > 1e-10:  # 避免除以接近零的值
            autocorr = autocorr / center_val
        return autocorr

    def compute_gridness(self, autocorr):
        """
        计算改进的gridness分数，基于Nature论文中的方法
        
        参考Banino et al. (2018) 和 Hafting et al. (2005)
        我们比较60°和120°旋转的相关性与30°、90°和150°旋转的相关性差异
        
        Args:
            autocorr: 2D 自相关图（numpy 数组）
        Returns:
            gridness: float，gridness 分数
        """
        # 定义中心点和半径范围
        center_y, center_x = autocorr.shape[0] // 2, autocorr.shape[1] // 2
        
        # 创建环形掩码，排除中心点和边缘
        max_radius = min(center_y, center_x) // 2
        min_radius = max(3, max_radius // 4)  # 内半径，避免中心峰值
        
        y, x = np.ogrid[-center_y:autocorr.shape[0]-center_y, -center_x:autocorr.shape[1]-center_x]
        radius = np.sqrt(x*x + y*y)
        ring_mask = (radius >= min_radius) & (radius <= max_radius)
        
        # 提取环形区域
        ring_area = autocorr.copy()
        ring_area[~ring_mask] = 0
        
        # 计算不同角度旋转的相关性
        angles_pos = [60, 120]  # 期望高相关性
        angles_neg = [30, 90, 150]  # 期望低相关性
        
        corrs_pos = []
        corrs_neg = []
        
        for angle in angles_pos:
            rotated = rotate(ring_area, angle, reshape=False, order=3)  # 使用三次样条插值
            # 计算原始环形区域与旋转后区域的相关性
            valid_mask = ring_mask & (rotated != 0)
            if np.sum(valid_mask) > 0:
                orig_valid = ring_area[valid_mask].flatten()
                rot_valid = rotated[valid_mask].flatten()
                if len(orig_valid) > 1:  # 确保有足够的点进行相关性计算
                    corr = np.corrcoef(orig_valid, rot_valid)[0, 1]
                    corrs_pos.append(corr)
        
        for angle in angles_neg:
            rotated = rotate(ring_area, angle, reshape=False, order=3)
            valid_mask = ring_mask & (rotated != 0)
            if np.sum(valid_mask) > 0:
                orig_valid = ring_area[valid_mask].flatten()
                rot_valid = rotated[valid_mask].flatten()
                if len(orig_valid) > 1:
                    corr = np.corrcoef(orig_valid, rot_valid)[0, 1]
                    corrs_neg.append(corr)
        
        # 如果没有足够的相关性值，返回低分数
        if len(corrs_pos) == 0 or len(corrs_neg) == 0:
            return -1.0
            
        # 计算gridness分数：60°和120°的平均相关性减去30°、90°和150°的平均相关性
        gridness = np.mean(corrs_pos) - np.mean(corrs_neg)
        return gridness

    def compute_ratemap_kde(self, positions, activations, env_size=15, resolution=50, bandwidth=0.5):
        """
        使用高斯核密度估计（KDE）计算平滑的ratemap
        
        Args:
            positions: shape [S, 2], 值范围[0, env_size]的位置数据
            activations: shape [S], 对应位置处的神经元激活值
            env_size: 环境大小
            resolution: 输出ratemap的分辨率
            bandwidth: KDE的带宽参数，控制平滑程度
            
        Returns:
            ratemap: shape [resolution, resolution], 平滑的空间激活图
        """
        # 创建网格点
        x = np.linspace(0, env_size, resolution)
        y = np.linspace(0, env_size, resolution)
        X, Y = np.meshgrid(x, y)
        grid_points = np.vstack([Y.ravel(), X.ravel()]).T  # [resolution^2, 2]
        
        # 归一化位置数据到[0,1]范围，便于KDE计算
        positions_norm = positions / env_size
        
        # 创建weight_map和count_map
        weight_map = np.zeros((resolution, resolution))
        count_map = np.zeros((resolution, resolution))
        
        # 遍历所有位置-激活对
        for pos, act in zip(positions, activations):
            # 找到最近的网格点索引
            ix = min(int(pos[0] / env_size * (resolution-1)), resolution-1)
            iy = min(int(pos[1] / env_size * (resolution-1)), resolution-1)
            
            weight_map[iy, ix] += act
            count_map[iy, ix] += 1
        
        # 处理未访问区域
        mask = count_map > 0
        ratemap = np.zeros((resolution, resolution))
        ratemap[mask] = weight_map[mask] / count_map[mask]
        
        # 使用高斯滤波平滑ratemap
        smoothed_ratemap = gaussian_filter(ratemap, sigma=bandwidth)
        
        return smoothed_ratemap

    def collect_trajectories_and_activations(self, model, dataloader, device, num_batches=10):
        """
        从多个批次收集轨迹和神经元激活数据
        修改：排除第一个时间步，因为它是输入不是预测
        
        Args:
            model: 训练中的网络模型
            dataloader: 数据加载器
            device: 计算设备
            num_batches: 收集的批次数量
            
        Returns:
            positions_list: 位置列表 (排除第一个时间步)
            bottleneck_list: 瓶颈层激活值列表 (排除第一个时间步)
        """
        model.eval()
        positions_list = []
        bottleneck_list = []
        
        with torch.no_grad():
            for i, batch in enumerate(dataloader):
                if i >= num_batches:
                    break
                
                positions = batch['positions'].to(device)
                angles = batch['angles'].to(device)
                velocities = batch['velocities'].to(device)
                ang_vels = batch['angular_velocities'].to(device)
                
                # 准备模型输入
                w = ang_vels.unsqueeze(-1)
                velocity_input = torch.cat([velocities, w], dim=-1)
                init_pos = positions[:, 0]
                init_hd = angles[:, 0]
                
                # 获取模型输出
                outputs = model(velocity_input, init_pos, init_hd)
                
                # 收集位置和bottleneck激活，排除第一个时间步
                for b in range(positions.size(0)):
                    # 从索引1开始 (跳过初始点)
                    pos_sample = positions[b, 1:].cpu().numpy()
                    bottleneck_sample = outputs['bottleneck'][b, 1:].cpu().numpy()
                    
                    positions_list.append(pos_sample)
                    bottleneck_list.append(bottleneck_sample)
        
        return positions_list, bottleneck_list

    def visualize_grid_cells(self, model, dataloader, device, epoch, num_batches=10, num_neurons=6, suffix=""):
        """
        可视化栅格细胞的活动模式，类似Nature 2018文章的风格
        修改：仅考虑从第二个时间步开始的预测
        
        Args:
            model: 训练中的网络模型
            dataloader: 数据加载器
            device: 计算设备
            epoch: 当前训练轮次
            num_batches: 用于可视化的批次数量
            num_neurons: 要可视化的神经元数量
            
        Returns:
            avg_gridness: 所选神经元的平均gridness分数
        """
        # 收集多个批次的轨迹和激活数据 (已排除第一个时间步)
        positions_list, bottleneck_list = self.collect_trajectories_and_activations(
            model, dataloader, device, num_batches)
        
        # 识别模型bottleneck的大小
        if isinstance(model, torch.nn.parallel.DistributedDataParallel):
            bottleneck_size = model.module.bottleneck_size
        else:
            bottleneck_size = model.bottleneck_size
        
        # 计算每个神经元的gridness分数
        gridness_scores = []
        all_positions = np.concatenate([p.reshape(-1, 2) for p in positions_list], axis=0)
        
        # 将所有bottleneck激活堆叠起来，每列代表一个神经元的所有激活
        all_bottleneck = np.concatenate([b.reshape(-1, bottleneck_size) for b in bottleneck_list], axis=0)
        
        # 选择gridness分数最高的神经元
        candidate_neurons = np.random.choice(bottleneck_size, min(bottleneck_size, 30), replace=False)
        candidate_gridness = []
        
        resolution = 50  # ratemap分辨率
        
        for neuron_idx in candidate_neurons:
            activations = all_bottleneck[:, neuron_idx]
            
            # 计算平滑的ratemap
            ratemap = self.compute_ratemap_kde(all_positions, activations, resolution=resolution)
            
            # 计算自相关图
            autocorr = self.compute_spatial_autocorr(ratemap)
            
            # 计算gridness分数
            gridness = self.compute_gridness(autocorr)
            candidate_gridness.append((neuron_idx, gridness))
        
        # 按gridness分数排序
        candidate_gridness.sort(key=lambda x: x[1], reverse=True)
        
        # 选择得分最高的num_neurons个神经元
        selected_neurons = [n for n, _ in candidate_gridness[:num_neurons]]
        
        # 创建多个子图用于可视化
        fig = plt.figure(figsize=(10, num_neurons * 2.5))
        gridspec = fig.add_gridspec(num_neurons, 3, width_ratios=[1, 1, 0.05])
        
        for i, neuron_idx in enumerate(selected_neurons):
            activations = all_bottleneck[:, neuron_idx]
            
            # 计算平滑的ratemap
            ratemap = self.compute_ratemap_kde(all_positions, activations, resolution=resolution)
            
            # 计算自相关图
            autocorr = self.compute_spatial_autocorr(ratemap)
            
            # 计算gridness分数
            gridness = self.compute_gridness(autocorr)
            gridness_scores.append(gridness)
            
            # 绘制ratemap
            ax1 = fig.add_subplot(gridspec[i, 0])
            im1 = ax1.imshow(ratemap, origin='lower', extent=[0, 15, 0, 15], 
                        cmap='viridis', interpolation='bilinear')
            ax1.set_title(f"Neuron {neuron_idx} Ratemap (t=1:)")
            ax1.set_xlabel("X position (m)")
            if i == 0:
                ax1.set_ylabel("Y position (m)")
            
            # 绘制自相关图
            ax2 = fig.add_subplot(gridspec[i, 1])
            im2 = ax2.imshow(autocorr, origin='lower', extent=[-15, 15, -15, 15], 
                        cmap=self.autocorr_cmap, vmin=-1, vmax=1, interpolation='bilinear')
            ax2.set_title(f"Autocorrelation (Gridness: {gridness:.2f})")
            ax2.set_xlabel("X lag (m)")
            if i == 0:
                ax2.set_ylabel("Y lag (m)")
            
            # 只为最后一行添加colorbar
            if i == num_neurons - 1:
                cax = fig.add_subplot(gridspec[:, 2])
                plt.colorbar(im2, cax=cax, label="Correlation")
        
        plt.tight_layout()
        save_path = os.path.join(self.save_dir, f'grid_cells_epoch_{epoch}_{suffix}.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        # 计算平均gridness
        avg_gridness = np.mean(gridness_scores) if gridness_scores else 0.0
        print(f"Epoch {epoch}-{suffix}: Average gridness score of top {num_neurons} neurons: {avg_gridness:.3f}")
        
        # 另外创建一个可视化，展示这些神经元的六边形格子模式
        self.visualize_hexagonal_patterns(selected_neurons, all_positions, all_bottleneck, epoch)
        
        return avg_gridness

    def visualize_hexagonal_patterns(self, selected_neurons, all_positions, all_bottleneck, epoch):
        """
        在自相关图上叠加六边形参考线或六方向径向线，以辅助观察栅格单元的周期性结构。
        注意：输入的 all_positions 和 all_bottleneck 已经排除了第一个时间步
        
        Args:
            selected_neurons: list[int], 选出的神经元索引
            all_positions: shape [S, 2], 收集到的轨迹位置 (X, Y)，已排除第一个时间步
            all_bottleneck: shape [S, bottleneck_size], 对应所有位置的bottleneck层激活，已排除第一个时间步
            epoch: 当前训练轮次
        """
        # 函数内容不变，因为输入数据已经排除了第一个时间步
        import matplotlib.pyplot as plt
        from matplotlib.patches import RegularPolygon

        # 如果神经元数量很多，可以只画前几位
        max_plot = min(len(selected_neurons), 6)

        # 准备画布，行数 = 要展示的神经元数量，列数 = 2（自相关图 & 带网格线的自相关图）
        fig = plt.figure(figsize=(8, max_plot * 3))
        gs = fig.add_gridspec(max_plot, 2, wspace=0.3, hspace=0.4)

        resolution = 50  # 与compute_ratemap_kde中的分辨率相同
        env_size = 15    # 你的环境默认大小（注意保持一致）

        for i, neuron_idx in enumerate(selected_neurons[:max_plot]):
            # 取该神经元所有时间步的激活
            activations = all_bottleneck[:, neuron_idx]

            # 1) 计算ratemap和autocorr
            ratemap = self.compute_ratemap_kde(all_positions, activations, resolution=resolution)
            autocorr = self.compute_spatial_autocorr(ratemap)
            gridness = self.compute_gridness(autocorr)

            # 2) 在左图显示原始自相关图
            ax_left = fig.add_subplot(gs[i, 0])
            extent_val = [-env_size, env_size, -env_size, env_size]  # 因为 correlate2d 时 mode='same' 通常中心对准(0,0)
            im_left = ax_left.imshow(autocorr, origin='lower',
                                    extent=extent_val,
                                    cmap=self.autocorr_cmap, vmin=-1, vmax=1,
                                    interpolation='bilinear')
            ax_left.set_title(f"Neuron {neuron_idx} (t=1:)\nAutocorr (Gridness: {gridness:.2f})")
            ax_left.set_xlabel("X lag (m)")
            ax_left.set_ylabel("Y lag (m)")

            # 3) 在右图叠加六方向线或六边形
            ax_right = fig.add_subplot(gs[i, 1])
            im_right = ax_right.imshow(autocorr, origin='lower',
                                    extent=extent_val,
                                    cmap=self.autocorr_cmap, vmin=-1, vmax=1,
                                    interpolation='bilinear')
            ax_right.set_title("Hex Overlay")
            ax_right.set_xlabel("X lag (m)")
            ax_right.set_ylabel("Y lag (m)")

            # 在中心点画出 6 条射线 (0°, 60°, 120°, 180°, 240°, 300°)，示意网格方向
            center_x = 0.0
            center_y = 0.0
            max_radius = env_size  # 或者可以再小一点

            for angle_deg in [0, 60, 120, 180, 240, 300]:
                angle_rad = np.deg2rad(angle_deg)
                dx = max_radius * np.cos(angle_rad)
                dy = max_radius * np.sin(angle_rad)
                ax_right.plot([center_x, center_x + dx],
                            [center_y, center_y + dy],
                            color='black', linestyle='--', linewidth=1.0)

            # 可选：也可以叠加一个正六边形。比如对半径= max_radius/2
            hex_radius = env_size * 0.5
            hex_patch = RegularPolygon((center_x, center_y),
                                    numVertices=6,
                                    radius=hex_radius,
                                    orientation=-np.pi/2,
                                    fill=False,
                                    edgecolor='blue',
                                    linewidth=1.2)
            ax_right.add_patch(hex_patch)

            # 只在最后一行或最后一个神经元加 colorbar
            if i == max_plot - 1:
                cbar = plt.colorbar(im_right, ax=[ax_left, ax_right], fraction=0.045)
                cbar.set_label("Correlation")

        plt.tight_layout()
        save_path = os.path.join(self.save_dir, f'hex_patterns_epoch_{epoch}.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"Hexagonal pattern visualization saved at: {save_path}")


    def visualize_epoch(self, epoch, model, dataloader, device, metrics_history, suffix=""):
        """
        整合损失和网格细胞可视化
        
        Args:
            epoch: 当前训练轮次
            model: 训练中的网络模型
            dataloader: 数据加载器
            device: 计算设备
            metrics_history: 指标历史记录
            
        Returns:
            avg_gridness: 平均gridness分数
        """
        # 绘制损失曲线
        self.plot_metrics(metrics_history, epoch, suffix)
        
        # 可视化网格细胞
        avg_gridness = self.visualize_grid_cells(model, dataloader, device, epoch, num_batches=10, suffix=suffix)
        
        return avg_gridness

    def plot_metrics(self, metrics_history, epoch, suffix=""):
        """
        绘制损失曲线
        """
        plt.figure(figsize=(15, 5))
        
        plt.subplot(1, 3, 1)
        plt.plot(metrics_history['train_total'], label='Train Total Loss')
        plt.plot(metrics_history['val_total'], label='Val Total Loss')
        plt.title('Total Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()

        plt.subplot(1, 3, 2)
        plt.plot(metrics_history['train_place'], label='Train Place Loss')
        plt.plot(metrics_history['val_place'], label='Val Place Loss')
        plt.title('Place Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()

        plt.subplot(1, 3, 3)
        plt.plot(metrics_history['train_hd'], label='Train HD Loss')
        plt.plot(metrics_history['val_hd'], label='Val HD Loss')
        plt.title('HD Loss')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()

        plt.tight_layout()
        save_path = os.path.join(self.save_dir, f'loss_epoch_{epoch}_{suffix}.png')
        plt.savefig(save_path)
        plt.close()