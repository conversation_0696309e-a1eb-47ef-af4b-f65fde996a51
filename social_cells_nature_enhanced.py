# visualize_social_cells_enhanced_nature.py (采用grid_cell风格的Nature期刊版)
import torch
import numpy as np
import matplotlib.pyplot as plt
import os
import argparse
from tqdm import tqdm
from scipy.ndimage import gaussian_filter
import multiprocessing as mp
from dataclasses import dataclass, field
from typing import Dict, List, Tuple
import time
from collections import defaultdict

# --- 导入你项目中的模块 ---
from config import Config
from models.social_grid_cell import SocialGridCellNetwork
from models.place_hd_cells import PlaceCellEnsemble, HeadDirectionCellEnsemble

# 设置期刊级别的matplotlib风格 - 采用grid_cell_visualization-2.py的设置
plt.style.use('default')
plt.rcParams.update({
    'font.family': 'sans-serif',
    'font.sans-serif': ['Arial', 'Helvetica', 'DejaVu Sans'],
    'font.size': 10,
    'axes.labelsize': 12,
    'axes.titlesize': 12,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 8,
    'figure.titlesize': 16,
    'figure.dpi': 150,  # 高DPI设置
    'axes.linewidth': 0.8,
    'xtick.major.width': 0.8,
    'ytick.major.width': 0.8,
    'axes.spines.top': False,
    'axes.spines.right': False,
})

# --- 数据结构定义 ---
@dataclass
class ConditionAnalysis:
    peak_activation: float = 0.0
    activations: np.ndarray = field(default_factory=lambda: np.array([]))
    positions_to_plot: np.ndarray = field(default_factory=lambda: np.array([]))
    trajectory_key: str = "self"

@dataclass
class NeuronAnalysisResult:
    neuron_idx: int
    cell_type: str = "Unclassified"
    condition_data: Dict[int, ConditionAnalysis] = field(default_factory=dict)

# --- 细胞分类器 ---
class CellTypeDetector:
    @staticmethod
    def classify(peak_results: Dict[int, float]) -> str:
        c1, c2, c3, c4 = peak_results[1], peak_results[2], peak_results[3], peak_results[4]
        high_thresh, low_thresh = 0.5, 0.2
        is_responsive_to_self = c1 > high_thresh
        is_responsive_to_peer = c2 > high_thresh
        is_silent_when_static = c4 < low_thresh

        if not is_silent_when_static:
            return "Other (Active when static)"
        if is_responsive_to_self and is_responsive_to_peer:
            return "Special SPC"
        elif is_responsive_to_self and not is_responsive_to_peer:
            return "Pure Place Cell" if c3 > high_thresh else "Mixed Response"
        elif not is_responsive_to_self and is_responsive_to_peer:
            return "Pure SPC" if c3 > high_thresh else "Mixed Response"
        else:
            return "Other"

# --- 轨迹生成器 ---
class TrajectoryGenerator:
    def __init__(self, config: Config):
        self.maze_size = config.ENV_SIZE
        self.seq_len = config.SEQUENCE_LENGTH
        self.start_S = np.array([self.maze_size * 0.5, self.maze_size * 0.2])
        self.end_A = np.array([self.maze_size * 0.2, self.maze_size * 0.8])
        self.end_B = np.array([self.maze_size * 0.8, self.maze_size * 0.8])

    def _generate_path(self, start: np.ndarray, end: np.ndarray, moving: bool) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        if not moving:
            positions = np.tile(start, (self.seq_len, 1))
            velocities = np.zeros((self.seq_len, 2))
            angular_velocities = np.zeros(self.seq_len)
            return positions, velocities, angular_velocities

        # 创建基础直线路径
        base_positions = np.array([start + (t / max(1, self.seq_len - 1)) * (end - start) for t in range(self.seq_len)])
        
        # 创建平滑的随机扰动
        direction = (end - start) / (np.linalg.norm(end - start) + 1e-6)
        perpendicular = np.array([-direction[1], direction[0]])
        
        # 随机正弦波参数
        amplitude = np.random.uniform(0.5, 1.5)
        frequency = np.random.uniform(1.5, 2.5)
        phase = np.random.uniform(0, np.pi)
        
        t = np.linspace(0, 1, self.seq_len)
        sine_offset = amplitude * np.sin(2 * np.pi * frequency * t + phase)
        
        # 将扰动应用到垂直于运动方向的轴上
        positions = base_positions + sine_offset[:, np.newaxis] * perpendicular[np.newaxis, :]
        positions = np.clip(positions, 0.5, self.maze_size - 0.5)

        velocities = np.diff(positions, axis=0, prepend=positions[0:1])
        angles = np.arctan2(velocities[:, 1], velocities[:, 0])
        angular_velocities = np.diff(np.unwrap(angles), prepend=angles[0:1])
        return positions, velocities, angular_velocities

    def get_trajectories_for_condition(self, condition: int, num_reps: int = 20) -> Dict:
        all_self_pos, all_self_vel, all_self_ang_vel = [], [], []
        all_peer_pos, all_peer_vel, all_peer_ang_vel = [], [], []
        movement_patterns = {1: (True, False), 2: (False, True), 3: (True, True), 4: (False, False)}
        self_moving, peer_moving = movement_patterns[condition]
        
        for i in range(num_reps):
            target = self.end_A if i % 2 == 0 else self.end_B
            static_pos = self.start_S
            
            self_start, self_end = (self.start_S, target) if self_moving else (static_pos, static_pos)
            peer_start, peer_end = (self.start_S, target) if peer_moving else (static_pos, static_pos)

            self_pos, self_vel, self_ang_vel = self._generate_path(self_start, self_end, self_moving)
            peer_pos, peer_vel, peer_ang_vel = self._generate_path(peer_start, peer_end, peer_moving)
            
            all_self_pos.append(self_pos); all_self_vel.append(self_vel); all_self_ang_vel.append(self_ang_vel)
            all_peer_pos.append(peer_pos); all_peer_vel.append(peer_vel); all_peer_ang_vel.append(peer_ang_vel)
        
        return {
            'self_pos': np.array(all_self_pos), 'self_vel': np.array(all_self_vel), 'self_ang_vel': np.array(all_self_ang_vel),
            'peer_pos': np.array(all_peer_pos), 'peer_vel': np.array(all_peer_vel), 'peer_ang_vel': np.array(all_peer_ang_vel),
        }

def create_enhanced_heatmap(positions, activations, grid_size, env_size, sigma=1.5, vmin_bound=0.0, vmax_bound=0.8):
    """创建增强热图，采用occupancy-weighted方法，并限制数值范围"""
    heatmap, counts = np.zeros((grid_size, grid_size)), np.zeros((grid_size, grid_size))
    
    # 对激活值进行范围限制
    clipped_activations = np.clip(activations, vmin_bound, vmax_bound)
    
    for traj_idx in range(positions.shape[0]):
        for step_idx in range(positions.shape[1]):
            pos = positions[traj_idx, step_idx]
            act = clipped_activations[traj_idx, step_idx]  # 使用限制后的激活值
            grid_x = int(np.clip((pos[0] / env_size) * grid_size, 0, grid_size - 1))
            grid_y = int(np.clip((pos[1] / env_size) * grid_size, 0, grid_size - 1))
            heatmap[grid_y, grid_x] += act
            counts[grid_y, grid_x] += 1
    
    # Occupancy-weighted平均
    with np.errstate(divide='ignore', invalid='ignore'):
        avg_heatmap = np.where(counts > 0, heatmap / counts, 0)
    
    # 高斯平滑
    return gaussian_filter(avg_heatmap, sigma=sigma)

def visualize_neuron(args_tuple):
    """
    生成采用grid_cell风格的Nature期刊级别可视化图
    - 使用jet colormap
    - 高质量PDF输出
    - 白色轨迹线配黑色边框
    - 固定的颜色范围设置 (0-0.8)
    """
    result, config_dict, output_dir, vmin_global, vmax_global = args_tuple
    config = Config.from_dict(config_dict)
    
    neuron_idx = result.neuron_idx
    
    # 创建 1x4 的子图布局
    fig, axes = plt.subplots(1, 4, figsize=(18, 5))
    fig.suptitle(f'Neuron {neuron_idx} - {result.cell_type}', 
                fontsize=16, fontweight='bold', y=0.95)
    
    # 使用预设的固定颜色范围
    vmin, vmax = vmin_global, vmax_global
    
    # 条件标题映射
    title_map = {
        1: "Self Moving\nPeer Static", 
        2: "Peer Moving\nSelf Static", 
        3: "Both Moving", 
        4: "Both Static"
    }

    for i, condition in enumerate([1, 2, 3, 4]):
        ax = axes[i]
        data = result.condition_data[condition]
        
        # 创建高质量热图，应用数值范围限制
        smoothed_heatmap = create_enhanced_heatmap(
            data.positions_to_plot, 
            data.activations, 
            grid_size=50, 
            env_size=config.ENV_SIZE,
            vmin_bound=vmin,
            vmax_bound=vmax
        )
        
        # 使用jet colormap，与grid_cell_visualization-2.py保持一致
        im = ax.imshow(smoothed_heatmap, 
                      cmap='jet',  # 采用经典的jet colormap
                      origin='lower',
                      extent=[0, config.ENV_SIZE, 0, config.ENV_SIZE], 
                      vmin=vmin, 
                      vmax=vmax, 
                      aspect='equal',
                      interpolation='nearest')  # 保持清晰度
        
        # 绘制轨迹 - 使用白色线条配黑色边框（在jet colormap下清晰可见）
        for traj_idx in range(min(3, data.positions_to_plot.shape[0])):
            path = data.positions_to_plot[traj_idx]
            
            # 白色轨迹线配细黑边框
            ax.plot(path[:, 0], path[:, 1], color='white', alpha=0.9, linewidth=2.5, zorder=2)
            ax.plot(path[:, 0], path[:, 1], color='black', alpha=0.8, linewidth=1.0, zorder=1)
            
            if traj_idx == 0:  # 只在第一条轨迹上标记起点和终点
                # 起点 - 亮绿色圆点
                ax.scatter(path[0, 0], path[0, 1], color='lime', s=35, zorder=3, 
                          edgecolors='black', linewidth=1, label='Start')
                # 终点 - 红色方块
                ax.scatter(path[-1, 0], path[-1, 1], color='red', s=35, zorder=3, 
                          marker='s', edgecolors='black', linewidth=1, label='End')

        # 设置标题和标签 - 显示实际峰值和限制范围
        actual_peak = data.peak_activation
        clipped_peak = np.clip(actual_peak, vmin, vmax)
        peak_info = f"Peak: {clipped_peak:.3f}"
        if actual_peak != clipped_peak:
            peak_info += f" (raw: {actual_peak:.3f})"
        
        ax.set_title(f'{title_map[condition]}\n{peak_info}', 
                    fontsize=12, pad=15)
        
        ax.set_xlabel('X Position (m)', fontsize=11)
        if i == 0:
            ax.set_ylabel('Y Position (m)', fontsize=11)
            ax.legend(loc='upper right', frameon=True, fancybox=False, 
                     shadow=False, fontsize=8, framealpha=0.9)
        
        # 设置坐标轴
        ax.set_xlim(0, config.ENV_SIZE)
        ax.set_ylim(0, config.ENV_SIZE)
        ax.set_xticks(np.linspace(0, config.ENV_SIZE, 6))
        ax.set_yticks(np.linspace(0, config.ENV_SIZE, 6))
        
        # 添加白色网格线（在jet colormap下清晰可见）
        ax.grid(True, linestyle=':', alpha=0.6, color='white', linewidth=0.8)
        
        # 优化坐标轴
        ax.tick_params(direction='out', length=4, width=0.8)

    # 调整布局并添加colorbar
    plt.subplots_adjust(left=0.06, bottom=0.15, right=0.88, top=0.85, wspace=0.25)
    
    # 创建colorbar
    cbar_ax = fig.add_axes([0.90, 0.15, 0.02, 0.70])
    cbar = fig.colorbar(im, cax=cbar_ax)
    cbar.set_label('Neuronal Activation', rotation=270, labelpad=18, fontsize=12)
    cbar.ax.tick_params(labelsize=10)

    # 保存为高质量PDF
    cell_type_clean = result.cell_type.lower().replace(" ", "_").replace("(", "").replace(")", "")
    filename = f'neuron_{neuron_idx:03d}_{cell_type_clean}.pdf'
    save_path = os.path.join(output_dir, filename)
    
    plt.savefig(save_path, format='pdf', bbox_inches='tight', 
                dpi=300, facecolor='white', edgecolor='none')
    plt.close(fig)

def main():
    parser = argparse.ArgumentParser(description="Enhanced Social Cell Visualization with Grid Cell Style")
    parser.add_argument('--model_path', type=str, required=True, help='Path to the trained best_model.pth file.')
    parser.add_argument('--output_dir', type=str, default='social_cell_viz_grid_style', 
                       help='Directory to save visualizations.')
    parser.add_argument('--workers', type=int, default=max(1, mp.cpu_count() - 2), 
                       help='Number of parallel workers for plotting.')
    # 添加激活值范围参数
    parser.add_argument('--vmin', type=float, default=0, 
                       help='Minimum activation value for colormap (default: 0.0)')
    parser.add_argument('--vmax', type=float, default=1, 
                       help='Maximum activation value for colormap (default: 0.8)')
    args = parser.parse_args()

    start_time = time.time()
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 打印设置的激活值范围
    print(f"Activation value range: [{args.vmin:.1f}, {args.vmax:.1f}]")
    print(f"Values outside this range will be clipped to bounds.")
    
    config = Config()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    print("Loading model...")
    place_cells = PlaceCellEnsemble(
        n_cells=config.PLACE_CELLS_N, 
        scale=config.PLACE_CELLS_SCALE, 
        pos_min=0, pos_max=config.ENV_SIZE, 
        seed=config.SEED
    )
    hd_cells = HeadDirectionCellEnsemble(
        n_cells=config.HD_CELLS_N, 
        concentration=config.HD_CELLS_CONCENTRATION, 
        seed=config.SEED
    )
    
    model_config = {
        'HIDDEN_SIZE': config.HIDDEN_SIZE,
        'LATENT_DIM': config.LATENT_DIM,
        'dropout_rate': config.DROPOUT_RATE,
        'ego_token_size': getattr(config, 'ego_token_size', 4)
    }
    model = SocialGridCellNetwork(place_cells, hd_cells, model_config)
    
    # 加载模型权重
    state_dict = torch.load(args.model_path, map_location=device, weights_only=False)
    if all(key.startswith('module.') for key in state_dict.keys()):
        state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}
    model.load_state_dict(state_dict)
    model.to(device)
    model.eval()

    num_neurons = config.LATENT_DIM
    trajectory_gen = TrajectoryGenerator(config)
    all_results = {i: NeuronAnalysisResult(neuron_idx=i) for i in range(num_neurons)}

    print(f"Analyzing {num_neurons} neurons...")
    with torch.no_grad():
        for condition in tqdm(range(1, 5), desc="Processing Conditions"):
            trajs = trajectory_gen.get_trajectories_for_condition(condition, num_reps=100)
            
            # 准备输入数据
            self_vel_input = torch.cat([
                torch.from_numpy(trajs['self_vel']).float(), 
                torch.from_numpy(trajs['self_ang_vel']).float().unsqueeze(-1)
            ], dim=-1).to(device)
            
            self_init_pos = torch.from_numpy(trajs['self_pos'][:, 0, :]).float().to(device)
            self_init_hd = torch.from_numpy(np.arctan2(trajs['self_vel'][:, 0, 1], trajs['self_vel'][:, 0, 0])).float().to(device)
            
            peer_vel_input = torch.cat([
                torch.from_numpy(trajs['peer_vel']).float(), 
                torch.from_numpy(trajs['peer_ang_vel']).float().unsqueeze(-1)
            ], dim=-1).to(device)
            
            peer_init_pos = torch.from_numpy(trajs['peer_pos'][:, 0, :]).float().to(device)
            peer_init_hd = torch.from_numpy(np.arctan2(trajs['peer_vel'][:, 0, 1], trajs['peer_vel'][:, 0, 0])).float().to(device)

            # 前向传播
            outputs = model(self_vel_input, self_init_pos, self_init_hd, peer_vel_input, peer_init_pos, peer_init_hd)
            all_activations = outputs['bottleneck_self'].cpu().numpy()

            # 存储激活值
            for i in range(num_neurons):
                neuron_activations = all_activations[:, :, i]
                all_results[i].condition_data[condition] = ConditionAnalysis(
                    peak_activation=np.max(neuron_activations),
                    activations=neuron_activations,
                    positions_to_plot={'self': trajs['self_pos'], 'peer': trajs['peer_pos']}
                )

    print("Classifying neurons...")
    trajectory_map = {
        "Pure SPC": {1: "self", 2: "peer", 3: "peer", 4: "peer"},
        "Special SPC": {1: "self", 2: "peer", 3: "self", 4: "peer"},
        "Pure Place Cell": {1: "self", 2: "peer", 3: "self", 4: "peer"},
        "Mixed Response": {1: "self", 2: "peer", 3: "self", 4: "peer"},
        "Other": {1: "self", 2: "peer", 3: "self", 4: "peer"},
        "Other (Active when static)": {1: "self", 2: "peer", 3: "self", 4: "peer"}
    }
    
    categorized_cells = defaultdict(list)
    activation_stats = []  # 用于统计激活值分布
    
    for i in range(num_neurons):
        res = all_results[i]
        peak_results = {cond: data.peak_activation for cond, data in res.condition_data.items()}
        res.cell_type = CellTypeDetector.classify(peak_results)
        
        # 收集激活值统计信息
        for cond_num, data in res.condition_data.items():
            activation_stats.extend(data.activations.flatten())
            plot_key = trajectory_map[res.cell_type][cond_num]
            data.trajectory_key = plot_key
            data.positions_to_plot = data.positions_to_plot[plot_key]

        categorized_cells[res.cell_type].append(res)

    # 打印激活值统计信息
    activation_stats = np.array(activation_stats)
    print(f"\nActivation statistics:")
    print(f"  Range: [{activation_stats.min():.3f}, {activation_stats.max():.3f}]")
    print(f"  Mean: {activation_stats.mean():.3f}, Std: {activation_stats.std():.3f}")
    print(f"  Percentiles: 25%={np.percentile(activation_stats, 25):.3f}, "
          f"75%={np.percentile(activation_stats, 75):.3f}, "
          f"95%={np.percentile(activation_stats, 95):.3f}")
    
    # 计算有多少值会被clipping
    clipped_low = np.sum(activation_stats < args.vmin)
    clipped_high = np.sum(activation_stats > args.vmax)
    total_values = len(activation_stats)
    print(f"  Clipping: {clipped_low}/{total_values} ({clipped_low/total_values*100:.1f}%) values < {args.vmin}")
    print(f"           {clipped_high}/{total_values} ({clipped_high/total_values*100:.1f}%) values > {args.vmax}")

    print("\n--- Analysis Complete ---")
    for cell_type, results in categorized_cells.items():
        print(f"Found {len(results)} neurons of type: {cell_type}")

    print(f"\nGenerating visualizations with fixed range [{args.vmin}, {args.vmax}]...")
    tasks = []
    for cell_type, results in categorized_cells.items():
        if "Other" not in cell_type:  # 只可视化有意义的细胞类型
            for result in results:
                # 传递vmin和vmax参数
                tasks.append((result, config.to_dict(), args.output_dir, args.vmin, args.vmax))
    
    if tasks:
        print(f"Creating {len(tasks)} enhanced figures...")
        with mp.Pool(processes=args.workers) as pool:
            list(tqdm(pool.imap_unordered(visualize_neuron, tasks), 
                     total=len(tasks), desc="Saving Enhanced PDFs"))

    total_time = time.time() - start_time
    print(f"\nAll enhanced visualizations saved in '{args.output_dir}'")
    print(f"Colormap range: [{args.vmin}, {args.vmax}] (fixed across all figures)")
    print(f"Total execution time: {total_time:.2f} seconds.")

if __name__ == '__main__':
    try:
        mp.set_start_method('spawn', force=True)
    except RuntimeError:
        pass
    main()