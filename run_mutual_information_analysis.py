#!/usr/bin/env python3
"""
运行互信息分析的主脚本
第三步：计算互信息并进行可视化

使用方法:
python run_mutual_information_analysis.py --model_path logs_social_grid_cells_ddp/20250801_163726/checkpoints/best_model.pth --output_dir mi_analysis_results --workers 32

或者使用rational head模型:
python run_mutual_information_analysis.py --model_path logs_social_grid_cells_ddp/20250802_230022/checkpoints/best_model.pth --output_dir mi_analysis_results_rational --workers 32
"""

import os
import sys
import argparse
import torch
import numpy as np
from collections import defaultdict
from tqdm import tqdm

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from config import Config
from models.social_grid_cell import SocialGridCellNetwork
from models.place_hd_cells import PlaceCellEnsemble, HeadDirectionCellEnsemble
from utils.mutual_information_analysis import run_mutual_information_analysis


# 复制细胞分类器（从已有代码中）
class CellTypeDetector:
    @staticmethod
    def classify(peak_results: dict) -> str:
        c1, c2, c3, c4 = peak_results[1], peak_results[2], peak_results[3], peak_results[4]
        high_thresh, low_thresh = 0.5, 0.2
        is_responsive_to_self = c1 > high_thresh
        is_responsive_to_peer = c2 > high_thresh
        is_silent_when_static = c4 < low_thresh

        if not is_silent_when_static:
            return "Other (Active when static)"
        if is_responsive_to_self and is_responsive_to_peer:
            return "Special SPC"
        elif is_responsive_to_self and not is_responsive_to_peer:
            return "Pure Place Cell" if c3 > high_thresh else "Mixed Response"
        elif not is_responsive_to_self and is_responsive_to_peer:
            return "Pure SPC" if c3 > high_thresh else "Mixed Response"
        else:
            return "Other"


# 复制轨迹生成器（从已有代码中）
class TrajectoryGenerator:
    def __init__(self, config: Config):
        self.maze_size = config.ENV_SIZE
        self.seq_len = config.SEQUENCE_LENGTH
        self.start_S = np.array([self.maze_size * 0.5, self.maze_size * 0.2])
        self.end_A = np.array([self.maze_size * 0.2, self.maze_size * 0.8])
        self.end_B = np.array([self.maze_size * 0.8, self.maze_size * 0.8])

    def _generate_path(self, start: np.ndarray, end: np.ndarray, moving: bool):
        if not moving:
            positions = np.tile(start, (self.seq_len, 1))
            velocities = np.zeros((self.seq_len, 2))
            angular_velocities = np.zeros(self.seq_len)
            return positions, velocities, angular_velocities

        # 创建基础直线路径
        base_positions = np.array([start + (t / max(1, self.seq_len - 1)) * (end - start) for t in range(self.seq_len)])
        
        # 创建平滑的随机扰动
        direction = (end - start) / (np.linalg.norm(end - start) + 1e-6)
        perpendicular = np.array([-direction[1], direction[0]])
        
        # 随机正弦波参数
        amplitude = np.random.uniform(0.5, 1.5)
        frequency = np.random.uniform(1.5, 2.5)
        phase = np.random.uniform(0, np.pi)
        
        t = np.linspace(0, 1, self.seq_len)
        sine_offset = amplitude * np.sin(2 * np.pi * frequency * t + phase)
        
        # 将扰动应用到垂直于运动方向的轴上
        positions = base_positions + sine_offset[:, np.newaxis] * perpendicular[np.newaxis, :]
        positions = np.clip(positions, 0.5, self.maze_size - 0.5)

        velocities = np.diff(positions, axis=0, prepend=positions[0:1])
        angles = np.arctan2(velocities[:, 1], velocities[:, 0])
        angular_velocities = np.diff(np.unwrap(angles), prepend=angles[0:1])
        return positions, velocities, angular_velocities

    def get_trajectories_for_condition(self, condition: int, num_reps: int = 20):
        all_self_pos, all_self_vel, all_self_ang_vel = [], [], []
        all_peer_pos, all_peer_vel, all_peer_ang_vel = [], [], []
        movement_patterns = {1: (True, False), 2: (False, True), 3: (True, True), 4: (False, False)}
        self_moving, peer_moving = movement_patterns[condition]

        for i in range(num_reps):
            target = self.end_A if i % 2 == 0 else self.end_B
            static = self.start_S
            
            self_start, self_end = (self.start_S, target) if self_moving else (static, static)
            peer_start, peer_end = (self.start_S, target) if peer_moving else (static, static)
            
            self_pos, self_vel, self_ang_vel = self._generate_path(self_start, self_end, self_moving)
            peer_pos, peer_vel, peer_ang_vel = self._generate_path(peer_start, peer_end, peer_moving)
            
            all_self_pos.append(self_pos)
            all_self_vel.append(self_vel)
            all_self_ang_vel.append(self_ang_vel)
            all_peer_pos.append(peer_pos)
            all_peer_vel.append(peer_vel)
            all_peer_ang_vel.append(peer_ang_vel)

        return {
            'self_pos': np.array(all_self_pos),
            'self_vel': np.array(all_self_vel),
            'self_ang_vel': np.array(all_self_ang_vel),
            'peer_pos': np.array(all_peer_pos),
            'peer_vel': np.array(all_peer_vel),
            'peer_ang_vel': np.array(all_peer_ang_vel)
        }


class AnalysisResult:
    def __init__(self, neuron_idx: int):
        self.neuron_idx = neuron_idx
        self.condition_data = {}
        self.cell_type = None


class ConditionData:
    def __init__(self):
        self.activations = None
        self.peak_activation = 0.0
        self.positions_to_plot = {}


def collect_cell_classifications(model, config, device, num_reps: int = 100):
    """
    收集细胞分类数据（复制自已有代码的逻辑）
    """
    print("Collecting cell classification data...")
    
    trajectory_gen = TrajectoryGenerator(config)
    
    # 获取神经元数量
    if hasattr(model, 'module'):
        num_neurons = model.module.relational_size
    else:
        num_neurons = model.relational_size
    
    all_results = [AnalysisResult(i) for i in range(num_neurons)]
    
    model.eval()
    with torch.no_grad():
        for condition in tqdm(range(1, 5), desc="Processing Conditions"):
            trajs = trajectory_gen.get_trajectories_for_condition(condition, num_reps=num_reps)
            
            # 准备输入数据
            self_vel_input = torch.cat([
                torch.from_numpy(trajs['self_vel']).float(), 
                torch.from_numpy(trajs['self_ang_vel']).float().unsqueeze(-1)
            ], dim=-1).to(device)
            
            self_init_pos = torch.from_numpy(trajs['self_pos'][:, 0, :]).float().to(device)
            self_init_hd = torch.from_numpy(np.arctan2(trajs['self_vel'][:, 0, 1], trajs['self_vel'][:, 0, 0])).float().to(device)
            
            peer_vel_input = torch.cat([
                torch.from_numpy(trajs['peer_vel']).float(), 
                torch.from_numpy(trajs['peer_ang_vel']).float().unsqueeze(-1)
            ], dim=-1).to(device)
            
            peer_init_pos = torch.from_numpy(trajs['peer_pos'][:, 0, :]).float().to(device)
            peer_init_hd = torch.from_numpy(np.arctan2(trajs['peer_vel'][:, 0, 1], trajs['peer_vel'][:, 0, 0])).float().to(device)
            
            # 前向传播
            outputs = model(self_vel_input, self_init_pos, self_init_hd, peer_vel_input, peer_init_pos, peer_init_hd)
            
            # 获取激活
            if 'relational_activations' in outputs:
                activations = outputs['relational_activations'].cpu().numpy()
            elif 'bottleneck_activations' in outputs:
                activations = outputs['bottleneck_activations'].cpu().numpy()
            else:
                raise ValueError("Cannot find activation data in model outputs")
            
            # 处理每个神经元
            for i in range(num_neurons):
                neuron_activations = activations[:, :, i]
                peak_activation = np.max(neuron_activations)
                
                condition_data = ConditionData()
                condition_data.activations = neuron_activations
                condition_data.peak_activation = peak_activation
                condition_data.positions_to_plot = {
                    'self': trajs['self_pos'],
                    'peer': trajs['peer_pos']
                }
                
                all_results[i].condition_data[condition] = condition_data
    
    # 分类神经元
    categorized_cells = defaultdict(list)
    
    for i in range(num_neurons):
        res = all_results[i]
        peak_results = {cond: data.peak_activation for cond, data in res.condition_data.items()}
        res.cell_type = CellTypeDetector.classify(peak_results)
        categorized_cells[res.cell_type].append(res)
    
    print("\n--- Cell Classification Results ---")
    for cell_type, results in categorized_cells.items():
        print(f"Found {len(results)} neurons of type: {cell_type}")
    
    return categorized_cells


def main():
    parser = argparse.ArgumentParser(description='Run mutual information analysis')
    parser.add_argument('--model_path', type=str, required=True,
                       help='Path to the trained model checkpoint')
    parser.add_argument('--output_dir', type=str, default='mi_analysis_results',
                       help='Output directory for results')
    parser.add_argument('--num_reps', type=int, default=200,
                       help='Number of trajectory repetitions for data collection')
    parser.add_argument('--workers', type=int, default=32,
                       help='Number of worker processes (for compatibility)')
    
    args = parser.parse_args()
    
    print("="*60)
    print("MUTUAL INFORMATION ANALYSIS")
    print("="*60)
    print(f"Model path: {args.model_path}")
    print(f"Output directory: {args.output_dir}")
    print(f"Number of repetitions: {args.num_reps}")
    print("="*60)
    
    # 检查模型文件是否存在
    if not os.path.exists(args.model_path):
        print(f"Error: Model file not found: {args.model_path}")
        sys.exit(1)
    
    # 加载配置和模型
    config = Config()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 加载模型
    place_cells = PlaceCellEnsemble(
        n_cells=config.PLACE_CELLS_N, 
        scale=config.PLACE_CELLS_SCALE, 
        pos_min=0, pos_max=config.ENV_SIZE, 
        seed=config.SEED
    )
    hd_cells = HeadDirectionCellEnsemble(
        n_cells=config.HD_CELLS_N, 
        concentration=config.HD_CELLS_CONCENTRATION, 
        seed=config.SEED
    )
    
    model_config = {
        'HIDDEN_SIZE': config.HIDDEN_SIZE,
        'LATENT_DIM': config.LATENT_DIM,
        'dropout_rate': config.DROPOUT_RATE,
        'ego_token_size': getattr(config, 'ego_token_size', 4)
    }
    
    model = SocialGridCellNetwork(
        place_cells=place_cells,
        hd_cells=hd_cells,
        **model_config
    ).to(device)
    
    # 加载权重
    checkpoint = torch.load(args.model_path, map_location=device)
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)
    
    print("Model loaded successfully!")
    
    # 第一步：收集细胞分类
    print("\n" + "="*50)
    print("STEP 1: Cell Classification")
    print("="*50)
    categorized_cells = collect_cell_classifications(model, config, device, num_reps=100)
    
    # 第二步：运行互信息分析
    print("\n" + "="*50)
    print("STEP 2: Mutual Information Analysis")
    print("="*50)
    mi_results = run_mutual_information_analysis(
        args.model_path, args.output_dir, categorized_cells, args.num_reps)
    
    print("\n" + "="*60)
    print("ANALYSIS COMPLETED SUCCESSFULLY!")
    print("="*60)
    print(f"All results saved to: {args.output_dir}")
    print("\nGenerated files:")
    print("- double_dissociation_plot.png: 双重解离可视化")
    print("- detailed_mi_analysis.png: 详细互信息分析")
    print("- mutual_information_report.md: 统计报告")


if __name__ == "__main__":
    main()
