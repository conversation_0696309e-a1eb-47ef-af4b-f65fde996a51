# circle_visualize_social_cells.py (完全修正版)
import torch
import numpy as np
import matplotlib.pyplot as plt
import os
import argparse
from tqdm import tqdm
from scipy.ndimage import gaussian_filter
import multiprocessing as mp
from dataclasses import dataclass, field
from typing import Dict, List, Tuple
import time
from collections import defaultdict

# --- 导入你项目中的模块 ---
from config import Config
from models.social_grid_cell import SocialGridCellNetwork
from models.place_hd_cells import PlaceCellEnsemble, HeadDirectionCellEnsemble

# --- 数据结构定义 ---
@dataclass
class ConditionAnalysis:
    peak_activation: float = 0.0
    activations: np.ndarray = field(default_factory=lambda: np.array([]))
    positions_to_plot: np.ndarray = field(default_factory=lambda: np.array([]))
    trajectory_key: str = "self"

@dataclass
class NeuronAnalysisResult:
    neuron_idx: int
    cell_type: str = "Unclassified"
    condition_data: Dict[int, ConditionAnalysis] = field(default_factory=dict)

# --- 修正的细胞分类器 ---
class CellTypeDetector:
    @staticmethod
    def classify(peak_results: Dict[int, float]) -> str:
        """
        根据bat hippocampus论文的标准分类：
        
        Pure SPC (Social Place Cell):
        - 条件1 (Peer静止, Self运动): 弱激活
        - 条件2 (Peer运动, Self静止): 强激活  
        - 条件3 (Both运动): 强激活
        - 条件4 (Both静止): 弱激活
        
        Special SPC (既有PC又有SPC特性):
        - 条件1: 强激活 (PC特性)
        - 条件2: 强激活 (SPC特性)
        - 条件3: 强激活 (两种特性)
        - 条件4: 弱激活
        
        Pure Place Cell:
        - 条件1: 强激活 (Self运动)
        - 条件2: 弱激活 (Self静止)
        - 条件3: 强激活 (Self运动)
        - 条件4: 弱激活 (Self静止)
        """
        c1, c2, c3, c4 = peak_results[1], peak_results[2], peak_results[3], peak_results[4]
        
        # 调整阈值
        high_thresh = 0.08  # 强激活阈值
        low_thresh = 0.03   # 弱激活阈值
        
        print(f"  Activation pattern: C1={c1:.3f}, C2={c2:.3f}, C3={c3:.3f}, C4={c4:.3f}")
        
        # Pure Place Cell: C1和C3强(Self运动时), C2和C4弱(Self静止时)
        is_pure_pc = (c1 > high_thresh and c2 < low_thresh and c3 > high_thresh and c4 < low_thresh)
        
        # Pure SPC: C1弱(Peer静止时), C2和C3强(Peer运动时), C4弱(Both静止时)
        is_pure_spc = (c1 < low_thresh and c2 > high_thresh and c3 > high_thresh and c4 < low_thresh)
        
        # Special SPC: C1,C2,C3都强(既响应Self又响应Peer), C4弱
        is_special_spc = (c1 > high_thresh and c2 > high_thresh and c3 > high_thresh and c4 < low_thresh)
        
        # 添加更宽松的条件
        # 如果主要响应运动条件(C1,C2,C3)而不响应静止条件(C4)
        movement_responsive = (c1 > 0.04 or c2 > 0.04 or c3 > 0.04) and c4 < 0.05
        
        if is_special_spc:
            return "Special SPC"
        elif is_pure_pc:
            return "Pure Place Cell"
        elif is_pure_spc:
            return "Pure SPC"
        elif movement_responsive:
            # 进一步区分弱响应的类型
            if c1 > c2 and c3 > c2:  # 主要响应Self运动
                return "Weak Place Cell"
            elif c2 > c1 and c3 > c1:  # 主要响应Peer运动
                return "Weak SPC"
            else:
                return "Mixed Response"
        else:
            return "Other"

class TrajectoryGenerator:
    def __init__(self, config: Config):
        self.maze_size = config.ENV_SIZE
        self.seq_len = config.SEQUENCE_LENGTH
        # 重新定义位置 - S在底部中央，A和B都在顶部中央
        self.start_S = np.array([8.0, 4.0])  # 起点S
        self.end_A = np.array([8.0, 12.0])   # 终点A (通过左半圆)
        self.end_B = np.array([8.0, 12.0])   # 终点B (通过右半圆)

    def _generate_arc_path(self, start: np.ndarray, end: np.ndarray, arc_type: str, moving: bool) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """生成半圆弧轨迹路径"""
        if not moving:
            # 静止：位置完全不变，速度和角速度都为0
            positions = np.tile(start, (self.seq_len, 1))
            velocities = np.zeros((self.seq_len, 2))
            angular_velocities = np.zeros(self.seq_len)
            return positions, velocities, angular_velocities
        
        # 计算半圆弧路径
        center_x = (start[0] + end[0]) / 2  # 圆心x坐标
        center_y = (start[1] + end[1]) / 2  # 圆心y坐标
        radius = np.linalg.norm(end - start) / 2  # 半径
        
        # 添加随机扰动
        np.random.seed(hash(arc_type) % 2**32)  # 基于arc_type的固定种子，保证可重复性
        radius_noise = np.random.uniform(0.8, 1.2)  # 半径随机扰动
        center_offset = np.random.uniform(-0.5, 0.5, 2)  # 圆心随机偏移
        
        radius *= radius_noise
        center_x += center_offset[0] 
        center_y += center_offset[1]
        
        positions = []
        
        if arc_type == "left":
            # 左半圆：从start到end，经过左侧
            start_angle = np.arctan2(start[1] - center_y, start[0] - center_x)
            end_angle = np.arctan2(end[1] - center_y, end[0] - center_x)
            
            # 确保走左半圆（逆时针方向）
            if end_angle <= start_angle:
                end_angle += 2 * np.pi
            
            # 添加角度随机扰动
            angle_noise = np.random.uniform(-0.3, 0.3, self.seq_len)
            
            for i in range(self.seq_len):
                t = i / max(1, self.seq_len - 1)
                angle = start_angle + t * (end_angle - start_angle) + angle_noise[i]
                
                # 添加径向随机扰动
                r_noise = radius * (1 + np.random.uniform(-0.1, 0.1))
                
                x = center_x + r_noise * np.cos(angle)
                y = center_y + r_noise * np.sin(angle)
                positions.append([x, y])
                
        else:  # arc_type == "right"
            # 右半圆：从start到end，经过右侧
            start_angle = np.arctan2(start[1] - center_y, start[0] - center_x)
            end_angle = np.arctan2(end[1] - center_y, end[0] - center_x)
            
            # 确保走右半圆（顺时针方向）
            if start_angle <= end_angle:
                start_angle += 2 * np.pi
                
            # 添加角度随机扰动
            angle_noise = np.random.uniform(-0.3, 0.3, self.seq_len)
            
            for i in range(self.seq_len):
                t = i / max(1, self.seq_len - 1)
                angle = start_angle + t * (end_angle - start_angle) + angle_noise[i]
                
                # 添加径向随机扰动
                r_noise = radius * (1 + np.random.uniform(-0.1, 0.1))
                
                x = center_x + r_noise * np.cos(angle)
                y = center_y + r_noise * np.sin(angle)
                positions.append([x, y])
        
        positions = np.array(positions)
        
        # 确保起点和终点准确
        positions[0] = start
        positions[-1] = end
        
        # 计算速度和角速度
        velocities = np.diff(positions, axis=0, prepend=positions[0:1])
        angles = np.arctan2(velocities[:, 1], velocities[:, 0])
        angular_velocities = np.diff(np.unwrap(angles), prepend=angles[0:1])
        
        return positions, velocities, angular_velocities

    def get_trajectories_for_condition(self, condition: int, num_reps: int = 20) -> Dict:
        """
        为指定条件生成轨迹：
        条件1: Self运动, Peer静止
        条件2: Self静止, Peer运动  
        条件3: Self运动, Peer运动
        条件4: Self静止, Peer静止
        
        现在使用半圆弧轨迹：左半圆到A，右半圆到B
        """
        all_self_pos, all_self_vel, all_self_ang_vel = [], [], []
        all_peer_pos, all_peer_vel, all_peer_ang_vel = [], [], []
        
        # 定义运动模式
        movement_patterns = {
            1: (True, False),   # Self运动, Peer静止
            2: (False, True),   # Self静止, Peer运动
            3: (True, True),    # Both运动
            4: (False, False)   # Both静止
        }
        self_moving, peer_moving = movement_patterns[condition]
        
        for i in range(num_reps):
            # 交替选择左半圆(到A)和右半圆(到B)
            if i % 2 == 0:
                arc_type = "left"   # 左半圆弧到A
                target = self.end_A
            else:
                arc_type = "right"  # 右半圆弧到B  
                target = self.end_B
            
            # 为静止的agent选择一个固定位置
            static_pos = self.start_S  # 静止agent停留在起点S
            
            # 生成Self轨迹
            if self_moving:
                self_pos, self_vel, self_ang_vel = self._generate_arc_path(
                    self.start_S, target, arc_type, self_moving)
            else:
                self_pos, self_vel, self_ang_vel = self._generate_arc_path(
                    static_pos, static_pos, arc_type, self_moving)
            
            # 生成Peer轨迹  
            if peer_moving:
                peer_pos, peer_vel, peer_ang_vel = self._generate_arc_path(
                    self.start_S, target, arc_type, peer_moving)
            else:
                peer_pos, peer_vel, peer_ang_vel = self._generate_arc_path(
                    static_pos, static_pos, arc_type, peer_moving)
            
            all_self_pos.append(self_pos)
            all_self_vel.append(self_vel) 
            all_self_ang_vel.append(self_ang_vel)
            all_peer_pos.append(peer_pos)
            all_peer_vel.append(peer_vel)
            all_peer_ang_vel.append(peer_ang_vel)
        
        return {
            'self_pos': np.array(all_self_pos), 
            'self_vel': np.array(all_self_vel), 
            'self_ang_vel': np.array(all_self_ang_vel),
            'peer_pos': np.array(all_peer_pos), 
            'peer_vel': np.array(all_peer_vel), 
            'peer_ang_vel': np.array(all_peer_ang_vel),
        }

# --- 修正的可视化函数 ---
def visualize_neuron(args_tuple):
    """为单个神经元的分析结果生成并保存可视化图像"""
    result, config_dict, output_dir = args_tuple
    config = Config.from_dict(config_dict)
    
    neuron_idx = result.neuron_idx
    fig, axes = plt.subplots(1, 4, figsize=(24, 6.5))
    fig.suptitle(f'Neuron {neuron_idx} - Classified as: {result.cell_type}', fontsize=18, fontweight='bold')
    
    # 统一颜色带范围
    vmax = 0.3
    vmin = 0.0

    # 条件标签和期望激活
    condition_info = {
        1: {"label": "Self Move, Peer Static", "expected": "PC: Strong, SPC: Weak"},
        2: {"label": "Self Static, Peer Move", "expected": "PC: Weak, SPC: Strong"}, 
        3: {"label": "Both Move", "expected": "PC: Strong, SPC: Strong"},
        4: {"label": "Both Static", "expected": "Both: Weak"}
    }

    for i, condition in enumerate([1, 2, 3, 4]):
        ax = axes[i]
        data = result.condition_data[condition]
        
        grid_size = 40
        heatmap = np.zeros((grid_size, grid_size))
        counts = np.zeros((grid_size, grid_size))

        positions = data.positions_to_plot
        activations = data.activations

        # 逐点累加激活值到对应的空间位置
        for traj_idx in range(positions.shape[0]):
            for step_idx in range(positions.shape[1]):
                pos = positions[traj_idx, step_idx]
                act = activations[traj_idx, step_idx]
                
                grid_x = int(np.clip((pos[0] / config.ENV_SIZE) * grid_size, 0, grid_size - 1))
                grid_y = int(np.clip((pos[1] / config.ENV_SIZE) * grid_size, 0, grid_size - 1))
                
                heatmap[grid_y, grid_x] += act
                counts[grid_y, grid_x] += 1
        
        # 计算平均激活
        with np.errstate(divide='ignore', invalid='ignore'):
            avg_heatmap = np.nan_to_num(heatmap / counts)
        
        # 高斯平滑
        smoothed_heatmap = gaussian_filter(avg_heatmap, sigma=1.5)
        
        # 绘制热力图
        im = ax.imshow(smoothed_heatmap, cmap='jet', origin='lower',
                       extent=[0, config.ENV_SIZE, 0, config.ENV_SIZE], vmin=vmin, vmax=vmax)
        
        # # 绘制轨迹线（只绘制前几条作为参考）
        # for traj_idx in range(min(3, positions.shape[0])):
        #     traj_pos = positions[traj_idx, :, :]
        #     ax.plot(traj_pos[:, 0], traj_pos[:, 1], 
        #            color='white', alpha=0.7, linewidth=2.0)
        #     # 标记起点和终点
        #     ax.scatter(traj_pos[0, 0], traj_pos[0, 1], color='lime', s=60, alpha=0.8, marker='o', label='Start' if traj_idx == 0 else "")
        #     ax.scatter(traj_pos[-1, 0], traj_pos[-1, 1], color='red', s=60, alpha=0.8, marker='s', label='End' if traj_idx == 0 else "")

        # 设置标题和标签
        info = condition_info[condition]
        ax.set_title(f'Cond {condition}: {info["label"]}\n'
                    f'Trajectory: {data.trajectory_key.upper()}, Peak: {data.peak_activation:.3f}\n'
                    f'{info["expected"]}', fontsize=9)
        ax.set_xlabel('X Position')
        ax.set_ylabel('Y Position')
        ax.set_xlim(0, config.ENV_SIZE)
        ax.set_ylim(0, config.ENV_SIZE)
        ax.grid(True, linestyle='--', alpha=0.3)

    # 添加颜色条
    fig.colorbar(im, ax=axes.ravel().tolist(), shrink=0.7, label=f'Activation (0-{vmax})')
    plt.tight_layout(rect=[0, 0, 1, 0.95])
    
    # 保存图像
    filename = f'neuron_{neuron_idx:04d}_{result.cell_type.lower().replace(" ", "_")}.png'
    save_path = os.path.join(output_dir, filename)
    plt.savefig(save_path, dpi=200, bbox_inches='tight')
    plt.close()

def main():
    parser = argparse.ArgumentParser(description="Visualize Social Place Cells from a trained model (GPU Accelerated).")
    parser.add_argument('--model_path', type=str, required=True, help='Path to the trained best_model.pth file.')
    parser.add_argument('--output_dir', type=str, default='social_cell_visualizations_corrected', help='Directory to save visualizations.')
    parser.add_argument('--workers', type=int, default=max(1, mp.cpu_count() - 2), help='Number of parallel workers for PLOTTING.')
    args = parser.parse_args()

    start_time = time.time()
    os.makedirs(args.output_dir, exist_ok=True)
    config = Config()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # --- 1. 加载模型 ---
    print("Loading model...")
    place_cells = PlaceCellEnsemble(n_cells=config.PLACE_CELLS_N, scale=config.PLACE_CELLS_SCALE, 
                                   pos_min=0, pos_max=config.ENV_SIZE, seed=config.SEED)
    hd_cells = HeadDirectionCellEnsemble(n_cells=config.HD_CELLS_N, 
                                       concentration=config.HD_CELLS_CONCENTRATION, seed=config.SEED)
    model_config = {
        'hidden_size': config.HIDDEN_SIZE, 
        'bottleneck_size': config.LATENT_DIM, 
        'dropout_rate': config.DROPOUT_RATE
    }
    model = SocialGridCellNetwork(place_cells, hd_cells, model_config)
    
    state_dict = torch.load(args.model_path, map_location=device)
    if all(key.startswith('module.') for key in state_dict.keys()):
        state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}
    model.load_state_dict(state_dict)
    model.to(device)
    model.eval()

    # --- 2. 批量分析所有神经元 ---
    num_neurons = config.LATENT_DIM
    trajectory_gen = TrajectoryGenerator(config)
    all_results = {i: NeuronAnalysisResult(neuron_idx=i) for i in range(num_neurons)}

    print(f"Analyzing all {num_neurons} neurons using GPU batching...")
    with torch.no_grad():
        for condition in tqdm(range(1, 5), desc="Processing Conditions"):
            print(f"\nProcessing Condition {condition}...")
            trajs = trajectory_gen.get_trajectories_for_condition(condition, num_reps=20)
            
            # 准备模型输入
            self_vel_input = torch.cat([
                torch.from_numpy(trajs['self_vel']).float(), 
                torch.from_numpy(trajs['self_ang_vel']).float().unsqueeze(-1)
            ], dim=-1).to(device)
            
            self_init_pos = torch.from_numpy(trajs['self_pos'][:, 0, :]).float().to(device)
            self_init_hd = torch.from_numpy(np.arctan2(trajs['self_vel'][:, 0, 1], 
                                                      trajs['self_vel'][:, 0, 0])).float().to(device)
            
            peer_vel_input = torch.cat([
                torch.from_numpy(trajs['peer_vel']).float(), 
                torch.from_numpy(trajs['peer_ang_vel']).float().unsqueeze(-1)
            ], dim=-1).to(device)
            
            peer_init_pos = torch.from_numpy(trajs['peer_pos'][:, 0, :]).float().to(device)
            peer_init_hd = torch.from_numpy(np.arctan2(trajs['peer_vel'][:, 0, 1], 
                                                      trajs['peer_vel'][:, 0, 0])).float().to(device)

            # 前向传播获取激活
            outputs = model(self_vel_input, self_init_pos, self_init_hd, 
                          peer_vel_input, peer_init_pos, peer_init_hd)
            all_activations = outputs['bottleneck_self'].cpu().numpy()  # [num_reps, seq_len, num_neurons]

            # 为每个神经元存储结果
            for i in range(num_neurons):
                neuron_activations = all_activations[:, :, i]
                all_results[i].condition_data[condition] = ConditionAnalysis(
                    peak_activation=np.max(neuron_activations),
                    activations=neuron_activations,
                    positions_to_plot={'self': trajs['self_pos'], 'peer': trajs['peer_pos']}
                )

    # --- 3. 分类所有神经元 ---
    print("\nClassifying neurons...")
    
    # 根据bat hippocampus论文的标准定义轨迹映射
    trajectory_map = {
        "Pure Place Cell": {1: "self", 2: "self", 3: "self", 4: "self"},  # 总是关注Self
        "Weak Place Cell": {1: "self", 2: "self", 3: "self", 4: "self"},
        "Pure SPC": {1: "peer", 2: "peer", 3: "peer", 4: "peer"},         # 总是关注Peer
        "Weak SPC": {1: "peer", 2: "peer", 3: "peer", 4: "peer"},
        "Special SPC": {1: "self", 2: "peer", 3: "self", 4: "peer"},      # 条件1,3关注Self，条件2,4关注Peer
        "Mixed Response": {1: "self", 2: "peer", 3: "self", 4: "peer"},
        "Other": {1: "self", 2: "peer", 3: "self", 4: "peer"}
    }
    
    categorized_cells = defaultdict(list)
    
    for i in range(num_neurons):
        res = all_results[i]
        peak_results = {cond: data.peak_activation for cond, data in res.condition_data.items()}
        
        print(f"\nNeuron {i}:")
        res.cell_type = CellTypeDetector.classify(peak_results)
        print(f"  Classified as: {res.cell_type}")
        
        # 根据分类结果设置正确的轨迹
        for cond_num, data in res.condition_data.items():
            plot_key = trajectory_map[res.cell_type][cond_num]
            data.trajectory_key = plot_key
            data.positions_to_plot = data.positions_to_plot[plot_key]

        categorized_cells[res.cell_type].append(res)

    print("\n" + "="*50)
    print("FINAL ANALYSIS RESULTS")
    print("="*50)
    for cell_type, results in categorized_cells.items():
        print(f"Found {len(results):3d} neurons of type: {cell_type}")
    print("="*50)

    # --- 4. 生成可视化 ---
    print("\nGenerating visualizations for interesting cells...")
    tasks = []
    for cell_type, results in categorized_cells.items():
        if cell_type != "Other":
            # 按激活强度排序，选择最有代表性的
            sorted_results = sorted(results, 
                                  key=lambda x: max(d.peak_activation for d in x.condition_data.values()), 
                                  reverse=True)
            # 每种类型最多可视化15个
            for result in sorted_results[:15]:
                tasks.append((result, config.to_dict(), args.output_dir))
    
    if tasks:
        print(f"Creating visualizations for {len(tasks)} neurons...")
        with mp.Pool(processes=args.workers) as pool:
            list(tqdm(pool.imap_unordered(visualize_neuron, tasks), 
                     total=len(tasks), desc="Saving Plots"))
    else:
        print("No interesting cells found to visualize!")

    total_time = time.time() - start_time
    print(f"\nAll visualizations saved in '{args.output_dir}'")
    print(f"Total execution time: {total_time:.2f} seconds.")

if __name__ == '__main__':
    try:
        mp.set_start_method('spawn')
    except RuntimeError:
        pass
    main()