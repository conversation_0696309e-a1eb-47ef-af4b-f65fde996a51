# models/toroidal_grid_cell.py
import torch
import torch.nn as nn
import torch.nn.functional as F

class GridCellNetwork(nn.Module):
    """
    LSTM-based network that receives velocity as input and predicts place/HD cell activity.
    其中，初始状态映射模块（用于生成 LSTM 的 h0 和 c0）已在 __init__ 中定义为 self.init_fc，
    使其成为模型参数的一部分，从而实现稳定且可学习的初始化。
    """

    def __init__(
        self,
        place_cells,
        hd_cells,
        input_size=3,
        hidden_size=128,
        bottleneck_size=256,
        dropout_rate=0.5,
    ):
        super().__init__()
        self.place_cells = place_cells
        self.hd_cells = hd_cells
        self.hidden_size = hidden_size
        self.bottleneck_size = bottleneck_size

        # LSTM层
        self.lstm = nn.LSTM(
            input_size=input_size,  # 例如：速度(2D) + 角速度(1D) => 3
            hidden_size=hidden_size,
            num_layers=1,
            batch_first=True
        )

        # Bottleneck层
        self.bottleneck = nn.Sequential(
            nn.Linear(hidden_size, bottleneck_size, bias=False),
            nn.Dropout(dropout_rate)
        )

        # 输出预测器：place predictor 和 HD predictor
        self.place_predictor = nn.Linear(bottleneck_size, place_cells.n_cells)
        self.hd_predictor = nn.Linear(bottleneck_size, hd_cells.n_cells)

        # 将 LSTM 状态初始化的全连接层移到 __init__ 中
        # 输入为 place_cells 和 hd_cells 的激活拼接（维度：n_place + n_hd），输出为 hidden_size*2（后续拆分为 h0 和 c0）
        self.init_fc = nn.Linear(place_cells.n_cells + hd_cells.n_cells, hidden_size * 2)
        # 在__init__中定义两个独立的初始化层
        self.h0_init = nn.Linear(place_cells.n_cells + hd_cells.n_cells, hidden_size)
        self.c0_init = nn.Linear(place_cells.n_cells + hd_cells.n_cells, hidden_size)

    def forward(self, velocity, init_pos, init_hd):
        """
        velocity: [B, seq, 3]
        init_pos: [B, 2]
        init_hd:  [B]
        返回：包含 place_logits、hd_logits、bottleneck 和 lstm_out 的字典
        """
        # print("init_pos range:", init_pos.min().item(), init_pos.max().item())
        # print("init_place_act mean:", init_place_act.mean().item(), "std:", init_place_act.std().item())


        batch_size, seq_len = velocity.shape[:2]

        # 计算初始激活（用于初始化 LSTM 隐状态）
        init_place_act = self.place_cells.compute_activation(init_pos)  # [B, n_place]
        init_hd_act = self.hd_cells.compute_activation(init_hd)          # [B, n_hd]
        init_repr = torch.cat([init_place_act, init_hd_act], dim=-1)       # [B, n_place+n_hd]

        # 使用预先定义的全连接层获得 h0 和 c0 的初始值
        h0 = self.h0_init(init_repr).unsqueeze(0).contiguous()
        c0 = self.c0_init(init_repr).unsqueeze(0).contiguous()

        # LSTM 前向传播
        lstm_out, _ = self.lstm(velocity, (h0, c0))  # [B, seq, hidden_size]

        # Bottleneck 层及预测器
        B, T, H = lstm_out.shape
        lstm_out_flat = lstm_out.reshape(B * T, H)
        bottleneck_flat = self.bottleneck(lstm_out_flat)  # [B*T, bottleneck_size]
        place_logits_flat = self.place_predictor(bottleneck_flat)
        hd_logits_flat = self.hd_predictor(bottleneck_flat)

        # 恢复为 [B, T, -1]
        place_logits = place_logits_flat.reshape(B, T, -1)
        hd_logits = hd_logits_flat.reshape(B, T, -1)

        return {
            'place_logits': place_logits,  # [B, seq, n_place]
            'hd_logits': hd_logits,        # [B, seq, n_hd]
            'bottleneck': bottleneck_flat.reshape(B, T, -1),
            'lstm_out': lstm_out,
        }


# class GridCellSupervisedLoss(nn.Module):
#     """
#     包含 place 和 HD 的 KL 散度损失（监督路径积分训练）。
#     修改为排除第一个时间步的损失计算，因为初始点已知。
#     """

#     def __init__(self):
#         super().__init__()

#     def forward(self, outputs, targets):
#         # outputs 包含 'place_logits' 和 'hd_logits'
#         # targets 包含 'place_targets' 和 'hd_targets'
        
#         # 排除第一个时间步，只计算从t=1开始的损失
#         # 所有tensor的形状应该是 [B, seq, dim]
#         place_logits = outputs['place_logits'][:, 1:, :]  # 从第二个时间步开始
#         place_targets = targets['place_targets'][:, 1:, :]
        
#         hd_logits = outputs['hd_logits'][:, 1:, :]  # 从第二个时间步开始
#         hd_targets = targets['hd_targets'][:, 1:, :]
        
#         # 计算从第二个时间步开始的损失
#         place_log_softmax = F.log_softmax(place_logits, dim=-1)
#         place_loss = F.kl_div(
#             place_log_softmax.reshape(-1, place_log_softmax.size(-1)),
#             place_targets.reshape(-1, place_targets.size(-1)),
#             reduction='batchmean'
#         )

#         hd_log_softmax = F.log_softmax(hd_logits, dim=-1)
#         hd_loss = F.kl_div(
#             hd_log_softmax.reshape(-1, hd_log_softmax.size(-1)),
#             hd_targets.reshape(-1, hd_targets.size(-1)),
#             reduction='batchmean'
#         )

#         total_loss = place_loss + hd_loss

#         return {
#             'total': total_loss,
#             'place': place_loss.detach().cpu().item(),
#             'hd': hd_loss.detach().cpu().item(),
#         }

class InitialStateRegularizedLoss(nn.Module):
    """
    在GridCellSupervisedLoss基础上，添加初始状态正则化
    特别重点关注前几帧的预测准确性
    """

    def __init__(self, initial_frames_weight=5.0, decay_factor=0.8):
        """
        Args:
            initial_frames_weight: 对初始帧的额外权重因子
            decay_factor: 权重随帧数增加的衰减因子
        """
        super().__init__()
        self.initial_frames_weight = initial_frames_weight
        self.decay_factor = decay_factor

    def forward(self, outputs, targets):
        # 获取所有时间步的预测和目标
        place_logits = outputs['place_logits']  # [B, seq, n_place]
        place_targets = targets['place_targets']  # [B, seq, n_place]
        
        hd_logits = outputs['hd_logits']  # [B, seq, n_hd]
        hd_targets = targets['hd_targets']  # [B, seq, n_hd]
        
        batch_size, seq_len = place_logits.shape[:2]
        
        # 初始化损失
        total_place_loss = 0.0
        total_hd_loss = 0.0
        total_weight = 0.0
        
        # 为每个时间步计算加权损失
        for t in range(seq_len):
            # 计算当前时间步的权重，初始帧权重高，然后逐渐衰减
            if t == 0:  # 第一帧（初始位置）给予最高权重
                weight = self.initial_frames_weight * 2.0  # 第一帧权重加倍
            elif t < 5:  # 前5帧给予较高权重
                weight = self.initial_frames_weight * (self.decay_factor ** (t-1))
            else:
                weight = 1.0  # 其余帧使用标准权重
            
            # 计算当前时间步的place和hd损失
            place_log_softmax_t = F.log_softmax(place_logits[:, t], dim=-1)
            place_loss_t = F.kl_div(
                place_log_softmax_t,
                place_targets[:, t],
                reduction='batchmean'
            )
            
            hd_log_softmax_t = F.log_softmax(hd_logits[:, t], dim=-1)
            hd_loss_t = F.kl_div(
                hd_log_softmax_t,
                hd_targets[:, t],
                reduction='batchmean'
            )
            
            # 加权累加
            total_place_loss += weight * place_loss_t
            total_hd_loss += weight * hd_loss_t
            total_weight += weight
        
        # 归一化以获得加权平均损失
        avg_place_loss = total_place_loss / total_weight
        avg_hd_loss = total_hd_loss / total_weight
        total_loss = avg_place_loss + avg_hd_loss
        
        # 添加明确的初始点一致性损失（确保第一点的预测与给定的初始点保持一致）
        first_place_pred = F.log_softmax(place_logits[:, 0], dim=-1)
        first_place_target = place_targets[:, 0]
        init_consistency_loss = F.kl_div(
            first_place_pred,
            first_place_target,
            reduction='batchmean'
        ) * 2.0  # 额外强调初始点一致性
        
        # 添加连续性损失（确保前后帧预测的平滑过渡）
        continuity_loss = 0.0
        if seq_len > 1:
            for t in range(1, min(6, seq_len)):  # 只对前5个过渡帧应用连续性约束
                continuity_weight = 1.0 - 0.15 * (t-1)  # 权重从1.0开始逐渐减小
                place_pred_prev = F.softmax(place_logits[:, t-1], dim=-1)
                place_pred_curr = F.softmax(place_logits[:, t], dim=-1)
                
                # 计算前后帧预测之间的KL散度，鼓励连续性
                continuity_loss += continuity_weight * F.kl_div(
                    torch.log(place_pred_curr + 1e-10),
                    place_pred_prev,
                    reduction='batchmean'
                )
        
        # 将所有损失组合
        final_loss = total_loss + init_consistency_loss + 0.1 * continuity_loss
        
        return {
            'total': final_loss,
            'place': avg_place_loss.detach().cpu().item(),
            'hd': avg_hd_loss.detach().cpu().item(),
            'init_consistency': init_consistency_loss.detach().cpu().item(),
            'continuity': continuity_loss.detach().cpu().item() if isinstance(continuity_loss, torch.Tensor) else 0.0,
        }