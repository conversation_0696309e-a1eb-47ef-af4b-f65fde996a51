# models/social_grid_cell.py (Relational Task Version)
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, <PERSON><PERSON>


try:
    from .place_hd_cells import Place<PERSON>ellEnsemble, HeadDirectionCellEnsemble
except ImportError:
    from place_hd_cells import Place<PERSON>ellEnsemble, HeadDirectionCellEnsemble


class SocialGridCellNetwork(nn.Module):
    """
    在不对称输入的基础上，增加了一个关系预测头 (relational head)，
    用于预测 self 和 peer 之间的未来距离，从而催生 Special SPC。
    """
    def __init__(self, place_cells: PlaceCellEnsemble, hd_cells: HeadDirectionCellEnsemble, config: Dict):
        super().__init__()
        self.place_cells = place_cells
        self.hd_cells = hd_cells
        self.hidden_size = config['HIDDEN_SIZE']
        self.bottleneck_size = config['LATENT_DIM']
        self.ego_token_size = config.get('ego_token_size', 4)

        self.ego_token = nn.Parameter(torch.randn(1, 1, self.ego_token_size))
        lstm_input_size = 3 + self.ego_token_size

        self.path_integrator_lstm = nn.LSTM(
            input_size=lstm_input_size,
            hidden_size=self.hidden_size,
            num_layers=1,
            batch_first=True
        )

        init_input_size = self.place_cells.n_cells + self.hd_cells.n_cells
        self.h0_generator = nn.Linear(init_input_size, self.hidden_size)
        self.c0_generator = nn.Linear(init_input_size, self.hidden_size)

        self.joint_bottleneck_layer = nn.Sequential(
            nn.Linear(self.hidden_size, self.bottleneck_size),
            nn.ReLU(),
            nn.Dropout(config['dropout_rate'])
        )

        self.self_place_predictor = nn.Linear(self.bottleneck_size, self.place_cells.n_cells)
        self.self_hd_predictor = nn.Linear(self.bottleneck_size, self.hd_cells.n_cells)
        
        self.peer_place_predictor = nn.Linear(self.bottleneck_size, self.place_cells.n_cells)
        self.peer_hd_predictor = nn.Linear(self.bottleneck_size, self.hd_cells.n_cells)

        # --- NEW: 关系预测头 (Relational Head) ---
        # 这个头从联合表征中预测一个标量值（例如，两者间的距离）
        self.relational_head = nn.Sequential(
            nn.Linear(self.bottleneck_size, 64),
            nn.ReLU(),
            nn.Linear(64, 1) # 输出一个标量
        )

    def _get_initial_state(self, init_pos: torch.Tensor, init_hd: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        init_place_activation = self.place_cells.compute_activation(init_pos)
        init_hd_activation = self.hd_cells.compute_activation(init_hd)
        init_representation = torch.cat([init_place_activation, init_hd_activation], dim=-1)
        h0 = self.h0_generator(init_representation).unsqueeze(0)
        c0 = self.c0_generator(init_representation).unsqueeze(0)
        return h0, c0

    def forward(self, self_vel: torch.Tensor, self_init_pos: torch.Tensor, self_init_hd: torch.Tensor,
                peer_vel: torch.Tensor, peer_init_pos: torch.Tensor, peer_init_hd: torch.Tensor) -> Dict:
        batch_size, seq_len = self_vel.shape[:2]

        ego_token_expanded = self.ego_token.expand(batch_size, seq_len, -1)
        peer_token_expanded = torch.zeros_like(ego_token_expanded)
        self_vel_tagged = torch.cat([self_vel, ego_token_expanded], dim=-1)
        peer_vel_tagged = torch.cat([peer_vel, peer_token_expanded], dim=-1)

        h0_self, c0_self = self._get_initial_state(self_init_pos, self_init_hd)
        h0_peer, c0_peer = self._get_initial_state(peer_init_pos, peer_init_hd)

        lstm_out_self, _ = self.path_integrator_lstm(self_vel_tagged, (h0_self, c0_self))
        lstm_out_peer, _ = self.path_integrator_lstm(peer_vel_tagged, (h0_peer, c0_peer))

        joint_representation = lstm_out_self + lstm_out_peer
        
        joint_representation_flat = joint_representation.reshape(batch_size * seq_len, self.hidden_size)
        joint_bottleneck = self.joint_bottleneck_layer(joint_representation_flat)

        relational_hidden_activations = self.relational_head[1](self.relational_head[0](joint_bottleneck))

        self_place_logits_flat = self.self_place_predictor(joint_bottleneck)
        self_hd_logits_flat = self.self_hd_predictor(joint_bottleneck)
        peer_place_logits_flat = self.peer_place_predictor(joint_bottleneck)
        peer_hd_logits_flat = self.peer_hd_predictor(joint_bottleneck)

        # --- NEW: 通过关系头进行预测 ---
        predicted_distance_flat = self.relational_head(joint_bottleneck)

        def reshape_output(tensor):
            return tensor.reshape(batch_size, seq_len, -1)

        return {
            'self_place_logits': reshape_output(self_place_logits_flat),
            'self_hd_logits': reshape_output(self_hd_logits_flat),
            'peer_place_logits': reshape_output(peer_place_logits_flat),
            'peer_hd_logits': reshape_output(peer_hd_logits_flat),
            'predicted_distance': reshape_output(predicted_distance_flat).squeeze(-1), # Shape: [B, T]
            'bottleneck_self': reshape_output(joint_bottleneck),
            'relational_activations': reshape_output(relational_hidden_activations),
        }

class SocialNavigationLoss(nn.Module):
    """
    修改损失函数，加入关系预测的损失。
    """
    def __init__(self, relational_loss_weight: float = 0.1):
        super().__init__()
        self.kld_loss = nn.KLDivLoss(reduction='batchmean')
        self.distance_loss = nn.MSELoss()
        self.relational_loss_weight = relational_loss_weight

    def _calculate_pi_loss(self, logits: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """计算路径积分 (Path Integration) 的损失"""
        logits = logits[:, 1:, :]
        targets = targets[:, 1:, :]
        log_probs = F.log_softmax(logits, dim=-1)
        return self.kld_loss(log_probs.flatten(0, 1), targets.flatten(0, 1))

    def forward(self, outputs: Dict, targets: Dict) -> Dict:
        # 1. 路径积分损失 (与之前相同)
        loss_self_place = self._calculate_pi_loss(outputs['self_place_logits'], targets['self_place_targets'])
        loss_self_hd = self._calculate_pi_loss(outputs['self_hd_logits'], targets['self_hd_targets'])
        loss_self = loss_self_place + loss_self_hd

        loss_peer_place = self._calculate_pi_loss(outputs['peer_place_logits'], targets['peer_place_targets'])
        loss_peer_hd = self._calculate_pi_loss(outputs['peer_hd_logits'], targets['peer_hd_targets'])
        loss_peer = loss_peer_place + loss_peer_hd
        
        path_integration_loss = loss_self + loss_peer

        # --- 2. NEW: 关系预测损失 ---
        # 计算真实的未来距离作为目标
        true_distance = torch.norm(targets['self_positions'] - targets['peer_positions'], dim=-1)
        predicted_distance = outputs['predicted_distance']
        
        # 我们只关心对未来的预测，所以从 t=1 开始计算损失
        relational_loss = self.distance_loss(predicted_distance[:, 1:], true_distance[:, 1:])
        
        # 3. 组合总损失
        total_loss = path_integration_loss + self.relational_loss_weight * relational_loss
        
        return {
            'total': total_loss,
            'path_integration_loss': path_integration_loss.detach().item(),
            'relational_loss': relational_loss.detach().item(),
            'loss_self': loss_self.detach().item(),
            'loss_peer': loss_peer.detach().item(),
        }
